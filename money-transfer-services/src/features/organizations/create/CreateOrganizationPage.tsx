'use client'
import { <PERSON>, Divider, Grid, Stack, Typography } from '@mui/material'
import React, { useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import { BackButton } from '@dtbx/ui/components/Button'
import { CustomStepper } from '@/utils/CustomStepper'
import CompanyDetails from './CompanyDetails'
import CheckerVerification from './CheckerVerification'
import { useAppDispatch, useAppSelector } from '@/store'
import { setCurrentStep } from '@/store/reducers/organizations/organizations'

export enum CreateOrganisationSteps {
  COMPANY_DETAILS = 1,
  CHECKER_VERIFICATION,
  COMPLETE,
}

type OnboardingStep = {
  title: string
  description?: string
}
const STEPS: OnboardingStep[] = [
  {
    title: 'Provide Company Details',
    description: 'Organization details including DTB account number.',
  },
  {
    title: 'Checker Verification',
    description: 'Accounts details are verified.',
  },
  {
    title: 'Complete',
    description: 'Registration Complete.',
  },
]

const CreateOrganisationPage = () => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const currentStep = useAppSelector((state) => state.organizations.currentStep)
  const createdOrg = useAppSelector(
    (state) => state.organizations.createdOrganization
  )
  const [confirmationDetails, setConfirmationDetails] = useState({
    mtoName: '',
    mtoAccountNumber: '',
    countryId: '',
  })
  const steps: OnboardingStep[] = useMemo(() => {
    let steps: OnboardingStep[] = STEPS
    return steps
  }, [])

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
        height: '100%',
      }}
    >
      <Stack
        sx={{ backgroundColor: '#FFFFFF' }}
        direction="row"
        paddingInline={3}
        paddingBlock={2}
        spacing={2}
        alignItems="flex-start"
      >
        <BackButton onClick={() => router.back()} />
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            color: '#000A12',
          }}
        >
          Create MTO
        </Typography>
      </Stack>
      <Divider />
      <Box sx={{ flexGrow: 1, height: '100%', overflow: 'hidden' }}>
        <Grid
          container
          spacing={2}
          sx={{ height: '100%', overflowY: { sm: 'auto', md: 'hidden' } }}
        >
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 4, lg: 3 }}
            sx={{ height: { md: '100%' }, overflowY: 'auto' }}
          >
            <CustomStepper
              steps={steps}
              currentStep={currentStep}
              setStep={(step) => dispatch(setCurrentStep(step))}
            />
          </Grid>
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 8, lg: 9 }}
            sx={{ height: { md: '100%' }, overflowY: { md: 'auto' } }}
          >
            <Box width={{ sm: '100%', md: '100%', lg: '70%' }}>
              {(() => {
                switch (currentStep) {
                  case CreateOrganisationSteps.COMPANY_DETAILS:
                    return (
                      <CompanyDetails
                        setStep={(step) => dispatch(setCurrentStep(step))}
                      />
                    )
                  case CreateOrganisationSteps.CHECKER_VERIFICATION:
                  case CreateOrganisationSteps.COMPLETE:
                    return (
                      <CheckerVerification
                        details={confirmationDetails}
                        onSubmit={() => {
                          dispatch(
                            setCurrentStep(CreateOrganisationSteps.COMPLETE)
                          )
                        }}
                        actionType="CHECKER"
                        requestDetails={createdOrg!}
                      />
                    )
                }
              })()}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  )
}

export default CreateOrganisationPage
