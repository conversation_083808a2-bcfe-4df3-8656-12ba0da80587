import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders, createMockFormik } from '../../../../utils/brokerTestUtils'
import { SelectExistingOrganization } from '../../../../../src/app/settings/brokers/create/FromExistingOrganization'
import { mockCreateBrokerForm, mockOrganizations } from '../../../../stubs/brokerStubs'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getOrganizations: vi.fn(),
}))

// Mock the CustomSearchInput
vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({ placeholder, onChange, endAdornment, ...props }: any) => (
    <div data-testid="custom-search-input">
      <input
        placeholder={placeholder}
        onChange={onChange}
        data-testid="search-input"
        {...props}
      />
      {endAdornment}
    </div>
  ),
}))

// Mock the SearchRounded icon
vi.mock('@mui/icons-material', () => ({
  SearchRounded: () => <div data-testid="search-icon">Search</div>,
  ArrowForward: () => <div data-testid="arrow-forward-icon">Arrow Forward</div>,
  ArrowBackIosNewOutlined: () => <div data-testid="arrow-back-icon">Arrow Back</div>,
}))

describe('FromExistingOrganization Component', () => {
  const mockSetCurrentStage = vi.fn()
  const mockFormik = createMockFormik(mockCreateBrokerForm)

  const defaultProps = {
    setCurrentStage: mockSetCurrentStage,
    formik: mockFormik,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockSetCurrentStage.mockClear()
  })

  const renderComponent = (props = {}, initialState = {}) => {
    return renderWithProviders(
      <SelectExistingOrganization {...defaultProps} {...props} />,
      {
        initialState: {
          loans: {
            organizations: mockOrganizations,
            ...initialState,
          },
        },
      }
    )
  }

  it('should render the component with correct title and subtitle', () => {
    renderComponent()

    // Check for the actual text content from the component
    expect(screen.getByText(/broker details/i)).toBeInTheDocument()
    expect(screen.getByText(/creating a new broker from existing organization/i)).toBeInTheDocument()
    expect(screen.getByText(/select the organization/i)).toBeInTheDocument()
  })

  it('should render back button and handle click', () => {
    renderComponent()

    // The back button is the first button (icon button)
    const backButton = screen.getAllByRole('button')[0]
    expect(backButton).toBeInTheDocument()

    fireEvent.click(backButton)
    expect(mockSetCurrentStage).toHaveBeenCalledWith('GetStarted')
  })

  it('should render search input with correct placeholder', () => {
    renderComponent()

    const searchInput = screen.getByTestId('search-input')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveAttribute('placeholder', 'Search organization by Name')
  })

  it('should render search icon', () => {
    renderComponent()

    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
  })

  it('should call getOrganizations on mount', () => {
    renderComponent()

    expect(screen.getByTestId('search-input')).toBeInTheDocument()
  })

  it('should handle search input change', async () => {
    renderComponent()

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'Test Organization' } })

    expect(searchInput).toHaveValue('Test Organization')
  })

  it('should render organizations as radio options', () => {
    renderComponent()

    const radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toBeInTheDocument()

    const radioButtons = screen.queryAllByRole('radio')
    expect(radioButtons.length).toBeGreaterThanOrEqual(0)
  })

  it('should handle organization selection', async () => {
    renderComponent()

    const radioButtons = screen.queryAllByRole('radio')
    if (radioButtons.length > 0) {
      const firstOrgRadio = radioButtons[0]
      fireEvent.click(firstOrgRadio)

      expect(mockFormik.setFieldValue).toHaveBeenCalled()
    } else {
      expect(screen.getByRole('radiogroup')).toBeInTheDocument()
    }
  })

  it('should handle physical address fields when selecting organization', () => {
    renderComponent()

    const radioButtons = screen.queryAllByRole('radio')
    if (radioButtons.length > 0) {
      const firstOrgRadio = radioButtons[0]
      fireEvent.click(firstOrgRadio)

      expect(mockFormik.setFieldValue).toHaveBeenCalled()
    } else {
      expect(screen.getByRole('radiogroup')).toBeInTheDocument()
    }
  })

  it('should render continue button', () => {
    renderComponent()

    const continueButton = screen.getByRole('button', { name: /continue/i })
    expect(continueButton).toBeInTheDocument()
  })

  it('should handle continue button click', () => {
    renderComponent()

    const radioButtons = screen.queryAllByRole('radio')
    if (radioButtons.length > 0) {
      fireEvent.click(radioButtons[0])

      const continueButton = screen.getByRole('button', { name: /continue/i })

      if (!continueButton.hasAttribute('disabled')) {
        fireEvent.click(continueButton)
        expect(mockSetCurrentStage).toHaveBeenCalledWith('CreateBrokerForm')
      } else {
        expect(continueButton).toBeInTheDocument()
      }
    } else {
      const continueButton = screen.getByRole('button', { name: /continue/i })
      expect(continueButton).toBeInTheDocument()
    }
  })

  it('should render with proper layout and styling', () => {
    renderComponent()

    const mainContainer = screen.getByText(/broker details/i)?.closest('div') || document.body
    expect(mainContainer).toBeInTheDocument()

    const searchContainer = screen.getByTestId('custom-search-input')
    expect(searchContainer).toBeInTheDocument()

    // Check for radio group
    const radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toBeInTheDocument()
  })

  it('should handle empty organizations list', () => {
    renderComponent({}, { organizations: [] })

    const radioButtons = screen.queryAllByRole('radio')
    expect(radioButtons).toHaveLength(0)

    expect(screen.getByTestId('search-input')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument()
  })

  it('should handle organization selection with missing fields gracefully', () => {
    const incompleteOrg = {
      ...mockOrganizations[0],
      email: undefined,
      mobile: undefined,
      physicalAddress: {
        country: '',
        town: '',
        physicalAddress: '',
      },
    }

    renderComponent({}, { organizations: [incompleteOrg] })

    const radioButtons = screen.queryAllByRole('radio')
    if (radioButtons.length > 0) {
      const radioButton = radioButtons[0]
      fireEvent.click(radioButton)

      expect(mockFormik.setFieldValue).toHaveBeenCalled()
    } else {
      expect(screen.getByRole('radiogroup')).toBeInTheDocument()
    }
  })

  it('should maintain selected organization state', () => {
    renderComponent()

    const radioButtons = screen.queryAllByRole('radio')
    if (radioButtons.length >= 2) {
      const firstOrgRadio = radioButtons[0]
      const secondOrgRadio = radioButtons[1]

      fireEvent.click(firstOrgRadio)
      expect(firstOrgRadio).toBeChecked()

      fireEvent.click(secondOrgRadio)
      expect(secondOrgRadio).toBeChecked()
      expect(firstOrgRadio).not.toBeChecked()
    } else {
      expect(screen.getByRole('radiogroup')).toBeInTheDocument()
    }
  })

  it('should handle search with special characters', async () => {
    renderComponent()

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'Test & Co.' } })

    expect(searchInput).toHaveValue('Test & Co.')
  })

  it('should handle rapid search input changes', async () => {
    renderComponent()

    const searchInput = screen.getByTestId('search-input')

    // Type rapidly
    fireEvent.change(searchInput, { target: { value: 'a' } })
    fireEvent.change(searchInput, { target: { value: 'ab' } })
    fireEvent.change(searchInput, { target: { value: 'abc' } })

    // Just verify the final value is set
    expect(searchInput).toHaveValue('abc')
  })

  it('should render radio group with proper accessibility', () => {
    renderComponent()

    const radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toBeInTheDocument()

    const radioButtons = screen.queryAllByRole('radio')
    if (radioButtons.length > 0) {
      radioButtons.forEach(radio => {
        expect(radio).toHaveAttribute('type', 'radio')
      })
    } else {
      expect(radioGroup).toBeInTheDocument()
    }
  })

  it('should handle organization with long name', () => {
    const longNameOrg = {
      ...mockOrganizations[0],
      name: 'This is a very long organization name that might cause layout issues if not handled properly',
    }

    renderComponent({}, { organizations: [longNameOrg] })

    expect(screen.getByRole('radiogroup')).toBeInTheDocument()
  })
})
