import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders } from '../../../../utils/brokerTestUtils'
import BrokerMoreMenu from '../../../../../src/app/settings/brokers/create/BrokerMoreMenu'
import { mockBroker } from '../../../../stubs/brokerStubs'

// Mock the custom router hook
const mockPush = vi.fn()
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: mockPush,
  }),
}))

// Mock the store actions
vi.mock('@/store/reducers', () => ({
  setSelectedBroker: vi.fn((broker) => ({ type: 'SET_SELECTED_BROKER', payload: broker })),
}))

// Mock the child components
vi.mock('../../../../../src/app/settings/brokers/GenerateApiForm', () => ({
  default: ({ open, setOpen, email, onClose }: any) => (
    <div data-testid="generate-api-form">
      {open && (
        <div>
          <span>Generate API Form for {email}</span>
          <button onClick={() => setOpen(false)}>Close API Form</button>
          <button onClick={onClose}>Close via onClose</button>
        </div>
      )}
    </div>
  ),
}))

vi.mock('../../../../../src/app/settings/brokers/BrokerStatusModal', () => ({
  default: ({ open, setOpen, broker }: any) => (
    <div data-testid="broker-status-modal">
      {open && (
        <div>
          <span>Status Modal for {broker.name}</span>
          <button onClick={() => setOpen(false)}>Close Status Modal</button>
        </div>
      )}
    </div>
  ),
}))

describe('BrokerMoreMenu Component', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockPush.mockClear()
    mockDispatch.mockClear()
  })

  const renderComponent = (broker = mockBroker) => {
    return renderWithProviders(<BrokerMoreMenu row={broker} />, {
      initialState: {
        loans: {
          selectedBroker: broker,
        },
      },
    })
  }

  it('should render the more menu button', () => {
    renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    expect(menuButton).toBeInTheDocument()
    expect(menuButton).toHaveAttribute('aria-label', 'more')
  })

  it('should open menu when button is clicked', async () => {
    renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument()
    })
  })

  it('should display correct menu options for active broker', async () => {
    const activeBroker = { ...mockBroker, status: 'Active' }
    renderComponent(activeBroker)
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      expect(screen.getByText('View details')).toBeInTheDocument()
      expect(screen.getByText('Manage API Credentials')).toBeInTheDocument()
      expect(screen.getByText('Deactivate broker')).toBeInTheDocument()
    })
  })

  it('should display correct menu options for blocked broker', async () => {
    const blockedBroker = { ...mockBroker, status: 'Blocked' }
    renderComponent(blockedBroker)
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      expect(screen.getByText('View details')).toBeInTheDocument()
      expect(screen.getByText('Manage API Credentials')).toBeInTheDocument()
      expect(screen.getByText('Activate broker')).toBeInTheDocument()
    })
  })

  it('should display correct menu options for inactive broker', async () => {
    const inactiveBroker = { ...mockBroker, status: 'Inactive' }
    renderComponent(inactiveBroker)
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      expect(screen.getByText('View details')).toBeInTheDocument()
      expect(screen.getByText('Manage API Credentials')).toBeInTheDocument()
      expect(screen.getByText('Re-activate broker')).toBeInTheDocument()
    })
  })

  it('should navigate to view details when "View details" is clicked', async () => {
    const { store } = renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      const viewDetailsOption = screen.getByText('View details')
      fireEvent.click(viewDetailsOption)
    })

    expect(mockPush).toHaveBeenCalledWith('/settings/brokers/view/')
  })

  it('should open API credentials dialog when "Manage API Credentials" is clicked', async () => {
    renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      const apiCredentialsOption = screen.getByText('Manage API Credentials')
      fireEvent.click(apiCredentialsOption)
    })

    expect(screen.getByTestId('generate-api-form')).toBeInTheDocument()
    expect(screen.getByText(`Generate API Form for ${mockBroker.email}`)).toBeInTheDocument()
  })

  it('should open status modal when status option is clicked', async () => {
    renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      const deactivateOption = screen.getByText('Deactivate broker')
      fireEvent.click(deactivateOption)
    })

    expect(screen.getByTestId('broker-status-modal')).toBeInTheDocument()
    expect(screen.getByText(`Status Modal for ${mockBroker.name}`)).toBeInTheDocument()
  })

  it('should close menu after selecting an option', async () => {
    renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      const viewDetailsOption = screen.getByText('View details')
      fireEvent.click(viewDetailsOption)
    })

    await waitFor(() => {
      expect(screen.queryByRole('menu')).not.toBeInTheDocument()
    })
  })

  it('should close API dialog when close button is clicked', async () => {
    renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      const apiCredentialsOption = screen.getByText('Manage API Credentials')
      fireEvent.click(apiCredentialsOption)
    })

    const closeButton = screen.getByText('Close API Form')
    fireEvent.click(closeButton)

    await waitFor(() => {
      expect(screen.queryByText(`Generate API Form for ${mockBroker.email}`)).not.toBeInTheDocument()
    })
  })

  it('should close status modal when close button is clicked', async () => {
    renderComponent()
    
    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      const deactivateOption = screen.getByText('Deactivate broker')
      fireEvent.click(deactivateOption)
    })

    const closeButton = screen.getByText('Close Status Modal')
    fireEvent.click(closeButton)

    await waitFor(() => {
      expect(screen.queryByText(`Status Modal for ${mockBroker.name}`)).not.toBeInTheDocument()
    })
  })

  it('should have proper accessibility attributes', async () => {
    renderComponent()

    const menuButton = screen.getByRole('button', { name: /more/i })
    expect(menuButton).toHaveAttribute('aria-haspopup', 'true')
    fireEvent.click(menuButton)

    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'true')
      expect(menuButton).toHaveAttribute('aria-controls', 'long-menu')
    })
  })

  it('should handle menu close when clicking outside', async () => {
    renderComponent()

    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument()
    })
    fireEvent.click(document.body)

    await new Promise(resolve => setTimeout(resolve, 100))

    expect(screen.getByRole('menu')).toBeInTheDocument()
  })

  it('should render menu with proper structure', async () => {
    renderComponent()

    const menuButton = screen.getByRole('button', { name: /more/i })
    fireEvent.click(menuButton)

    await waitFor(() => {
      const menu = screen.getByRole('menu')
      expect(menu).toBeInTheDocument()

      const menuItems = screen.getAllByRole('menuitem')
      expect(menuItems.length).toBeGreaterThan(0)
    })
  })
})
