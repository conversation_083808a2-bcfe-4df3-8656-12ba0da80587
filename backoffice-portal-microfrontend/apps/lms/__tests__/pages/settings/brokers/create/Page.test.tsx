import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'

import { renderWithProviders } from '../../../../utils/brokerTestUtils'
import CreateBrokerPage from '../../../../../src/app/settings/brokers/create/page'
import { mockCreateBrokerForm } from '../../../../stubs/brokerStubs'

// Mock the custom router hook
const mockPush = vi.fn()
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: mockPush,
  }),
}))

// Mock the store actions
vi.mock('@/store/actions', () => ({
  createBroker: vi.fn(),
  getBankBranches: vi.fn(),
  getLoanProducts: vi.fn(),
  getOrganizations: vi.fn(),
}))

// Mock the access rights utility
vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: vi.fn(() => true),
}))

// Mock the phone validation
vi.mock('mui-tel-input', () => ({
  matchIsValidTel: vi.fn((tel: string) => tel.length >= 10),
  MuiTelInput: ({ value, onChange, label, ...props }: any) => (
    <input
      data-testid="phone-input"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={label}
      {...props}
    />
  ),
}))

// Mock the child components
vi.mock('../../../../../src/app/settings/brokers/create/GetStarted', () => ({
  GetStarted: ({ setCurrentStage }: any) => (
    <div data-testid="get-started">
      <button onClick={() => setCurrentStage('CreateBrokerForm')}>Create New Broker</button>
      <button onClick={() => setCurrentStage('FromExistingOrganization')}>From Existing Org</button>
    </div>
  ),
}))

vi.mock('../../../../../src/app/settings/brokers/create/CreateBrokerForm', () => ({
  CreateBrokerForm: ({ setCurrentStage, formik }: any) => (
    <div data-testid="create-broker-form">
      <button onClick={() => setCurrentStage('GetStarted')}>Back</button>
      <form onSubmit={formik.handleSubmit}>
        <input
          data-testid="broker-name"
          value={formik.values.name}
          onChange={(e) => formik.setFieldValue('name', e.target.value)}
        />
        <button type="submit">Submit</button>
      </form>
    </div>
  ),
}))

vi.mock('../../../../../src/app/settings/brokers/create/FromExistingOrganization', () => ({
  SelectExistingOrganization: ({ setCurrentStage, formik }: any) => (
    <div data-testid="select-existing-organization">
      <button onClick={() => setCurrentStage('GetStarted')}>Back</button>
      <button onClick={() => setCurrentStage('CreateBrokerForm')}>Continue</button>
    </div>
  ),
}))

describe('CreateBrokerPage Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockPush.mockClear()
  })

  const renderComponent = (initialState = {}) => {
    return renderWithProviders(<CreateBrokerPage />, {
      initialState: {
        loans: {
          isLoadingCreateBroker: false,
          isCreateBrokerSuccess: false,
          isCreateBrokerFailure: false,
          bankBranches: [],
          loanProducts: [],
          organizations: [],
          ...initialState,
        },
      },
    })
  }

  it('should render GetStarted component by default', () => {
    renderComponent()

    expect(screen.getByTestId('get-started')).toBeInTheDocument()
    expect(screen.queryByTestId('create-broker-form')).not.toBeInTheDocument()
    expect(screen.queryByTestId('select-existing-organization')).not.toBeInTheDocument()
  })

  it('should navigate to CreateBrokerForm when "Create New Broker" is clicked', async () => {
    renderComponent()

    const createNewBrokerButton = screen.getByText('Create New Broker')
    fireEvent.click(createNewBrokerButton)

    await waitFor(() => {
      expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
      expect(screen.queryByTestId('get-started')).not.toBeInTheDocument()
    })
  })

  it('should navigate to FromExistingOrganization when "From Existing Org" is clicked', async () => {
    renderComponent()

    const fromExistingOrgButton = screen.getByText('From Existing Org')
    fireEvent.click(fromExistingOrgButton)

    await waitFor(() => {
      expect(screen.getByTestId('select-existing-organization')).toBeInTheDocument()
      expect(screen.queryByTestId('get-started')).not.toBeInTheDocument()
    })
  })

  it('should navigate back to GetStarted from CreateBrokerForm', async () => {
    renderComponent()

    // Navigate to CreateBrokerForm
    const createNewBrokerButton = screen.getByText('Create New Broker')
    fireEvent.click(createNewBrokerButton)

    await waitFor(() => {
      expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
    })

    // Navigate back
    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    await waitFor(() => {
      expect(screen.getByTestId('get-started')).toBeInTheDocument()
      expect(screen.queryByTestId('create-broker-form')).not.toBeInTheDocument()
    })
  })

  it('should navigate back to GetStarted from SelectExistingOrganization', async () => {
    renderComponent()

    // Navigate to SelectExistingOrganization
    const fromExistingOrgButton = screen.getByText('From Existing Org')
    fireEvent.click(fromExistingOrgButton)

    await waitFor(() => {
      expect(screen.getByTestId('select-existing-organization')).toBeInTheDocument()
    })

    // Navigate back
    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    await waitFor(() => {
      expect(screen.getByTestId('get-started')).toBeInTheDocument()
      expect(screen.queryByTestId('select-existing-organization')).not.toBeInTheDocument()
    })
  })

  it('should navigate from SelectExistingOrganization to CreateBrokerForm', async () => {
    renderComponent()

    // Navigate to SelectExistingOrganization
    const fromExistingOrgButton = screen.getByText('From Existing Org')
    fireEvent.click(fromExistingOrgButton)

    await waitFor(() => {
      expect(screen.getByTestId('select-existing-organization')).toBeInTheDocument()
    })

    // Navigate to CreateBrokerForm
    const continueButton = screen.getByText('Continue')
    fireEvent.click(continueButton)

    await waitFor(() => {
      expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
      expect(screen.queryByTestId('select-existing-organization')).not.toBeInTheDocument()
    })
  })

  it('should handle form submission', async () => {
    renderComponent()

    // Navigate to CreateBrokerForm
    const createNewBrokerButton = screen.getByText('Create New Broker')
    fireEvent.click(createNewBrokerButton)

    await waitFor(() => {
      expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
    })

    // Fill form and submit
    const nameInput = screen.getByTestId('broker-name')
    const submitButton = screen.getByText('Submit')

    fireEvent.change(nameInput, { target: { value: 'Test Broker' } })
    fireEvent.click(submitButton)

    // Form submission should be handled by the mocked component
    expect(submitButton).toBeInTheDocument()
  })

  it('should initialize formik with correct validation schema', () => {
    renderComponent()

    // Navigate to CreateBrokerForm to trigger formik initialization
    const createNewBrokerButton = screen.getByText('Create New Broker')
    fireEvent.click(createNewBrokerButton)

    expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
  })

  it('should handle form validation errors', async () => {
    renderComponent()

    // Navigate to CreateBrokerForm
    const createNewBrokerButton = screen.getByText('Create New Broker')
    fireEvent.click(createNewBrokerButton)

    await waitFor(() => {
      expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
    })

    // Try to submit empty form
    const submitButton = screen.getByText('Submit')
    fireEvent.click(submitButton)

    // Form should handle validation internally
    expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
  })

  it('should redirect on successful broker creation', async () => {
    renderComponent({ isCreateBrokerSuccess: true })

    // The component should handle success state
    expect(screen.getByTestId('get-started')).toBeInTheDocument()
  })

  it('should handle broker creation failure', () => {
    renderComponent({ isCreateBrokerFailure: true })

    // The component should still render normally
    expect(screen.getByTestId('get-started')).toBeInTheDocument()
  })

  it('should maintain form state during navigation', async () => {
    renderComponent()

    // Navigate to CreateBrokerForm
    const createNewBrokerButton = screen.getByText('Create New Broker')
    fireEvent.click(createNewBrokerButton)

    await waitFor(() => {
      expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
    })

    // Fill form
    const nameInput = screen.getByTestId('broker-name')
    fireEvent.change(nameInput, { target: { value: 'Test Broker' } })

    // Navigate back and forth
    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    await waitFor(() => {
      expect(screen.getByTestId('get-started')).toBeInTheDocument()
    })

    // Navigate back to form
    const createNewBrokerButtonAgain = screen.getByText('Create New Broker')
    fireEvent.click(createNewBrokerButtonAgain)

    await waitFor(() => {
      expect(screen.getByTestId('create-broker-form')).toBeInTheDocument()
    })

    // Form state should be maintained (this is handled by formik)
    expect(screen.getByTestId('broker-name')).toBeInTheDocument()
  })

  it('should handle unknown stage gracefully', async () => {
    renderComponent()

    // The component should render GetStarted by default for any unknown stage
    expect(screen.getByTestId('get-started')).toBeInTheDocument()
  })
})
