import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { renderWithProviders } from '../../../../utils/brokerTestUtils'
import BrokerOption from '../../../../../src/app/settings/brokers/create/BrokerOption'

vi.mock('@mui/icons-material/ChevronRight', () => ({
  default: () => <div data-testid="chevron-right-icon">ChevronRight</div>,
}))

vi.mock('@dtbx/ui/icons', () => ({
  Broker: () => <div data-testid="broker-icon">Broker Icon</div>,
  ExistingBroker: () => <div data-testid="existing-broker-icon">Existing Broker Icon</div>,
}))

describe('BrokerOption Component', () => {
  const mockOnClick = vi.fn()
  
  const defaultProps = {
    imageSrc: <div data-testid="test-icon">Test Icon</div>,
    title: 'Test Broker Option',
    description: 'This is a test description for the broker option',
    onClick: mockOnClick,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockOnClick.mockClear()
  })

  const renderComponent = (props = {}) => {
    return renderWithProviders(
      <BrokerOption {...defaultProps} {...props} />
    )
  }

  it('should render the component with all required elements', () => {
    renderComponent()

    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
    expect(screen.getByText('Test Broker Option')).toBeInTheDocument()
    expect(screen.getByText('This is a test description for the broker option')).toBeInTheDocument()
    expect(screen.getByTestId('chevron-right-icon')).toBeInTheDocument()
  })

  it('should call onClick when the component is clicked', () => {
    renderComponent()

    const optionContainer = screen.getByText('Test Broker Option').closest('div')
    fireEvent.click(optionContainer!)

    expect(mockOnClick).toHaveBeenCalledTimes(1)
  })

  it('should render with custom title and description', () => {
    const customProps = {
      title: 'Custom Broker Title',
      description: 'Custom broker description with more details',
    }

    renderComponent(customProps)

    expect(screen.getByText('Custom Broker Title')).toBeInTheDocument()
    expect(screen.getByText('Custom broker description with more details')).toBeInTheDocument()
  })

  it('should render with different icon components', () => {
    const customProps = {
      imageSrc: <div data-testid="custom-icon">Custom Icon</div>,
    }

    renderComponent(customProps)

    expect(screen.getByTestId('custom-icon')).toBeInTheDocument()
    expect(screen.queryByTestId('test-icon')).not.toBeInTheDocument()
  })

  it('should have proper styling and layout structure', () => {
    renderComponent()

    const container = screen.getByText('Test Broker Option').closest('div')
    expect(container).toBeInTheDocument()

    const avatar = screen.getByTestId('test-icon').closest('div')
    expect(avatar).toBeInTheDocument()

    expect(screen.getByTestId('chevron-right-icon')).toBeInTheDocument()
  })

  it('should render title with correct typography styles', () => {
    renderComponent()

    const title = screen.getByText('Test Broker Option')
    expect(title).toBeInTheDocument()
    
    expect(title.tagName).toBe('H6')
  })

  it('should render description with correct typography styles', () => {
    renderComponent()

    const description = screen.getByText('This is a test description for the broker option')
    expect(description).toBeInTheDocument()
    
    // The description should be in a paragraph
    expect(description.tagName).toBe('P')
  })

  it('should handle empty or undefined props gracefully', () => {
    const emptyProps = {
      imageSrc: null,
      title: '',
      description: '',
      onClick: mockOnClick,
    }

    renderComponent(emptyProps)

    const container = screen.getByTestId('chevron-right-icon').closest('div')
    expect(container).toBeInTheDocument()
    
    // Should still be clickable
    fireEvent.click(container!)
    expect(mockOnClick).toHaveBeenCalledTimes(1)
  })

  it('should be keyboard accessible', () => {
    renderComponent()

    const container = screen.getByText('Test Broker Option').closest('div')
    
    fireEvent.keyDown(container!, { key: 'Enter', code: 'Enter' })
    fireEvent.keyUp(container!, { key: 'Enter', code: 'Enter' })
    
    expect(container).toBeInTheDocument()
  })

  it('should maintain responsive design structure', () => {
    renderComponent()

    const container = screen.getByText('Test Broker Option').closest('div')
    expect(container).toBeInTheDocument()


    expect(container).toBeInTheDocument()
  })

  it('should render with Broker icon from DTBX', () => {
    const BrokerIcon = () => <div data-testid="broker-icon">Broker Icon</div>
    
    renderComponent({
      imageSrc: <BrokerIcon />,
    })

    expect(screen.getByTestId('broker-icon')).toBeInTheDocument()
  })

  it('should render with ExistingBroker icon from DTBX', () => {
    const ExistingBrokerIcon = () => <div data-testid="existing-broker-icon">Existing Broker Icon</div>
    
    renderComponent({
      imageSrc: <ExistingBrokerIcon />,
    })

    expect(screen.getByTestId('existing-broker-icon')).toBeInTheDocument()
  })

  it('should handle multiple rapid clicks', () => {
    renderComponent()

    const container = screen.getByText('Test Broker Option').closest('div')
    
    // Simulate rapid clicking
    fireEvent.click(container!)
    fireEvent.click(container!)
    fireEvent.click(container!)

    expect(mockOnClick).toHaveBeenCalledTimes(3)
  })

  it('should maintain proper spacing and alignment', () => {
    renderComponent()

    const icon = screen.getByTestId('test-icon')
    const title = screen.getByText('Test Broker Option')
    const description = screen.getByText('This is a test description for the broker option')
    const chevron = screen.getByTestId('chevron-right-icon')

    expect(icon).toBeInTheDocument()
    expect(title).toBeInTheDocument()
    expect(description).toBeInTheDocument()
    expect(chevron).toBeInTheDocument()

    expect(icon).toBeInTheDocument()
    expect(title).toBeInTheDocument()
    expect(description).toBeInTheDocument()
    expect(chevron).toBeInTheDocument()
  })

  it('should render with long title and description', () => {
    const longProps = {
      title: 'This is a very long title that might wrap to multiple lines in the component',
      description: 'This is a very long description that contains a lot of text and should handle wrapping gracefully within the component layout without breaking the design or functionality of the broker option component',
    }

    renderComponent(longProps)

    expect(screen.getByText(longProps.title)).toBeInTheDocument()
    expect(screen.getByText(longProps.description)).toBeInTheDocument()
  })

  it('should handle special characters in title and description', () => {
    const specialProps = {
      title: 'Broker & Co. (Special Characters) - Test!',
      description: 'Description with special chars: @#$%^&*()_+{}|:"<>?[]\\;\',./',
    }

    renderComponent(specialProps)

    expect(screen.getByText(specialProps.title)).toBeInTheDocument()
    expect(screen.getByText(specialProps.description)).toBeInTheDocument()
  })
})
