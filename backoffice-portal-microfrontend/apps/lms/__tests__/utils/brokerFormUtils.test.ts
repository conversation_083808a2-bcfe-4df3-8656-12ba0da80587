import { describe, it, expect } from 'vitest'
import { mockCreateBrokerForm, mockBroker, mockOrganizations } from '../stubs/brokerStubs'

// Form utility functions to test
const sanitizeFormData = (formData: any) => {
  const sanitized = { ...formData }
  
  // Trim string fields
  const stringFields = ['name', 'email', 'bankName', 'bankCode', 'swiftCode', 'bankAccountNumber', 'branchCode', 'accountBranchName', 'callBackUrl', 'status']
  stringFields.forEach(field => {
    if (sanitized[field] && typeof sanitized[field] === 'string') {
      sanitized[field] = sanitized[field].trim()
    }
  })
  
  // Normalize email
  if (sanitized.email) {
    sanitized.email = sanitized.email.toLowerCase()
  }
  
  // Normalize swift code
  if (sanitized.swiftCode) {
    sanitized.swiftCode = sanitized.swiftCode.toUpperCase()
  }
  
  // Remove spaces from mobile
  if (sanitized.mobile) {
    sanitized.mobile = sanitized.mobile.replace(/\s/g, '')
  }
  
  // Sanitize physical address
  if (sanitized.physicalAddress) {
    sanitized.physicalAddress = {
      country: sanitized.physicalAddress.country?.trim() || '',
      town: sanitized.physicalAddress.town?.trim() || '',
      physicalAddress: sanitized.physicalAddress.physicalAddress?.trim() || '',
    }
  }
  
  return sanitized
}

const validateFormStep = (step: string, formData: any) => {
  const errors: any = {}
  
  switch (step) {
    case 'basic-info':
      if (!formData.name) errors.name = 'Name is required'
      if (!formData.email) errors.email = 'Email is required'
      if (!formData.mobile) errors.mobile = 'Mobile is required'
      break
      
    case 'banking-info':
      if (!formData.bankName) errors.bankName = 'Bank name is required'
      if (!formData.bankCode) errors.bankCode = 'Bank code is required'
      if (!formData.swiftCode) errors.swiftCode = 'Swift code is required'
      if (!formData.bankAccountNumber) errors.bankAccountNumber = 'Account number is required'
      if (!formData.branchCode) errors.branchCode = 'Branch code is required'
      if (!formData.accountBranchName) errors.accountBranchName = 'Branch name is required'
      break
      
    case 'address-info':
      if (!formData.physicalAddress?.country) errors.country = 'Country is required'
      if (!formData.physicalAddress?.town) errors.town = 'Town is required'
      if (!formData.physicalAddress?.physicalAddress) errors.address = 'Address is required'
      break
      
    case 'configuration':
      if (!formData.callBackUrl) errors.callBackUrl = 'Callback URL is required'
      if (!formData.productIds || formData.productIds.length === 0) errors.productIds = 'Products are required'
      if (!formData.status) errors.status = 'Status is required'
      break
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

const getFormProgress = (formData: any) => {
  const totalFields = 13 // Total required fields
  let completedFields = 0
  
  // Basic info (3 fields)
  if (formData.name) completedFields++
  if (formData.email) completedFields++
  if (formData.mobile) completedFields++
  
  // Banking info (6 fields)
  if (formData.bankName) completedFields++
  if (formData.bankCode) completedFields++
  if (formData.swiftCode) completedFields++
  if (formData.bankAccountNumber) completedFields++
  if (formData.branchCode) completedFields++
  if (formData.accountBranchName) completedFields++
  
  // Address info (3 fields)
  if (formData.physicalAddress?.country) completedFields++
  if (formData.physicalAddress?.town) completedFields++
  if (formData.physicalAddress?.physicalAddress) completedFields++
  
  // Configuration (1 field - callback URL, products and status are handled separately)
  if (formData.callBackUrl) completedFields++
  
  const percentage = Math.round((completedFields / totalFields) * 100)
  
  return {
    completedFields,
    totalFields,
    percentage,
    isComplete: completedFields === totalFields && formData.productIds?.length > 0 && !!formData.status
  }
}

const generateFormSummary = (formData: any) => {
  return {
    basicInfo: {
      name: formData.name || 'Not provided',
      email: formData.email || 'Not provided',
      mobile: formData.mobile || 'Not provided',
    },
    bankingInfo: {
      bankName: formData.bankName || 'Not provided',
      bankCode: formData.bankCode || 'Not provided',
      swiftCode: formData.swiftCode || 'Not provided',
      accountNumber: formData.bankAccountNumber || 'Not provided',
      branchCode: formData.branchCode || 'Not provided',
      branchName: formData.accountBranchName || 'Not provided',
    },
    addressInfo: {
      country: formData.physicalAddress?.country || 'Not provided',
      town: formData.physicalAddress?.town || 'Not provided',
      address: formData.physicalAddress?.physicalAddress || 'Not provided',
    },
    configuration: {
      callbackUrl: formData.callBackUrl || 'Not provided',
      productCount: formData.productIds?.length || 0,
      status: formData.status || 'Not provided',
    }
  }
}

const compareFormData = (original: any, updated: any) => {
  const changes: any = {}
  
  const fieldsToCompare = [
    'name', 'email', 'mobile', 'bankName', 'bankCode', 'swiftCode',
    'bankAccountNumber', 'branchCode', 'accountBranchName', 'callBackUrl', 'status'
  ]
  
  fieldsToCompare.forEach(field => {
    if (original[field] !== updated[field]) {
      changes[field] = {
        from: original[field],
        to: updated[field]
      }
    }
  })
  
  // Compare physical address
  if (original.physicalAddress && updated.physicalAddress) {
    const addressFields = ['country', 'town', 'physicalAddress']
    addressFields.forEach(field => {
      if (original.physicalAddress[field] !== updated.physicalAddress[field]) {
        if (!changes.physicalAddress) changes.physicalAddress = {}
        changes.physicalAddress[field] = {
          from: original.physicalAddress[field],
          to: updated.physicalAddress[field]
        }
      }
    })
  }
  
  // Compare product IDs
  const originalProducts = original.productIds || []
  const updatedProducts = updated.productIds || []
  
  if (JSON.stringify(originalProducts.sort()) !== JSON.stringify(updatedProducts.sort())) {
    changes.productIds = {
      from: originalProducts,
      to: updatedProducts
    }
  }
  
  return {
    hasChanges: Object.keys(changes).length > 0,
    changes,
    changeCount: Object.keys(changes).length
  }
}

const resetFormSection = (formData: any, section: string) => {
  const resetData = { ...formData }
  
  switch (section) {
    case 'basic-info':
      resetData.name = ''
      resetData.email = ''
      resetData.mobile = ''
      break
      
    case 'banking-info':
      resetData.bankName = ''
      resetData.bankCode = ''
      resetData.swiftCode = ''
      resetData.bankAccountNumber = ''
      resetData.branchCode = ''
      resetData.accountBranchName = ''
      break
      
    case 'address-info':
      resetData.physicalAddress = {
        country: '',
        town: '',
        physicalAddress: '',
      }
      break
      
    case 'configuration':
      resetData.callBackUrl = ''
      resetData.productIds = []
      resetData.status = ''
      break
      
    case 'all':
      return {
        name: '',
        email: '',
        mobile: '',
        bankName: '',
        bankCode: '',
        swiftCode: '',
        bankAccountNumber: '',
        branchCode: '',
        accountBranchName: '',
        physicalAddress: {
          country: '',
          town: '',
          physicalAddress: '',
        },
        callBackUrl: '',
        productIds: [],
        status: '',
      }
  }
  
  return resetData
}

describe('Broker Form Utilities', () => {
  describe('sanitizeFormData', () => {
    it('should trim string fields', () => {
      const dirtyData = {
        name: '  Test Broker  ',
        email: '  <EMAIL>  ',
        bankName: '  Test Bank  ',
      }
      
      const sanitized = sanitizeFormData(dirtyData)
      
      expect(sanitized.name).toBe('Test Broker')
      expect(sanitized.email).toBe('<EMAIL>')
      expect(sanitized.bankName).toBe('Test Bank')
    })

    it('should normalize email to lowercase', () => {
      const data = { email: '<EMAIL>' }
      const sanitized = sanitizeFormData(data)
      
      expect(sanitized.email).toBe('<EMAIL>')
    })

    it('should normalize swift code to uppercase', () => {
      const data = { swiftCode: 'testke22' }
      const sanitized = sanitizeFormData(data)
      
      expect(sanitized.swiftCode).toBe('TESTKE22')
    })

    it('should remove spaces from mobile number', () => {
      const data = { mobile: '+254 712 345 678' }
      const sanitized = sanitizeFormData(data)
      
      expect(sanitized.mobile).toBe('+************')
    })

    it('should sanitize physical address fields', () => {
      const data = {
        physicalAddress: {
          country: '  Kenya  ',
          town: '  Nairobi  ',
          physicalAddress: '  123 Test Street  ',
        }
      }
      
      const sanitized = sanitizeFormData(data)
      
      expect(sanitized.physicalAddress.country).toBe('Kenya')
      expect(sanitized.physicalAddress.town).toBe('Nairobi')
      expect(sanitized.physicalAddress.physicalAddress).toBe('123 Test Street')
    })
  })

  describe('validateFormStep', () => {
    it('should validate basic info step', () => {
      const validData = { name: 'Test', email: '<EMAIL>', mobile: '+************' }
      const invalidData = { name: '', email: '', mobile: '' }
      
      const validResult = validateFormStep('basic-info', validData)
      const invalidResult = validateFormStep('basic-info', invalidData)
      
      expect(validResult.isValid).toBe(true)
      expect(invalidResult.isValid).toBe(false)
      expect(invalidResult.errors.name).toBe('Name is required')
    })

    it('should validate banking info step', () => {
      const validData = {
        bankName: 'Test Bank',
        bankCode: '001',
        swiftCode: 'TESTKE22',
        bankAccountNumber: '123456',
        branchCode: '001',
        accountBranchName: 'Main Branch'
      }
      
      const result = validateFormStep('banking-info', validData)
      expect(result.isValid).toBe(true)
    })

    it('should validate address info step', () => {
      const validData = {
        physicalAddress: {
          country: 'Kenya',
          town: 'Nairobi',
          physicalAddress: '123 Test Street'
        }
      }
      
      const result = validateFormStep('address-info', validData)
      expect(result.isValid).toBe(true)
    })

    it('should validate configuration step', () => {
      const validData = {
        callBackUrl: 'https://test.com',
        productIds: ['product-1'],
        status: 'Active'
      }
      
      const result = validateFormStep('configuration', validData)
      expect(result.isValid).toBe(true)
    })
  })

  describe('getFormProgress', () => {
    it('should calculate progress correctly for complete form', () => {
      const progress = getFormProgress(mockCreateBrokerForm)
      
      expect(progress.completedFields).toBe(13)
      expect(progress.totalFields).toBe(13)
      expect(progress.percentage).toBe(100)
      expect(progress.isComplete).toBe(true)
    })

    it('should calculate progress correctly for partial form', () => {
      const partialForm = {
        name: 'Test',
        email: '<EMAIL>',
        mobile: '+************',
      }
      
      const progress = getFormProgress(partialForm)
      
      expect(progress.completedFields).toBe(3)
      expect(progress.percentage).toBe(23) // 3/13 * 100 rounded
      expect(progress.isComplete).toBe(false)
    })

    it('should handle empty form', () => {
      const progress = getFormProgress({})
      
      expect(progress.completedFields).toBe(0)
      expect(progress.percentage).toBe(0)
      expect(progress.isComplete).toBe(false)
    })
  })

  describe('generateFormSummary', () => {
    it('should generate summary for complete form', () => {
      const summary = generateFormSummary(mockCreateBrokerForm)
      
      expect(summary.basicInfo.name).toBe(mockCreateBrokerForm.name)
      expect(summary.bankingInfo.bankName).toBe(mockCreateBrokerForm.bankName)
      expect(summary.addressInfo.country).toBe(mockCreateBrokerForm.physicalAddress.country)
      expect(summary.configuration.productCount).toBe(mockCreateBrokerForm.productIds.length)
    })

    it('should handle missing data with default values', () => {
      const summary = generateFormSummary({})
      
      expect(summary.basicInfo.name).toBe('Not provided')
      expect(summary.bankingInfo.bankName).toBe('Not provided')
      expect(summary.addressInfo.country).toBe('Not provided')
      expect(summary.configuration.productCount).toBe(0)
    })
  })

  describe('compareFormData', () => {
    it('should detect changes between forms', () => {
      const original = { ...mockCreateBrokerForm }
      const updated = { ...mockCreateBrokerForm, name: 'Updated Name', email: '<EMAIL>' }
      
      const comparison = compareFormData(original, updated)
      
      expect(comparison.hasChanges).toBe(true)
      expect(comparison.changeCount).toBe(2)
      expect(comparison.changes.name.from).toBe(original.name)
      expect(comparison.changes.name.to).toBe('Updated Name')
    })

    it('should detect no changes for identical forms', () => {
      const comparison = compareFormData(mockCreateBrokerForm, mockCreateBrokerForm)
      
      expect(comparison.hasChanges).toBe(false)
      expect(comparison.changeCount).toBe(0)
    })

    it('should detect changes in physical address', () => {
      const original = { ...mockCreateBrokerForm }
      const updated = {
        ...mockCreateBrokerForm,
        physicalAddress: {
          ...mockCreateBrokerForm.physicalAddress,
          country: 'Updated Country'
        }
      }
      
      const comparison = compareFormData(original, updated)
      
      expect(comparison.hasChanges).toBe(true)
      expect(comparison.changes.physicalAddress.country.to).toBe('Updated Country')
    })

    it('should detect changes in product IDs', () => {
      const original = { ...mockCreateBrokerForm, productIds: ['product-1'] }
      const updated = { ...mockCreateBrokerForm, productIds: ['product-1', 'product-2'] }
      
      const comparison = compareFormData(original, updated)
      
      expect(comparison.hasChanges).toBe(true)
      expect(comparison.changes.productIds.to).toEqual(['product-1', 'product-2'])
    })
  })

  describe('resetFormSection', () => {
    it('should reset basic info section', () => {
      const reset = resetFormSection(mockCreateBrokerForm, 'basic-info')
      
      expect(reset.name).toBe('')
      expect(reset.email).toBe('')
      expect(reset.mobile).toBe('')
      expect(reset.bankName).toBe(mockCreateBrokerForm.bankName) // Should not be reset
    })

    it('should reset banking info section', () => {
      const reset = resetFormSection(mockCreateBrokerForm, 'banking-info')
      
      expect(reset.bankName).toBe('')
      expect(reset.bankCode).toBe('')
      expect(reset.swiftCode).toBe('')
      expect(reset.name).toBe(mockCreateBrokerForm.name) // Should not be reset
    })

    it('should reset address info section', () => {
      const reset = resetFormSection(mockCreateBrokerForm, 'address-info')
      
      expect(reset.physicalAddress.country).toBe('')
      expect(reset.physicalAddress.town).toBe('')
      expect(reset.physicalAddress.physicalAddress).toBe('')
      expect(reset.name).toBe(mockCreateBrokerForm.name) // Should not be reset
    })

    it('should reset configuration section', () => {
      const reset = resetFormSection(mockCreateBrokerForm, 'configuration')
      
      expect(reset.callBackUrl).toBe('')
      expect(reset.productIds).toEqual([])
      expect(reset.status).toBe('')
      expect(reset.name).toBe(mockCreateBrokerForm.name) // Should not be reset
    })

    it('should reset entire form', () => {
      const reset = resetFormSection(mockCreateBrokerForm, 'all')
      
      expect(reset.name).toBe('')
      expect(reset.email).toBe('')
      expect(reset.bankName).toBe('')
      expect(reset.physicalAddress.country).toBe('')
      expect(reset.callBackUrl).toBe('')
      expect(reset.productIds).toEqual([])
    })
  })
})
