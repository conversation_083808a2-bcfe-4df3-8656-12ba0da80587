import { describe, it, expect } from 'vitest'

// Constants to test
const BROKER_STATUSES = {
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
  PENDING: 'Pending',
  SUSPENDED: 'Suspended',
} as const

const BROKER_CREATION_STAGES = {
  GET_STARTED: 'GetStarted',
  CREATE_BROKER_FORM: 'CreateBrokerForm',
  SELECT_EXISTING_ORGANIZATION: 'SelectExistingOrganization',
} as const

const VALIDATION_RULES = {
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100,
    REQUIRED: true,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    REQUIRED: true,
  },
  MOBILE: {
    PATTERN: /^\+254\d{9}$/,
    REQUIRED: true,
  },
  BANK_CODE: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 10,
    REQUIRED: true,
  },
  SWIFT_CODE: {
    LENGTH: 8,
    PATTERN: /^[A-Z]{6}[A-Z0-9]{2}$/,
    REQUIRED: true,
  },
  CALLBACK_URL: {
    PATTERN: /^https?:\/\/.+/,
    REQUIRED: true,
  },
  PRODUCT_IDS: {
    MIN_COUNT: 1,
    REQUIRED: true,
  },
} as const

const DEFAULT_FORM_VALUES = {
  name: '',
  email: '',
  mobile: '',
  bankName: '',
  bankCode: '',
  swiftCode: '',
  bankAccountNumber: '',
  branchCode: '',
  accountBranchName: '',
  physicalAddress: {
    country: '',
    town: '',
    physicalAddress: '',
  },
  callBackUrl: '',
  productIds: [],
  status: BROKER_STATUSES.ACTIVE,
} as const

const ERROR_MESSAGES = {
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_MOBILE: 'Please enter a valid mobile number (+254XXXXXXXXX)',
  INVALID_URL: 'Please enter a valid URL',
  INVALID_SWIFT_CODE: 'Swift code must be 8 characters (6 letters + 2 letters/numbers)',
  MIN_PRODUCTS: 'At least one product must be selected',
  NAME_TOO_SHORT: 'Name must be at least 2 characters',
  NAME_TOO_LONG: 'Name cannot exceed 100 characters',
  BANK_CODE_INVALID: 'Bank code must be between 3 and 10 characters',
} as const

const API_ENDPOINTS = {
  CREATE_BROKER: '/lms/brokers/',
  CREATE_BROKER_MAKE: '/lms/brokers/make',
  UPDATE_BROKER: (id: string) => `/lms/brokers/${id}/`,
  UPDATE_BROKER_MAKE: (id: string) => `/lms/brokers/${id}/make`,
  GET_BROKERS: '/lms/brokers/',
  GET_ORGANIZATIONS: '/lms/organizations/',
  GET_LOAN_PRODUCTS: '/lms/loan-products/',
  GET_BANK_BRANCHES: '/lms/bank-branches/',
} as const

// Utility functions to test
const isValidBrokerStatus = (status: string): boolean => {
  return ['Active', 'Inactive', 'Pending', 'Suspended'].includes(status)
}

const isValidCreationStage = (stage: string): boolean => {
  return Object.values(BROKER_CREATION_STAGES).includes(stage as any)
}

const getErrorMessage = (field: string, value: any): string | null => {
  switch (field) {
    case 'name':
      if (!value || value.trim() === '') return ERROR_MESSAGES.REQUIRED_FIELD
      if (value.length < VALIDATION_RULES.NAME.MIN_LENGTH) return ERROR_MESSAGES.NAME_TOO_SHORT
      if (value.length > VALIDATION_RULES.NAME.MAX_LENGTH) return ERROR_MESSAGES.NAME_TOO_LONG
      return null
    
    case 'email':
      if (!value || value.trim() === '') return ERROR_MESSAGES.REQUIRED_FIELD
      if (!VALIDATION_RULES.EMAIL.PATTERN.test(value)) return ERROR_MESSAGES.INVALID_EMAIL
      return null
    
    case 'mobile':
      if (!value || value.trim() === '') return ERROR_MESSAGES.REQUIRED_FIELD
      if (!VALIDATION_RULES.MOBILE.PATTERN.test(value)) return ERROR_MESSAGES.INVALID_MOBILE
      return null
    
    case 'swiftCode':
      if (!value || value.trim() === '') return ERROR_MESSAGES.REQUIRED_FIELD
      if (!VALIDATION_RULES.SWIFT_CODE.PATTERN.test(value)) return ERROR_MESSAGES.INVALID_SWIFT_CODE
      return null
    
    case 'callBackUrl':
      if (!value || value.trim() === '') return ERROR_MESSAGES.REQUIRED_FIELD
      if (!VALIDATION_RULES.CALLBACK_URL.PATTERN.test(value)) return ERROR_MESSAGES.INVALID_URL
      return null
    
    case 'productIds':
      if (!value || value.length < VALIDATION_RULES.PRODUCT_IDS.MIN_COUNT) return ERROR_MESSAGES.MIN_PRODUCTS
      return null
    
    case 'bankCode':
      if (!value || value.trim() === '') return ERROR_MESSAGES.REQUIRED_FIELD
      if (value.length < VALIDATION_RULES.BANK_CODE.MIN_LENGTH || value.length > VALIDATION_RULES.BANK_CODE.MAX_LENGTH) {
        return ERROR_MESSAGES.BANK_CODE_INVALID
      }
      return null
    
    default:
      return null
  }
}

const getApiEndpoint = (action: string, id?: string): string => {
  switch (action) {
    case 'create':
      return API_ENDPOINTS.CREATE_BROKER
    case 'create-make':
      return API_ENDPOINTS.CREATE_BROKER_MAKE
    case 'update':
      return id ? API_ENDPOINTS.UPDATE_BROKER(id) : ''
    case 'update-make':
      return id ? API_ENDPOINTS.UPDATE_BROKER_MAKE(id) : ''
    case 'get-brokers':
      return API_ENDPOINTS.GET_BROKERS
    case 'get-organizations':
      return API_ENDPOINTS.GET_ORGANIZATIONS
    case 'get-loan-products':
      return API_ENDPOINTS.GET_LOAN_PRODUCTS
    case 'get-bank-branches':
      return API_ENDPOINTS.GET_BANK_BRANCHES
    default:
      return ''
  }
}

describe('Broker Constants and Utilities', () => {
  describe('BROKER_STATUSES', () => {
    it('should have all required status values', () => {
      expect(BROKER_STATUSES.ACTIVE).toBe('Active')
      expect(BROKER_STATUSES.INACTIVE).toBe('Inactive')
      expect(BROKER_STATUSES.PENDING).toBe('Pending')
      expect(BROKER_STATUSES.SUSPENDED).toBe('Suspended')
    })

    it('should be immutable', () => {
      // In JavaScript, const objects are not deeply immutable by default
      // This test checks that the object exists and has the expected structure
      expect(BROKER_STATUSES).toBeDefined()
      expect(typeof BROKER_STATUSES).toBe('object')
    })
  })

  describe('BROKER_CREATION_STAGES', () => {
    it('should have all required stage values', () => {
      expect(BROKER_CREATION_STAGES.GET_STARTED).toBe('GetStarted')
      expect(BROKER_CREATION_STAGES.CREATE_BROKER_FORM).toBe('CreateBrokerForm')
      expect(BROKER_CREATION_STAGES.SELECT_EXISTING_ORGANIZATION).toBe('SelectExistingOrganization')
    })
  })

  describe('VALIDATION_RULES', () => {
    it('should have correct email pattern', () => {
      expect(VALIDATION_RULES.EMAIL.PATTERN.test('<EMAIL>')).toBe(true)
      expect(VALIDATION_RULES.EMAIL.PATTERN.test('invalid-email')).toBe(false)
    })

    it('should have correct mobile pattern', () => {
      expect(VALIDATION_RULES.MOBILE.PATTERN.test('+254712345678')).toBe(true)
      expect(VALIDATION_RULES.MOBILE.PATTERN.test('0712345678')).toBe(false)
      expect(VALIDATION_RULES.MOBILE.PATTERN.test('+254712')).toBe(false)
    })

    it('should have correct swift code pattern', () => {
      expect(VALIDATION_RULES.SWIFT_CODE.PATTERN.test('TESTKE22')).toBe(true)
      expect(VALIDATION_RULES.SWIFT_CODE.PATTERN.test('testke22')).toBe(false)
      expect(VALIDATION_RULES.SWIFT_CODE.PATTERN.test('TEST')).toBe(false)
    })

    it('should have correct URL pattern', () => {
      expect(VALIDATION_RULES.CALLBACK_URL.PATTERN.test('https://example.com')).toBe(true)
      expect(VALIDATION_RULES.CALLBACK_URL.PATTERN.test('http://example.com')).toBe(true)
      expect(VALIDATION_RULES.CALLBACK_URL.PATTERN.test('ftp://example.com')).toBe(false)
      expect(VALIDATION_RULES.CALLBACK_URL.PATTERN.test('example.com')).toBe(false)
    })
  })

  describe('DEFAULT_FORM_VALUES', () => {
    it('should have all required fields with default values', () => {
      expect(DEFAULT_FORM_VALUES.name).toBe('')
      expect(DEFAULT_FORM_VALUES.email).toBe('')
      expect(DEFAULT_FORM_VALUES.mobile).toBe('')
      expect(DEFAULT_FORM_VALUES.status).toBe('Active')
      expect(DEFAULT_FORM_VALUES.productIds).toEqual([])
      expect(DEFAULT_FORM_VALUES.physicalAddress).toEqual({
        country: '',
        town: '',
        physicalAddress: '',
      })
    })
  })

  describe('isValidBrokerStatus', () => {
    it('should return true for valid statuses', () => {
      expect(isValidBrokerStatus('Active')).toBe(true)
      expect(isValidBrokerStatus('Inactive')).toBe(true)
      expect(isValidBrokerStatus('Pending')).toBe(true)
      expect(isValidBrokerStatus('Suspended')).toBe(true)
    })

    it('should return false for invalid statuses', () => {
      expect(isValidBrokerStatus('Invalid')).toBe(false)
      expect(isValidBrokerStatus('active')).toBe(false)
      expect(isValidBrokerStatus('')).toBe(false)
    })
  })

  describe('isValidCreationStage', () => {
    it('should return true for valid stages', () => {
      expect(isValidCreationStage('GetStarted')).toBe(true)
      expect(isValidCreationStage('CreateBrokerForm')).toBe(true)
      expect(isValidCreationStage('SelectExistingOrganization')).toBe(true)
    })

    it('should return false for invalid stages', () => {
      expect(isValidCreationStage('InvalidStage')).toBe(false)
      expect(isValidCreationStage('getstarted')).toBe(false)
      expect(isValidCreationStage('')).toBe(false)
    })
  })

  describe('getErrorMessage', () => {
    it('should return correct error messages for name field', () => {
      expect(getErrorMessage('name', '')).toBe(ERROR_MESSAGES.REQUIRED_FIELD)
      expect(getErrorMessage('name', 'A')).toBe(ERROR_MESSAGES.NAME_TOO_SHORT)
      expect(getErrorMessage('name', 'A'.repeat(101))).toBe(ERROR_MESSAGES.NAME_TOO_LONG)
      expect(getErrorMessage('name', 'Valid Name')).toBe(null)
    })

    it('should return correct error messages for email field', () => {
      expect(getErrorMessage('email', '')).toBe(ERROR_MESSAGES.REQUIRED_FIELD)
      expect(getErrorMessage('email', 'invalid-email')).toBe(ERROR_MESSAGES.INVALID_EMAIL)
      expect(getErrorMessage('email', '<EMAIL>')).toBe(null)
    })

    it('should return correct error messages for mobile field', () => {
      expect(getErrorMessage('mobile', '')).toBe(ERROR_MESSAGES.REQUIRED_FIELD)
      expect(getErrorMessage('mobile', '0712345678')).toBe(ERROR_MESSAGES.INVALID_MOBILE)
      expect(getErrorMessage('mobile', '+254712345678')).toBe(null)
    })

    it('should return correct error messages for swift code field', () => {
      expect(getErrorMessage('swiftCode', '')).toBe(ERROR_MESSAGES.REQUIRED_FIELD)
      expect(getErrorMessage('swiftCode', 'invalid')).toBe(ERROR_MESSAGES.INVALID_SWIFT_CODE)
      expect(getErrorMessage('swiftCode', 'TESTKE22')).toBe(null)
    })

    it('should return correct error messages for callback URL field', () => {
      expect(getErrorMessage('callBackUrl', '')).toBe(ERROR_MESSAGES.REQUIRED_FIELD)
      expect(getErrorMessage('callBackUrl', 'invalid-url')).toBe(ERROR_MESSAGES.INVALID_URL)
      expect(getErrorMessage('callBackUrl', 'https://valid.com')).toBe(null)
    })

    it('should return correct error messages for product IDs field', () => {
      expect(getErrorMessage('productIds', [])).toBe(ERROR_MESSAGES.MIN_PRODUCTS)
      expect(getErrorMessage('productIds', ['product-1'])).toBe(null)
    })

    it('should return correct error messages for bank code field', () => {
      expect(getErrorMessage('bankCode', '')).toBe(ERROR_MESSAGES.REQUIRED_FIELD)
      expect(getErrorMessage('bankCode', 'AB')).toBe(ERROR_MESSAGES.BANK_CODE_INVALID)
      expect(getErrorMessage('bankCode', 'A'.repeat(11))).toBe(ERROR_MESSAGES.BANK_CODE_INVALID)
      expect(getErrorMessage('bankCode', 'ABC')).toBe(null)
    })

    it('should return null for unknown fields', () => {
      expect(getErrorMessage('unknownField', 'value')).toBe(null)
    })
  })

  describe('getApiEndpoint', () => {
    it('should return correct endpoints for broker operations', () => {
      expect(getApiEndpoint('create')).toBe('/lms/brokers/')
      expect(getApiEndpoint('create-make')).toBe('/lms/brokers/make')
      expect(getApiEndpoint('update', 'broker-123')).toBe('/lms/brokers/broker-123/')
      expect(getApiEndpoint('update-make', 'broker-123')).toBe('/lms/brokers/broker-123/make')
    })

    it('should return correct endpoints for data fetching', () => {
      expect(getApiEndpoint('get-brokers')).toBe('/lms/brokers/')
      expect(getApiEndpoint('get-organizations')).toBe('/lms/organizations/')
      expect(getApiEndpoint('get-loan-products')).toBe('/lms/loan-products/')
      expect(getApiEndpoint('get-bank-branches')).toBe('/lms/bank-branches/')
    })

    it('should return empty string for invalid actions', () => {
      expect(getApiEndpoint('invalid-action')).toBe('')
    })

    it('should return empty string for update actions without ID', () => {
      expect(getApiEndpoint('update')).toBe('')
      expect(getApiEndpoint('update-make')).toBe('')
    })
  })
})
