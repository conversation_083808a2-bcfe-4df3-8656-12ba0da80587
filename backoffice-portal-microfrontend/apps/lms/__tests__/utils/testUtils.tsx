import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { ThemeProvider } from '@mui/material/styles'
import { createTheme } from '@mui/material/styles'
import { FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { matchIsValidTel } from 'mui-tel-input'

// Import your actual reducers
import { loansReducer } from '@dtbx/store/reducers'

// Mock theme
const mockTheme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
})

// Mock store state
export const mockInitialState = {
  loans: {
    // Broker management
    brokersSummary: {
      pageNumber: 1,
      pageSize: 10,
      totalElements: 0,
      totalNumberOfPages: 1,
    },
    isLoadingBrokers: false,
    brokers: [],
    isLoadingCreateBroker: false,
    isCreateBrokerSuccess: false,
    isCreateBrokerFailure: false,
    createdBroker: {},
    isLoadingUpdateBroker: false,
    isUpdateBrokerSuccess: false,
    isUpdateBrokerFailure: false,
    isGenerateBrokerSecretLoading: false,
    isGenerateBrokerSecretSuccess: false,
    isGenerateBrokerSecretFailure: false,
    selectedBrokerExisting: {},
    selectedBroker: {},
    isLoadingAssignBrokerProduct: false,
    isSuccessAssignBrokerProduct: false,
    isFailureAssignBrokerProduct: false,
    brokerProducts: [],
    
    // Organizations
    organizations: [],
    isLoadingOrganizations: false,
    organizationsSummary: {
      pageNumber: 1,
      pageSize: 10,
      totalElements: 0,
      totalNumberOfPages: 1,
    },
    
    // Loan products
    loanProducts: [],
    isLoadingLoanProducts: false,
    loanProductsSummary: {
      pageNumber: 1,
      pageSize: 10,
      totalElements: 0,
      totalNumberOfPages: 1,
    },
    
    // Bank branches
    bankBranches: [],
    
    // Other required fields
    loanRequests: [],
    loanRequestsSummary: {
      pageNumber: 1,
      pageSize: 10,
      totalElements: 0,
      totalNumberOfPages: 1,
    },
    isLoadingLoans: false,
    selectedRequest: {},
    customerProfile: {},
    isLoadingCustomerProfile: false,
    customerDocuments: [],
    customerChecks: [],
    loanRepaymentHistory: [],
    productToView: {},
    createdProduct: {},
    isLoadingCreateProduct: false,
    isLoadingUpdateProduct: false,
    productCategories: [],
    productCategoryTypes: [],
    isLoadingCreateCategory: false,
    isLoadingUpdateCategory: false,
    loanValidations: [],
    productValidations: [],
    singleProductValidations: [],
    isLoadingProductValidations: false,
    isLoadingAddValidation: false,
    isLoadingOverrideValidationCheck: false,
    isCheckRerunLoading: false,
    isLoadingUploadDocument: false,
    isLoadingCreateOrganization: false,
    isLoadingEditOrganization: false,
    editOrganization: {},
    loanReports: [],
    loanReportLoading: false,
    loanReportsSuccess: false,
    generatedLoanReportLoading: false,
    generatedLoanReportSuccess: false,
    loanRequestReportsResponse: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalElements: 0,
      totalNumberOfPages: 1,
    },
    loanRequestReportLoading: false,
    loanRequestReportSuccess: false,
    generatedRequestReportLoading: false,
    organizationLimitReport: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalElements: 0,
      totalNumberOfPages: 1,
    },
    organizationLimitReportLoading: false,
    organizationLimitReportSuccess: false,
    organizationLimitReportExportLoading: false,
    organizationLimitReportExportSuccess: false,
    requestCheckReportsResponse: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalElements: 0,
      totalNumberOfPages: 1,
    },
    requestCheckReportsLoading: false,
    requestCheckReportsSuccess: false,
    requestCheckReportsExportLoading: false,
    requestCheckReportsExportSuccess: false,
    isLoadingCancelRequest: false,
    userProducts: [],
  },
}

// Create mock store
export const createMockStore = (initialState = mockInitialState) => {
  return configureStore({
    reducer: {
      loans: loansReducer,
    },
    preloadedState: initialState,
  })
}

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any
  store?: any
}

export const renderWithProviders = (
  ui: ReactElement,
  {
    initialState = mockInitialState,
    store = createMockStore(initialState),
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <ThemeProvider theme={mockTheme}>
          {children}
        </ThemeProvider>
      </Provider>
    )
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) }
}

// Broker validation schema for testing
export const brokerValidationSchema = Yup.object({
  mobile: Yup.string()
    .required('Phone must not be empty')
    .test('is-valid-phone', 'Invalid phone number', (value) => {
      return matchIsValidTel(value || '')
    }),
  name: Yup.string()
    .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed')
    .required('Name must not be empty'),
  email: Yup.string()
    .email('Invalid email')
    .required('Email must not be empty'),
  bankName: Yup.string().required('Bank Name must not be empty'),
  bankAccountNumber: Yup.string().required(
    'Bank Account Number must not be empty'
  ),
  bankCode: Yup.string().required('Bank Code must not be empty'),
  swiftCode: Yup.string().required('Swift Code must not be empty'),
  branchCode: Yup.string().required('Branch Code must not be empty'),
  accountBranchName: Yup.string().required(
    'Account Branch Name must not be empty'
  ),
  'physicalAddress.country': Yup.string().required(
    'Country must not be empty'
  ),
  'physicalAddress.town': Yup.string().required('Town must not be empty'),
  'physicalAddress.physicalAddress': Yup.string().required(
    'Physical Address must not be empty'
  ),
  callBackUrl: Yup.string()
    .url('Invalid URL format')
    .required('Callback URL must not be empty'),
  productIds: Yup.array()
    .of(Yup.string())
    .min(1, 'At least one product must be selected'),
})

// Mock formik wrapper for testing forms
export const MockFormikWrapper = ({ 
  children, 
  initialValues, 
  validationSchema = brokerValidationSchema,
  onSubmit = vi.fn()
}: {
  children: React.ReactNode
  initialValues: any
  validationSchema?: any
  onSubmit?: any
}) => {
  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit,
  })

  return (
    <FormikProvider value={formik}>
      {children}
    </FormikProvider>
  )
}

// Mock router
export const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
}

// Mock useCustomRouter hook
export const mockUseCustomRouter = () => mockRouter

// Mock dispatch function
export const mockDispatch = vi.fn()

// Mock API responses
export const mockApiResponses = {
  createBrokerSuccess: {
    data: {
      id: 'broker-123',
      name: 'Test Broker',
      email: '<EMAIL>',
      status: 'Active',
    },
  },
  updateBrokerSuccess: {
    data: {
      id: 'broker-123',
      name: 'Updated Broker',
      email: '<EMAIL>',
      status: 'Active',
    },
  },
}

// Mock user permissions
export const mockUserPermissions = {
  SUPER_CREATE_BROKER: true,
  SUPER_UPDATE_BROKER: true,
  SUPER_VIEW_BROKER: true,
  MAKE_CREATE_BROKER: false,
  MAKE_UPDATE_BROKER: false,
}

// Mock HasAccessToRights function
export const mockHasAccessToRights = (rights: string[]) => {
  return rights.every(right => mockUserPermissions[right as keyof typeof mockUserPermissions])
}

export * from '@testing-library/react'
export { renderWithProviders as render }
