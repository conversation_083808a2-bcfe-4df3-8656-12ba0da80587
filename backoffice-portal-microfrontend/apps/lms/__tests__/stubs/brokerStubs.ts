import {
  IBroker,
  IBrokerCreate,
  ICreateBrokerForm,
  ILoanProduct,
  IOrganization,
} from '@/store/interfaces'

export const mockBrokerCreate: IBrokerCreate = {
  id: 'broker-123',
  dateCreated: '2023-01-01T10:00:00Z',
  dateModified: '2023-01-15T14:30:00Z',
  createdBy: 'test-user',
  modifiedBy: 'test-user',
  name: 'Test Broker',
  status: 'Active',
  email: '<EMAIL>',
  mobile: '+************',
  bankName: 'Test Bank',
  bankCode: '001',
  swiftCode: 'TESTKE22',
  bankAccountNumber: '**********',
  branchCode: '001',
  organizationId: 'org-123',
  accountBranchName: 'Main Branch',
  physicalAddress: {
    country: 'Kenya',
    town: 'Nairobi',
    physicalAddress: '123 Test Street, Nairobi',
  },
  credentialStatus: 'Active',
  limit: 1000000,
  limitCurrency: 'KES',
  totalDisbursements: '500000',
  callBackUrl: 'https://test-broker.com/callback',
  productIds: ['product-1', 'product-2'],
}

export const mockBroker: IBroker = {
  ...mockBrokerCreate,
  id: 'broker-123',
  dateCreated: '2023-01-01T10:00:00Z',
  dateModified: '2023-01-15T14:30:00Z',
  createdBy: 'test-user',
  modifiedBy: 'test-user',
  organizationId: 'org-123',
  credentialStatus: 'Active',
  organization: {
    id: 'org-123',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-15T14:30:00Z',
    createdBy: 'test-user',
    modifiedBy: 'test-user',
    name: 'Test Organization',
    status: 'Active',
    cbsIdentifier: 'ORG001',
    email: '<EMAIL>',
    mobile: '+************',
    bankName: 'Test Bank',
    bankCode: '001',
    swiftCode: 'TESTKE22',
    bankAccountNumber: '**********',
    branchCode: '001',
    accountBranchName: 'Main Branch',
    physicalAddress: {
      country: 'Kenya',
      town: 'Nairobi',
      physicalAddress: '456 Org Street, Nairobi',
    },
    limit: 5000000,
    limitCurrency: 'KES',
  },
  limit: 1000000,
  limitCurrency: 'KES',
  totalDisbursements: '500000',
}

export const mockCreateBrokerForm: ICreateBrokerForm = {
  name: 'Test Broker',
  email: '<EMAIL>',
  mobile: '+************',
  bankName: 'Test Bank',
  status: 'Active',
  bankCode: '001',
  swiftCode: 'TESTKE22',
  bankAccountNumber: '**********',
  branchCode: '001',
  accountBranchName: 'Main Branch',
  physicalAddress: {
    country: 'Kenya',
    town: 'Nairobi',
    physicalAddress: '123 Test Street, Nairobi',
  },
  productIds: ['product-1', 'product-2'],
  callBackUrl: 'https://test-broker.com/callback',
}

export const mockLoanProducts: ILoanProduct[] = [
  {
    id: 'product-1',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-15T14:30:00Z',
    createdBy: 'test-user',
    modifiedBy: 'test-user',
    code: 'PROD001',
    name: 'Personal Loan',
    country: 'Kenya',
    currency: 'KES',
    organization: null,
    type: {
      id: 'type-1',
      dateCreated: '2023-01-01T10:00:00Z',
      dateModified: '2023-01-15T14:30:00Z',
      createdBy: 'test-user',
      modifiedBy: 'test-user',
      code: 'PERSONAL',
      name: 'Personal Loan Type',
    },
    expiryDate: '2025-12-31',
    exposureLimit: 1000000,
    status: 'Active',
    customerType: 'Individual',
    minimumAmount: 5000,
    maximumAmount: 500000,
    measureOfTenure: 'Months',
    minimumTenure: 1,
    maximumTenure: 60,
    interestRateType: 'Fixed',
    dynamicInterestFixedComponent: 0,
    interestRate: 12.5,
    facilityFee: 2.5,
    exciseDuty: 0.2,
    facilityFeeRecoveryType: 'Upfront',
    interestRecoveryType: 'Monthly',
    minimumInterestRecoveryType: 'Fixed',
    upfrontInterestRecognitionType: 'Accrued',
    minimumInterestCalculationMode: 'Simple',
    externalProductName: 'Personal Loan External',
    rollOverFee: 100,
    rollOverPeriod: 12,
    maxRollOverCount: 3,
    prepaymentType: 'Partial',
    prepaymentCalculation: 'Simple',
    prepaymentValue: 2.5,
    penalty: 1.0,
    interestCalculation: 'Simple',
    amortizationMode: 'Equal Installments',
    multipleDrawDown: false,
    tranches: false,
    trancheInterval: 'Monthly',
    repaymentCycle: 'Monthly',
    numberOfInstallments: 12,
    earlyPaymentsAllowed: true,
    periodInArrears: 30,
    minimumInterestValue: 100,
    interestGl: 'GL001',
    facilityFeeGl: 'GL002',
    exciseDutyGl: 'GL003',
    rollOverGl: 'GL004',
    penaltyGl: 'GL005',
    prepaymentGl: 'GL006',
    disbursementGl: 'GL007',
    upfrontInterestLiabilityGl: 'GL008',
    disbursementCreditAccountType: 'Savings',
    repaymentGl: 'GL009',
    gracePeriodType: 'Days',
    hasRecoveryTracking: true,
    externalProductCode: 'EXT001',
    disbursementCreditAccount: 'ACC001',
    disbursementCreditAccountBranch: 'BR001',
    isManaged: true,
    manualApprovalAmount: 100000,
    typeId: 'type-1',
    organizationId: 'org-123',
    loanCreationBranch: 'BR001',
  },
  {
    id: 'product-2',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-15T14:30:00Z',
    createdBy: 'test-user',
    modifiedBy: 'test-user',
    code: 'PROD002',
    name: 'Business Loan',
    country: 'Kenya',
    currency: 'KES',
    organization: null,
    type: {
      id: 'type-2',
      dateCreated: '2023-01-01T10:00:00Z',
      dateModified: '2023-01-15T14:30:00Z',
      createdBy: 'test-user',
      modifiedBy: 'test-user',
      code: 'BUSINESS',
      name: 'Business Loan Type',
    },
    expiryDate: '2025-12-31',
    exposureLimit: 5000000,
    status: 'Active',
    customerType: 'Business',
    minimumAmount: 50000,
    maximumAmount: 2000000,
    measureOfTenure: 'Months',
    minimumTenure: 6,
    maximumTenure: 120,
    interestRateType: 'Variable',
    dynamicInterestFixedComponent: 2.5,
    interestRate: 15.0,
    facilityFee: 3.0,
    exciseDuty: 0.2,
    facilityFeeRecoveryType: 'Upfront',
    interestRecoveryType: 'Monthly',
    minimumInterestRecoveryType: 'Fixed',
    upfrontInterestRecognitionType: 'Accrued',
    minimumInterestCalculationMode: 'Compound',
    externalProductName: 'Business Loan External',
    rollOverFee: 500,
    rollOverPeriod: 12,
    maxRollOverCount: 2,
    prepaymentType: 'Full',
    prepaymentCalculation: 'Compound',
    prepaymentValue: 5.0,
    penalty: 2.0,
    interestCalculation: 'Compound',
    amortizationMode: 'Equal Principal',
    multipleDrawDown: true,
    tranches: true,
    trancheInterval: 'Quarterly',
    repaymentCycle: 'Monthly',
    numberOfInstallments: 24,
    earlyPaymentsAllowed: false,
    periodInArrears: 60,
    minimumInterestValue: 500,
    interestGl: 'GL011',
    facilityFeeGl: 'GL012',
    exciseDutyGl: 'GL013',
    rollOverGl: 'GL014',
    penaltyGl: 'GL015',
    prepaymentGl: 'GL016',
    disbursementGl: 'GL017',
    upfrontInterestLiabilityGl: 'GL018',
    disbursementCreditAccountType: 'Current',
    repaymentGl: 'GL019',
    gracePeriodType: 'Months',
    hasRecoveryTracking: true,
    externalProductCode: 'EXT002',
    disbursementCreditAccount: 'ACC002',
    disbursementCreditAccountBranch: 'BR002',
    isManaged: false,
    manualApprovalAmount: 500000,
    typeId: 'type-2',
    organizationId: 'org-123',
    loanCreationBranch: 'BR002',
  },
]

export const mockOrganizations: IOrganization[] = [
  {
    id: 'org-123',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-15T14:30:00Z',
    createdBy: 'test-user',
    modifiedBy: 'test-user',
    name: 'Test Organization',
    status: 'Active',
    cbsIdentifier: 'ORG001',
    email: '<EMAIL>',
    mobile: '+************',
    bankName: 'Test Bank',
    bankCode: '001',
    swiftCode: 'TESTKE22',
    bankAccountNumber: '**********',
    branchCode: '001',
    accountBranchName: 'Main Branch',
    physicalAddress: {
      country: 'Kenya',
      town: 'Nairobi',
      physicalAddress: '456 Org Street, Nairobi',
    },
    limit: 5000000,
    limitCurrency: 'KES',
  },
  {
    id: 'org-456',
    dateCreated: '2023-02-01T10:00:00Z',
    dateModified: '2023-02-15T14:30:00Z',
    createdBy: 'test-user',
    modifiedBy: 'test-user',
    name: 'Another Organization',
    status: 'Active',
    cbsIdentifier: 'ORG002',
    email: '<EMAIL>',
    mobile: '+************',
    bankName: 'Another Bank',
    bankCode: '002',
    swiftCode: 'ANOTKE22',
    bankAccountNumber: '**********',
    branchCode: '002',
    accountBranchName: 'Secondary Branch',
    physicalAddress: {
      country: 'Kenya',
      town: 'Mombasa',
      physicalAddress: '789 Another Street, Mombasa',
    },
    limit: 3000000,
    limitCurrency: 'KES',
  },
]

export const mockBankBranches = [
  {
    code: '001',
    name: 'Main Branch',
    city: 'Nairobi',
    address: '123 Main Street, Nairobi',
    phone: '+************',
  },
  {
    code: '002',
    name: 'Secondary Branch',
    city: 'Mombasa',
    address: '456 Secondary Street, Mombasa',
    phone: '+************',
  },
]

export const mockFormikValues = {
  name: 'Test Broker',
  email: '<EMAIL>',
  mobile: '+************',
  bankName: 'Test Bank',
  bankCode: '001',
  swiftCode: 'TESTKE22',
  bankAccountNumber: '**********',
  branchCode: '001',
  accountBranchName: 'Main Branch',
  callBackUrl: 'https://test-broker.com/callback',
  physicalAddress: {
    country: 'Kenya',
    town: 'Nairobi',
    physicalAddress: '123 Test Street, Nairobi',
  },
  productIds: ['product-1'],
  status: 'Active',
}

export const mockValidationErrors = {
  name: 'Name must not be empty',
  email: 'Invalid email',
  mobile: 'Invalid phone number',
  bankName: 'Bank Name must not be empty',
  bankAccountNumber: 'Bank Account Number must not be empty',
}
