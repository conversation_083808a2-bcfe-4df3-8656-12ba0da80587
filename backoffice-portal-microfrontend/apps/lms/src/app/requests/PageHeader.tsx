import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
} from '@mui/material'
import React, { useState } from 'react'
import FilterListOffIcon from '@mui/icons-material/FilterListOff'
import FilterListIcon from '@mui/icons-material/FilterList'
import { useAppDispatch } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'
import { getApprovals } from '@/store/actions'
import { CustomSearchByInput } from '@dtbx/ui/components/Input'

const requestsSearchByItems: {
  label: string
  value: Array<keyof IApprovalRequest>
}[] = [
  {
    label: 'Maker',
    value: ['maker'],
  },
  {
    label: 'Status',
    value: ['status'],
  },
  // {
  //   label: 'Type',
  //   value: ['makerCheckerType'],
  // },
]

const PageHeader = ({
  selectedStatus,
  onStatusChange,
}: {
  selectedStatus: string
  onStatusChange: (value: string) => void
}) => {
  const dispatch = useAppDispatch()
  const [searchTerm, setSearchTerm] = React.useState<string>('')
  const [searchBy, setSearchBy] = React.useState<{
    label: string
    value: Array<keyof IApprovalRequest>
  }>({ label: 'Maker', value: ['maker'] })
  const [openFilter, setOpenFilter] = useState(false)

  const handleSearch = async () => {
    let url = ``
    searchBy.value[0] === 'status'
      ? (url = `?channel=LMS&status=${searchTerm.toUpperCase()}`)
      : (url = `?channel=LMS&maker=${searchTerm}`)
    await getApprovals(dispatch, url)
  }

  return (
    <Stack
      sx={{
        flexDirection: 'column',
        justifyContent: 'space-between',
        px: '2%',
        pt: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          gap: '2%',
        }}
      >
        <CustomSearchByInput<IApprovalRequest>
          searchByDropDownItems={requestsSearchByItems}
          width="55%"
          searchByValue={searchBy}
          onChange={(search: string) => {
            setSearchTerm(search)
          }}
          onKeyDown={() => {
            handleSearch()
          }}
          onSearchBySelect={(value: {
            label: string
            value: Array<keyof IApprovalRequest>
          }) => {
            setSearchBy(value)
          }}
          value={searchTerm}
          placeholder="Type and press enter to search approval requests"
        />
        <Button
          variant="outlined"
          sx={{
            border: openFilter ? '1px solid #000A12' : '1px solid #D0D5DD',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          startIcon={openFilter ? <FilterListOffIcon /> : <FilterListIcon />}
          onClick={() => setOpenFilter(!openFilter)}
        >
          {openFilter ? 'Hide' : 'Show'} Filters
        </Button>
      </Stack>
      {openFilter && (
        <Stack justifyContent={'space-around'} mt={'2%'}>
          <FormControl
            sx={{
              width: '30%',
            }}
            variant={'outlined'}
          >
            <InputLabel
              sx={{
                background: '#FFFFFF',
              }}
            >
              Status
            </InputLabel>
            <Select
              value={selectedStatus}
              onChange={(e) => onStatusChange(e.target.value as string)}
            >
              {['PENDING', 'APPROVED', 'REJECTED'].map((status) => (
                <MenuItem key={status} value={status}>
                  {status}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>
      )}
    </Stack>
  )
}

export default PageHeader
