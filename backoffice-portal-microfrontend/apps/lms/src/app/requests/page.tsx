'use client'
import React, { useEffect, useState } from 'react'
import { Divider, Stack, Typography } from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import { getApprovals } from '@/store/actions'
import { LoadingListsSkeleton } from '@dtbx/ui/components/Loading'

import PageHeader from '../requests/PageHeader'
import RequestsList from '../requests/RequestsList'
import { IApprovalRequest } from '@/store/interfaces'
import { setSelectedApprovalRequest } from '@/store/reducers'

const RequestsPage = () => {
  const dispatch = useAppDispatch()
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const { isLoadingRequests } = useAppSelector(
    (state) => state.approvalRequests
  )
  useEffect(() => {
    const params = `?channel=LMS&page=1&size=10${selectedStatus ? `&status=${selectedStatus}` : ''}`
    dispatch(setSelectedApprovalRequest({} as IApprovalRequest))
    getApprovals(dispatch, params)
  }, [selectedStatus])

  return (
    <Stack>
      <Typography
        variant="h5"
        sx={{
          textAlign: 'left',
          py: '2%',
          px: '2%',
        }}
      >
        Requests
      </Typography>
      <Divider />
      <PageHeader
        selectedStatus={selectedStatus}
        onStatusChange={setSelectedStatus}
      />
      {isLoadingRequests ? <LoadingListsSkeleton /> : <RequestsList selectedStatus={selectedStatus} />}
    </Stack>
  )
}

export default RequestsPage
