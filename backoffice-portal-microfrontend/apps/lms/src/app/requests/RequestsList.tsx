'use client'
import {
  Box,
  Chip,
  ChipProps,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import React, { useState } from 'react'
import { IHeadCell } from '@dtbx/store/interfaces'
import { IApprovalRequest } from '@/store/interfaces'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useAppDispatch, useAppSelector } from '@/store'
import { getApprovals } from '@/store/actions'
import { formatTimestamp } from '@dtbx/store/utils'
import {
  CustomErrorChip,
  CustomSuccessChip,
  CustomWarningChip,
} from '@dtbx/ui/components/Chip'

import { RequestsMoreMenu } from '@/app/requests/RequestsMoreMenu'
import EmptyPage from '../reports/Tables/EmptyPage'

export const RequestChip = styled(Chip)<ChipProps>(() => ({
  padding: '2px 8px',
  borderRadius: '16px',
  background: '#F3F5F5',
  height: '24px',
  width: 'auto',
  minWidth: '0', // Add this line
}))
export const CustomTableCell = styled(TableCell)(() => ({
  color: '#667085',
}))
const userRequestHeaders: IHeadCell[] = [
  {
    label: 'Request type',
    id: 'request_type',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Module',
    id: 'module',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Maker Timestamp',
    id: 'dateCreated',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'STATUS',
    id: 'status',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Maker',
    id: 'maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'ACTIONS',
    id: 'actions',
    alignCenter: false,
    alignRight: false,
  },
]
const RequestsList = ({ selectedStatus }: { selectedStatus: string }) => {
  const dispatch = useAppDispatch()
  const { approvalRequests, approvalRequestResponse } = useAppSelector(
    (state) => state.approvalRequests
  )
  const [paginationOptions, setPaginationOptions] = useState({
    page: approvalRequestResponse.pageNumber,
    size: 10,
    totalPages: approvalRequestResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    const params = `?channel=LMS&page=${newOptions.page}&size=${newOptions.size}${
      selectedStatus ? `&status=${selectedStatus}` : ''
    }`
    await getApprovals(dispatch, params)
  }
  /*************************end pagination handlers**************************/
  const requestName = (request: IApprovalRequest) => {
    if (
      request?.diff[0]?.field === 'cancelled' &&
      request?.diff[0]?.newValue === 'true'
    ) {
      return 'Cancel Loan Request'
    }
    return request?.makerCheckerType?.name
  }
  if (approvalRequestResponse.totalNumberOfPages === 0 || approvalRequests.length === 0) {
    return <EmptyPage />
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        px: '1.8%',
        py: '1.6%',
        gap: '17px',
      }}
    >
      <Paper
        elevation={0}
        sx={{
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          borderRadius: '4px',
          border: '1px solid #EAECF0',
          background: '#FFFFFF',
        }}
      >
        <TableContainer component={Paper} elevation={0}>
          <Table stickyHeader sx={{}}>
            <CustomTableHeader
              order={'desc'}
              orderBy={''}
              rowCount={0}
              headLabel={userRequestHeaders}
              numSelected={0}
            />
            <TableBody>
              {approvalRequests &&
                approvalRequests.map((row) => (
                  <TableRow key={row.id}>
                    <CustomTableCell
                      sx={{
                        padding: '10px 24px 10px 16px',
                      }}
                    >
                      <Box>
                        <RequestChip
                          label={requestName(row)}
                          sx={{ width: 'auto' }}
                        />
                      </Box>
                    </CustomTableCell>
                    <CustomTableCell>
                      {row.makerCheckerType.module}
                    </CustomTableCell>
                    <CustomTableCell>
                      {formatTimestamp(row.dateCreated)}
                    </CustomTableCell>
                    <CustomTableCell>
                      {row.status === 'PENDING' ? (
                        <CustomWarningChip label={row.status} />
                      ) : row.status === 'REJECTED' ? (
                        <CustomErrorChip label={row.status} />
                      ) : (
                        <CustomSuccessChip label={row.status} />
                      )}
                    </CustomTableCell>
                    <CustomTableCell>{row.maker}</CustomTableCell>
                    <CustomTableCell sx={{ padding: 0 }}>
                      <RequestsMoreMenu request={row} />
                    </CustomTableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        {approvalRequestResponse.totalNumberOfPages > 0 && (
          <CustomPagination
            options={{
              ...paginationOptions,
              totalPages: approvalRequestResponse.totalNumberOfPages,
            }}
            handlePagination={handlePagination}
          />
        )}
      </Paper>
    </Stack>
  )
}

export default RequestsList
