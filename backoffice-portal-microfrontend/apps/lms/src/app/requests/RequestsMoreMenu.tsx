import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { sentenceCase } from 'tiny-case'
import { ArrowOutwardOutlined, CloseRounded } from '@mui/icons-material'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'
import {
  approveMakeCreateBroker,
  approveOrganizationRequest,
  approveRejectProductCreation,
  approveUpdateBroker,
  getLoanCustomerProfile,
  getLoanRequestById,
  getOptinRequestById,
  rejectOrganizationRequest,
} from '@/store/actions'
import {
  setIsCheckerViewProfileOpen,
  setSelectedApprovalRequest,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { RequestsApprovalIcon } from '@dtbx/ui/icons'
import { AccessControlWrapper, formatTimestamp } from '@dtbx/store/utils'

export const RequestsMoreMenu = ({
  request,
}: {
  request: IApprovalRequest
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = async () => {
    setAnchorEl(null)
  }
  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: 500,
        }}
        endIcon={<KeyboardArrowDownIcon />}
      >
        Actions
      </Button>
      <Menu
        id="demo-customized-menu"
        slotProps={{
          list: {
            'aria-labelledby': 'demo-customized-button',
          },
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <ReviewRequest request={request} />
      </Menu>
    </>
  )
}
const ReviewRequest = ({ request }: { request: IApprovalRequest }) => {
  const [open, setOpen] = useState<boolean>(false)
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const [checkerComments, setCheckerComments] = useState<string>('')
  const [checkerCommentsError, setCheckerCommentsError] =
    useState<boolean>(false)

  const handleClose = (
    e: React.MouseEvent<HTMLButtonElement> | null,
    action: string
  ) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }

  const handleRejectRequest = async () => {
    if (!checkerComments) {
      setCheckerCommentsError(true)
      return
    }
    switch (request.makerCheckerType.type) {
      case 'LMS_CREATE_ORGANIZATION':
        return await rejectOrganizationRequest(dispatch, request.id)
      case 'LMS_UPDATE_BROKER':
        return await approveUpdateBroker(
          request.entityId || '',
          request.id,
          dispatch,
          'reject',
          checkerComments
        )
      case 'LMS_CREATE_PRODUCT':
        return await approveRejectProductCreation(
          { comments: checkerComments },
          request.id,
          'Reject',
          dispatch
        )
      case 'LMS_CREATE_BROKER':
        return await approveMakeCreateBroker(
          request.id || '',
          dispatch,
          'reject',
          checkerComments
        )
      default:
        setOpen(false)
    }
    setOpen(false)
  }
  const handleApproveRequest = async () => {
    if (
      !checkerComments &&
      request.makerCheckerType.type !== 'LMS_UPDATE_REQUEST'
    ) {
      setCheckerCommentsError(true)
      return
    }
    switch (request.makerCheckerType.type) {
      case 'LMS_UPDATE_REQUEST':
        dispatch(setSelectedApprovalRequest(request))
        dispatch(setIsCheckerViewProfileOpen(true))
        if (request.entity != null) {
          const reqId =
            JSON.parse(request.entity).requestId || request.entityId || ''
          const requestType = JSON.parse(request.entity).requestType || ''
          if (requestType === 'OPTIN') {
            await getOptinRequestById(dispatch, reqId)
            request.status === 'PENDING' &&
              dispatch(setIsCheckerViewProfileOpen(true))
            return router.push('/loans/optinRequest')
          } else {
            const response = await getLoanRequestById(dispatch, reqId)
            if (response) {
              await getLoanCustomerProfile(
                dispatch,
                response.customer.id,
                response.id
              )
              return router.push(`/loans/request/checks/kyc`)
            } else {
              setOpen(false)
              return dispatch(
                setNotification({
                  message: 'This loan request was not found',
                  type: 'warning',
                })
              )
            }
          }
        }
        dispatch(
          setNotification({
            message: 'This loan request was not found',
            type: 'warning',
          })
        )
        return router.push(`/loans/request`)
      case 'LMS_CREATE_PRODUCT':
        return await approveRejectProductCreation(
          { comments: checkerComments },
          request.id,
          'Approve',
          dispatch
        )
      case 'LMS_CREATE_ORGANIZATION':
        return await approveOrganizationRequest(dispatch, request.id)
      case 'LMS_UPDATE_BROKER':
        return await approveUpdateBroker(
          request.entityId || '',
          request.id,
          dispatch,
          'accept',
          checkerComments
        )
      case 'LMS_CREATE_BROKER':
        return await approveMakeCreateBroker(
          request.id || '',
          dispatch,
          'accept',
          checkerComments
        )
      default:
        setOpen(false)
    }
  }

  return (
    <>
      <MenuItem
        onClick={() => setOpen(!open)}
        sx={{
          color: '',
        }}
      >
        Review Request
      </MenuItem>
      <Dialog open={open} maxWidth="sm" fullWidth>
        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '9px',
          }}
        >
          <Stack
            sx={{
              justifyContent: 'space-between',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            <RequestsApprovalIcon />
            <IconButton onClick={(e) => handleClose(e, 'close')}>
              <CloseRounded />
            </IconButton>
          </Stack>
          <Typography
            variant="subtitle1"
            sx={{
              fontSize: '20px',
              mb: '1vh',
            }}
          >
            Approval request details
          </Typography>
          <Stack
            sx={{
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            <TextField
              fullWidth
              label="Approval request type"
              sx={{}}
              value={
                request?.makerCheckerType.type
                  ? sentenceCase(request?.makerCheckerType.type as string)
                  : ''
              }
            />
            <TextField
              fullWidth
              label="Changes made"
              sx={{
                width: 'auto',
              }}
              multiline
              value={
                request &&
                request.diff &&
                request.diff
                  .map(
                    (change, index) =>
                      `${index + 1}. ${change.field} was changed from  ${typeof change.oldValue === 'string' || typeof change.oldValue === 'boolean' || change.oldValue === null ? change.oldValue : change.oldValue && Array.isArray(change.oldValue) && change.oldValue.map((val) => val?.name).join(', ')} to ${typeof change.newValue === 'string' || typeof change.newValue === 'boolean' || change.newValue === null ? change.newValue : change.newValue && Array.isArray(change.newValue) && change.newValue.map((val) => val?.name).join(', ')}`
                  )
                  .join('\n')
              }
            />
            <TextField
              fullWidth
              label="Maker"
              sx={{}}
              value={request && request.maker}
            />
            <TextField
              fullWidth
              label="Maker timestamp"
              sx={{}}
              value={request && formatTimestamp(request.dateCreated)}
            />
            <TextField
              fullWidth
              label="Maker comment"
              sx={{}}
              value={
                request && request.makerComments
                  ? request.makerComments
                  : 'No comment'
              }
            />
            <TextField
              label="Checker Comments"
              placeholder="Write your comment here"
              value={checkerComments}
              onChange={(e) => {
                setCheckerComments(e.target.value)
                e.target.value.length > 0
                  ? setCheckerCommentsError(false)
                  : setCheckerCommentsError(true)
              }}
              error={checkerCommentsError}
              helperText={checkerCommentsError ? 'Please enter comments' : ''}
              multiline
              sx={{
                display:
                  request.makerCheckerType.type !== 'LMS_UPDATE_REQUEST'
                    ? 'flex'
                    : 'none',
              }}
              rows={4}
              fullWidth
            />
          </Stack>
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '5%',
            }}
          >
            <AccessControlWrapper
              rights={request?.makerCheckerType?.checkerPermissions?.filter(
                (perm) => perm.includes('REJECT')
              )}
            >
              <Button
                variant="outlined"
                disabled={request.status !== 'PENDING'}
                fullWidth
                sx={{}}
                onClick={handleRejectRequest}
              >
                {request.makerCheckerType.type === 'LMS_UPDATE_REQUEST'
                  ? 'Back'
                  : 'Reject'}
              </Button>
            </AccessControlWrapper>
            <AccessControlWrapper
              rights={request?.makerCheckerType?.checkerPermissions?.filter(
                (perm) => perm.includes('ACCEPT')
              )}
            >
              <Button
                variant="contained"
                fullWidth
                disabled={
                  request.status !== 'PENDING' &&
                  request.makerCheckerType.type !== 'LMS_UPDATE_REQUEST'
                }
                sx={{
                  textWrap: 'nowrap',
                }}
                onClick={handleApproveRequest}
              >
                {request.status !== 'PENDING'
                  ? 'View Entity'
                  : request.makerCheckerType.type === 'LMS_UPDATE_REQUEST'
                    ? 'Review Request'
                    : 'Approve'}
                {request.makerCheckerType.type === 'LMS_UPDATE_REQUEST' && (
                  <ArrowOutwardOutlined />
                )}
              </Button>
            </AccessControlWrapper>
          </Stack>
        </DialogContent>
      </Dialog>
    </>
  )
}
