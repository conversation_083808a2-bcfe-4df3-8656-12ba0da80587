import {
  Button,
  <PERSON>,
  linearProgressClasses,
  Stack,
  Typography,
} from '@mui/material'
import { ArrowForwardIos } from '@mui/icons-material'
import { ILoanRequest, IRepaymentHistory } from '@/store/interfaces'
import { formatCurrency } from '@dtbx/store/utils'
import { CustomLinearProgress } from '@dtbx/ui/components/Progress'

type IProfile = {
  selectedRequest: ILoanRequest
  txns: IRepaymentHistory[]
}
export const LoanProfile = ({ selectedRequest, txns }: IProfile) => {
  return (
    <>
      <Stack
        sx={{
          px: '2%',
          py: '1%',
        }}
      >
        <Typography variant={'h6'}>Loan Profile</Typography>
        <Stack
          sx={{
            border: '1px solid #D0D5DD',
            background: '#FFF',
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            padding: '2%',
            gap: '20px',
          }}
        >
          {selectedRequest.loan?.loanAmount &&
            selectedRequest.loan?.paidPrepaymentAmount && (
              <CustomLinearProgress
                variant={'determinate'}
                sx={{
                  [`& .${linearProgressClasses.bar}`]: {
                    borderRadius: 5,
                    backgroundColor:
                      selectedRequest.loan.status === 'ACTIVE'
                        ? 'success.main'
                        : 'info.main',
                  },
                }}
                value={
                  selectedRequest.loan
                    ? ((selectedRequest.loan.principalAmount +
                        selectedRequest.loan.interestAmount -
                        (selectedRequest.loan.outstandingInterestAmount +
                          selectedRequest.loan.outstandingPrincipalAmount)) /
                        (selectedRequest.loan.principalAmount +
                          selectedRequest.loan.interestAmount)) *
                      100
                    : 0
                }
              />
            )}
          <Stack direction={'row'} justifyContent={'space-between'}>
            <Typography variant={'body2'}>
              Amount repaid:{' '}
              {selectedRequest.loan &&
                formatCurrency(
                  selectedRequest.loan.principalAmount +
                    selectedRequest.loan.interestAmount -
                    (selectedRequest.loan.outstandingInterestAmount +
                      selectedRequest.loan.outstandingPrincipalAmount)
                )}
            </Typography>
            <Typography variant={'body2'}>
              Total Amount Due:{' '}
              {selectedRequest.loan &&
                formatCurrency(
                  selectedRequest.loan.principalAmount +
                    selectedRequest.loan.interestAmount
                )}
            </Typography>
          </Stack>
          <Stack direction={'row'} justifyContent={'space-between'}>
            <Typography variant={'body1'}>
              Date Disbursed:{' '}
              <Typography
                sx={{
                  fontWeight: 700,
                }}
              >
                {selectedRequest.loan?.issueDate}
              </Typography>
            </Typography>
            <Typography variant={'body1'}>
              CBS Reference:{' '}
              <Typography
                sx={{
                  fontWeight: 700,
                }}
              >
                {selectedRequest.loan?.externalLoanId}
              </Typography>
            </Typography>
            <Typography variant={'body1'}>
              Loan Charges:{' '}
              <Typography
                sx={{
                  fontWeight: 700,
                }}
              >
                {formatCurrency(selectedRequest.loan?.interestAmount)}
              </Typography>
            </Typography>
            <Typography variant={'body1'}>
              Initial Amount Prepaid:{' '}
              <Typography
                sx={{
                  fontWeight: 700,
                }}
              >
                {formatCurrency(selectedRequest.loan?.paidPrepaymentAmount)}
              </Typography>
            </Typography>
            <Typography variant={'body1'}>
              Tenure{' '}
              <Typography
                sx={{
                  fontWeight: 700,
                }}
              >
                {selectedRequest.loanRequest.loanTenure}{' '}
                {selectedRequest.loan?.frequency === 'M' ? 'Months' : 'Days'}
              </Typography>
            </Typography>
            <Typography variant={'body1'}>
              Principal Amount Disbursed{' '}
              <Typography
                sx={{
                  fontWeight: 700,
                }}
              >
                {formatCurrency(selectedRequest.loan?.principalAmount)}
              </Typography>
            </Typography>
            <Typography>
              Status{' '}
              <Typography>
                {selectedRequest.loan?.status === 'ACTIVE' ? (
                  <Chip label={selectedRequest.loan.status} color={'success'} />
                ) : (
                  <Chip label={selectedRequest.loan?.status} color={'info'} />
                )}
              </Typography>
            </Typography>
          </Stack>
        </Stack>
      </Stack>
      <Stack
        sx={{
          px: '2%',
          py: '1%',
        }}
      >
        <Typography variant={'h6'}>Repayment History</Typography>
        {txns.map((txn, index) => (
          <Stack
            key={index}
            sx={{
              borderBottom: '1px solid #D0D5DD',
              background: '#FFF',
              boxShadow:
                '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
              padding: '2%',
              gap: '20px',
              marginBottom: '16px',
            }}
          >
            <Stack direction={'row'} justifyContent={'space-between'}>
              <Typography variant={'body1'}>
                Amount Paid{' '}
                <Typography
                  sx={{
                    fontWeight: 700,
                  }}
                >
                  {txn.amount}
                </Typography>
              </Typography>
              <Typography variant={'body1'}>
                Date Paid:{' '}
                <Typography
                  sx={{
                    fontWeight: 700,
                  }}
                >
                  {txn.transactionDate}
                </Typography>
              </Typography>
              <Typography variant={'body1'}>
                Transaction Type:{' '}
                <Typography
                  sx={{
                    fontWeight: 700,
                  }}
                >
                  {txn.transactionType === 'D' ? 'Debit' : 'Credit'}
                </Typography>
              </Typography>
              <Typography variant={'body1'}>
                Transaction Ref:{' '}
                <Typography
                  sx={{
                    fontWeight: 700,
                  }}
                >
                  {txn.transactionReference}
                </Typography>
              </Typography>

              <Typography variant={'body1'}>
                DTB Ref No{' '}
                <Typography
                  sx={{
                    fontWeight: 700,
                  }}
                >
                  {txn.transactionReference}
                </Typography>
              </Typography>
              <Button variant={'text'} endIcon={<ArrowForwardIos />}>
                View Transaction
              </Button>
            </Stack>
          </Stack>
        ))}
      </Stack>
    </>
  )
}
