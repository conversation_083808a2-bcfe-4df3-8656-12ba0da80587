'use client'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material'
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined'
import HourglassBottomOutlinedIcon from '@mui/icons-material/HourglassBottomOutlined'
import PersonOutlinedIcon from '@mui/icons-material/PersonOutlined'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { ArrowForwardIos, ShieldOutlined } from '@mui/icons-material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import React, { useEffect } from 'react'
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { getLoanRepayments } from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { setIsCheckerViewProfileOpen } from '@/store/reducers'
import { formatCurrency } from '@dtbx/store/utils'

import { EmptyLoanProfile } from '@/app/loans/request/EmptyLoanProfile'
import { LoanProfile } from '@/app/loans/request/LoanProfile'

const stages = [
  {
    id: 1,
    title: 'Pre validation',
    key: 'prevalidation',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 2,
    title: 'Customer KYC',
    key: 'kyc',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 3,
    title: 'Credit Checks',
    key: 'checks',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 4,
    title: 'Credit Terms Accepted',
    key: 'accept',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 5,
    title: 'Prepayment Complete',
    key: 'prepayment',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 6,
    title: 'Disbursement',
    key: 'disbursement',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 7,
    title: 'Processed',
    key: 'processed',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
]
const ViewRequestPage = () => {
  const dispatch = useAppDispatch()
  const { selectedRequest, loanRepayments, currentProduct } = useAppSelector(
    (state) => state.loans
  )

  useEffect(() => {
    if (selectedRequest.loan !== null) {
      getLoanRepayments(dispatch, selectedRequest.id)
    }
  }, [selectedRequest])
  const router = useCustomRouter()
  return (
    <>
      <Stack
        direction="column"
        sx={{
          gap: '20px',
          background: '#F7F7F7',
        }}
      >
        <Stack
          sx={{
            gap: '20px',
            px: '2%',
            py: '1%',
          }}
        >
          <Stack justifyContent={'flex-start'}>
            <IconButton
              sx={{
                padding: '8px',
                border: '1px solid #667085',
                borderRadius: '8px',
                width: '50px',
              }}
              onClick={() => window.history.back()}
            >
              <ArrowBackIcon />
            </IconButton>
          </Stack>
          <Stack
            direction="row"
            sx={{
              gap: '10%',
            }}
          >
            <Stack direction="column">
              <Typography variant="body1">Request No.</Typography>
              <Typography variant="h6">
                {selectedRequest.requestReference}
              </Typography>
            </Stack>
            <Stack direction="column">
              <Typography
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                }}
                variant="body1"
              >
                <ShieldOutlined /> Provider
              </Typography>
              <Typography variant="h6">{currentProduct.name}</Typography>
            </Stack>
            <Stack direction="column">
              <Typography
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                }}
                variant="body1"
              >
                <PersonOutlinedIcon />
                {selectedRequest.customer?.firstName}{' '}
                {selectedRequest.customer?.lastName}
              </Typography>
              <Typography variant="h6">
                {selectedRequest.customer?.idDocumentNumber}
              </Typography>
              <Button
                variant="text"
                sx={{
                  textAlign: 'left',
                  padding: 0,
                  paddingTop: '5px',
                  textDecoration: 'underline',
                  gap: 0,
                }}
                endIcon={<ArrowForwardIos />}
                onClick={() => {
                  dispatch(setIsCheckerViewProfileOpen(false))
                  return router.push('/loans/request/checks/kyc')
                }}
              >
                View Customer Profile
              </Button>
            </Stack>
            <Stack>
              <Typography
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                }}
                variant="body1"
              >
                Loan Amount <InfoOutlinedIcon />
              </Typography>
              <Typography variant="h6">
                {formatCurrency(selectedRequest.loanRequest.loanAmount)}
              </Typography>
            </Stack>
          </Stack>
        </Stack>
        <Stack
          direction="row"
          sx={{
            gap: '10px',
            px: '1%',
            py: '1%',
            background: '#FFF',
          }}
        >
          {stages.map((stage, id) => {
            const currentStage = stages.find((stage) =>
              selectedRequest.status.toLowerCase().includes(stage.key)
            )
            if (currentStage && currentStage.id > stage.id) {
              stage.icon = (
                <CheckCircleOutlinedIcon
                  sx={{
                    color: '#2970FF',
                    fontSize: '20px',
                  }}
                />
              )
              stage.status = 'passed'
            } else if (currentStage && currentStage.id === stage.id) {
              if (selectedRequest.status.toLowerCase().includes('failed')) {
                stage.icon = (
                  <ErrorOutlineOutlinedIcon
                    color="error"
                    sx={{
                      fontSize: '20px',
                    }}
                  />
                )
                stage.status = 'failed'
              } else if (
                selectedRequest.status.toLowerCase().includes('awaiting') ||
                selectedRequest.status.toLowerCase().includes('pending')
              ) {
                stage.icon = (
                  <HourglassBottomOutlinedIcon
                    color="info"
                    sx={{
                      fontSize: '20px',
                    }}
                  />
                )
                stage.status = 'pending'
              } else {
                stage.icon = (
                  <CheckCircleOutlinedIcon
                    sx={{
                      color: '#2970FF',
                      fontSize: '20px',
                    }}
                  />
                )
                stage.status = 'passed'
              }
            } else {
              stage.icon = (
                <HourglassBottomOutlinedIcon
                  sx={{
                    fontSize: '20px',
                  }}
                />
              )
              stage.status = 'pending'
            }
            const handleClick = () => {
              if (stage.key === 'kyc' && stage.status === 'failed') {
                router.push('/loans/request/checks/kyc')
              }
              if (stage.key === 'checks') {
                router.push('/loans/request/checks/credit')
              } else return
            }
            return (
              <Button
                key={id}
                variant="outlined"
                sx={{
                  borderRadius: '9999px',
                  border: `1px solid ${stage.status === 'passed' ? '#2E90FA' : stage.status === 'failed' ? '#FDA29B' : '#EAECF0'}`,
                  background:
                    stage.status === 'passed'
                      ? '#E0EAFF'
                      : stage.status === 'failed'
                        ? '#FEE4E2'
                        : '#FFF',
                  '&:hover': {
                    border: `1px solid ${stage.status === 'passed' ? '#2E90FA' : stage.status === 'failed' ? '#FDA29B' : '#EAECF0'}`,
                    background:
                      stage.status === 'passed'
                        ? '#E0EAFF'
                        : stage.status === 'failed'
                          ? '#FEE4E2'
                          : '#FFF',
                  },
                  py: '5px',
                  px: '1%',
                  display: 'flex',
                  flexDirection: 'row',
                  textAlign: 'left',
                  gap: 1,
                }}
                onClick={handleClick}
              >
                {stage.icon}
                <Typography
                  variant="body1"
                  sx={{
                    color:
                      stage.status === 'passed'
                        ? '#2970FF'
                        : stage.status === 'failed'
                          ? '#FF4D4F'
                          : '#98A2B3',
                    textWrap: 'nowrap',
                  }}
                >
                  {stage.title}{' '}
                  {stage.status === 'failed' && sentenceCase(stage.status)}
                </Typography>
                {stage.key === 'kyc' && stage.status === 'failed' && (
                  <Stack
                    sx={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      border: '1px solid #FECDCA',
                      borderRadius: '9999px',
                      gap: '5px',
                      px: '5px',
                      color: '#B42318',
                      fontSize: '14px',
                      fontWeight: 500,
                    }}
                  >
                    Review
                    <ArrowForwardIos
                      color="error"
                      sx={{
                        fontSize: '12px',
                      }}
                    />
                  </Stack>
                )}
              </Button>
            )
          })}
        </Stack>
        <Stack
          sx={{
            px: '2%',
            py: '1%',
          }}
        >
          <Typography variant="h6">Product Details</Typography>
          <Stack
            sx={{
              border: '1px solid #D0D5DD',
              background: '#FFF',
              boxShadow:
                '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
              padding: '2%',
            }}
          >
            <Stack
              direction="row"
              justifyContent={'space-between'}
              sx={{
                marginBottom: '15px',
              }}
            >
              <Typography variant="body1">
                Total Premium Amount:{' '}
                <span
                  style={{
                    fontWeight: 700,
                  }}
                >
                  {formatCurrency(selectedRequest.loanRequest.loanAmount)}
                </span>
              </Typography>
              <Typography variant="body1">
                Prepayment Amount:
                <span
                  style={{
                    fontWeight: 700,
                  }}
                >
                  {selectedRequest.loan
                    ? formatCurrency(selectedRequest.loan.prepaymentAmount)
                    : '0'}
                </span>
              </Typography>
              <Typography variant="body1">
                Provider Ref.{' '}
                <span
                  style={{
                    fontWeight: 700,
                  }}
                >
                  {selectedRequest.loanRequest.providerItemReference}
                </span>
              </Typography>
              <Typography variant="body1">
                Policy Type:{' '}
                <span
                  style={{
                    fontWeight: 700,
                  }}
                >
                  {selectedRequest.loanRequest.loanPurpose}
                </span>
              </Typography>
              <Typography variant="body1">
                Tenure:{' '}
                <span
                  style={{
                    fontWeight: 700,
                  }}
                >
                  {selectedRequest.loanRequest.loanTenure} Months
                </span>
              </Typography>
            </Stack>
            <Divider
              sx={{
                background: '#D0D5DD',
                height: '1px',
                marginBottom: '10px',
              }}
            />
            <Stack>
              <Typography>Supporting Documents </Typography>
            </Stack>
          </Stack>
        </Stack>
        {selectedRequest.loan! == null ? (
          <EmptyLoanProfile
            customerName={`${selectedRequest.customer?.firstName} ${selectedRequest.customer?.lastName}`}
            status={selectedRequest.status}
          />
        ) : (
          <LoanProfile
            selectedRequest={selectedRequest}
            txns={loanRepayments}
          />
        )}
      </Stack>
    </>
  )
}
export default ViewRequestPage
