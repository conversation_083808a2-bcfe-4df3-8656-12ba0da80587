'use client'

import { ArrowForwardIos } from '@mui/icons-material'
import { Button, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getLoanCustomerProfile, rerunCheck } from '@/store/actions'
import { CustomSkeleton, LoadingButton } from '@dtbx/ui/components/Loading'

import { ValidationCard } from '@/app/loans/request/checks/credit/Cards'

const CreditChecksPage = () => {
  const {
    selectedRequest,
    customerChecks,
    isLoadingCustomerProfile,
    creditTypeValidations,
    isCheckRerunLoading,
  } = useAppSelector((state) => state.loans)
  const dispatch = useAppDispatch()
  useEffect(() => {
    getLoanCustomerProfile(
      dispatch,
      selectedRequest.customer?.id,
      selectedRequest.id
    )
  }, [selectedRequest])
  const handleRetryOrgLimit = async () => {
    const checkId = customerChecks.find(
      (check) => check.code === 'ORGANIZATION_LIMIT_VALIDATION'
    )?.id
    await rerunCheck(selectedRequest.id, { checkId: checkId || '' }, dispatch)
  }
  return (
    <Stack>
      {isLoadingCustomerProfile ? (
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '1vw',
            width: '100%',
            px: '3%',
          }}
        >
          <CustomSkeleton
            animation="wave"
            variant="rectangular"
            width="30%"
            height="60vh"
          />
          <CustomSkeleton
            animation="wave"
            variant="rectangular"
            width="70%"
            height="60vh"
          />
        </Stack>
      ) : (
        <Stack
          direction="row"
          sx={{
            background: '#FFF',
            borderRadius: '12px',
            padding: '2%',
            width: '100%',
          }}
        >
          {customerChecks.find(
            (check) => check.code === 'ORGANIZATION_LIMIT_VALIDATION'
          )?.status === 'Passed' ? (
            <Stack
              sx={{
                border: '1px solid #12B76A',
                borderRadius: '4px',
                width: '50%',
                px: '2%',
                py: '1%',
                maxHeight: '15vh',
              }}
            >
              <Typography variant="subtitle2">
                Organization Limit status
              </Typography>
              <Stack
                sx={{
                  alignItems: 'center',
                  alignContent: 'center',
                  gap: '1vh',
                }}
              >
                <img
                  src={'/lms/icons/speedometer.svg'}
                  alt="speedometer"
                  style={{
                    width: '50px',
                  }}
                />
                <Typography variant="subtitle3">
                  Organization Limit Check Passed
                </Typography>
              </Stack>
            </Stack>
          ) : customerChecks.find(
              (check) => check.code === 'ORGANIZATION_LIMIT_VALIDATION'
            )?.status === 'Failed' ? (
            <Stack
              sx={{
                border: '1px solid #FDA29B',
                borderRadius: '4px',
                width: '50%',
                px: '2%',
                py: '1%',
                maxHeight: '20vh',
              }}
            >
              <Typography variant="subtitle2">
                Organization Limit status
              </Typography>
              <Stack
                sx={{
                  alignItems: 'center',
                  alignContent: 'center',
                  gap: '1vh',
                }}
              >
                <img
                  src={'/lms/icons/speedometer.svg'}
                  alt="speedometer"
                  style={{
                    width: '50px',
                  }}
                />
                <Typography variant="subtitle3">
                  Organization exceeded their limit
                </Typography>
                {isCheckRerunLoading ? (
                  <LoadingButton />
                ) : (
                  <Button
                    variant="contained"
                    fullWidth
                    endIcon={<ArrowForwardIos />}
                    onClick={handleRetryOrgLimit}
                  >
                    Retry
                  </Button>
                )}
              </Stack>
            </Stack>
          ) : (
            <Stack
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '4px',
                width: '50%',
                px: '2%',
                py: '1%',
                maxHeight: '20vh',
              }}
            >
              <Typography variant="subtitle2">
                Organization Limit status
              </Typography>
              <Stack
                sx={{
                  alignItems: 'center',
                  alignContent: 'center',
                  gap: '1vh',
                }}
              >
                <img
                  src={'/lms/icons/speedometer.svg'}
                  alt="speedometer"
                  style={{
                    width: '50px',
                  }}
                />
                <Typography variant="subtitle3">N/A</Typography>
              </Stack>
            </Stack>
          )}

          <Stack
            sx={{
              px: '2%',
              gap: '1vh',
              width: '50%',
            }}
          >
            {customerChecks
              .filter((check) =>
                creditTypeValidations.find(
                  (type) =>
                    check.code === type.code &&
                    type.type === 'CREDIT' &&
                    type.code !== 'ORGANIZATION_LIMIT_VALIDATION' &&
                    type.code !== 'CREDIT_ACCEPTANCE_OTP'
                )
              )
              .map((check) => (
                <ValidationCard
                  key={check.code}
                  check={check}
                  request={selectedRequest}
                  isChecker={false}
                />
              ))}
          </Stack>
        </Stack>
      )}
    </Stack>
  )
}

export default CreditChecksPage
