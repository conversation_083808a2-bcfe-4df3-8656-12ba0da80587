'use client'

import { ArrowForwardIos } from '@mui/icons-material'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import CloseIcon from '@mui/icons-material/Close'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined'
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined'
import HourglassBottomOutlinedIcon from '@mui/icons-material/HourglassBottomOutlined'
import { Button, IconButton, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { sentenceCase } from 'tiny-case'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { cancelLoanRequest, getAllValidations } from '@/store/actions'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { LMSCheckerRequestsApiHandler } from '@/app/CheckerRequestsApiHandler'
import { setApprovalActions, setOpenDialog } from '@/store/reducers'

const stages = [
  {
    id: 1,
    title: 'Pre validation',
    key: 'prevalidation',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 2,
    title: 'Customer KYC',
    key: 'kyc',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 3,
    title: 'Credit Checks',
    key: 'checks',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 4,
    title: 'Credit Terms Accepted',
    key: 'accept',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 5,
    title: 'Prepayment Complete',
    key: 'prepayment',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 6,
    title: 'Disbursement',
    key: 'disbursement',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
  {
    id: 7,
    title: 'Processed',
    key: 'processed',
    icon: <HourglassBottomOutlinedIcon />,
    status: 'pending',
  },
]

export default function ValidationsLayout(props: {
  children: React.ReactNode
}) {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { selectedRequest, isLoadingCancelRequest } = useAppSelector(
    (state) => state.loans
  )
  const { selectedApprovalRequest, approvalActions } = useAppSelector(
    (state) => state.approvalRequests
  )
  const handleCancelRequest = async () => {
    const data = {
      comments: 'Request cancelled by user',
    }
    if (HasAccessToRights(['SUPER_UPDATE_LOAN_REQUEST'])) {
      await cancelLoanRequest(selectedRequest.id, data, dispatch, 'super')
    } else if (HasAccessToRights(['MAKE_UPDATE_LOAN_REQUEST'])) {
      await cancelLoanRequest(selectedRequest.id, data, dispatch, 'maker')
    }
  }
  const handleApprove = async () => {
    await LMSCheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `ACCEPT_${selectedApprovalRequest.makerCheckerType.type}`
    )
  }
  const openRejectDialog = () => {
    dispatch(setOpenDialog(true))
  }
  const isCancelled = () => {
    return (
      selectedApprovalRequest?.diff &&
      selectedApprovalRequest?.diff[0]?.field === 'cancelled' &&
      selectedApprovalRequest?.diff[0]?.newValue === 'true'
    )
  }
  useEffect(() => {
    dispatch(
      setApprovalActions(
        isCancelled() ? selectedApprovalRequest?.status === 'PENDING' : false
      )
    )
    getAllValidations(dispatch)
  }, [])
  return (
    <Stack>
      <Stack sx={{ background: '#FFFFFF', px: '2%', py: '1%', gap: '1vh' }}>
        <Stack justifyContent="flex-start">
          <IconButton
            sx={{
              padding: '8px',
              border: '1px solid #667085',
              borderRadius: '8px',
              width: '50px',
            }}
            onClick={() => window.history.back()}
          >
            <ArrowBackIcon />
          </IconButton>
        </Stack>
        <Stack direction="row" justifyContent="space-between">
          <Typography variant="subtitle2" color="text.primary">
            Verification Checks
          </Typography>
          {approvalActions ? (
            <Stack
              sx={{
                flexDirection: 'row',
              }}
            >
              <Button
                variant="contained"
                sx={{
                  background: '#D92D20',
                  height: '34px',
                  '&:hover': {
                    background: '#D92D20',
                  },
                }}
                onClick={openRejectDialog}
              >
                Reject Cancellation
                <CloseIcon />
              </Button>
              <Button
                variant="contained"
                sx={{
                  height: '34px',
                  marginLeft: '10px',
                }}
                onClick={handleApprove}
              >
                Approve Request
                <ArrowForwardIcon />
              </Button>
            </Stack>
          ) : isLoadingCancelRequest ? (
            <LoadingButton width={'15%'} />
          ) : (
            <AccessControlWrapper
              rights={['MAKE_UPDATE_LOAN_REQUEST', 'SUPER_UPDATE_LOAN_REQUEST']}
            >
              <Button
                variant="contained"
                sx={{
                  height: '34px',
                }}
                onClick={handleCancelRequest}
                disabled={selectedRequest.cancelled}
              >
                Cancel Request
              </Button>
            </AccessControlWrapper>
          )}
        </Stack>
        <Stack
          direction="row"
          sx={{
            gap: '10px',
            px: '1%',
            py: '1%',
            background: '#FFF',
          }}
        >
          {stages.map((stage, id) => {
            const currentStage = stages.find((stage) =>
              selectedRequest?.status?.toLowerCase().includes(stage.key)
            )
            if (currentStage && currentStage.id > stage.id) {
              stage.icon = (
                <CheckCircleOutlinedIcon
                  sx={{
                    color: '#2970FF',
                    fontSize: '20px',
                  }}
                />
              )
              stage.status = 'passed'
            } else if (currentStage && currentStage.id === stage.id) {
              if (selectedRequest.status.toLowerCase().includes('failed')) {
                stage.icon = (
                  <ErrorOutlineOutlinedIcon
                    color="error"
                    sx={{
                      fontSize: '20px',
                    }}
                  />
                )
                stage.status = 'failed'
              } else if (
                selectedRequest.status.toLowerCase().includes('awaiting') ||
                selectedRequest.status.toLowerCase().includes('pending')
              ) {
                stage.icon = (
                  <HourglassBottomOutlinedIcon
                    sx={{
                      color: '#2970FF',
                      fontSize: '20px',
                    }}
                  />
                )
                stage.status = 'pending'
              } else {
                stage.icon = (
                  <CheckCircleOutlinedIcon
                    sx={{
                      color: '#2970FF',
                      fontSize: '20px',
                    }}
                  />
                )
                stage.status = 'passed'
              }
            } else {
              stage.icon = (
                <HourglassBottomOutlinedIcon
                  sx={{
                    fontSize: '20px',
                  }}
                />
              )
              stage.status = 'pending'
            }
            const handleClick = () => {
              if (stage.key === 'kyc') {
                router.push('/loans/request/checks/kyc')
              }
              if (stage.key === 'checks') {
                router.push('/loans/request/checks/credit')
              } else return
            }
            return (
              <Button
                key={id}
                variant="outlined"
                sx={{
                  borderRadius: '9999px',
                  border: `1px solid ${stage.status === 'passed' ? '#2E90FA' : stage.status === 'failed' ? '#FDA29B' : '#EAECF0'}`,
                  background:
                    stage.status === 'passed'
                      ? '#E0EAFF'
                      : stage.status === 'failed'
                        ? '#FEE4E2'
                        : '#FFF',
                  '&:hover': {
                    border: `1px solid ${stage.status === 'passed' ? '#2E90FA' : stage.status === 'failed' ? '#FDA29B' : '#EAECF0'}`,
                    background:
                      stage.status === 'passed'
                        ? '#E0EAFF'
                        : stage.status === 'failed'
                          ? '#FEE4E2'
                          : '#FFF',
                  },
                  py: '5px',
                  px: '1%',
                  display: 'flex',
                  flexDirection: 'row',
                  textAlign: 'left',
                  gap: 1,
                }}
                onClick={handleClick}
              >
                {stage.icon}
                <Typography
                  variant="body1"
                  sx={{
                    color:
                      stage.status === 'passed'
                        ? '#2970FF'
                        : stage.status === 'failed'
                          ? '#FF4D4F'
                          : '#98A2B3',
                    textWrap: 'nowrap',
                  }}
                >
                  {stage.title}{' '}
                  {stage.status === 'failed' && sentenceCase(stage.status)}
                </Typography>
                {stage.key === 'kyc' && stage.status === 'failed' && (
                  <Stack
                    sx={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      border: '1px solid #FECDCA',
                      borderRadius: '9999px',
                      gap: '5px',
                      px: '5px',
                      color: '#B42318',
                      fontSize: '14px',
                      fontWeight: 500,
                    }}
                  >
                    Review
                    <ArrowForwardIos
                      color="error"
                      sx={{
                        fontSize: '12px',
                      }}
                    />
                  </Stack>
                )}
              </Button>
            )
          })}
        </Stack>
      </Stack>
      <Stack
        sx={{
          background: '#F7F7F7',
          px: '10%',
          py: '1%',
          minHeight: '75vh',
        }}
      >
        {props.children}
      </Stack>
    </Stack>
  )
}
