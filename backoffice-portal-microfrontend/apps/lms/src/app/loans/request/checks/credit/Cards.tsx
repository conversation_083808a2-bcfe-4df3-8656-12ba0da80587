'use client'
import { QueryBuilder } from '@mui/icons-material'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { Button, Stack, styled, Table, TableBody, TableCell, TableHead, TableRow, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { IApprovalRequest, ICustomerCheck, ILoanRequest } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { completeValidationOverride, getExistingLoansByCustomerNo, initiateOverrideValidationCheck, rerunCheck, superOverrideValidationCheck } from '@/store/actions'
import { formatCurrency, HasAccessToRights } from '@dtbx/store/utils'
import { ApproveChangesDrawer, OverrideValidationDialog, RejectOverride } from '../../KYCStages'
import { LoadingButton } from '@dtbx/ui/components/Loading'

const StyledButton = styled(Button)({
  padding: 0,
  fontWeight: 500,
})
export const ValidationCard = ({
  key,
  check,
  request,
  isChecker,
  selectedApprovalRequest,
}: {
  key: string
  check: ICustomerCheck
  request: ILoanRequest
  isChecker: boolean
  selectedApprovalRequest?: IApprovalRequest
}) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading, customerProfile } = useAppSelector(
    (state) => state.loans
  )
  const handleRecheck = async () => {
    await rerunCheck(request.id, { checkId: check?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: check?.id || '',
      status: 'Passed',
      comments: comments || '',
    }

    check?.id &&
      (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])
        ? await superOverrideValidationCheck(
            overrideData,
            request.id,
            check.id,
            dispatch
          )
        : await initiateOverrideValidationCheck(
            overrideData,
            request.id,
            check.id,
            dispatch
          ))
  }
  useEffect(() => {
    check.code === 'EXISTING_TERM_LOAN_ARREARS' &&
      getExistingLoansByCustomerNo(customerProfile.cif || '', dispatch)
  }, [])
  return (
    <Stack
      key={key}
      sx={{
        borderRadius: '6px',
        border: `1px solid ${check.status === 'Failed' ? '#F04438' : '#D0D5DD'}`,
        py: '2%',
        px: '3%',
        width: '100%',
      }}
    >
      <Stack direction="row" justifyContent="space-between">
        <Typography variant="subtitle2" color="text.primary">
          {check.code}
        </Typography>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {check?.status === 'Failed' && !request?.cancelled && !isChecker && (
            <OverrideValidationDialog
              title={`${check.code} Check Failed`}
              description={check.message}
              handleOverride={handleOverride}
            />
          )}
          {check?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={check.code}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={check.code}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            check?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      {check.code === 'EXISTING_TERM_LOAN_ARREARS' &&
        check.status === 'Failed' && (
          <ArrearsTable amountDue={'200'} arrearsCount={2} />
        )}
      {check.status === 'Failed' ||
        (check.status === 'Pending' && (
          <Typography variant="subtitle2">Reason: {check.message}</Typography>
        ))}
      <Typography
        variant="subtitle3"
        sx={{
          color:
            check?.status === 'Failed'
              ? 'error.main'
              : check?.status === 'Passed'
                ? '#12B76A'
                : 'warning.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {check?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed
          </>
        ) : check?.status === 'Pending' || check?.status === 'NotRun' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            {check?.status === 'Pending' ? 'Pending' : 'Not Run'}
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed
            <CheckCircleIcon
              sx={{
                color: '#12B76A',
              }}
            />
          </>
        )}
      </Typography>
    </Stack>
  )
}

const ArrearsTable = ({
  amountDue,
  arrearsCount,
}: {
  amountDue: string
  arrearsCount: number
}) => {
  //TODO: Remove the sample once backend provides the data
  const sampleArrears = [
    {
      refNo: '12345',
      principalAmount: '120000',
      amountOverdue: '100000',
      daysOverdue: '12',
    },
    {
      refNo: '12346',
      principalAmount: '1000000',
      amountOverdue: '100000',
      daysOverdue: '120',
    },
  ]
  return (
    <Stack
      sx={{
        borderRadius: '12px',
        my: '1vh',
        border: '1px solid #EAECF0',
        background: '#FFF',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Table>
        <TableHead>
          <TableRow>
            <TableCell colSpan={4} align={'left'}>
              <Typography
                variant="subtitle2"
                color="primary.main"
              >{`(Sample)Total Amount Overdue =${formatCurrency(amountDue)}`}</Typography>
              <Typography variant="body2">
                Showing {arrearsCount} loan arrears
              </Typography>
            </TableCell>
          </TableRow>
          <TableRow>
            {[
              'Loan Ref No',
              'Principal Amount',
              'Amount Overdue',
              'Days Overdue',
            ].map((item) => (
              <TableCell key={item}>{item}</TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {sampleArrears.map((arrear) => (
            <TableRow key={arrear.refNo}>
              <TableCell>{arrear.refNo}</TableCell>
              <TableCell>{formatCurrency(arrear.principalAmount)}</TableCell>
              <TableCell>{formatCurrency(arrear.amountOverdue)}</TableCell>
              <TableCell>{arrear.daysOverdue}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Stack>
  )
}
