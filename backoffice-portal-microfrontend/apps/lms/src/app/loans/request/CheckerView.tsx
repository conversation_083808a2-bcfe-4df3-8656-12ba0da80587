import React from 'react'
import { I<PERSON>ustomerCheck, ICustomerDocument } from '@/store/interfaces'
import { useAppSelector } from '@/store'

import { KYCValidationCard } from '../../loans/request/checks/kyc/Cards'
import {
  NationalIDCheck,
  PIPPEPStatus,
  USACitizenship,
} from '../../loans/request/KYCStages'

export const CheckerView = ({
  kycData,
  requestId,
  customerDocuments,
}: {
  kycData: ICustomerCheck[]
  requestId: string
  customerDocuments: ICustomerDocument[]
}) => {
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { selectedRequest, kycTypeValidations } = useAppSelector(
    (state) => state.loans
  )
  return (
    <>
      {kycData.filter(
        (data) =>
          data.code === 'ID_DOCUMENT_FRONT_OCR' ||
          data.code === 'ID_DOCUMENT_BACK_OCR'
      ) && (
        <NationalIDCheck
          data={kycData.filter(
            (data) =>
              data.code === 'ID_DOCUMENT_FRONT_OCR' ||
              data.code === 'ID_DOCUMENT_BACK_OCR'
          )}
          requestId={requestId}
          documents={customerDocuments}
          selectedApprovalRequest={selectedApprovalRequest}
          isChecker={true}
          request={selectedRequest}
        />
      )}
      {kycData
        .filter((check) =>
          kycTypeValidations.find(
            (type) =>
              check.code === type.code &&
              type.type === 'KYC' &&
              type.code !== 'ID_DOCUMENT_FRONT_OCR' &&
              type.code !== 'ID_DOCUMENT_BACK_OCR' &&
              type.code !== 'US_CITIZEN_CHECK' &&
              type.code !== 'PEP_PIP_VALIDATION' &&
              check.status !== 'NotApplicable'
          )
        )
        .map((check) => (
          <KYCValidationCard
            key={check.code}
            check={check}
            isChecker={true}
            request={selectedRequest}
            selectedApprovalRequest={selectedApprovalRequest}
          />
        ))}

      {kycData.find(
        (data) =>
          data.code === 'US_CITIZEN_CHECK' && data.status !== 'NotApplicable'
      ) && (
        <USACitizenship
          data={kycData.find((data) => data.code === 'US_CITIZEN_CHECK')}
          requestId={requestId}
          request={selectedRequest}
          isChecker={true}
        />
      )}
      {kycData.find(
        (data) =>
          data.code === 'US_CITIZEN_CHECK' && data.status !== 'NotApplicable'
      ) && (
        <PIPPEPStatus
          data={kycData.find((data) => data.code === 'PEP_PIP_VALIDATION')}
          requestId={requestId}
          request={selectedRequest}
          isChecker={true}
        />
      )}
    </>
  )
}
