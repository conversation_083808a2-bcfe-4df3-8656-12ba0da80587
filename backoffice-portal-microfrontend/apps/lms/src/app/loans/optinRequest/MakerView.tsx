'use client'
import React from 'react'
import {
  ICustomerCheck,
  ICustomerDocument,
  ILoanOptinRequest,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'

import {
  NationalIDCheck,
  PIPPEPStatus,
  USACitizenship,
} from '@/app/loans/optinRequest/KYCStages'

import { OptinKYCValidationCard } from '@/app/loans/optinRequest/KYCCards'
import { OptinCreditValidationCard } from '@/app/loans/optinRequest/CreditCards'

export const OptinMakerView = ({
  kycData,
  requestId,
  customerDocuments,
  request,
}: {
  kycData: ICustomerCheck[]
  requestId: string
  customerDocuments: ICustomerDocument[]
  request: ILoanOptinRequest
}) => {
  const { kycTypeValidations, creditTypeValidations } = useAppSelector(
    (state) => state.loans
  )
  return (
    <>
      {kycData.find(
        (data) =>
          data.code === 'ID_DOCUMENT_FRONT_OCR' ||
          data.code === 'ID_DOCUMENT_BACK_OCR'
      ) && (
        <NationalIDCheck
          data={kycData.filter(
            (data) =>
              data.code === 'ID_DOCUMENT_FRONT_OCR' ||
              data.code === 'ID_DOCUMENT_BACK_OCR'
          )}
          requestId={requestId}
          documents={customerDocuments}
          request={request}
        />
      )}
      {kycData
        .filter((check) =>
          kycTypeValidations.find(
            (type) =>
              check.code === type.code &&
              type.type === 'KYC' &&
              type.code !== 'ID_DOCUMENT_FRONT_OCR' &&
              type.code !== 'ID_DOCUMENT_BACK_OCR' &&
              type.code !== 'US_CITIZEN_CHECK' &&
              type.code !== 'PEP_PIP_VALIDATION' &&
              check.status !== 'NotApplicable'
          )
        )
        .map((check) => (
          <>
            {console.log('check >', check)}
            <OptinKYCValidationCard
              key={check.code}
              check={check}
              isChecker={false}
              request={request}
            />
          </>
        ))}
      {kycData.find(
        (data) =>
          data.code === 'US_CITIZEN_CHECK' && data.status !== 'NotApplicable'
      ) && (
        <USACitizenship
          data={kycData.find((data) => data.code === 'US_CITIZEN_CHECK')}
          requestId={requestId}
          request={request}
          isChecker={false}
        />
      )}
      {kycData.find(
        (data) =>
          data.code === 'PEP_PIP_VALIDATION' && data.status !== 'NotApplicable'
      ) && (
        <PIPPEPStatus
          data={kycData.find((data) => data.code === 'PEP_PIP_VALIDATION')}
          requestId={requestId}
          request={request}
          isChecker={false}
        />
      )}
      {kycData
        .filter((check) =>
          creditTypeValidations.find(
            (type) =>
              check.code === type.code &&
              type.type === 'CREDIT' &&
              check.status !== 'NotApplicable'
          )
        )
        .map((check) => (
          <OptinCreditValidationCard
            key={check.code}
            check={check}
            isChecker={false}
            request={request}
          />
        ))}
    </>
  )
}
