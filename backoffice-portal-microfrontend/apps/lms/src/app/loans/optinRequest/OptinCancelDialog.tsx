import { Dialog } from '@dtbx/ui/components/Overlay'
import React, { useState } from 'react'
import { Button } from '@mui/material'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { initiateCancelOptinRequest } from '@/store/actions'
import { ILoanOptinRequest } from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'

export const CancelOptinRequestDialog = ({
  request,
}: {
  request: ILoanOptinRequest
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState<boolean>(false)
  const router = useCustomRouter()
  const handleChangeReason = async (reasons: string[]) => {
    const reason = reasons.join(', ')
    await handleCancelRequest(reason)
  }

  const handleCancelRequest = async (reason: string) => {
    const data = {
      comments: reason,
    }
    if (HasAccessToRights(['SUPER_UPDATE_LOAN_REQUEST'])) {
      await initiateCancelOptinRequest(dispatch, request.id, 'super', data)
    } else if (HasAccessToRights(['MAKE_UPDATE_LOAN_REQUEST'])) {
      await initiateCancelOptinRequest(dispatch, request.id, 'maker', data)
      router.push('/requests')
    }
  }
  return (
    <>
      <AccessControlWrapper
        rights={['MAKE_UPDATE_LOAN_REQUEST', 'SUPER_UPDATE_LOAN_REQUEST']}
      >
        <Button
          variant="contained"
          sx={{
            height: '34px',
          }}
          onClick={() => setOpen(!open)}
          disabled={request.cancelled}
        >
          Cancel Request
        </Button>
      </AccessControlWrapper>
      <Dialog
        open={open}
        setOpen={setOpen}
        concatReason
        title={'Cancel Request'}
        buttonText={'Cancel'}
        descriptionText={`Please let us know why you are cancelling this optin request`}
        onClick={async (reason: string[]) => {
          await handleChangeReason(reason)
        }}
        buttonProps={{ color: '#027A48' }}
        reasons={[
          'Insufficient income',
          'Adverse credit history',
          'Inadequate collateral',
          'Unstable employment',
          'Non-compliance with loan policy',
        ]}
      />
    </>
  )
}
