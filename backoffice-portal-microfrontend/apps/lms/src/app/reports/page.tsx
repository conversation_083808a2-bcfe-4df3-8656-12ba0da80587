'use client'
import React, { useRef, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>row,
  InputBase,
  Paper,
  Popper,
  Stack,
  Typography,
} from '@mui/material'
import { SearchRounded } from '@mui/icons-material'

import Loan from './Tables/Loan'
import OrganizationLimit from './Tables/OrganizationLimit'
import RequestReports from './Tables/RequestsReports'
import Tabs from './RequestTabs/index'
import RequestChecks from './Tables/RequestChecks'
import { CustomerValidationReport } from '@/app/reports/Tables/CustomerValidationReport'

type SearchPlaceholderType = {
  placeholder: string
  searchBy: Record<string, string>
}[]

const searchPlaceholder: SearchPlaceholderType = [
  {
    placeholder: 'Organization name',
    searchBy: {
      organizationName: 'Organization Name',
    },
  },
  {
    placeholder: 'Customer ID Number or Product ID',
    searchBy: {
      customerID: 'Customer ID number',
      productName: 'Product Name',
      organizationName: 'Organization Name',
    },
  },
  {
    placeholder: 'Customer ID, CIF or Reference number ',
    searchBy: {
      organizationName: 'Organization Name',
      customerName: 'Customer Name',
      CIF: 'CIF',
      flexLoanNumber: 'Ref No ',
      customerIdNumber: 'Customer ID Number',
      productName: 'Product Name',
    },
  },
  {
    placeholder: 'Customer ID Number or Product ID',
    searchBy: {
      customerID: 'Customer ID number',
      productName: 'Product Name',
      organizationName: 'Organization Name',
    },
  },
  {
    placeholder: 'Customer ID Number or Check ID',
    searchBy: {
      customerID: 'Customer ID number',
      productName: 'Product Name',
      organizationName: 'Organization Name',
    },
  },
]

const HomePage = () => {
  const [activeTab, setActiveTab] = useState<number>(0)
  const [open, setOpen] = useState<boolean>(false)
  const [searchBy, setSearchBy] = useState<string>('organizationName')
  const [searchValue, setSearchValue] = useState<string>('')
  const anchorRef = useRef<HTMLButtonElement | null>(null)

  return (
    <Stack>
      <Stack
        sx={{
          py: '0.5%',
          px: '1%',
        }}
      >
        <Typography
          variant="h5"
          sx={{
            marginLeft: '2%',
            marginTop: '0.2%',
            marginBottom: '0.5%',
          }}
        >
          Reports
        </Typography>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '5%',
            justifyContent: 'space-between',
          }}
        >
          <Tabs
            onTabChange={(tab: number) => {
              setActiveTab(tab)
            }}
          />
        </Stack>
      </Stack>

      <Divider />
      <Stack
        sx={{
          gap: '24px',
          py: '0.5%',
          px: '1%',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            display: activeTab === 4 ? 'none' : 'flex',
          }}
        >
          <>
            <Button
              sx={{
                height: '40px',
                border: '1px solid  #AAADB0',
                borderRadius: '6px 0px 0px 6px',
              }}
              ref={anchorRef}
              onClick={() => setOpen((prev) => !prev)}
            >
              <Typography>
                Search by {searchPlaceholder[activeTab].searchBy[searchBy]}
              </Typography>
            </Button>

            <Popper
              open={open}
              anchorEl={anchorRef.current}
              role={undefined}
              placement="bottom-start"
              transition
              disablePortal
              sx={{
                zIndex: '10',
              }}
            >
              {({ TransitionProps, placement }) => (
                <Grow
                  {...TransitionProps}
                  style={{
                    transformOrigin:
                      placement === 'bottom-start' ? 'left top' : 'left bottom',
                  }}
                >
                  <Paper
                    sx={{
                      padding: '5px',
                      maxHeight: '40vh',
                      display: 'flex',
                      flexDirection: 'column',
                      width: '100%',
                    }}
                  >
                    <Stack
                      sx={{
                        background: '#FFF',
                        borderRadius: '8px',
                        marginTop: '5px',
                      }}
                    >
                      {Object.keys(searchPlaceholder[activeTab].searchBy).map(
                        (key, index) => (
                          <Button
                            key={index}
                            sx={{
                              padding: '10px',
                              width: '100%',
                              justifyContent: 'flex-start',
                            }}
                            onClick={() => {
                              setOpen(false)
                              setSearchBy(key)
                            }}
                          >
                            <Typography>
                              {searchPlaceholder[activeTab].searchBy[key]}
                            </Typography>
                          </Button>
                        )
                      )}
                    </Stack>
                  </Paper>
                </Grow>
              )}
            </Popper>
          </>
          <InputBase
            endAdornment={<SearchRounded />}
            sx={{
              width: '35vw',
              height: '40px',
              borderRight: '1px solid  #AAADB0',
              borderTop: '1px solid  #AAADB0',
              borderBottom: '1px solid  #AAADB0',
              borderRadius: '0px 6px 6px 0px',
              padding: '0px 16px',
            }}
            placeholder={searchPlaceholder[activeTab].placeholder}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
        </Stack>
        {activeTab === 0 ? (
          <OrganizationLimit />
        ) : activeTab === 1 ? (
          <RequestReports />
        ) : activeTab === 2 ? (
          <Loan />
        ) : activeTab === 3 ? (
          <RequestChecks />
        ) : activeTab === 4 ? (
          <CustomerValidationReport activeTab={activeTab} />
        ) : null}
      </Stack>
    </Stack>
  )
}

export default HomePage
