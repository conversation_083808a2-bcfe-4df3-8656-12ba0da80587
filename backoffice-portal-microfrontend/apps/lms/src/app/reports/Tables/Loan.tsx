import React, { useEffect, useState } from 'react'
import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { IHeadCell } from '@dtbx/store/interfaces'
import { ILoanReport } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { generateLoanReports, getLoanReports } from '@/store/actions'
import { DownloadCloudIcon } from '@dtbx/ui/icons'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

import EmptyPage from './EmptyPage'
import LoanReportFilters from '../Filters/LoanReportsFilters'
import { ReportStatusChip } from '../StatusChips'

const tableHeader: IHeadCell[] = [
  {
    label: 'Organization',
    id: 'organizationName',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Product',
    id: 'productName',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Customer',
    id: 'customerID',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Date requested',
    id: 'requestedDate',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Amount',
    id: 'amount',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Status',
    id: 'status',
    alignCenter: false,
    alignRight: false,
  },
]

const Loan = () => {
  const dispatch = useAppDispatch()
  const {
    isLoadingLoanReport,
    loanReportResponse,
    isSuccessfulLoanReport,
    isLoadingGenerateLoanReport: loading,
  } = useAppSelector((state) => state.loans)
  const [page, setPage] = React.useState<number>(1)
  const [filters, setFilters] = React.useState<{
    orgName: string
    pageSize: number
    creationDate?: {
      startDate: Dayjs
      endDate: Dayjs
    }
    maturityDate?: {
      startDate: Dayjs
      endDate: Dayjs
    }
    status?: string
  }>({
    orgName: '',
    pageSize: 10,
  })

  const [paginationOptions, setPaginationOptions] = useState({
    page: page,
    size: 10,
    totalPages: loanReportResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    await getLoanReports({
      dispatch,
      params: {
        ...newOptions,
      },
    })
  }
  /*************************end pagination handlers**************************/
  useEffect(() => {
    getLoanReports({
      dispatch,
      params: {
        size: filters.pageSize,
        page,
        organizationName: filters.orgName,
        creationStartDate: filters.creationDate?.startDate.format('YYYY-MM-DD'),
        creationEndDate: filters.creationDate?.endDate.format('YYYY-MM-DD'),
        maturityStartDate: filters.maturityDate?.startDate.format('YYYY-MM-DD'),
        maturityEndDate: filters.maturityDate?.endDate.format('YYYY-MM-DD'),
        status: filters.status,
      },
    })
  }, [
    page,
    filters.orgName,
    filters.pageSize,
    filters.creationDate,
    filters.maturityDate,
    filters.status,
  ])

  return (
    <>
      {loanReportResponse.totalElements === 0 ? (
        <EmptyPage />
      ) : (
        <Paper
          sx={{
            height: 'auto',
            overflow: 'auto',
            borderRadius: '12px',
          }}
        >
          <Stack
            sx={{
              padding: '20px 24px 0px 24px',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            {/* header */}
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '10px',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                }}
              >
                <Typography
                  sx={{
                    fontSize: '18px',
                    lineHeight: '28px',
                    fontWeight: '700',
                  }}
                >
                  Loan Reports
                </Typography>
                <Chip
                  sx={{
                    maxHeight: '18px',
                    backgroundColor: '#F9DBAF',
                    color: '#B42318',
                    border: '1px solid #B93815',
                  }}
                  label={`${isSuccessfulLoanReport ? loanReportResponse.totalElements : 0} loan${isSuccessfulLoanReport && loanReportResponse.totalElements > 0 ? 's' : ''}`}
                />
              </Box>
              <Typography variant="subtitle3">
                Showing{' '}
                {isLoadingLoanReport ? (
                  <></>
                ) : isSuccessfulLoanReport ? (
                  loanReportResponse.totalElements
                ) : (
                  0
                )}{' '}
                loan
                {isSuccessfulLoanReport && loanReportResponse.totalElements > 1
                  ? 's'
                  : ''}
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                alignItems: 'center',
              }}
            >
              <LoanReportFilters
                onFilter={(value: {
                  orgName: string
                  pageSize: number
                  creationDate?: {
                    startDate: Dayjs
                    endDate: Dayjs
                  }
                  maturityDate?: {
                    startDate: Dayjs
                    endDate: Dayjs
                  }
                }) => {
                  setFilters(value)
                }}
              />
              <Box
                sx={{
                  m: 1,
                  position: 'relative',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Button
                  disabled={loading}
                  variant="contained"
                  startIcon={<DownloadCloudIcon />}
                  onClick={() => {
                    generateLoanReports({
                      dispatch,
                      params: {
                        size: filters.pageSize,
                        page,
                        organizationName: filters.orgName,
                        creationStartDate:
                          filters.creationDate?.startDate.format('YYYY-MM-DD'),
                        creationEndDate:
                          filters.creationDate?.endDate.format('YYYY-MM-DD'),
                        maturityStartDate:
                          filters.maturityDate?.startDate.format('YYYY-MM-DD'),
                        maturityEndDate:
                          filters.maturityDate?.endDate.format('YYYY-MM-DD'),
                        status: filters.status,
                      },
                    })
                  }}
                >
                  Export report
                </Button>
                {loading && (
                  <CircularProgress
                    size={24}
                    sx={{
                      color: 'white',
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      marginTop: '-12px',
                      marginLeft: '-12px',
                    }}
                  />
                )}
              </Box>
            </Box>
          </Stack>
          {isLoadingLoanReport ? (
            <CustomSkeleton
              variant="rectangular"
              sx={{
                width: '100%',
                height: '45vh',
                margin: '10px 2px',
              }}
            />
          ) : (
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                width: '100%',
                overflowX: 'auto',
              }}
            >
              <Table stickyHeader>
                <CustomTableHeader
                  order={'desc'}
                  orderBy={''}
                  rowCount={0}
                  headLabel={tableHeader}
                  numSelected={0}
                />
                <TableBody>
                  {isSuccessfulLoanReport &&
                    loanReportResponse?.data.map(
                      (row: ILoanReport, index: number) => {
                        const {
                          organizationName,
                          productName,
                          productID,
                          loanCreationDate,
                          loanStatus,
                          loanID,
                          customerID,
                          loanPrincipalAmount,
                        } = row
                        return (
                          <TableRow key={loanID || index}>
                            <TableCell
                              sx={{
                                padding: '12px 24px',
                              }}
                            >
                              {organizationName}
                            </TableCell>
                            <TableCell
                              sx={{
                                padding: '0px',
                              }}
                            >
                              <Box
                                sx={{
                                  width: '100%',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  gap: '1px',
                                }}
                              >
                                <Typography>{productName}</Typography>
                                <Typography variant="caption">
                                  ID: {productID}
                                </Typography>
                              </Box>
                            </TableCell>
                            <TableCell
                              sx={{
                                padding: '12px 24px',
                              }}
                            >
                              {customerID}
                            </TableCell>
                            <TableCell
                              sx={{
                                padding: '12px 24px',
                              }}
                            >
                              {dayjs(loanCreationDate).format('MMM D, YYYY')}
                            </TableCell>
                            <TableCell
                              sx={{
                                padding: '12px 24px',
                              }}
                            >
                              KES{' '}
                              {new Intl.NumberFormat().format(
                                loanPrincipalAmount
                              )}
                            </TableCell>
                            <TableCell
                              sx={{
                                padding: '12px 24px',
                              }}
                            >
                              {<ReportStatusChip label={loanStatus} />}
                            </TableCell>
                          </TableRow>
                        )
                      }
                    )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          {loanReportResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: loanReportResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </>
  )
}

export default Loan
