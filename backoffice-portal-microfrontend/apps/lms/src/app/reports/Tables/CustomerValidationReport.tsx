import React, { useEffect } from 'react'
import {
  Button,
  Chip,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import { Dayjs } from 'dayjs'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { IHeadCell } from '@dtbx/store/interfaces'
import { ICustomerRequestCheckReport } from '@/store/interfaces'
import {
  exportCustomerRequestChecksReport,
  getCustomerRequestChecksReport,
  getValidationsByProductId,
} from '@/store/actions'
import { setCustomerRequestChecksFilters } from '@/store/reducers'
import { DownloadCloudIcon } from '@dtbx/ui/icons'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import EmptyPage from './EmptyPage'
import { ReportStatusChip } from '../StatusChips'
import { formatTimestamp } from '@dtbx/store/utils'
import { CustomerRequestChecksFilter } from '@/app/reports/Filters/CustomerRequestChecks'
import { Clear } from '@mui/icons-material'

const tableHeader: IHeadCell[] = [
  {
    label: 'Check Type',
    id: 'checkType',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Customer',
    id: 'customerName',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Status',
    id: 'status',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Message',
    id: 'message',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Date Checked',
    id: 'dateCreated',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Date Modified',
    id: 'dateModified',
    alignCenter: false,
    alignRight: false,
  },

  {
    label: 'Reference',
    id: 'reference',
    alignCenter: false,
    alignRight: false,
  },
]

export const CustomerValidationReport = ({
  activeTab,
}: {
  activeTab: number
}) => {
  const dispatch = useAppDispatch()
  const {
    isLoadingCustomerRequestChecksReport,
    isLoadingCustomerReportCheckReportExport,
    userProducts,
    customerRequestChecksReport,
    customerRequestChecksFilters,
  } = useAppSelector((state) => state.loans)

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    const params = new URLSearchParams()
    Object.keys(customerRequestChecksFilters).map((key) => {
      if (customerRequestChecksFilters[key]) {
        params.append(`${key}`, customerRequestChecksFilters[key].toString())
      }
    })
    params.append('productId', userProducts[0].id)
    params.append('page', newOptions.page.toString())
    await getCustomerRequestChecksReport(dispatch, params.toString())
  }
  /*************************end pagination handlers**************************/
  const handleFilterChange = async (filters: {
    [key: string]: string | number | Dayjs
  }) => {
    dispatch(
      setCustomerRequestChecksFilters({
        ...filters,
        productId: userProducts[0].id,
      })
    )
    const params = new URLSearchParams()
    Object.keys(filters).map((key) => {
      if (filters[key]) {
        params.append(`${key}`, filters[key].toString())
      }
    })
    params.append('productId', userProducts[0].id)
    await getCustomerRequestChecksReport(dispatch, params.toString())
  }

  const handleClearFilters = () => {
    const params = new URLSearchParams()
    dispatch(setCustomerRequestChecksFilters({}))
    params.append('productId', userProducts[0].id)
    dispatch(setCustomerRequestChecksFilters({ productId: userProducts[0].id }))
    getCustomerRequestChecksReport(dispatch, params.toString())
    getValidationsByProductId(userProducts[0].id, dispatch)
  }
  const handleExportReport = async () => {
    const params = new URLSearchParams()
    Object.keys(customerRequestChecksFilters).map((key) => {
      if (
        customerRequestChecksFilters[key] &&
        key !== 'size' &&
        key !== 'page'
      ) {
        params.append(`${key}`, customerRequestChecksFilters[key].toString())
      }
    })
    params.append('productId', userProducts[0].id)

    await exportCustomerRequestChecksReport(dispatch, params.toString())
  }
  useEffect(() => {
    const params = new URLSearchParams()
    if (activeTab === 4 && userProducts && userProducts.length > 0) {
      params.append('productId', userProducts[0].id)
      dispatch(
        setCustomerRequestChecksFilters({ productId: userProducts[0].id })
      )
      getCustomerRequestChecksReport(dispatch, params.toString())
      getValidationsByProductId(userProducts[0].id, dispatch)
    }
  }, [activeTab])
  return (
    <>
      <Paper
        sx={{
          borderRadius: '12px',
          border: '1px solid #EAECF0',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          mt: '3vh',
        }}
      >
        <Stack
          sx={{
            padding: '10px 10px 0px 14px',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          {/* header */}
          <Stack>
            <Stack
              sx={{
                flexDirection: 'row',
                gap: '10px',
                alignItems: 'center',
                px: 0,
              }}
            >
              <Typography
                sx={{
                  fontSize: '18px',
                  lineHeight: '28px',
                  fontWeight: '700',
                }}
              >
                Customer Requests Check Status Reports
              </Typography>
              <Chip
                sx={{
                  maxHeight: '18px',
                  backgroundColor: '#F9DBAF',
                  color: '#B42318',
                  border: '1px solid #B93815',
                }}
                label={`${customerRequestChecksReport.totalElements} Request check${customerRequestChecksReport.totalElements > 0 ? 's' : ''}`}
              />
            </Stack>
            <Typography variant="subtitle3">
              Showing {customerRequestChecksReport.totalElements} records
            </Typography>
          </Stack>
          <Stack
            sx={{
              gap: '10px',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            <CustomerRequestChecksFilter
              setFilters={handleFilterChange}
              key={`${Object.values(customerRequestChecksFilters).length}`}
            />
            <Button
              variant={'outlined'}
              startIcon={<Clear />}
              onClick={handleClearFilters}
              sx={{
                display:
                  Object.values(customerRequestChecksFilters).length > 1
                    ? 'flex'
                    : 'none',
              }}
            >
              Clear Filters
            </Button>
            <Button
              disabled={isLoadingCustomerRequestChecksReport}
              variant="contained"
              loading={isLoadingCustomerReportCheckReportExport}
              startIcon={<DownloadCloudIcon />}
              onClick={handleExportReport}
            >
              Export report
            </Button>
          </Stack>
        </Stack>
        {isLoadingCustomerRequestChecksReport ? (
          <CustomSkeleton
            variant="rectangular"
            sx={{
              width: '100%',
              height: '45vh',
              margin: '10px 2px',
            }}
          />
        ) : customerRequestChecksReport.totalElements === 0 ? (
          <EmptyPage />
        ) : (
          <TableContainer component={Paper} elevation={0}>
            <Table size="small">
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={tableHeader}
                numSelected={0}
              />
              <TableBody>
                {customerRequestChecksReport &&
                  customerRequestChecksReport?.data?.map(
                    (row: ICustomerRequestCheckReport, index: number) => {
                      const {
                        customerName,
                        customerId,
                        checkType,
                        status,
                        message,
                        dateCreated,
                        dateModified,
                        loanRequestId,
                        reference,
                      } = row
                      return (
                        <TableRow key={index}>
                          <TableCell>{checkType}</TableCell>
                          <TableCell>
                            <Stack
                              sx={{
                                flexDirection: 'column',
                                gap: '1px',
                              }}
                            >
                              <Typography variant="body2">
                                {sentenceCase(customerName)}
                              </Typography>
                              <Typography variant="caption">
                                ID: {customerId}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            {<ReportStatusChip label={status} />}
                          </TableCell>
                          <TableCell>{message}</TableCell>
                          <TableCell>{formatTimestamp(dateCreated)}</TableCell>
                          <TableCell>{formatTimestamp(dateModified)}</TableCell>
                          <TableCell>
                            <Typography>{reference}</Typography>
                          </TableCell>
                        </TableRow>
                      )
                    }
                  )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
        {customerRequestChecksReport.totalNumberOfPages > 0 && (
          <CustomPagination
            key={`${customerRequestChecksReport.pageNumber}-${customerRequestChecksReport.pageSize}-${customerRequestChecksReport.totalElements}-${customerRequestChecksReport.totalNumberOfPages}`}
            options={{
              page: customerRequestChecksReport.pageNumber,
              size: customerRequestChecksReport.pageSize,
              totalPages: customerRequestChecksReport.totalNumberOfPages,
            }}
            handlePagination={handlePagination}
          />
        )}
      </Paper>
    </>
  )
}
