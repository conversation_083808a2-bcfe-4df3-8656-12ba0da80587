import React, { useEffect, useState } from 'react'
import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { IHeadCell } from '@dtbx/store/interfaces'
import { IRequestCheck } from '@/store/interfaces'
import {
  generateRequestChecksReport,
  getRequestCheckReport,
} from '@/store/actions'
import { DownloadCloudIcon } from '@dtbx/ui/icons'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

import ValidationReportsFilter from '../Filters/ValidationReportsFilter'
import EmptyPage from './EmptyPage'
import { ReportStatusChip } from '../StatusChips'
import { formatTimestamp } from '@dtbx/store/utils'

const tableHeader: IHeadCell[] = [
  {
    label: 'Organization',
    id: 'organizationName',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Product',
    id: 'productName',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Channel Reference',
    id: 'channelReference',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Check',
    id: 'checkDate',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Message',
    id: 'message',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Status',
    id: 'status',
    alignCenter: false,
    alignRight: false,
  },
]

const RequestChecks = () => {
  const dispatch = useAppDispatch()
  const {
    isLoadingRequestReportsCheck: loading,
    isSuccessRequestReportsCheck: isSuccess,
    isLoadingRequestReportsCheckExport: isLoadingExport,
    requestReports: data,
  } = useAppSelector((state) => state.loans)
  const [page, setPage] = React.useState<number>(1)
  const [filters, setFilters] = React.useState<{
    orgName: string
    pageSize: number
    creationDate?: {
      startDate: Dayjs
      endDate: Dayjs
    }
    status?: string
    module?: string
  }>({
    orgName: '',
    pageSize: 10,
  })
  const [paginationOptions, setPaginationOptions] = useState({
    page: page,
    size: 10,
    totalPages: 0,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    const startDate = `${filters.creationDate?.startDate ? filters.creationDate?.startDate.format('YYYY-MM-DD') : ''}`
    const endDate = `${filters.creationDate?.endDate ? filters.creationDate?.endDate.format('YYYY-MM-DD') : ''}`
    const params = `?size=${filters.pageSize}&page=${page}&creationStartDate=${startDate}&creationEndDate=${endDate}&organizationName=${filters.orgName ? filters.orgName : ''}&status=${filters.status ? filters.status : ''}`

    getRequestCheckReport(dispatch, params)
  }
  /*************************end pagination handlers**************************/
  useEffect(() => {
    const startDate = `${filters.creationDate?.startDate ? filters.creationDate?.startDate.format('YYYY-MM-DD') : ''}`
    const endDate = `${filters.creationDate?.endDate ? filters.creationDate?.endDate.format('YYYY-MM-DD') : ''}`

    const params = `?size=${filters.pageSize}&page=${page}&creationStartDate=${startDate}&creationEndDate=${endDate}&organizationName=${filters.orgName ? filters.orgName : ''}&status=${filters.status ? filters.status : ''}`

    getRequestCheckReport(dispatch, params)
  }, [
    page,
    filters.orgName,
    filters.pageSize,
    filters.creationDate,
    filters.status,
  ])

  return (
    <>
      {data.totalElements === 0 ? (
        <EmptyPage />
      ) : (
        <Paper
          sx={{
            height: 'auto',
            overflow: 'auto',
            borderRadius: '12px',
          }}
        >
          <Stack
            sx={{
              padding: '20px 24px 0px 24px',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            {/* header */}
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '10px',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                }}
              >
                <Typography
                  sx={{
                    fontSize: '18px',
                    lineHeight: '28px',
                    fontWeight: '700',
                  }}
                >
                  Validation Consumption Reports
                </Typography>
                <Chip
                  sx={{
                    maxHeight: '18px',
                    backgroundColor: '#F9DBAF',
                    color: '#B42318',
                    border: '1px solid #B93815',
                  }}
                  label={`${isSuccess ? data.totalElements : 0} Request check${isSuccess && data.totalElements > 0 ? 's' : ''}`}
                />
              </Box>
              <Typography variant="subtitle3">
                Showing {loading ? <></> : isSuccess ? data.totalElements : 0}{' '}
                record
                {isSuccess && data.totalElements > 1 ? 's' : ''}
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                alignItems: 'center',
              }}
            >
              <ValidationReportsFilter
                onFilter={(value: {
                  orgName: string
                  pageSize: number
                  creationDate?: {
                    startDate: Dayjs
                    endDate: Dayjs
                  }
                }) => {
                  setFilters(value)
                }}
              />
              <Box
                sx={{
                  m: 1,
                  position: 'relative',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Button
                  disabled={loading}
                  variant="contained"
                  startIcon={<DownloadCloudIcon />}
                  onClick={() => {
                    const startDate = `${filters.creationDate?.startDate ? filters.creationDate?.startDate.format('YYYY-MM-DD') : ''}`
                    const endDate = `${filters.creationDate?.endDate ? filters.creationDate?.endDate.format('YYYY-MM-DD') : ''}`

                    const params = `?size=${filters.pageSize}&page=${page}&creationStartDate=${startDate}&creationEndDate=${endDate}&organizationName=${filters.orgName ? filters.orgName : ''}&status=${filters.status ? filters.status : ''}`

                    generateRequestChecksReport(dispatch, params)
                  }}
                >
                  Export report
                </Button>
                {isLoadingExport && (
                  <CircularProgress
                    size={24}
                    sx={{
                      color: 'white',
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      marginTop: '-12px',
                      marginLeft: '-12px',
                    }}
                  />
                )}
              </Box>
            </Box>
          </Stack>
          {loading ? (
            <CustomSkeleton
              variant="rectangular"
              sx={{
                width: '100%',
                height: '45vh',
                margin: '10px 2px ',
              }}
            />
          ) : (
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                width: '100%',
                overflowX: 'auto',
              }}
            >
              <Table stickyHeader>
                <CustomTableHeader
                  order={'desc'}
                  orderBy={''}
                  rowCount={0}
                  headLabel={tableHeader}
                  numSelected={0}
                />
                <TableBody>
                  {isSuccess &&
                    data?.data.map((row: IRequestCheck, index: number) => {
                      const {
                        organizationName,
                        productName,
                        productId,
                        status,
                        message,
                        checkDate,
                        channelReference,
                        checkName,
                      } = row
                      return (
                        <TableRow key={index}>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            {organizationName}
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '0px',
                            }}
                          >
                            <Box
                              sx={{
                                width: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '1px',
                              }}
                            >
                              <Typography>{productName}</Typography>
                              <Typography variant="caption">
                                ID: {productId}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            <Box
                              sx={{
                                width: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '1px',
                              }}
                            >
                              <Typography>{channelReference}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            <Box
                              sx={{
                                width: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '1px',
                              }}
                            >
                              <Typography>
                                {checkName ? sentenceCase(checkName) : ''}
                              </Typography>
                              <Typography variant="caption">
                                Check Date: {formatTimestamp(checkDate)}
                              </Typography>
                            </Box>
                          </TableCell>

                          <TableCell
                            sx={{
                              padding: '12px 24px',
                              maxWidth: '20vw',
                            }}
                          >
                            {' '}
                            <Typography
                              sx={{
                                textWrap: 'wrap',
                              }}
                            >
                              {message}
                            </Typography>
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            {<ReportStatusChip label={status} />}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          {data.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: data.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </>
  )
}

export default RequestChecks
