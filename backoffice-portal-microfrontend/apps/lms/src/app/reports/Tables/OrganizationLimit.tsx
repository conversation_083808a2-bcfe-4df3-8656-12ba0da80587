import {
  Button,
  Chip,
  CircularProgress,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { IHeadCell } from '@dtbx/store/interfaces'
import { IOrganizationLimit } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  generateOrganizationLimitReport,
  getOrganizationLimitReports,
} from '@/store/actions'
import { DownloadCloudIcon } from '@dtbx/ui/icons'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import { formatCurrency } from '@dtbx/store/utils'

import EmptyPage from './EmptyPage'
import ReportsDropDownFilter from '../Filters/OrganizationLimitFilter'

const tableHeader: IHeadCell[] = [
  {
    id: 'organization',
    label: 'Organization',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'insurer mis code',
    label: 'Organization MIS code',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'insurerLimit',
    label: 'Organization Exposure Limit',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'currentExposure',
    label: 'Current Exposure',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'availableLimit',
    label: 'Available Limit',
    alignCenter: false,
    alignRight: false,
  },
]
const OrganizationLimit = () => {
  const dispatch = useAppDispatch()
  const {
    organizationLimitReportResponse: data,
    isLoadingOrgLimitReports: isLoading,
    isSuccessOrgLimitReports: isSuccess,
    isLoadingOrgLimitReportsExport: loading,
  } = useAppSelector((state) => state.loans)

  const [filters, setFilters] = useState<{
    orgName: string
    pageSize: number
  }>({ orgName: '', pageSize: 10 })

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
    totalPages: 0,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    await getOrganizationLimitReports({
      dispatch,
      params: `page=${newOptions.page}&size=${filters.pageSize}`,
    })
  }
  /*************************end pagination handlers**************************/
  useEffect(() => {
    getOrganizationLimitReports({
      dispatch,
      params: `page=1&size=${filters.pageSize}&organizationName=${filters.orgName}`,
    })
  }, [filters.orgName, filters.pageSize, loading])
  return (!isLoading && !data.totalElements) || data.totalElements === 0 ? (
    <EmptyPage />
  ) : (
    <Paper
      sx={{
        height: 'auto',
        overflow: 'auto',
        borderRadius: '12px',
      }}
    >
      <Stack
        sx={{
          padding: '20px 24px 0px 24px',
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        {/* header */}
        <Stack>
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '10px',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }}
          >
            <Typography
              sx={{
                fontSize: '18px',
                lineHeight: '28px',
                fontWeight: '700',
              }}
            >
              Organization limit report
            </Typography>
            <Chip
              sx={{
                maxHeight: '18px',
                backgroundColor: '#F9DBAF',
                color: '#B42318',
                border: '1px solid #B93815',
              }}
              label={`${isSuccess ? data.totalElements : 0} insurer${isSuccess && data.totalElements > 0 ? 's' : ''}`}
            />
          </Stack>
          <Typography variant="subtitle3">
            Showing {isLoading ? <></> : isSuccess ? data.totalElements : 0}{' '}
            insurer
            {isSuccess && data.totalElements > 1 ? 's' : ''}
          </Typography>
        </Stack>
        <Stack
          sx={{
            gap: '10px',
            alignItems: 'center',
            flexDirection: 'row',
          }}
        >
          <ReportsDropDownFilter
            onFilter={(value: { orgName: string; pageSize: number }) => {
              setFilters(value)
            }}
          />
          <Stack
            sx={{
              m: 1,
              position: 'relative',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            <Button
              disabled={loading}
              variant="contained"
              startIcon={<DownloadCloudIcon />}
              onClick={async () => {
                await generateOrganizationLimitReport({
                  dispatch,
                  params: `?page=${data.pageNumber}&size=${filters.pageSize}&organizationName=${filters.orgName}`,
                })
              }}
            >
              Export report
            </Button>
            {loading && (
              <CircularProgress
                size={24}
                sx={{
                  color: 'white',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  marginTop: '-12px',
                  marginLeft: '-12px',
                }}
              />
            )}
          </Stack>
        </Stack>
      </Stack>
      {isLoading ? (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '100%',
            height: '45vh',
            margin: '10px 2px ',
          }}
        />
      ) : (
        <TableContainer
          component={Paper}
          elevation={0}
          sx={{
            width: '100%',
            overflowX: 'auto',
          }}
        >
          <Table stickyHeader>
            <CustomTableHeader
              order={'desc'}
              orderBy={''}
              rowCount={0}
              headLabel={tableHeader}
              numSelected={0}
            />
            <TableBody>
              {isSuccess &&
                data.data.map((row: IOrganizationLimit, index: number) => {
                  const {
                    availableLimit,
                    currentExposure,
                    exposureLimit,
                    misCode,
                    organizationName,
                  } = row
                  return (
                    <TableRow key={index}>
                      <TableCell>{organizationName}</TableCell>
                      <TableCell>{misCode}</TableCell>
                      <TableCell>{formatCurrency(exposureLimit)}</TableCell>
                      <TableCell>{formatCurrency(currentExposure)}</TableCell>
                      <TableCell>{formatCurrency(availableLimit)}</TableCell>
                    </TableRow>
                  )
                })}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      {data.totalNumberOfPages > 0 && (
        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: data.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
        />
      )}
    </Paper>
  )
}

export default OrganizationLimit
