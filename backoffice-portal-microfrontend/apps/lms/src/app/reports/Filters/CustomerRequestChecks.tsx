import React, { useMemo, useRef, useState } from 'react'
import dayjs, { Dayjs } from 'dayjs'
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grow,
  InputBase,
  List,
  ListItem,
  MenuItem,
  Paper,
  Popper,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Typography,
} from '@mui/material'
import { FilterListOff, FilterListRounded, Search } from '@mui/icons-material'
import { DateRangePicker } from '@dtbx/ui/components/DropDownMenus'
import { useAppSelector } from '@/store'
import { CustomSkeleton } from '@dtbx/ui/components'

const SearchBox = ({
  onDropDownSelect,
  currentCheckType,
}: {
  onDropDownSelect?: (value: string) => void
  currentCheckType: string
}) => {
  const { singleProductValidations, isLoadingProductValidations } =
    useAppSelector((state) => state.loans)
  const anchorRef = useRef<HTMLInputElement>(null)
  const [openInputDropdown, setOpenInputDropdown] = useState<boolean>(false)
  const [search, setSearch] = useState<string>(currentCheckType || '')

  const filteredValidation = useMemo(() => {
    return (
      !isLoadingProductValidations &&
      singleProductValidations.filter((validation) =>
        validation.validation.code.toLowerCase().includes(search.toLowerCase())
      )
    )
  }, [singleProductValidations, search])

  return (
    <>
      <Stack
        sx={{
          border: '1px solid #D0D5DD',
          borderRadius: '8px',
          padding: '14px 10px',
          height: '40px',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <InputBase
          placeholder={'Check Type'}
          ref={anchorRef}
          id="composition-button"
          aria-controls={openInputDropdown ? 'composition-menu' : undefined}
          aria-expanded={openInputDropdown ? 'true' : undefined}
          aria-haspopup="true"
          value={search}
          onChange={(e) => {
            setOpenInputDropdown(true)
            setSearch(e.target.value)
          }}
          sx={{
            width: '100%',
          }}
          startAdornment={
            <Search
              sx={{
                color: '#667085',
              }}
            />
          }
        />
      </Stack>

      <Popper
        open={openInputDropdown}
        anchorEl={anchorRef.current}
        placement="top-start"
        role={undefined}
        transition
        disablePortal
        sx={{
          zIndex: '10',
        }}
      >
        {({ TransitionProps, placement }) => {
          return (
            <Grow
              {...TransitionProps}
              style={{
                transformOrigin:
                  placement === 'bottom-start' ? 'left top' : 'left bottom',
              }}
            >
              <Paper
                sx={{
                  maxHeight: '30vh',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                {' '}
                <Stack>
                  <List
                    sx={{
                      overflow: 'auto',
                      padding: '0px',
                    }}
                  >
                    {!isLoadingProductValidations &&
                      filteredValidation &&
                      filteredValidation.map((validation, index) => {
                        return (
                          <ListItem
                            key={index || validation.id}
                            sx={{
                              padding: '4px',
                            }}
                            onClick={() => {
                              setOpenInputDropdown(false)
                              setSearch(validation.validation.code)
                              onDropDownSelect &&
                                onDropDownSelect(validation.validation.code)
                            }}
                          >
                            {' '}
                            <Divider orientation="horizontal" />
                            <Button
                              sx={{
                                width: '100%',
                                height: '100%',
                                padding: '6px 6px 6px 15px',
                                justifyContent: 'flex-start',
                              }}
                            >
                              <Typography>
                                {validation.validation.code
                                  .split(' ')
                                  .slice(0, 2)
                                  .join(' ')}
                              </Typography>
                            </Button>
                          </ListItem>
                        )
                      })}

                    {isLoadingProductValidations &&
                      Array.from({ length: 10 }).map((_, index) => {
                        return (
                          <ListItem
                            key={index}
                            sx={{
                              padding: '4px',
                            }}
                          >
                            <CustomSkeleton
                              variant="rectangular"
                              sx={{
                                width: '100%',
                                height: '40px',
                                borderRadius: '6px',
                              }}
                            />
                          </ListItem>
                        )
                      })}
                  </List>
                </Stack>
              </Paper>
            </Grow>
          )
        }}
      </Popper>
    </>
  )
}
const RenderSizeRadio = ({
  setNewSize,
  currentSize,
}: {
  setNewSize: (newSize: number) => void
  currentSize: number
}) => {
  const [size, setSize] = useState<number>(currentSize || 10)
  const handleSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSize(Number((event.target as HTMLInputElement).value))
    setNewSize(Number(event.target.value))
  }

  return (
    <>
      {
        <FormControl>
          <FormLabel id="demo-radio-buttons-group-label">
            <Typography
              variant={'subtitle2'}
              sx={{
                fontWeight: 'bold',
              }}
            >
              Records per page
            </Typography>
          </FormLabel>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            defaultValue="10"
            row
            name="radio-buttons-group"
            onChange={handleSizeChange}
            value={size.toString()}
          >
            <FormControlLabel value="10" control={<Radio />} label="10" />
            <FormControlLabel value="15" control={<Radio />} label="15" />
            <FormControlLabel value="20" control={<Radio />} label="20" />
            <FormControlLabel value="25" control={<Radio />} label="25" />
          </RadioGroup>
        </FormControl>
      }
    </>
  )
}
const statusOptions = [
  'PENDING',
  'PASSED',
  'FAILED',
  'NOT_APPLICABLE',
  'NOT_RUN',
]
export const CustomerRequestChecksFilter = ({
  setFilters,
}: {
  setFilters: (value: { [key: string]: string | number | Dayjs }) => void
}) => {
  const { customerRequestChecksFilters } = useAppSelector(
    (state) => state.loans
  )
  const anchorRef = useRef<HTMLButtonElement>(null)
  const [open, setOpen] = useState<boolean>(false)
  const [checkType, setCheckType] = useState<string>(
    customerRequestChecksFilters.checkType as string
  )
  const [pageSize, setPageSize] = useState<number>(
    (customerRequestChecksFilters.size as number) || 10
  )
  const [date, setDate] = useState<{
    start: Dayjs | null
    end: Dayjs | null
  }>({
    start: dayjs(customerRequestChecksFilters.creationStartDate) || null,
    end: dayjs(customerRequestChecksFilters.creationEndDate) || null,
  })

  const [status, setStatus] = useState<string>(
    customerRequestChecksFilters.status as string
  )

  const handleToggle = () => {
    setOpen((prev) => !prev)
  }
  const handleApply = () => {
    setFilters({
      checkType: checkType,
      size: pageSize,
      productId: '',
      creationStartDate: date?.start?.format('YYYY-MM-DD') || '',
      creationEndDate: date?.end?.format('YYYY-MM-DD') || '',
      status: status || '',
    })
    setOpen(false)
  }
  return (
    <>
      <Button
        variant="outlined"
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        startIcon={open ? <FilterListOff /> : <FilterListRounded />}
      >
        Filters
      </Button>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        modifiers={[
          {
            name: 'preventOverflow',
            options: {
              boundary: 'viewport',
            },
          },
          {
            name: 'flip',
            options: {
              fallbackPlacements: ['top-start', 'top-end'],
            },
          },
        ]}
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                width: {
                  xs: '35vw',
                  sm: '35vw',
                  lg: '25vw',
                },
              }}
            >
              <Stack
                sx={{
                  padding: '24px 24px 34px 34px',
                  gap: '10px',
                  marginTop: '5px',
                  maxHeight: '23.5rem',
                  overflow: 'auto',
                }}
              >
                <Stack
                  sx={{
                    gap: '0px',
                  }}
                >
                  <Typography
                    variant={'subtitle2'}
                    sx={{
                      fontWeight: 'bold',
                    }}
                  >
                    Filter
                  </Typography>
                  <Typography variant={'subtitle3'}>
                    Apply filter to table data
                  </Typography>
                </Stack>
                <Stack>
                  <Typography
                    variant={'subtitle2'}
                    sx={{
                      fontWeight: 'bold',
                    }}
                  >
                    Creation Date
                  </Typography>
                  <DateRangePicker
                    onApplyDateRange={(date) => {
                      setDate(date)
                    }}
                    currentStartDate={dayjs(
                      customerRequestChecksFilters?.creationStartDate
                    )}
                    currentEndDate={dayjs(
                      customerRequestChecksFilters?.creationEndDate
                    )}
                  />
                </Stack>
                <Stack>
                  <Typography
                    variant={'subtitle2'}
                    sx={{
                      fontWeight: 'bold',
                    }}
                  >
                    Status
                  </Typography>
                  <FormControl>
                    <Select
                      value={status}
                      size="small"
                      onChange={(e) => setStatus(e.target.value)}
                    >
                      {statusOptions.map((option) => (
                        <MenuItem value={option} key={option}>
                          {option}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Stack>
                <Stack>
                  <Typography
                    variant={'subtitle2'}
                    sx={{
                      fontWeight: 'bold',
                    }}
                  >
                    Check Type
                  </Typography>
                  <SearchBox
                    onDropDownSelect={(val: string) => {
                      setCheckType(val)
                    }}
                    currentCheckType={
                      customerRequestChecksFilters?.checkType as string
                    }
                  />
                </Stack>
                <RenderSizeRadio
                  setNewSize={setPageSize}
                  currentSize={pageSize}
                />
              </Stack>
              <Divider />
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '10px',
                  padding: '10px 16px 10px 8px',
                }}
              >
                <Button
                  variant="outlined"
                  sx={{
                    padding: '8px 14px',
                  }}
                  onClick={() => {
                    setOpen(false)
                  }}
                  fullWidth
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    padding: '8px 14px',
                  }}
                  fullWidth
                >
                  <Typography
                    sx={{
                      color: 'white',
                    }}
                    onClick={handleApply}
                  >
                    Apply Filter
                  </Typography>
                </Button>
              </Box>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  )
}
