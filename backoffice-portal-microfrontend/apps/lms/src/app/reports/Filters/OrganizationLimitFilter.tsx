import { FilterListRounded, Search } from '@mui/icons-material'
import {
  Box,
  Button,
  ClickAwayListener,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grow,
  InputBase,
  List,
  ListItem,
  Paper,
  Popper,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from '@mui/material'
import React from 'react'
import { useAppSelector } from '@/store'
import { IOrganization } from '@/store/interfaces'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

interface IReportFilter {
  onFilter: (value: { orgName: string; pageSize: number }) => void
}

const SearchBox = ({
  onDropDownSelect,
}: {
  onDropDownSelect?: (value: string) => void
}) => {
  const { organizations, isLoadingOrganizations } = useAppSelector(
    (state) => state.loans
  )

  const organization =
    (organizations as unknown as { data: IOrganization[] })?.data || []
  const anchorRef = React.useRef<HTMLInputElement>(null)
  const [openInputDropdown, setOpenInputDropdown] =
    React.useState<boolean>(false)
  const [search, setSearch] = React.useState<string>('')

  const filteredOrg = React.useMemo(() => {
    return (
      !isLoadingOrganizations &&
      organization.filter((org) =>
        org.name.toLowerCase().includes(search.toLowerCase())
      )
    )
  }, [organization, search])

  return (
    <>
      <Box
        sx={{
          border: '1px solid #D0D5DD',
          borderRadius: '8px',
          padding: '14px 10px',
          height: '40px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <InputBase
          placeholder={'Organization name'}
          ref={anchorRef}
          id="composition-button"
          aria-controls={openInputDropdown ? 'composition-menu' : undefined}
          aria-expanded={openInputDropdown ? 'true' : undefined}
          aria-haspopup="true"
          value={search}
          onChange={(e) => {
            setOpenInputDropdown(true)
            setSearch(e.target.value)
          }}
          sx={{
            width: '100%',
          }}
          startAdornment={
            <Search
              sx={{
                color: '#667085',
              }}
            />
          }
        />
      </Box>
      <Popper
        open={openInputDropdown}
        anchorEl={anchorRef.current}
        placement="bottom"
        role={undefined}
        transition
        disablePortal
        sx={{
          zIndex: '10',
        }}
      >
        {({ TransitionProps, placement }) => {
          return (
            <Grow
              {...TransitionProps}
              style={{
                transformOrigin:
                  placement === 'bottom' ? 'left bottom' : 'right bottom',
              }}
            >
              <Paper
                sx={{
                  maxHeight: '40vh',
                  display: 'flex',
                  flexDirection: 'column',
                  width: '14vw',
                  marginTop: '6px',
                }}
              >
                <List
                  sx={{
                    overflow: 'auto',
                    padding: '0px',
                  }}
                >
                  {!isLoadingOrganizations &&
                    filteredOrg &&
                    filteredOrg.map((org, index) => {
                      return (
                        <ListItem
                          key={index || org.id}
                          sx={{
                            padding: '4px',
                          }}
                          onClick={() => {
                            setOpenInputDropdown(false)
                            setSearch(org.name)
                            onDropDownSelect && onDropDownSelect(org.name)
                          }}
                        >
                          {' '}
                          <Divider orientation="horizontal" />
                          <Button
                            sx={{
                              width: '100%',
                              height: '100%',
                              padding: '6px 6px 6px 15px',
                              justifyContent: 'flex-start',
                            }}
                          >
                            <Typography>
                              {org.name.split(' ').slice(0, 2).join(' ')}
                            </Typography>
                          </Button>
                        </ListItem>
                      )
                    })}

                  {isLoadingOrganizations &&
                    Array.from({ length: 10 }).map((_, index) => {
                      return (
                        <ListItem
                          key={index}
                          sx={{
                            padding: '4px',
                          }}
                        >
                          <CustomSkeleton
                            variant="rectangular"
                            sx={{
                              width: '100%',
                              height: '40px',
                              borderRadius: '6px',
                            }}
                          />
                        </ListItem>
                      )
                    })}
                </List>
              </Paper>
            </Grow>
          )
        }}
      </Popper>
    </>
  )
}

const ReportsDropDownFilter: React.FC<IReportFilter> = ({ onFilter }) => {
  const anchorRef = React.useRef<HTMLButtonElement>(null)
  const [open, setOpen] = React.useState<boolean>(false)
  const [size, setSize] = React.useState<number>(10)
  const [name, setName] = React.useState<string>('')

  const handleToggle = () => {
    setOpen((prev) => !prev)
  }
  const handleSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSize(Number((event.target as HTMLInputElement).value))
  }

  const RenderSizeRadio = () => {
    return (
      <>
        {
          <FormControl>
            <FormLabel id="demo-radio-buttons-group-label">
              <Typography
                variant={'subtitle2'}
                sx={{
                  fontWeight: 'bold',
                }}
              >
                Records per page
              </Typography>
            </FormLabel>
            <RadioGroup
              aria-labelledby="demo-radio-buttons-group-label"
              defaultValue="10"
              name="radio-buttons-group"
              onChange={handleSizeChange}
              value={size.toString()}
            >
              <FormControlLabel value="10" control={<Radio />} label="10" />
              <FormControlLabel value="15" control={<Radio />} label="15" />
              <FormControlLabel value="20" control={<Radio />} label="20" />
              <FormControlLabel value="25" control={<Radio />} label="25" />
            </RadioGroup>
          </FormControl>
        }
      </>
    )
  }
  return (
    <>
      <Button
        variant="outlined"
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        startIcon={<FilterListRounded />}
      >
        Filters
      </Button>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                width: {
                  xs: '35vw',
                  sm: '35vw',
                  lg: '25vw',
                },
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setOpen(false)
                }}
              >
                <Stack>
                  <Stack
                    sx={{
                      padding: '24px 24px 34px 34px',
                      gap: '20px',
                      marginTop: '10px',
                    }}
                  >
                    <Stack
                      sx={{
                        gap: '4px',
                      }}
                    >
                      <Typography
                        variant={'subtitle2'}
                        sx={{
                          fontWeight: 'bold',
                        }}
                      >
                        Filter
                      </Typography>
                      <Typography variant={'subtitle3'}>
                        Apply filter to table data
                      </Typography>
                    </Stack>
                    <Stack>
                      <Typography
                        variant={'subtitle2'}
                        sx={{
                          fontWeight: 'bold',
                        }}
                      >
                        Organization
                      </Typography>
                      <SearchBox
                        onDropDownSelect={(val: string) => {
                          setName(val)
                        }}
                      />
                    </Stack>
                    <Stack>
                      {RenderSizeRadio()}
                      {/* <CustomSlider
              aria-label="Records per page"
              getAriaValueText={(value) => `${value}`}
              marks={[...generateMarks(10, 10, 100)]}
              valueLabelDisplay="auto"
              min={10}
              max={sliderMax}
              value={size}
              onChange={handleSizeChange}
            /> */}
                    </Stack>
                  </Stack>
                  <Divider />
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: '10px',
                      padding: '10px 16px 10px 8px',
                    }}
                  >
                    <Button
                      variant="outlined"
                      sx={{
                        padding: '8px 14px',
                      }}
                      onClick={() => {
                        setOpen(false)
                      }}
                      fullWidth
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      sx={{
                        padding: '8px 14px',
                      }}
                      fullWidth
                    >
                      <Typography
                        sx={{
                          color: 'white',
                        }}
                        onClick={() => {
                          onFilter({
                            orgName: name,
                            pageSize: size,
                          })
                          setOpen(false)
                        }}
                      >
                        Apply Filter
                      </Typography>
                    </Button>
                  </Box>
                </Stack>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  )
}

export default ReportsDropDownFilter
