import { FilterListRounded, Search } from '@mui/icons-material'
import {
  Box,
  Button,
  ClickAwayListener,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grow,
  InputBase,
  List,
  ListItem,
  Paper,
  Popper,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from '@mui/material'
import React from 'react'
import { Dayjs } from 'dayjs'
import { useAppSelector } from '@/store'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import { DateRangePicker } from '@dtbx/ui/components/DropDownMenus'

import { loanStatus, StatusFilter } from '../StatusChips'

interface IReportFilter {
  onFilter: (value: {
    orgName: string
    pageSize: number
    maturityDate?: {
      startDate: Dayjs
      endDate: Dayjs
    }
    creationDate?: {
      startDate: Dayjs
      endDate: Dayjs
    }
    status: string
  }) => void
}

const SearchBox = ({
  onDropDownSelect,
}: {
  onDropDownSelect?: (value: string) => void
}) => {
  const { organizations, isLoadingOrganizations } = useAppSelector(
    (state) => state.loans
  )
  const anchorRef = React.useRef<HTMLInputElement>(null)
  const [openInputDropdown, setOpenInputDropdown] =
    React.useState<boolean>(false)
  const [search, setSearch] = React.useState<string>('')

  const filteredOrg = React.useMemo(() => {
    return (
      !isLoadingOrganizations &&
      organizations.filter((org) =>
        org.name.toLowerCase().includes(search.toLowerCase())
      )
    )
  }, [organizations, search])

  return (
    <>
      <Box
        sx={{
          border: '1px solid #D0D5DD',
          borderRadius: '8px',
          padding: '14px 10px',
          height: '40px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <InputBase
          placeholder={'Organization name'}
          ref={anchorRef}
          id="composition-button"
          aria-controls={openInputDropdown ? 'composition-menu' : undefined}
          aria-expanded={openInputDropdown ? 'true' : undefined}
          aria-haspopup="true"
          value={search}
          onChange={(e) => {
            setOpenInputDropdown(true)
            setSearch(e.target.value)
          }}
          sx={{
            width: '100%',
          }}
          startAdornment={
            <Search
              sx={{
                color: '#667085',
              }}
            />
          }
        />
      </Box>

      <Popper
        open={openInputDropdown}
        anchorEl={anchorRef.current}
        placement="top-start"
        role={undefined}
        transition
        disablePortal
        sx={{
          zIndex: '10',
        }}
      >
        {({ TransitionProps, placement }) => {
          return (
            <Grow
              {...TransitionProps}
              style={{
                transformOrigin:
                  placement === 'bottom-start' ? 'left top' : 'left bottom',
              }}
            >
              <Paper
                sx={{
                  maxHeight: '30vh',
                  display: 'flex',
                  flexDirection: 'column',
                  width: '14vw',
                }}
              >
                <List
                  sx={{
                    overflow: 'auto',
                    padding: '0px',
                  }}
                >
                  {!isLoadingOrganizations &&
                    filteredOrg &&
                    filteredOrg.map((org, index) => {
                      return (
                        <ListItem
                          key={index || org.id}
                          sx={{
                            padding: '4px',
                          }}
                          onClick={() => {
                            setOpenInputDropdown(false)
                            setSearch(org.name)
                            onDropDownSelect && onDropDownSelect(org.name)
                          }}
                        >
                          {' '}
                          <Divider orientation="horizontal" />
                          <Button
                            sx={{
                              width: '100%',
                              height: '100%',
                              padding: '6px 6px 6px 15px',
                              justifyContent: 'flex-start',
                            }}
                          >
                            <Typography>
                              {org.name.split(' ').slice(0, 2).join(' ')}
                            </Typography>
                          </Button>
                        </ListItem>
                      )
                    })}

                  {isLoadingOrganizations &&
                    Array.from({ length: 10 }).map((_, index) => {
                      return (
                        <ListItem
                          key={index}
                          sx={{
                            padding: '4px',
                          }}
                        >
                          <CustomSkeleton
                            variant="rectangular"
                            sx={{
                              width: '100%',
                              height: '40px',
                              borderRadius: '6px',
                            }}
                          />
                        </ListItem>
                      )
                    })}
                </List>
              </Paper>
            </Grow>
          )
        }}
      </Popper>
    </>
  )
}

const LoanReportFilters: React.FC<IReportFilter> = ({ onFilter }) => {
  const anchorRef = React.useRef<HTMLButtonElement>(null)
  const [open, setOpen] = React.useState<boolean>(false)
  const [size, setSize] = React.useState<number>(10)
  const [name, setName] = React.useState<string>('')
  const [date, setDate] = React.useState<{
    start: Dayjs | null
    end: Dayjs | null
  }>({
    start: null,
    end: null,
  })
  const [maturityDate, setMaturityDate] = React.useState<{
    start: Dayjs | null
    end: Dayjs | null
  }>({
    start: null,
    end: null,
  })
  const [status, setStatus] = React.useState<{
    label: string
    key: string
  } | null>({
    label: 'Active',
    key: 'ACTIVE',
  })

  const handleToggle = () => {
    setOpen((prev) => !prev)
  }
  const handleSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSize(Number((event.target as HTMLInputElement).value))
  }

  const RenderSizeRadio = () => {
    return (
      <>
        {
          <FormControl>
            <FormLabel id="demo-radio-buttons-group-label">
              <Typography
                variant={'subtitle2'}
                sx={{
                  fontWeight: 'bold',
                }}
              >
                Records per page
              </Typography>
            </FormLabel>
            <RadioGroup
              aria-labelledby="demo-radio-buttons-group-label"
              defaultValue="10"
              name="radio-buttons-group"
              onChange={handleSizeChange}
              value={size.toString()}
            >
              <FormControlLabel value="10" control={<Radio />} label="10" />
              <FormControlLabel value="15" control={<Radio />} label="15" />
              <FormControlLabel value="20" control={<Radio />} label="20" />
              <FormControlLabel value="25" control={<Radio />} label="25" />
            </RadioGroup>
          </FormControl>
        }
      </>
    )
  }
  return (
    <>
      <Button
        variant="outlined"
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        startIcon={<FilterListRounded />}
      >
        Filters
      </Button>

      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        modifiers={[
          {
            name: 'preventOverflow',
            options: {
              boundary: 'viewport',
            },
          },
          {
            name: 'flip',
            options: {
              fallbackPlacements: ['top-start', 'top-end'],
            },
          },
        ]}
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                width: {
                  xs: '35vw',
                  sm: '35vw',
                  lg: '25vw',
                },
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setOpen(false)
                }}
              >
                <Stack>
                  <Stack
                    sx={{
                      padding: '24px 24px 34px 34px',
                      gap: '10px',
                      marginTop: '5px',
                      maxHeight: '23.5rem',
                      overflow: 'auto',
                    }}
                  >
                    <Stack
                      sx={{
                        gap: '0px',
                      }}
                    >
                      <Typography
                        variant={'subtitle2'}
                        sx={{
                          fontWeight: 'bold',
                        }}
                      >
                        Filter
                      </Typography>
                      <Typography variant={'subtitle3'}>
                        Apply filter to table data
                      </Typography>
                    </Stack>
                    <Stack>
                      <Typography
                        variant={'subtitle2'}
                        sx={{
                          fontWeight: 'bold',
                        }}
                      >
                        Creation Date
                      </Typography>
                      <DateRangePicker
                        onApplyDateRange={(date) => {
                          setDate(date)
                        }}
                      />
                    </Stack>
                    <Stack>
                      <Typography
                        variant={'subtitle2'}
                        sx={{
                          fontWeight: 'bold',
                        }}
                      >
                        Maturity Date
                      </Typography>
                      <DateRangePicker
                        onApplyDateRange={(date) => {
                          setMaturityDate(date)
                        }}
                      />
                    </Stack>
                    <Stack>
                      <Typography
                        variant={'subtitle2'}
                        sx={{
                          fontWeight: 'bold',
                        }}
                      >
                        Status
                      </Typography>
                      <StatusFilter
                        status={[...loanStatus]}
                        onChange={(value) => {
                          setStatus(value)
                        }}
                        value={status}
                      />
                    </Stack>
                    <Stack>
                      <Typography
                        variant={'subtitle2'}
                        sx={{
                          fontWeight: 'bold',
                        }}
                      >
                        Organization
                      </Typography>
                      <SearchBox
                        onDropDownSelect={(val: string) => {
                          setName(val)
                        }}
                      />
                    </Stack>
                    <Stack>
                      {RenderSizeRadio()}
                      {/* <CustomSlider
                    aria-label="Records per page"
                    getAriaValueText={(value) => `${value}`}
                    marks={[...generateMarks(10, 10, 100)]}
                    valueLabelDisplay="auto"
                    min={10}
                    value={size}
                    onChange={handleSizeChange}
                  /> */}
                    </Stack>
                  </Stack>
                </Stack>
              </ClickAwayListener>

              <Divider />
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '10px',
                  padding: '10px 16px 10px 8px',
                }}
              >
                <Button
                  variant="outlined"
                  sx={{
                    padding: '8px 14px',
                  }}
                  onClick={() => {
                    setOpen(false)
                  }}
                  fullWidth
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    padding: '8px 14px',
                  }}
                  fullWidth
                >
                  <Typography
                    sx={{
                      color: 'white',
                    }}
                    onClick={() => {
                      onFilter({
                        orgName: name,
                        pageSize: size,
                        creationDate:
                          date.start && date.end
                            ? { startDate: date.start, endDate: date.end }
                            : undefined,
                        maturityDate:
                          maturityDate.start && maturityDate.end
                            ? {
                                startDate: maturityDate.start,
                                endDate: maturityDate.end,
                              }
                            : undefined,
                        status: status?.key || '',
                      })
                      setOpen(false)
                    }}
                  >
                    Apply Filter
                  </Typography>
                </Button>
              </Box>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  )
}

export default LoanReportFilters
