import {
  AccessTimeRounded,
  CheckCircleOutlineRounded,
  ErrorOutlineRounded,
} from '@mui/icons-material'
import {
  Autocomplete,
  Chip,
  ChipProps,
  ListItem,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import { useState } from 'react'
import { sentenceCase } from 'tiny-case'

export const loanStatus = [
  {
    label: 'REVERSED',
    key: 'REVERSED',
  },
  {
    label: 'LOAN_CREATION_FAILED',
    key: 'LOAN_CREATION_FAILED',
  },
  {
    label: 'CUSTOMER_CREATION_FAILED',
    key: 'CUSTOMER_CREATION_FAILED',
  },
  {
    label: 'UNKNOWN',
    key: 'UNKNOWN',
  },
  {
    label: 'ACCOUNT_CREATION_FAILED',
    key: 'ACCOUNT_CREATION_FAILED',
  },

  // {
  //   label: 'PENDING APPROVAL',
  //   key: 'PENDING_APPROVAL',
  // },
  {
    label: 'PENDING DISBURSEMENT',
    key: 'PENDING_DISBURSEMENT',
  },
  {
    label: 'PENDING PREPAYMENT',
    key: 'PENDING_PREPAYMENT',
  },
  {
    label: 'ACTIVE',
    key: 'ACTIVE',
  },
  {
    label: 'LIQUIDATED',
    key: 'LIQUIDATED',
  },
]

export const reportStatus = [
  {
    label: 'New',
    key: 'New',
  },
  {
    label: 'Processed',
    key: 'Processed',
  },
  {
    label: 'PassedKYC',
    key: 'PassedKYC',
  },
  {
    label: 'PassedAcceptance',
    key: 'PassedAcceptance',
  },
  {
    label: 'FailedChecks',
    key: 'FailedChecks',
  },
  {
    label: 'OrgLimitExceeded',
    key: 'OrgLimitExceeded',
  },
  {
    label: 'FailedAcceptance',
    key: 'FailedAcceptance',
  },
  {
    label: 'Awaiting Acceptance',
    key: 'AwaitingAcceptance',
  },
  {
    label: 'Pending Disbursement',
    key: 'PendingDisbursement',
  },
  {
    label: 'Ongoing KYC',
    key: 'OngoingKYC',
  },
  {
    label: 'AwaitingManualApproval',
    key: 'AwaitingManualApproval',
  },
  {
    label: 'AwaitingPrepayment',
    key: 'AwaitingPrepayment',
  },
  {
    label: 'PendingActivation',
    key: 'PendingActivation',
  },
  {
    label: 'OngoingChecks',
    key: 'OngoingChecks',
  },
  {
    label: 'AwaitingDisbursement',
    key: 'AwaitingDisbursement',
  },
  {
    label: 'Cancelled',
    key: 'Cancelled',
  },
]
interface Status {
  label: string
  key: string
}
interface StatusFilterProps {
  status: Status[]
  onChange: (value: Status | null) => void
  value: Status | null
}
export const StatusFilter: React.FC<StatusFilterProps> = (params) => {
  const { status, onChange, value: v } = params
  const [value, setValue] = useState<Status | null>({
    label: (v && v.label) || '',
    key: (v && v.key) || '',
  })

  return (
    <Autocomplete
      size="small"
      disablePortal
      id="status-filter"
      options={status}
      value={value}
      sx={{ width: '100%' }}
      onChange={(_, value: Status | null) => {
        setValue(value)
        onChange(value)
      }}
      getOptionLabel={(option) => (option ? sentenceCase(option.label) : '')}
      renderInput={(params) => <TextField {...params} />}
      isOptionEqualToValue={(option, value) => option.key === value.key}
      renderOption={(props, option) => (
        <ListItem
          {...props}
          sx={{
            margin: '4px 6px 4px 6px',
          }}
        >
          <Typography variant="label2">{sentenceCase(option.label)}</Typography>
        </ListItem>
      )}
    />
  )
}
export const ReportStatusChip = styled(({ ...other }: ChipProps) => {
  /**
   * This component is used to display the status of a report
   * @organizationLimit - status of the organization limit
   * @RequestReports - status of the request report
   * @loanReports - status of the loan report
   * @changebehaviour - add a new status to the list of status @errorStatus @successStatus @warningStatus and set the color @CHIPCOLORS
   **/

  const errorStatus = [
    'FailedKYC',
    'FailedPrevalidation',
    'FailedChecks',
    'OrgLimitExceeded',
    'FailedAcceptance',
    'Cancelled',
    'LOAN_CREATION_FAILED',
    'CUSTOMER_CREATION_FAILED',
    'UNKNOWN',
    'ACCOUNT_CREATION_FAILED',
    'REVERSED',
    'Failed',
  ]
  const successStatus = [
    'ACTIVE',
    'Processed',
    'PassedKYC',
    'PassedAcceptance',
    'LIQUIDATED',
    'Passed',
  ]
  const warningStatus = [
    'New',
    'PENDING_APPROVAL',
    'PENDING_DISBURSEMENT',
    'PENDING_PREPAYMENT',
    'AwaitingAcceptance',
    'PendingDisbursement',
    'OngoingKYC',
    'AwaitingManualApproval',
    'AwaitingPrepayment',
    'PendingActivation',
    'OngoingChecks',
    'AwaitingDisbursement',
    'Pending',
  ]

  const CHIPCOLORS = {
    success: '#50af89',
    warning: '#175CD3',
    error: '#B42318',
    sucessBackground: '#D1FAE5',
    warningBackground: '#EAECF0',
    errorBackground: '#FECDCA',
    sucessBorder: '#469975',
    warningBorder: '#175CD3',
    errorBorder: '#F04438',
  }

  return (
    <Chip
      {...other}
      sx={{
        padding: '2px 6px 2px 8px',
        maxHeight: '22px',
        minWidth: '52px',
        background: successStatus.includes(other.label as string)
          ? CHIPCOLORS.sucessBackground
          : errorStatus.includes(other.label as string)
            ? CHIPCOLORS.errorBackground
            : warningStatus.includes(other.label as string)
              ? CHIPCOLORS.warningBackground
              : '#jjjjjF',

        border: `1px solid ${successStatus.includes(other.label as string) ? CHIPCOLORS.sucessBorder : errorStatus.includes(other.label as string) ? CHIPCOLORS.errorBorder : warningStatus.includes(other.label as string) ? CHIPCOLORS.warningBorder : '#EAECF0'}`,
      }}
      icon={
        successStatus.includes(other.label as string) ? (
          <CheckCircleOutlineRounded
            sx={{
              fontSize: '12px',
            }}
            style={{ color: CHIPCOLORS.success }}
          />
        ) : warningStatus.includes(other.label as string) ? (
          <AccessTimeRounded
            sx={{
              fontSize: '12px',
            }}
            style={{ color: CHIPCOLORS.warning }}
          />
        ) : errorStatus.includes(other.label as string) ? (
          <ErrorOutlineRounded
            sx={{
              fontSize: '12px',
            }}
            style={{ color: CHIPCOLORS.error }}
          />
        ) : (
          <></>
        )
      }
      label={
        <Typography
          variant="label2"
          sx={{
            color: successStatus.includes(other.label as string)
              ? CHIPCOLORS.success
              : errorStatus.includes(other.label as string)
                ? CHIPCOLORS.error
                : CHIPCOLORS.warning,
            fontSize: '12px',
            fontStyle: 'normal',
            fontWeight: 500,
            lineHeight: '16px',
          }}
        >
          {sentenceCase(
            typeof other.label !== 'string' ? '' : (other.label as string)
          )}
        </Typography>
      }
    />
  )
})(() => ({
  padding: 0,
  textAlign: 'center',
  fontSize: '12px',
  fontStyle: 'normal',
  fontWeight: 500,
  lineHeight: '16px',
}))
