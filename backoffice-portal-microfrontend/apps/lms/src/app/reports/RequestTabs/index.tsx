import { Button, Stack, Typography } from '@mui/material'
import React from 'react'

interface ITabs {
  onTabChange: (tab: number) => void
}

const Tabs: React.FC<ITabs> = ({ onTabChange }) => {
  const [activeTab, setActiveTab] = React.useState<number>(0)

  React.useEffect(() => {
    onTabChange(activeTab)
  }, [activeTab, onTabChange])
  return (
    <Stack
      sx={{
        flexDirection: 'row',
        gap: '4px',
      }}
    >
      <Button
        variant={activeTab === 0 ? 'contained' : 'outlined'}
        onClick={() => setActiveTab(0)}
        sx={{
          height: '40px',
          border: '1px solid  #D0D5DD',
        }}
      >
        <Typography
          variant="label2"
          sx={{
            color: activeTab === 0 ? 'white' : 'black',
          }}
        >
          Organization limit Consumption Report
        </Typography>
      </Button>
      <Button
        variant={activeTab === 1 ? 'contained' : 'outlined'}
        onClick={() => setActiveTab(1)}
        sx={{
          height: '40px',
          border: '1px solid  #D0D5DD',
        }}
      >
        <Typography
          variant="label2"
          sx={{
            color: activeTab === 1 ? 'white' : 'black',
          }}
        >
          Request Report
        </Typography>
      </Button>
      <Button
        variant={activeTab === 2 ? 'contained' : 'outlined'}
        onClick={() => setActiveTab(2)}
        sx={{
          height: '40px',
          border: '1px solid  #D0D5DD',
        }}
      >
        <Typography
          variant="label2"
          sx={{
            color: activeTab === 2 ? 'white' : 'black',
          }}
        >
          Loan Report
        </Typography>
      </Button>
      <Button
        variant={activeTab === 3 ? 'contained' : 'outlined'}
        onClick={() => setActiveTab(3)}
        sx={{
          height: '40px',
          border: '1px solid  #D0D5DD',
        }}
      >
        <Typography
          variant="label2"
          sx={{
            color: activeTab === 3 ? 'white' : 'black',
          }}
        >
          Validation Report
        </Typography>
      </Button>
      <Button
        variant={activeTab === 4 ? 'contained' : 'outlined'}
        onClick={() => setActiveTab(4)}
        sx={{
          height: '40px',
          border: '1px solid  #D0D5DD',
        }}
      >
        <Typography
          variant="label2"
          sx={{
            color: activeTab === 4 ? 'white' : 'black',
          }}
        >
          Customer Validation Report
        </Typography>
      </Button>
    </Stack>
  )
}

export default Tabs
