
import { IApprovalRequest } from '@/store/interfaces'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { Dispatch } from '@reduxjs/toolkit'
import { acceptCancelLoanRequest, rejectCancelLoanRequest } from '@/store/actions'


//This function takes the action and approval request and makes the api call to the selected api call and routes to the final route
export const LMSCheckerRequestsApiHandler = async (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  action: string,
  comments?: string
) => {
  const handler = handlers[request.makerCheckerType.module]
  if (handler) {
    if (typeof handler === 'function') {
      await handler(request, dispatch, router)
    } else {
      const subHandler = handler[action]
      if (subHandler) {
        await subHandler(request, dispatch, router, comments)
      }
    }
  }
}
type Handler = (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  comments?: string
) => Promise<void>
type Handlers = {
  [key: string]: Handler | { [key: string]: Handler }
}
const handlers: Handlers = {
  'Loan Request': {
    ACCEPT_LMS_UPDATE_REQUEST: async (request, dispatch, router) => {
      await acceptCancelLoanRequest({
        requestId: request.entityId || '',
        approvalId: request.id,
        comments: 'Approve cancel loan request',
        dispatch,
      })
      router.push('/requests')
    },
    REJECT_LMS_UPDATE_REQUEST: async (request, dispatch, router, comments) => {
      await rejectCancelLoanRequest({
        requestId: request.entityId || '',
        approvalId: request.id,
        comments: comments || 'Reject cancel loan request',
        dispatch,
      })
      router.push('/requests')
    },
  },
}
