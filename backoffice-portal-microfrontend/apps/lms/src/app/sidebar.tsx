/**
 * <AUTHOR> on 29/10/2024
 */
import { ISidebarConfigItem } from '@dtbx/ui/components/Sidebar'
import {
  CustomersIcon,
  CustomProductsIcon,
  HomeIcon,
  ProductCategoriesIcon,
  ReportsIcon,
  RequestsIcon,
  SettingsIcon,
} from '@dtbx/ui/icons'
import ShieldOutlinedIcon from '@mui/icons-material/ShieldOutlined'

export const sidebarConfig: ISidebarConfigItem[] = [
  {
    id: '1',
    title: 'Home',
    path: '/(home)',
    module: 'default',
    icon: <HomeIcon />,
    isProductionReady: true,
  },
  {
    id: '2',
    title: 'Reports',
    path: '/reports',
    module: 'Reports',
    icon: <ReportsIcon />,
    isProductionReady: true,
  },
  {
    id: '3',
    title: 'Requests',
    path: '/requests',
    module: 'default',
    icon: <RequestsIcon />,
    isProductionReady: true,
  },

  {
    id: '4',
    title: 'Organizations',
    path: '/settings/organizations',
    module: 'Product Configs',
    icon: <ShieldOutlinedIcon />,
    isProductionReady: true,
  },
  {
    id: '5',
    title: 'Products',
    path: '/settings/products',
    module: 'Product Configs',
    icon: <CustomProductsIcon width={'25'} height={'25'} />,
    isProductionReady: true,
  },
  {
    id: '6',
    title: 'Product Categories',
    path: '/settings/categories',
    module: 'Product Configs',
    icon: <ProductCategoriesIcon width={'25'} height={'25'} />,
    isProductionReady: true,
  },
  {
    id: '7',
    title: 'Brokers',
    path: '/settings/brokers',
    module: 'Product Configs',
    icon: <CustomersIcon />,
    isProductionReady: true,
  },
]
