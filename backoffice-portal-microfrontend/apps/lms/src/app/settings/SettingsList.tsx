'use client'

import { But<PERSON>, Stack, Typography } from '@mui/material'
import ShieldOutlinedIcon from '@mui/icons-material/ShieldOutlined'
import { ArrowForwardIos } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import React, { useEffect } from 'react'
import { getBrokers, getLoanProducts, getOrganizations } from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch } from '@/store'
import {
  CustomersIcon,
  CustomProductsIcon,
  ProductCategoriesIcon,
} from '@dtbx/ui/icons'

import { CreateCategory } from './categories/MoreMenu'

export const SettingsList = () => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  useEffect(() => {
    getOrganizations(dispatch, 'page=1&size=10')
    getLoanProducts(dispatch, 'page=1&size=10')
    getBrokers(dispatch, 'page=1&size=10')
  }, [])
  return (
    <>
      <Stack
        sx={{
          flexDirection: 'row',
          width: '100%',
          gap: '5%',
        }}
      >
        <Stack
          sx={{
            border: '1px solid #D0D5DD',
            background: '#FFFFFF',
            borderRadius: '8px',
            width: '30%',
            px: '2%',
            py: '2%',
            flexDirection: 'column',
            gap: '3vh',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              alignContent: 'center',
            }}
          >
            <ShieldOutlinedIcon />
            <Button
              sx={{
                padding: '0',
                color: '#155EEF',
                fontSize: '14px',
                px: '5px',
                backgroundColor: 'rgba(178, 221, 255, 0.3)',
                borderRadius: '9999px',
                border: '1px solid #7DD3FC',
              }}
              onClick={() => router.push('/settings/organizations')}
            >
              View all Organizations
              <ArrowForwardIos
                sx={{
                  fontSize: '14px',
                }}
              />
            </Button>
          </Stack>
          <Typography variant="h6">Manage Organizations</Typography>
          <Typography variant="body2">
            View and update records of registered insurers.
          </Typography>
          <Button
            variant="contained"
            endIcon={<AddOutlinedIcon />}
            onClick={() => router.push('/settings/organizations/create')}
          >
            Add New Organization
          </Button>
        </Stack>
        <Stack
          sx={{
            border: '1px solid #D0D5DD',
            background: '#FFFFFF',
            borderRadius: '8px',
            width: '30%',
            px: '2%',
            py: '2%',
            flexDirection: 'column',
            gap: '3vh',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              alignContent: 'center',
            }}
          >
            <CustomProductsIcon />
            <Button
              sx={{
                padding: '0',
                color: '#155EEF',
                fontSize: '14px',
                px: '5px',
                backgroundColor: 'rgba(178, 221, 255, 0.3)',
                borderRadius: '9999px',
                border: '1px solid #7DD3FC',
              }}
              onClick={() => router.push('/settings/products')}
            >
              View all Products
              <ArrowForwardIos
                sx={{
                  fontSize: '14px',
                }}
              />
            </Button>
          </Stack>
          <Typography variant="h6">Manage Products</Typography>
          <Typography variant="body2">
            View and edit existing products, or create new products.
          </Typography>
          <Button
            variant="contained"
            endIcon={<AddOutlinedIcon />}
            onClick={() => router.push('/settings/products/create')}
          >
            Add New Product
          </Button>
        </Stack>
        <Stack
          sx={{
            border: '1px solid #D0D5DD',
            background: '#FFFFFF',
            borderRadius: '8px',
            width: '30%',
            px: '2%',
            py: '2%',
            flexDirection: 'column',
            gap: '3vh',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              alignContent: 'center',
            }}
          >
            <ProductCategoriesIcon />
            <Button
              sx={{
                padding: '0',
                color: '#155EEF',
                fontSize: '14px',
                px: '5px',
                backgroundColor: 'rgba(178, 221, 255, 0.3)',
                borderRadius: '9999px',
                border: '1px solid #7DD3FC',
              }}
              onClick={() => router.push('/settings/categories')}
            >
              Categories
              <ArrowForwardIos
                sx={{
                  fontSize: '14px',
                }}
              />
            </Button>
          </Stack>
          <Typography variant="h6">Manage Product Categories</Typography>
          <Typography variant="body2">
            View and edit existing product categories.
          </Typography>

          <CreateCategory />
        </Stack>
      </Stack>
      {/* New Row Starts*/}
      <br />
      <Stack
        sx={{
          border: '1px solid #D0D5DD',
          background: '#FFFFFF',
          borderRadius: '8px',
          width: '30%',
          px: '2%',
          py: '2%',
          flexDirection: 'column',
          gap: '3vh',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            alignContent: 'center',
          }}
        >
          <CustomersIcon width="30" height="30" />

          <Button
            sx={{
              padding: '0',
              color: '#155EEF',
              fontSize: '14px',
              px: '5px',
              backgroundColor: 'rgba(178, 221, 255, 0.3)',
              borderRadius: '9999px',
              border: '1px solid #7DD3FC',
            }}
            onClick={() => router.push('/settings/brokers')}
          >
            View all Brokers
            <ArrowForwardIos
              sx={{
                fontSize: '14px',
              }}
            />
          </Button>
        </Stack>
        <Typography variant="h6">Manage Brokers</Typography>
        <Typography variant="body2">
          View and update records of registered brokers.
        </Typography>
        <Button
          variant="contained"
          endIcon={<AddOutlinedIcon />}
          onClick={() => router.push('/settings/brokers/create')}
        >
          Add New Broker
        </Button>
      </Stack>
    </>
  )
}
