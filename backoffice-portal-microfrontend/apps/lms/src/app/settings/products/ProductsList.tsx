'use client'

import {
  <PERSON>ton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import { sentenceCase } from 'tiny-case'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getLoanProducts } from '@/store/actions'
import { formatCurrency } from '@dtbx/store/utils'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import {
  CustomActiveChip,
  CustomErrorChip,
  CustomWarningChip,
} from '@dtbx/ui/components/Chip'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'

import { ProductMoreMenu } from '@/app/settings/products/MoreMenu'

const headerList = [
  { id: 'name', label: 'Name', alignRight: false },
  { id: 'organization', label: 'Organization', alignRight: false },
  { id: 'status', label: 'Status', alignRight: false },
  { id: 'type', label: 'Type of Product', alignRight: false },
  { id: 'exposureLimit', label: 'Exposure Limit', alignRight: false },
  { id: 'createdBy', label: 'createdBy', alignRight: false },
  { id: 'approvedBy', label: 'approvedBy', alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: false },
]
export const ProductsList = () => {
  const dispatch = useAppDispatch()
  const { loanProducts, loanProductsSummary } = useAppSelector(
    (state) => state.loans
  )
  const [selected, setSelected] = useState<readonly string[]>([])
  const [paginationOptions, setPaginationOptions] = useState({
    page: loanProductsSummary.pageNumber,
    size: 10,
    totalPages: loanProductsSummary.totalNumberOfPages,
  })

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newSelected: readonly string[] = []
    if (event.target.checked) {
      newSelected = loanProducts.map((product) => product.id)
    }
    setSelected(newSelected)
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []
    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }
  /********* start pagination handlers***********/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    const params = `page=${newOptions.page}&size=${newOptions.size}`
    await getLoanProducts(dispatch, params)
  }
  /************* end pagination handlers ********/
  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: '8px',
        border: '1px solid #EAECF0',
        background: '#FFFFFF',
        my: '1%',
        boxShadow: 'none',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          padding: '2%',
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Products</Typography>
          <Typography variant="subtitle3">
            Showing {loanProducts.length} products
          </Typography>
        </Stack>
        <Button variant="outlined" startIcon={<FilterListOutlinedIcon />}>
          Filter
        </Button>
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="products table" size="small">
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={headerList}
            showCheckbox={true}
            rowCount={10}
            numSelected={0}
            onRequestSort={() => {}}
            onSelectAllClick={handleSelectAll}
          />

          <TableBody>
            {loanProducts &&
              loanProducts.map((row) => {
                const { id, organization, type, name, exposureLimit, status } =
                  row
                const isItemSelected = selected.indexOf(id) !== -1
                return (
                  <TableRow
                    hover
                    key={id}
                    tabIndex={-1}
                    role="checkbox"
                    onClick={(event) => handleSelectOne(event, row.id)}
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        checked={isItemSelected}
                        inputProps={{
                          'aria-labelledby': id,
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography
                          variant="subtitle2"
                          sx={{ color: 'primary.main' }}
                        >
                          {name}
                        </Typography>
                        <Typography variant="caption">{id}</Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="subtitle2"
                        sx={{ color: 'primary.main' }}
                      >
                        {organization.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {status === 'Active' ? (
                        <CustomActiveChip label={sentenceCase(status)} />
                      ) : status === 'Inactive' ? (
                        <CustomErrorChip label={sentenceCase(status)} />
                      ) : status === 'Pending Approval' ? (
                        <CustomWarningChip label={sentenceCase(status)} />
                      ) : null}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {type.name || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatCurrency(exposureLimit) || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography variant="body2">
                          {type.createdBy}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography variant="body2">N/A</Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <ProductMoreMenu product={row} />
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>
      {loanProductsSummary.totalNumberOfPages > 0 && (
        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: loanProductsSummary.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
        />
      )}
    </Paper>
  )
}
