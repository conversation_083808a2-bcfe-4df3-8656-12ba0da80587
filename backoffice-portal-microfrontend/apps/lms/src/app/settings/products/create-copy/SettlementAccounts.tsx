import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Field, Typography } from '@mui/material'
import React from 'react'
import { FormikProps } from 'formik'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'

import { ILoanProductCreate } from '@/store/interfaces'

type ISettlementAccountsProps = {
  formik: FormikProps<ILoanProductCreate>
  setCurrentStep: (step: string) => void
}
const gls = [
  {
    key: 'interestGl',
    label: 'Interest GL',
  },
  // {
  //   key: 'facilityFeeGl',
  //   label: 'Facility Fee GL',
  // },
  // {
  //   key: 'exciseDutyGl',
  //   label: 'Excise Duty GL',
  // },
  // {
  //   key: 'rollOverGl',
  //   label: 'Roll Over GL',
  // },
  // {
  //   key: 'penaltyGl',
  //   label: 'Penalty GL',
  // },
  {
    key: 'prepaymentGl',
    label: 'Prepayment GL',
  },
  {
    key: 'disbursementGl',
    label: 'Disbursement GL',
  },
  {
    key: 'upfrontInterestLiabilityGl', //required if minimumInterestRecoveryType is Upfront and upfrontInterestRecognitionType is Amortized
    label: 'Upfront Interest Liability GL',
  },
  // {
  //   key: 'repaymentGl',
  //   label: 'Repayment GL',
  // },
]
const SettlementAccountFields: string[] = [
  'interestGl',
  'facilityFeeGl',
  'exciseDutyGl',
  'rollOverGl',
  'penaltyGl',
  'prepaymentGl',
  'disbursementGl',
  'repaymentGl',
  'upfrontInterestLiabilityGl',
]
export const SettlementAccounts = ({
  formik,
  setCurrentStep,
}: ISettlementAccountsProps) => {
  const handleContinue = () => {
    formik.validateForm().then((errors) => {
      const filteredErrors = Object.keys(errors)
        .filter((key) => SettlementAccountFields.includes(key))
        .reduce((obj: { [key: string]: string }, key) => {
          if (errors[key] !== undefined) {
            obj[key] = errors[key] || ''
          }
          return obj
        }, {})
      if (Object.keys(filteredErrors).length === 0) {
        setCurrentStep('Confirmation')
      } else {
        SettlementAccountFields.forEach((field) => {
          formik.setFieldTouched(field, true)
        })
      }
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Typography variant="subtitle1">Settlement Accounts</Typography>
      <Typography variant="subtitle2">
        Add the settlement accounts associated with this product
      </Typography>
      {gls.map((gl) => (
        <Stack
          sx={{
            flexDirection: 'column',
            width: '60%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            display: `${gl.key === 'upfrontInterestLiabilityGl' && formik.values.upfrontInterestRecognitionType !== 'Amortized' ? 'none' : 'flex'}`,
          }}
          key={gl.key}
        >
          <Typography variant="subtitle2" color={'primary.main'}>
            {gl.label}
          </Typography>
          <Stack
            direction="row"
            justifyContent={'space-between'}
            alignItems={'center'}
            alignContent={'center'}
            gap={'2%'}
          >
            <TextField
              margin={'normal'}
              fullWidth
              size="small"
              label="Enter Account Number"
              {...formik.getFieldProps(gl.key)}
              error={Boolean(formik.touched[gl.key] && formik.errors[gl.key])}
              helperText={formik.touched[gl.key] && formik.errors[gl.key]}
            />
            <Button
              variant="contained"
              startIcon={<AddOutlinedIcon />}
              sx={{ marginTop: '5px' }}
            >
              Save
            </Button>
          </Stack>
        </Stack>
      ))}
      <Stack
        sx={{
          flexDirection: 'row',
          width: '60%',
          borderRadius: '12px',
          gap: '10%',
        }}
      >
        <Button
          variant="outlined"
          type={'button'}
          fullWidth
          sx={{
            my: '2%',
          }}
          startIcon={<ArrowBackIcon />}
          onClick={() => setCurrentStep('Loan Settings')}
        >
          Back
        </Button>
        <Button
          variant="contained"
          type={'button'}
          fullWidth
          sx={{
            my: '2%',
          }}
          endIcon={<ArrowForwardIcon />}
          onClick={handleContinue}
        >
          Confirm
        </Button>
      </Stack>
    </Stack>
  )
}
