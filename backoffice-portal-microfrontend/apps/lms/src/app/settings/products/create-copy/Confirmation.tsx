import { FiberManualRecord } from '@mui/icons-material'
import {
  Button,
  IconButton,
  List,
  ListItem,
  Stack,
  Typography,
} from '@mui/material'
import React from 'react'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import { FormikProps } from 'formik'
import BorderColorOutlinedIcon from '@mui/icons-material/BorderColorOutlined'
import { ILoanProductCreate } from '@/store/interfaces'

type IConfirmProps = {
  formik: FormikProps<ILoanProductCreate>
  setCurrentStep: (step: string) => void
}
export const Confirmation = ({ formik, setCurrentStep }: IConfirmProps) => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Typography variant="subtitle1">Confirm</Typography>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
          border: '1.5px solid #D0D5DD',
          gap: '1vh',
        }}
      >
        <Stack
          direction="row"
          justifyContent={'space-between'}
          alignContent={'center'}
          alignItems={'center'}
        >
          <Typography variant="subtitle2" color={'primary.main'}>
            Basic Details
          </Typography>
          <IconButton
            onClick={() => {
              setCurrentStep('Basic Details')
            }}
          >
            <BorderColorOutlinedIcon color="info" />
          </IconButton>
        </Stack>
        <Stack>
          <Typography variant="body2">Product Name</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {formik.values.name}
          </Typography>
        </Stack>
        <Stack>
          <Typography variant="body2">Exposure Limit</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {' '}
            {formik.values.exposureLimit}
          </Typography>
        </Stack>
        <Stack direction="row" justifyContent={'space-between'}>
          <Stack>
            <Typography variant="body2">Organization</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.organizationId}
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="body2">Country</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.country}
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="body2">Currency</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.currency}
            </Typography>
          </Stack>
        </Stack>
        <Stack>
          <Typography variant="body2">Expiry Date</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {' '}
            {formik.values.expiryDate}
          </Typography>
        </Stack>
      </Stack>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
          border: '1.5px solid #D0D5DD',
        }}
        gap={'1vh'}
      >
        <Stack
          direction="row"
          justifyContent={'space-between'}
          alignContent={'center'}
          alignItems={'center'}
        >
          <Typography variant="subtitle2" color={'primary.main'}>
            Loan Settings
          </Typography>
          <IconButton
            onClick={() => {
              setCurrentStep('Loan Settings')
            }}
          >
            <BorderColorOutlinedIcon color="info" />
          </IconButton>
        </Stack>
        <Stack direction="row" gap={'2%'}>
          <Stack>
            <Typography variant="body2">Loan amount Limit</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.minimumAmount} - {formik.values.maximumAmount}
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="body2">Loan Tenure Limits</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.minimumTenure} - {formik.values.maximumTenure}{' '}
              {formik.values.measureOfTenure}
            </Typography>
          </Stack>
        </Stack>
        <Stack direction="row" gap={'2%'}>
          <Stack>
            <Typography variant="body2">Interest Rate Type</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.interestRateType}
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="body2">Interest Rate</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.interestRate}
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="body2">Interest Recovery Type</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.interestRecoveryType}
            </Typography>
          </Stack>
        </Stack>
        <Stack direction="row" gap={'2%'}>
          <Stack>
            <Typography variant="body2">Prepayment Type</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.prepaymentType}
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="body2">Prepayment Calculation</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.prepaymentCalculation}
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="body2">Prepayment Value</Typography>
            <Typography variant="body1" color={'primary.main'}>
              {' '}
              {formik.values.prepaymentValue}
            </Typography>
          </Stack>
        </Stack>
        <Stack>
          <Typography variant="body2">Repayment Cycle</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {' '}
            {formik.values.repaymentCycle}
          </Typography>
        </Stack>
        <Stack>
          <Typography variant="body2">Grace period Type</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {' '}
            {formik.values.gracePeriodType}
          </Typography>
        </Stack>
      </Stack>
      {/*<Stack*/}
      {/*  sx={{*/}
      {/*    flexDirection: 'column',*/}
      {/*    width: '60%',*/}
      {/*    background: '#FFFFFF',*/}
      {/*    padding: '2%',*/}
      {/*    borderRadius: '12px',*/}
      {/*    border: '1.5px solid #D0D5DD',*/}
      {/*  }}*/}
      {/*  gap={'1vh'}*/}
      {/*>*/}
      {/*  <Stack*/}
      {/*    direction="row"*/}
      {/*    justifyContent={'space-between'}*/}
      {/*    alignContent={'center'}*/}
      {/*    alignItems={'center'}*/}
      {/*  >*/}
      {/*    <Typography variant="subtitle2" color={'primary.main'}>*/}
      {/*      Cost of Credit*/}
      {/*    </Typography>*/}
      {/*    <IconButton*/}
      {/*      onClick={() => {*/}
      {/*        setCurrentStep('Cost of Credit')*/}
      {/*      }}*/}
      {/*    >*/}
      {/*      <BorderColorOutlinedIcon color="info" />*/}
      {/*    </IconButton>*/}
      {/*  </Stack>*/}
      {/*  <Stack direction="row" gap={'2%'}>*/}
      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Facility Fee (%)</Typography>*/}
      {/*      <Typography variant="body1" color={'primary.main'}>*/}
      {/*        {' '}*/}
      {/*        {formik.values.facilityFee}*/}
      {/*      </Typography>*/}
      {/*    </Stack>*/}
      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Facility Fee Recovery Type</Typography>*/}
      {/*      <Typography variant="body1" color={'primary.main'}>*/}
      {/*        {' '}*/}
      {/*        {formik.values.facilityFeeRecoveryType}*/}
      {/*      </Typography>*/}
      {/*    </Stack>*/}
      {/*  </Stack>*/}

      {/*  <Stack direction="row" gap={'2%'}>*/}
      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Roll Over Fee (%)</Typography>*/}
      {/*      <Typography variant="body1" color={'primary.main'}>*/}
      {/*        {' '}*/}
      {/*        {formik.values.rollOverFee}*/}
      {/*      </Typography>*/}
      {/*    </Stack>*/}

      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Roll Over Period</Typography>*/}
      {/*      <Typography variant="body1" color={'primary.main'}>*/}
      {/*        {' '}*/}
      {/*        {formik.values.rollOverPeriod}*/}
      {/*      </Typography>*/}
      {/*    </Stack>*/}
      {/*  </Stack>*/}
      {/*</Stack>*/}
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
          border: '1.5px solid #D0D5DD',
        }}
        gap={'1vh'}
      >
        <Stack
          direction="row"
          justifyContent={'space-between'}
          alignContent={'center'}
          alignItems={'center'}
        >
          <Typography variant="subtitle2" color={'primary.main'}>
            Settlement Accounts
          </Typography>
          <IconButton
            onClick={() => {
              setCurrentStep('Settlement Accounts')
            }}
          >
            <BorderColorOutlinedIcon color="info" />
          </IconButton>
        </Stack>
        {/*<Stack*/}
        {/*  direction="column"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Typography variant="body2">Roll Over Fee GL</Typography>*/}
        {/*  <Typography variant="body1" color={'primary.main'}>*/}
        {/*    {' '}*/}
        {/*    {formik.values.rollOverGl}*/}
        {/*  </Typography>*/}
        {/*</Stack>*/}
        {/*<Stack*/}
        {/*  direction="column"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Typography variant="body2">Penalty GL</Typography>*/}
        {/*  <Typography variant="body1" color={'primary.main'}>*/}
        {/*    {' '}*/}
        {/*    {formik.values.penaltyGl}*/}
        {/*  </Typography>*/}
        {/*</Stack>*/}
        <Stack
          direction="column"
          sx={{
            width: '100%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1.5px solid #D0D5DD',
          }}
        >
          <Typography variant="body2">Prepayment GL</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {' '}
            {formik.values.prepaymentGl}
          </Typography>
        </Stack>
        <Stack
          direction="column"
          sx={{
            width: '100%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1.5px solid #D0D5DD',
          }}
        >
          <Typography variant="body2">Disbursement GL</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {' '}
            {formik.values.disbursementGl}
          </Typography>
        </Stack>
        {/*<Stack*/}
        {/*  direction="column"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Typography variant="body2">Repayment GL</Typography>*/}
        {/*  <Typography variant="body1" color={'primary.main'}>*/}
        {/*    {' '}*/}
        {/*    {formik.values.repaymentGl}*/}
        {/*  </Typography>*/}
        {/*</Stack>*/}
        <Stack
          direction="column"
          sx={{
            width: '100%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1.5px solid #D0D5DD',
          }}
        >
          <Typography variant="body2">Interest GL</Typography>
          <Typography variant="body1" color={'primary.main'}>
            {' '}
            {formik.values.interestGl}
          </Typography>
        </Stack>
        {/*<Stack*/}
        {/*  direction="column"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Typography variant="body2">Facility Fee GL</Typography>*/}
        {/*  <Typography variant="body1" color={'primary.main'}>*/}
        {/*    {' '}*/}
        {/*    {formik.values.facilityFeeGl}*/}
        {/*  </Typography>*/}
        {/*</Stack>*/}
        {/*<Stack*/}
        {/*  direction="column"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Typography variant="body2">Excise Duty GL</Typography>*/}
        {/*  <Typography variant="body1" color={'primary.main'}>*/}
        {/*    {' '}*/}
        {/*    {formik.values.exciseDutyGl}*/}
        {/*  </Typography>*/}
        {/*</Stack>*/}
      </Stack>
      <Button
        variant="contained"
        sx={{ width: '60%' }}
        endIcon={<ArrowForwardIcon />}
        type={'submit'}
      >
        Submit
      </Button>
      {Object.keys(formik.errors).length > 0 && (
        <List
          sx={{
            width: '60%',
          }}
        >
          <Typography variant="subtitle2" color={'error'}>
            Please correct the following errors
          </Typography>

          {Object.keys(formik.errors).map((error) => (
            <ListItem
              key={error}
              // divider={true}
              sx={{
                textAlign: 'left',
                color: 'error.main',
                gap: '10px',
              }}
            >
              <FiberManualRecord
                sx={{
                  fontSize: '10px',
                }}
              />
              {formik.errors[error]}
            </ListItem>
          ))}
        </List>
      )}
    </Stack>
  )
}
