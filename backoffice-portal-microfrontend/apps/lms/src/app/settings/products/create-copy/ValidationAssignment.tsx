import {
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormControlLabelProps,
  FormGroup,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'

import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import { addProductValidation, getAllValidations } from '@/store/actions'

type IValidationAssignmentProps = {
  setCurrentStep: (step: string) => void
}
const CustomFormControlLabel = styled((props: FormControlLabelProps) => (
  <FormControlLabel {...props} />
))(({ theme }) => ({
  '&.MuiFormControlLabel-root': {
    justifyContent: 'space-between',
    background: '#F7F7F7',
    borderRadius: '8px',
    padding: '1%',
    marginBottom: '16px',
  },
  '.MuiFormControlLabel-label': {
    color: theme.palette.primary.main,
    fontWeight: 600,
    fontSize: '16px',
  },
}))
export const ValidationAssignment = ({
  setCurrentStep,
}: IValidationAssignmentProps) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { loanValidations, createdProduct } = useAppSelector(
    (state) => state.loans
  )
  const [selectedIds, setSelectedIds] = useState<
    {
      validationId: string
      stage: string
      validDays: number
      customerType: string
      optional: boolean
    }[]
  >([])

  const handleCompletion = async () => {
    setCurrentStep('Basic Details')
    await addProductValidation(selectedIds, createdProduct.id, dispatch)
    router.push('/settings/products')
  }

  const handleSelect = (id: string) => {
    if (selectedIds.some((item) => item.validationId === id)) {
      setSelectedIds(selectedIds.filter((item) => item.validationId !== id))
    } else {
      setSelectedIds([
        ...selectedIds,
        {
          validationId: id,
          stage: '',
          validDays: 0,
          customerType: createdProduct.customerType,
          optional: false,
        },
      ])
    }
  }
  const handleStageChange = (stage: string, valId: string) => {
    const newSelectedIds = selectedIds.map((item) => {
      if (item.validationId === valId) {
        return { ...item, stage }
      }
      return item
    })
    setSelectedIds(newSelectedIds)
  }
  const handleValidDaysChange = (days: number, valId: string) => {
    const newSelectedIds = selectedIds.map((item) => {
      if (item.validationId === valId) {
        return { ...item, validDays: days }
      }
      return item
    })
    setSelectedIds(newSelectedIds)
  }
  const handleIsOptionalChange = (isOptional: boolean, valId: string) => {
    const newSelectedIds = selectedIds.map((item) => {
      if (item.validationId === valId) {
        return { ...item, optional: isOptional }
      }
      return item
    })
    setSelectedIds(newSelectedIds)
  }
  useEffect(() => {
    getAllValidations(dispatch)
  }, [])
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Typography variant="subtitle1">
        Success! Product has been created successfully
      </Typography>
      <Typography variant="subtitle2" sx={{ textAlign: 'center' }}>
        One Last Thing.
      </Typography>
      <Typography variant="subtitle2" sx={{ textAlign: 'center' }}>
        You are creating an IPF product for customers. Please specify details
        required to verify the <br /> identity of individual customers who
        qualify for this loan. Select all that apply.
      </Typography>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
        }}
      >
        <Typography
          variant="subtitle2"
          textAlign="center"
          color={'primary.main'}
        >
          KYC verification Options
        </Typography>

        <FormControl margin={'normal'} fullWidth>
          <FormGroup>
            {loanValidations &&
              loanValidations.map((validation) => (
                <Stack key={validation.id} direction="column">
                  <CustomFormControlLabel
                    value={validation.id}
                    control={<Checkbox />}
                    checked={selectedIds.some(
                      (item) => item.validationId === validation.id
                    )}
                    onChange={() => handleSelect(validation.id)}
                    label={validation.code}
                    labelPlacement={'start'}
                  />
                  {selectedIds.some(
                    (item) => item.validationId === validation.id
                  ) && (
                    <Stack
                      sx={{
                        mx: '10%',
                        paddingBottom: '6%',
                      }}
                    >
                      <FormControl>
                        <InputLabel>Stage</InputLabel>
                        <Select
                          label={'Stage'}
                          value={
                            selectedIds.find(
                              (item) => item.validationId === validation.id
                            )?.stage
                              ? selectedIds.find(
                                  (item) => item.validationId === validation.id
                                )?.stage
                              : ''
                          }
                          onChange={(e) =>
                            handleStageChange(
                              e.target.value as string,
                              validation.id
                            )
                          }
                        >
                          <MenuItem value={'Optin'}>Opt in</MenuItem>
                          <MenuItem value={'Request'}>Request</MenuItem>
                        </Select>
                      </FormControl>

                      <TextField
                        margin={'normal'}
                        type={'number'}
                        label={'Valid For X Days'}
                        value={
                          selectedIds.find(
                            (item) => item.validationId === validation.id
                          )?.validDays
                            ? selectedIds.find(
                                (item) => item.validationId === validation.id
                              )?.validDays
                            : null
                        }
                        onChange={(e) =>
                          handleValidDaysChange(
                            parseInt(e.target.value),
                            validation.id
                          )
                        }
                      />
                      <FormControl>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={
                                selectedIds.find(
                                  (item) => item.validationId === validation.id
                                )?.optional
                                  ? selectedIds.find(
                                      (item) =>
                                        item.validationId === validation.id
                                    )?.optional
                                  : false
                              }
                              onChange={(e) =>
                                handleIsOptionalChange(
                                  e.target.checked as boolean,
                                  validation.id
                                )
                              }
                            />
                          }
                          label={'Is Optional'}
                        />
                      </FormControl>
                    </Stack>
                  )}
                </Stack>
              ))}
          </FormGroup>
        </FormControl>
      </Stack>
      <Button
        variant="contained"
        type={'button'}
        sx={{
          my: '2%',
          width: '60%',
        }}
        endIcon={<ArrowForwardIcon />}
        onClick={handleCompletion}
      >
        Complete
      </Button>
    </Stack>
  )
}
