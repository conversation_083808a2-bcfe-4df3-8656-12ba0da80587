'use client'

import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import {
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormControlLabelProps,
  FormGroup,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import {
  addProductValidation,
  getAllValidations,
  getValidationsByProductId,
} from '@/store/actions'
import { CustomSkeleton, LoadingButton } from '@dtbx/ui/components/Loading'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'

const CustomFormControlLabel = styled((props: FormControlLabelProps) => (
  <FormControlLabel {...props} />
))(({ theme }) => ({
  '&.MuiFormControlLabel-root': {
    justifyContent: 'space-between',
    background: '#F7F7F7',
    borderRadius: '8px',
    padding: '1%',
    marginBottom: '16px',
  },
  '.MuiFormControlLabel-label': {
    color: theme.palette.primary.main,
    fontWeight: 600,
    fontSize: '16px',
  },
}))
const ValidationAssignmentPage = () => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const {
    loanValidations,
    productToAssignValidations,
    isLoadingAddValidation,
    singleProductValidations,
    isLoadingProductValidations,
  } = useAppSelector((state) => state.loans)
  const [selectedIds, setSelectedIds] = useState<
    {
      validationId: string
      stage: string
      validDays: number
      customerType: string
      optional: boolean
    }[]
  >(
    singleProductValidations.map((item) => ({
      validationId: item.validation.id,
      stage: item.stage,
      validDays: item.validDays,
      customerType: 'Individual',
      optional: item.optional,
    })) || []
  )

  const handleCompletion = async () => {
    await addProductValidation(
      selectedIds,
      productToAssignValidations.id,
      dispatch
    )
    router.push('/settings/products')
  }
  const handleSelect = (id: string) => {
    if (selectedIds.some((item) => item.validationId === id)) {
      setSelectedIds(selectedIds.filter((item) => item.validationId !== id))
    } else {
      setSelectedIds([
        ...selectedIds,
        {
          validationId: id,
          stage: '',
          validDays: 0,
          customerType: productToAssignValidations.customerType,
          optional: false,
        },
      ])
    }
  }
  const handleStageChange = (stage: string, valId: string) => {
    const newSelectedIds = selectedIds.map((item) => {
      if (item.validationId === valId) {
        return { ...item, stage }
      }
      return item
    })
    setSelectedIds(newSelectedIds)
  }
  const handleCustomerTypeChange = (customerType: string, valId: string) => {
    const newSelectedIds = selectedIds.map((item) => {
      if (item.validationId === valId) {
        return { ...item, customerType }
      }
      return item
    })
    setSelectedIds(newSelectedIds)
  }
  const handleIsOptionalChange = (isOptional: boolean, valId: string) => {
    const newSelectedIds = selectedIds.map((item) => {
      if (item.validationId === valId) {
        return { ...item, optional: isOptional }
      }
      return item
    })
    setSelectedIds(newSelectedIds)
  }
  const handleValidDaysChange = (days: number, valId: string) => {
    const newSelectedIds = selectedIds.map((item) => {
      if (item.validationId === valId) {
        return { ...item, validDays: days }
      }
      return item
    })
    setSelectedIds(newSelectedIds)
  }
  useEffect(() => {
    getAllValidations(dispatch)
    getValidationsByProductId(productToAssignValidations.id, dispatch)
  }, [])
  useEffect(() => {
    setSelectedIds(
      singleProductValidations.map((item) => ({
        validationId: item.validation.id,
        stage: item.stage,
        validDays: item.validDays,
        customerType: 'Individual',
        optional: item.optional,
      })) || []
    )
  }, [singleProductValidations])
  return (
    <Stack sx={{}}>
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          width: '3vw',
          marginLeft: '2%',
          marginTop: '1%',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => window.history.back()}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>
      <Stack
        sx={{
          flexDirection: 'column',
          gap: '1vh',
          alignItems: 'center',
          alignContent: 'center',
        }}
      >
        <Typography variant="subtitle1">
          Assign Validations to {productToAssignValidations.name}
        </Typography>
        <Typography variant="subtitle2" sx={{ textAlign: 'center' }}>
          One Last Thing.
        </Typography>
        <Typography variant="subtitle2" sx={{ textAlign: 'center' }}>
          You are updating {productToAssignValidations.name} for{' '}
          {productToAssignValidations.customerType} customers. Please specify
          details required to verify the <br /> identity of{' '}
          {productToAssignValidations.customerType} customers who qualify for
          this loan. Select all that apply.
        </Typography>
        <Stack
          sx={{
            flexDirection: 'column',
            width: '60%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
          }}
        >
          <Typography
            variant="subtitle2"
            textAlign="center"
            color={'primary.main'}
          >
            Verification Options
          </Typography>
          {isLoadingProductValidations ? (
            Array.from({ length: 6 }).map((_, index) => {
              return (
                <CustomSkeleton
                  key={index}
                  variant="rectangular"
                  sx={{
                    height: '6vh',
                    marginBottom: '1vh',
                  }}
                />
              )
            })
          ) : (
            <FormControl margin={'normal'} fullWidth>
              <FormGroup>
                {loanValidations &&
                  loanValidations.map((validation) => (
                    <Stack key={validation.id} direction="column">
                      <CustomFormControlLabel
                        value={validation.id}
                        control={<Checkbox />}
                        checked={selectedIds.some(
                          (item) => item.validationId === validation.id
                        )}
                        onChange={() => handleSelect(validation.id)}
                        label={validation.code}
                        labelPlacement={'start'}
                      />
                      {selectedIds.some(
                        (item) => item.validationId === validation.id
                      ) && (
                        <Stack
                          sx={{
                            mx: '10%',
                            paddingBottom: '6%',
                          }}
                        >
                          <FormControl margin="normal">
                            <InputLabel>Customer Type</InputLabel>
                            <Select
                              label={'Customer Type'}
                              value={
                                selectedIds.find(
                                  (item) => item.validationId === validation.id
                                )?.customerType
                                  ? selectedIds.find(
                                      (item) =>
                                        item.validationId === validation.id
                                    )?.customerType
                                  : ''
                              }
                              onChange={(e) =>
                                handleCustomerTypeChange(
                                  e.target.value as string,
                                  validation.id
                                )
                              }
                            >
                              <MenuItem value={'Individual'}>
                                Individual
                              </MenuItem>
                              <MenuItem value={'Business'}>Business</MenuItem>
                              <MenuItem value={'Both'}>
                                Both Businesses and Individuals
                              </MenuItem>
                            </Select>
                          </FormControl>

                          <FormControl>
                            <InputLabel>Stage</InputLabel>
                            <Select
                              label={'Stage'}
                              value={
                                selectedIds.find(
                                  (item) => item.validationId === validation.id
                                )?.stage
                                  ? selectedIds.find(
                                      (item) =>
                                        item.validationId === validation.id
                                    )?.stage
                                  : ''
                              }
                              onChange={(e) =>
                                handleStageChange(
                                  e.target.value as string,
                                  validation.id
                                )
                              }
                            >
                              <MenuItem value={'Optin'}>Opt in</MenuItem>
                              <MenuItem value={'Request'}>Request</MenuItem>
                            </Select>
                          </FormControl>

                          <TextField
                            margin={'normal'}
                            type={'number'}
                            label={'Valid For X Days'}
                            value={
                              selectedIds.find(
                                (item) => item.validationId === validation.id
                              )?.validDays
                                ? selectedIds.find(
                                    (item) =>
                                      item.validationId === validation.id
                                  )?.validDays
                                : null
                            }
                            onChange={(e) =>
                              handleValidDaysChange(
                                parseInt(e.target.value),
                                validation.id
                              )
                            }
                          />
                          <FormControl>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={
                                    selectedIds.find(
                                      (item) =>
                                        item.validationId === validation.id
                                    )?.optional
                                      ? selectedIds.find(
                                          (item) =>
                                            item.validationId === validation.id
                                        )?.optional
                                      : false
                                  }
                                  onChange={(e) =>
                                    handleIsOptionalChange(
                                      e.target.checked as boolean,
                                      validation.id
                                    )
                                  }
                                />
                              }
                              label={'Is Optional'}
                            />
                          </FormControl>
                        </Stack>
                      )}
                    </Stack>
                  ))}
              </FormGroup>
            </FormControl>
          )}
        </Stack>
        {isLoadingAddValidation ? (
          <LoadingButton width="60%" />
        ) : (
          <Button
            variant="contained"
            type={'button'}
            sx={{
              my: '2%',
              width: '60%',
            }}
            endIcon={<ArrowForwardIcon />}
            onClick={handleCompletion}
          >
            Complete
          </Button>
        )}
      </Stack>
    </Stack>
  )
}

export default ValidationAssignmentPage
