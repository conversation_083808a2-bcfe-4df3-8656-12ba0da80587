'use client'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { Button, IconButton, Paper, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { useAppDispatch, useAppSelector } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { getLoanProducts } from '@/store/actions'
import { LoadingListsSkeleton } from '@dtbx/ui/components/Loading'

import { ProductsList } from '@/app/settings/products/ProductsList'
import { AccessControlWrapper } from '@dtbx/store/utils'

const ProductsPage = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { isLoadingLoanProducts } = useAppSelector((state) => state.loans)
  useEffect(() => {
    const params = 'page=1&size=10'
    getLoanProducts(dispatch, params)
  }, [])
  return (
    <Paper
      elevation={0}
      sx={{
        px: '2%',
        background: '#F7F7F7',
        minHeight: '94vh',
        py: '2%',
      }}
    >
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => window.history.back()}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>
      <Typography
        variant="h5"
        sx={{
          textAlign: 'left',
          py: '1%',
        }}
      >
        Manage Products
      </Typography>

      <Stack direction="row" justifyContent={'flex-end'}>
        {/*<CustomSearchInput*/}
        {/*  placeholder={'Search Product'}*/}
        {/*  endAdornment={*/}
        {/*    <SearchRounded*/}
        {/*      sx={{*/}
        {/*        color: 'black',*/}
        {/*      }}*/}
        {/*    />*/}
        {/*  }*/}
        {/*  sx={{*/}
        {/*    width: '30%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    borderRadius: '8px',*/}
        {/*    border: '1px solid #D0D5DD',*/}
        {/*    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',*/}
        {/*  }}*/}
        {/*/>*/}
        <AccessControlWrapper
          rights={['SUPER_CREATE_PRODUCT', 'MAKE_CREATE_PRODUCT']}
        >
          <Button
            variant="contained"
            endIcon={<AddOutlinedIcon />}
            onClick={() => router.push('/settings/products/create')}
          >
            Add New Product
          </Button>
        </AccessControlWrapper>
      </Stack>
      {isLoadingLoanProducts ? <LoadingListsSkeleton /> : <ProductsList />}
    </Paper>
  )
}
export default ProductsPage
