'use client'

import {
  ArrowFor<PERSON>Ios,
  ContentCopy,
  MoreVertOutlined,
} from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { IconButton, Menu, MenuItem } from '@mui/material'
import React, { useState } from 'react'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { ILoanProduct } from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import {
  setProductToAssignValidations,
  setProductToCopy,
  setProductToView,
} from '@/store/reducers'

export const ProductMoreMenu = ({ product }: { product: ILoanProduct }) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const handleCreateCopy = () => {
    dispatch(setProductToCopy(product))
    router.push('/settings/products/create-copy')
  }
  const handleAssignValidations = () => {
    dispatch(setProductToAssignValidations(product))
    router.push('/settings/products/validations')
  }
  const handleViewProduct = () => {
    dispatch(setProductToView(product))
    router.push(`/settings/products/view`)
    handleClose()
  }
  return (
    <>
      <IconButton onClick={handleClick}>
        <MoreVertOutlined />
      </IconButton>
      <Menu open={open} anchorEl={anchorEl} onClose={handleClose}>
        <MenuItem
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: '10px',
          }}
          onClick={handleViewProduct}
        >
          View Product
          <ArrowForwardIos fontSize="small" />
        </MenuItem>
        <MenuItem
          onClick={handleCreateCopy}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: '10px',
          }}
        >
          Create a copy
          <ContentCopy />
        </MenuItem>
        <MenuItem
          onClick={handleAssignValidations}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: '10px',
          }}
        >
          Manage Validations
          <AddOutlinedIcon fontSize="small" />
        </MenuItem>
      </Menu>
    </>
  )
}
