import CloseIcon from '@mui/icons-material/Close'
import {
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormControlLabelProps,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect } from 'react'
import { FormikProps } from 'formik'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { updateLoanProduct } from '@/store/actions'
import { ILoanProductCreate } from '@/store/interfaces'
import { useAppDispatch } from '@/store'

type ICostOfCreditProps = {
  formik: FormikProps<ILoanProductCreate>
  setCurrentStep: (step: string) => void
  productId: string
}
const CustomFormControlLabel = styled((props: FormControlLabelProps) => (
  <FormControlLabel {...props} />
))(({ theme }) => ({
  '&.MuiFormControlLabel-root': {
    justifyContent: 'space-between',
    background: '#F7F7F7',
    borderRadius: '8px',
    padding: '1%',
    marginBottom: '16px',
  },
  '.MuiFormControlLabel-label': {
    color: theme.palette.primary.main,
    fontWeight: 600,
    fontSize: '16px',
  },
}))
export const facilityFeeRecoveryTypes = ['Upfront', 'Spread']
const CostOfCreditFields: string[] = [
  'facilityFee',
  'facilityFeeRecoveryType',
  'rollOverFee',
  'maxRollOverCount',
  'rollOverPeriod',
  'exciseDuty',
  'penalty',
  'periodInArrears',
  'tranches',
  'trancheInterval',
]
export const EditCostOfCredit = ({
  formik,
  setCurrentStep,
  productId,
}: ICostOfCreditProps) => {
  const dispatch = useAppDispatch()
  useEffect(() => {
    formik.values.rollOverFee === 0 &&
      formik.setFieldValue('maxRollOverCount', 0)
  }, [formik.values.rollOverFee])
  const handleSave = () => {
    formik.validateForm().then(async (errors) => {
      const filteredErrors = Object.keys(errors)
        .filter((key) => CostOfCreditFields.includes(key))
        .reduce((obj: { [key: string]: string }, key) => {
          if (errors[key] !== undefined) {
            obj[key] = errors[key] || ''
          }
          return obj
        }, {})
      if (Object.keys(filteredErrors).length === 0) {
        await updateLoanProduct(productId, formik.values, dispatch)
        setCurrentStep('View')
      } else {
        CostOfCreditFields.forEach((field) => {
          formik.setFieldTouched(field, true)
        })
      }
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Typography variant="subtitle1">Cost of Credit</Typography>
      <Typography variant="subtitle2">Facility fees and Rollovers</Typography>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
        }}
      >
        <Typography variant="subtitle1">Facility Fee</Typography>
        <TextField
          margin={'normal'}
          fullWidth
          label="Facility Fee"
          {...formik.getFieldProps('facilityFee')}
          error={Boolean(
            formik.touched.facilityFee && formik.errors.facilityFee
          )}
          helperText={formik.touched.facilityFee && formik.errors.facilityFee}
        />
        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Select Facility Fee Recovery Type</InputLabel>
          <Select
            {...formik.getFieldProps('facilityFeeRecoveryType')}
            name={'facilityFeeRecoveryType'}
            error={Boolean(
              formik.touched.facilityFeeRecoveryType &&
                formik.errors.facilityFeeRecoveryType
            )}
            label={'Select Facility Fee Recovery Type'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {facilityFeeRecoveryTypes.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Typography variant="subtitle1">Rollovers</Typography>
        <TextField
          margin={'normal'}
          fullWidth
          label="Rollover Fee"
          {...formik.getFieldProps('rollOverFee')}
          error={Boolean(
            formik.touched.rollOverFee && formik.errors.rollOverFee
          )}
          helperText={formik.touched.rollOverFee && formik.errors.rollOverFee}
        />
        {formik.values.rollOverFee > 0 && (
          <TextField
            margin={'normal'}
            fullWidth
            label="Maximum Roll Over Count"
            {...formik.getFieldProps('maxRollOverCount')}
            error={Boolean(
              formik.touched.maxRollOverCount && formik.errors.maxRollOverCount
            )}
            helperText={
              formik.touched.maxRollOverCount && formik.errors.maxRollOverCount
            }
          />
        )}
        <TextField
          margin={'normal'}
          fullWidth
          label="Rollover Period"
          {...formik.getFieldProps('rollOverPeriod')}
          error={Boolean(
            formik.touched.rollOverPeriod && formik.errors.rollOverPeriod
          )}
          helperText={
            formik.touched.rollOverPeriod && formik.errors.rollOverPeriod
          }
        />
        <Typography variant="subtitle1">Excise Duty</Typography>
        <TextField
          margin={'normal'}
          fullWidth
          label="Excise Duty"
          {...formik.getFieldProps('exciseDuty')}
          error={Boolean(formik.touched.exciseDuty && formik.errors.exciseDuty)}
          helperText={formik.touched.exciseDuty && formik.errors.exciseDuty}
        />
        <Typography variant="subtitle1">Penalties</Typography>
        <TextField
          margin={'normal'}
          fullWidth
          label="Penalty"
          {...formik.getFieldProps('penalty')}
          error={Boolean(formik.touched.penalty && formik.errors.penalty)}
          helperText={formik.touched.penalty && formik.errors.penalty}
        />
        <TextField
          margin={'normal'}
          fullWidth
          label="Period In Arrears"
          {...formik.getFieldProps('periodInArrears')}
          error={Boolean(
            formik.touched.periodInArrears && formik.errors.periodInArrears
          )}
          helperText={
            formik.touched.periodInArrears && formik.errors.periodInArrears
          }
        />
        <Typography variant="subtitle1">Tranches</Typography>
        <FormControl fullWidth margin={'normal'}>
          <CustomFormControlLabel
            {...formik.getFieldProps('tranches')}
            control={<Checkbox />}
            label={'Has Tranches'}
            labelPlacement={'start'}
          />
        </FormControl>
        <TextField
          margin={'normal'}
          fullWidth
          label="Tranche Interval"
          {...formik.getFieldProps('trancheInterval')}
          error={Boolean(
            formik.touched.trancheInterval && formik.errors.trancheInterval
          )}
          helperText={
            formik.touched.trancheInterval && formik.errors.trancheInterval
          }
        />
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '3%',
          }}
        >
          <Button
            variant="outlined"
            type={'button'}
            fullWidth
            sx={{
              my: '2%',
            }}
            endIcon={<CloseIcon />}
            onClick={() => setCurrentStep('View')}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type={'button'}
            fullWidth
            sx={{
              my: '2%',
            }}
            endIcon={<ArrowForwardIcon />}
            onClick={handleSave}
          >
            Save Changes
          </Button>
        </Stack>
      </Stack>
    </Stack>
  )
}
