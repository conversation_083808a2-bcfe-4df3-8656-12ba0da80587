import CloseIcon from '@mui/icons-material/Close'
import {
  Autocomplete,
  Button,
  FormControl,
  FormControlLabel,
  FormControlLabelProps,
  FormHelperText,
  FormLabel,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import React, { useEffect, useState } from 'react'
import { FormikProps } from 'formik'
import {
  getLoanProductCategories,
  getLoanProductCategoryTypes,
  getOrganizations,
  updateLoanProduct,
} from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { ILoanProductCreate } from '@/store/interfaces'
import { setProductCategoryTypes } from '@/store/reducers'

const CustomFormControlLabel = styled((props: FormControlLabelProps) => (
  <FormControlLabel {...props} />
))(({ theme }) => ({
  '&.MuiFormControlLabel-root': {
    justifyContent: 'space-between',
    background: '#F7F7F7',
    borderRadius: '8px',
    padding: '1%',
    marginBottom: '16px',
  },
  '.MuiFormControlLabel-label': {
    color: theme.palette.primary.main,
    fontWeight: 600,
    fontSize: '16px',
  },
}))
type IBasicDetailsProps = {
  formik: FormikProps<ILoanProductCreate>
  setCurrentStep: (step: string) => void
  productId: string
}
const BasicDetailsFields: string[] = [
  'name',
  'externalProductName',
  'country',
  'currency',
  'exposureLimit',
  'numberOfInstallments',
  'expiryDate',
  'customerType',
  'typeId',
  'organizationId',
]
export const EditBasicDetails = ({
  formik,
  setCurrentStep,
  productId,
}: IBasicDetailsProps) => {
  const dispatch = useAppDispatch()
  const [categoryId, setCategoryId] = useState<string>('')
  const {
    productCategoryTypes,
    bankBranches,
    productCategories,
    organizations,
  } = useAppSelector((state) => state.loans)
  useEffect(() => {
    categoryId && getLoanProductCategoryTypes(dispatch, categoryId)
    categoryId === '' && formik.setFieldValue('typeId', formik.values.typeId)
  }, [categoryId])
  useEffect(() => {
    formik.values.loanCreationBranch !== 'Default Branch' &&
      formik.setFieldValue('defaultBranchCode', '')
  }, [formik.values.loanCreationBranch])
  useEffect(() => {
    dispatch(setProductCategoryTypes([]))
  }, [])
  const handleSave = () => {
    formik.validateForm().then(async (errors) => {
      const filteredErrors = Object.keys(errors)
        .filter((key) => BasicDetailsFields.includes(key))
        .reduce((obj: { [key: string]: string }, key) => {
          if (errors[key] !== undefined) {
            obj[key] = errors[key] || ''
          }
          return obj
        }, {})
      if (Object.keys(filteredErrors).length === 0) {
        await updateLoanProduct(productId, formik.values, dispatch)
        setCurrentStep('View')
      } else {
        BasicDetailsFields.forEach((field) => {
          formik.setFieldTouched(field, true)
        })
      }
    })
  }
  useEffect(() => {
    getOrganizations(dispatch, 'page=1&size=30')
    getLoanProductCategories(dispatch, 1, 10)
  }, [])
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Typography variant="subtitle1">Basic Details</Typography>
      <Typography variant="subtitle2">
        You are updating {formik.values.name} details
      </Typography>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
        }}
      >
        <TextField
          fullWidth
          margin={'normal'}
          label="Give your product a name"
          {...formik.getFieldProps('name')}
          error={Boolean(formik.touched.name && formik.errors.name)}
          helperText={formik.touched.name && formik.errors.name}
        />
        <TextField
          fullWidth
          margin={'normal'}
          label="Give your product an external name"
          {...formik.getFieldProps('externalProductName')}
          error={Boolean(
            formik.touched.externalProductName &&
              formik.errors.externalProductName
          )}
          helperText={
            formik.touched.externalProductName &&
            formik.errors.externalProductName
          }
        />
        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Select Category</InputLabel>
          <Select
            value={categoryId}
            onChange={(e) => setCategoryId(e.target.value as string)}
            name={'categoryId'}
            label={'Select Category'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {productCategories.length > 0 && (productCategories.map((category) => (
              <MenuItem key={category.id} value={category.id}>
                {category.name}
              </MenuItem>
            )))}
          </Select>
        </FormControl>
        {productCategoryTypes.length > 0 && (
          <FormControl fullWidth margin={'normal'}>
            <InputLabel>Select Type</InputLabel>
            <Select
              {...formik.getFieldProps('typeId')}
              name={'typeId'}
              label={'Select Type'}
              IconComponent={() => <KeyboardArrowDownIcon />}
            >
              {productCategoryTypes.map((type) => (
                <MenuItem key={type.id} value={type.id}>
                  {type.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Loan Creation Branch</InputLabel>
          <Select
            {...formik.getFieldProps('loanCreationBranch')}
            name={'loanCreationBranch'}
            label={'Select Loan Creation Branch'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            <MenuItem
              key={'Nominated Customer Account'}
              value={'Nominated Customer Account'}
            >
              Nominated Customer Account
            </MenuItem>
            <MenuItem key={'Default Branch'} value={'Default Branch'}>
              Default Branch
            </MenuItem>
          </Select>
        </FormControl>
        {formik.values.loanCreationBranch === 'Default Branch' && (
          <FormControl fullWidth margin={'normal'}>
            <Autocomplete
              disablePortal
              id="combo-box-demo"
              options={bankBranches}
              getOptionLabel={(option) => option.branchName}
              value={bankBranches.find(
                (branch) =>
                  formik.values.defaultBranchCode === branch.branchCode
              )}
              onChange={(event, newValue) => {
                formik.setFieldValue('defaultBranchCode', newValue?.branchCode)
              }}
              renderInput={(params) => (
                <TextField {...params} label={'Default Loan Branch'} />
              )}
            />
          </FormControl>
        )}
        <FormControl
          fullWidth
          margin={'normal'}
          error={Boolean(
            formik.touched.organizationId && formik.errors.organizationId
          )}
        >
          <InputLabel>Select Organization</InputLabel>
          <Select
            {...formik.getFieldProps('organizationId')}
            name={'organizationId'}
            label={'Select Organization'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {organizations.map((organization) => (
              <MenuItem key={organization.id} value={organization.id}>
                {organization.name}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.organizationId && formik.errors.organizationId && (
            <FormHelperText>{formik.errors.organizationId}</FormHelperText>
          )}
        </FormControl>
        <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
          <TextField
            margin={'normal'}
            fullWidth
            label="Country"
            {...formik.getFieldProps('country')}
            error={Boolean(formik.touched.country && formik.errors.country)}
            helperText={formik.touched.country && formik.errors.country}
          />
          <TextField
            margin={'normal'}
            fullWidth
            label="Currency"
            {...formik.getFieldProps('currency')}
            error={Boolean(formik.touched.currency && formik.errors.currency)}
            helperText={formik.touched.currency && formik.errors.currency}
          />
        </Stack>

        {/*<Stack direction="row" justifyContent={'space-between'} gap={'2%'}>*/}
        {/*  <TextField*/}
        {/*    fullWidth*/}
        {/*    margin={'normal'}*/}
        {/*    label="Set Exposure Limit"*/}
        {/*    {...formik.getFieldProps('exposureLimit')}*/}
        {/*    error={Boolean(*/}
        {/*      formik.touched.exposureLimit && formik.errors.exposureLimit*/}
        {/*    )}*/}
        {/*    helperText={*/}
        {/*      formik.touched.exposureLimit && formik.errors.exposureLimit*/}
        {/*    }*/}
        {/*  />*/}
        {/*  <TextField*/}
        {/*    fullWidth*/}
        {/*    margin={'normal'}*/}
        {/*    label="Set Number of Installments"*/}
        {/*    {...formik.getFieldProps('numberOfInstallments')}*/}
        {/*    error={Boolean(*/}
        {/*      formik.touched.numberOfInstallments &&*/}
        {/*        formik.errors.numberOfInstallments*/}
        {/*    )}*/}
        {/*    helperText={*/}
        {/*      formik.touched.numberOfInstallments &&*/}
        {/*      formik.errors.numberOfInstallments*/}
        {/*    }*/}
        {/*  />*/}
        {/*</Stack>*/}
        {/*<FormControl fullWidth margin={'normal'}>*/}
        {/*  <LocalizationProvider dateAdapter={AdapterDayjs}>*/}
        {/*    <DatePicker*/}
        {/*      value={dayjs(formik.values.expiryDate)}*/}
        {/*      onChange={(date) => {*/}
        {/*        formik.setFieldValue('expiryDate', date?.format('YYYY-MM-DD'))*/}
        {/*      }}*/}
        {/*      label={'When will this product expire?'}*/}
        {/*    />*/}
        {/*  </LocalizationProvider>*/}
        {/*</FormControl>*/}
        <FormControl
          margin={'normal'}
          fullWidth
          error={Boolean(
            formik.touched.customerType && formik.errors.customerType
          )}
        >
          <FormLabel>
            What kind of customers qualify for this product?
          </FormLabel>
          <RadioGroup {...formik.getFieldProps('customerType')}>
            <CustomFormControlLabel
              value={'Individual'}
              control={<Radio />}
              label="Individuals"
              labelPlacement={'start'}
            />
            <CustomFormControlLabel
              value={'Business'}
              control={<Radio />}
              label="Businesses"
              labelPlacement={'start'}
            />
            <CustomFormControlLabel
              value={'Both'}
              control={<Radio />}
              label="Both Businesses and Individuals"
              labelPlacement={'start'}
            />
          </RadioGroup>
          {formik.touched.customerType && formik.errors.customerType && (
            <FormHelperText>{formik.errors.customerType}</FormHelperText>
          )}
        </FormControl>
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '3%',
          }}
        >
          <Button
            variant="outlined"
            type={'button'}
            fullWidth
            sx={{
              my: '2%',
            }}
            endIcon={<CloseIcon />}
            onClick={() => setCurrentStep('View')}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type={'button'}
            fullWidth
            sx={{
              my: '2%',
            }}
            endIcon={<ArrowForwardIcon />}
            onClick={handleSave}
          >
            Save Changes
          </Button>
        </Stack>
      </Stack>
    </Stack>
  )
}
