import { IconButton, Stack, styled, Typography } from '@mui/material'
import React from 'react'
import BorderColorOutlinedIcon from '@mui/icons-material/BorderColorOutlined'
import { ILoanProduct } from '@/store/interfaces'

const StyledValueTypography = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  fontSize: '20px',
  marginTop: '5px',
  color: theme.palette.primary.main,
}))
type IViewProductProps = {
  productToView: ILoanProduct
  setCurrentStep: (step: string) => void
}
export const ViewProductPage = ({
  productToView,
  setCurrentStep,
}: IViewProductProps) => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        alignContent: 'center',
        alignItems: 'center',
        gap: '2vh',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
          border: '1.5px solid #D0D5DD',
          gap: '1vh',
        }}
      >
        <Stack
          direction="row"
          justifyContent={'space-between'}
          alignContent={'center'}
          alignItems={'center'}
        >
          <StyledValueTypography>Basic Details</StyledValueTypography>
          <IconButton onClick={() => setCurrentStep('Basic Details')}>
            <BorderColorOutlinedIcon color="info" />
          </IconButton>
        </Stack>
        <Stack>
          <Typography variant="body2">Product Name</Typography>
          <StyledValueTypography>
            {productToView.name} - For {productToView.type.name} Customers
          </StyledValueTypography>
        </Stack>
        <Stack>
          <Typography variant="body2">Exposure Limit</Typography>
          <StyledValueTypography>
            {' '}
            {productToView.exposureLimit}
          </StyledValueTypography>
        </Stack>
        <Stack direction="row" justifyContent={'space-between'}>
          <Stack>
            <Typography variant="body2">Organization</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.organization.name}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Country</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.country}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Currency</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.currency}
            </StyledValueTypography>
          </Stack>
        </Stack>
        <Stack direction="row" gap={'10%'}>
          <Stack>
            <Typography variant="body2">Expiry Date</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.expiryDate}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Category Type</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.type.name}
            </StyledValueTypography>
          </Stack>
        </Stack>
      </Stack>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
          border: '1.5px solid #D0D5DD',
        }}
        gap={'1vh'}
      >
        <Stack
          direction="row"
          justifyContent={'space-between'}
          alignContent={'center'}
          alignItems={'center'}
        >
          <StyledValueTypography>Loan Settings</StyledValueTypography>
          <IconButton onClick={() => setCurrentStep('Loan Settings')}>
            <BorderColorOutlinedIcon color="info" />
          </IconButton>
        </Stack>
        <Stack direction="row" gap={'2%'}>
          <Stack>
            <Typography variant="body2">Loan amount Limit</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.minimumAmount} - {productToView.maximumAmount}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Loan Tenure Limits</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.minimumTenure} - {productToView.maximumTenure}{' '}
              {productToView.measureOfTenure}
            </StyledValueTypography>
          </Stack>
        </Stack>
        <Stack direction="row" gap={'2%'}>
          <Stack>
            <Typography variant="body2">Interest Rate Type</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.interestRateType}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Interest Rate</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.interestRate}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Interest Recovery Type</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.interestRecoveryType}
            </StyledValueTypography>
          </Stack>
        </Stack>
        <Stack direction="row" gap={'2%'}>
          <Stack>
            <Typography variant="body2">Prepayment Type</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.prepaymentType}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Prepayment Calculation</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.prepaymentCalculation}
            </StyledValueTypography>
          </Stack>
          <Stack>
            <Typography variant="body2">Prepayment Value</Typography>
            <StyledValueTypography>
              {' '}
              {productToView.prepaymentValue}
            </StyledValueTypography>
          </Stack>
        </Stack>
        <Stack>
          <Typography variant="body2">Repayment Cycle</Typography>
          <StyledValueTypography>
            {' '}
            {productToView.repaymentCycle}
          </StyledValueTypography>
        </Stack>
        <Stack>
          <Typography variant="body2">Grace period Type</Typography>
          <StyledValueTypography>
            {' '}
            {productToView.gracePeriodType}
          </StyledValueTypography>
        </Stack>
      </Stack>
      {/*<Stack*/}
      {/*  sx={{*/}
      {/*    flexDirection: 'column',*/}
      {/*    width: '60%',*/}
      {/*    background: '#FFFFFF',*/}
      {/*    padding: '2%',*/}
      {/*    borderRadius: '12px',*/}
      {/*    border: '1.5px solid #D0D5DD',*/}
      {/*  }}*/}
      {/*  gap={'1vh'}*/}
      {/*>*/}
      {/*  <Stack*/}
      {/*    direction="row"*/}
      {/*    justifyContent={'space-between'}*/}
      {/*    alignContent={'center'}*/}
      {/*    alignItems={'center'}*/}
      {/*  >*/}
      {/*    <StyledValueTypography>Cost of Credit</StyledValueTypography>*/}
      {/*    <IconButton onClick={() => setCurrentStep('Cost of Credit')}>*/}
      {/*      <BorderColorOutlinedIcon color="info" />*/}
      {/*    </IconButton>*/}
      {/*  </Stack>*/}
      {/*  <Stack direction="row" gap={'2%'}>*/}
      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Facility Fee (%)</Typography>*/}
      {/*      <StyledValueTypography>*/}
      {/*        {' '}*/}
      {/*        {productToView.facilityFee}*/}
      {/*      </StyledValueTypography>*/}
      {/*    </Stack>*/}
      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Facility Fee Recovery Type</Typography>*/}
      {/*      <StyledValueTypography>*/}
      {/*        {' '}*/}
      {/*        {productToView.facilityFeeRecoveryType}*/}
      {/*      </StyledValueTypography>*/}
      {/*    </Stack>*/}
      {/*  </Stack>*/}

      {/*  <Stack direction="row" gap={'2%'}>*/}
      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Roll Over Fee (%)</Typography>*/}
      {/*      <StyledValueTypography>*/}
      {/*        {' '}*/}
      {/*        {productToView.rollOverFee}*/}
      {/*      </StyledValueTypography>*/}
      {/*    </Stack>*/}

      {/*    <Stack>*/}
      {/*      <Typography variant="body2">Roll Over Period</Typography>*/}
      {/*      <StyledValueTypography>*/}
      {/*        {' '}*/}
      {/*        {productToView.rollOverPeriod}*/}
      {/*      </StyledValueTypography>*/}
      {/*    </Stack>*/}
      {/*  </Stack>*/}
      {/*</Stack>*/}
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
          border: '1.5px solid #D0D5DD',
        }}
        gap={'1vh'}
      >
        <Stack
          direction="row"
          justifyContent={'space-between'}
          alignContent={'center'}
          alignItems={'center'}
        >
          <StyledValueTypography>Settlement Accounts</StyledValueTypography>
          <IconButton onClick={() => setCurrentStep('Settlement Accounts')}>
            <BorderColorOutlinedIcon color="info" />
          </IconButton>
        </Stack>
        {/*<Stack*/}
        {/*  direction="row"*/}
        {/*  justifyContent="space-between"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Stack>*/}
        {/*    <Typography variant="body1">Roll Over Fee GL</Typography>*/}
        {/*    <Typography variant="body2" color={'primary.main'}>*/}
        {/*      {' '}*/}
        {/*      {productToView.rollOverGl}*/}
        {/*    </Typography>*/}
        {/*  </Stack>*/}
        {/*</Stack>*/}
        {/*<Stack*/}
        {/*  direction="row"*/}
        {/*  justifyContent="space-between"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Stack>*/}
        {/*    <Typography variant="body1">Penalty GL</Typography>*/}
        {/*    <Typography variant="body2" color={'primary.main'}>*/}
        {/*      {' '}*/}
        {/*      {productToView.penaltyGl}*/}
        {/*    </Typography>*/}
        {/*  </Stack>*/}
        {/*</Stack>*/}
        <Stack
          direction="row"
          justifyContent="space-between"
          sx={{
            width: '100%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1.5px solid #D0D5DD',
          }}
        >
          <Stack>
            <Typography variant="body1">Prepayment GL</Typography>
            <Typography variant="body2" color={'primary.main'}>
              {' '}
              {productToView.prepaymentGl}
            </Typography>
          </Stack>
        </Stack>
        <Stack
          direction="row"
          justifyContent="space-between"
          sx={{
            width: '100%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1.5px solid #D0D5DD',
          }}
        >
          <Stack>
            <Typography variant="body1">Disbursement GL</Typography>
            <Typography variant="body2" color={'primary.main'}>
              {' '}
              {productToView.disbursementGl}
            </Typography>
          </Stack>
        </Stack>
        <Stack
          direction="row"
          justifyContent="space-between"
          sx={{
            width: '100%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1.5px solid #D0D5DD',
          }}
        >
          <Stack>
            <Typography variant="body1">Repayment GL</Typography>
            <Typography variant="body2" color={'primary.main'}>
              {' '}
              {productToView.repaymentGl}
            </Typography>
          </Stack>
        </Stack>
        <Stack
          direction="row"
          justifyContent="space-between"
          sx={{
            width: '100%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1.5px solid #D0D5DD',
          }}
        >
          <Stack>
            <Typography variant="body1">Interest GL</Typography>
            <Typography variant="body2" color={'primary.main'}>
              {' '}
              {productToView.interestGl}
            </Typography>
          </Stack>
        </Stack>
        {/*<Stack*/}
        {/*  direction="row"*/}
        {/*  justifyContent="space-between"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Stack>*/}
        {/*    <Typography variant="body1">Facility Fee GL</Typography>*/}
        {/*    <Typography variant="body2" color={'primary.main'}>*/}
        {/*      {' '}*/}
        {/*      {productToView.facilityFeeGl}*/}
        {/*    </Typography>*/}
        {/*  </Stack>*/}
        {/*</Stack>*/}
        {/*<Stack*/}
        {/*  direction="row"*/}
        {/*  justifyContent="space-between"*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    padding: '2%',*/}
        {/*    borderRadius: '12px',*/}
        {/*    border: '1.5px solid #D0D5DD',*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <Stack>*/}
        {/*    <Typography variant="body1">Excise Duty GL</Typography>*/}
        {/*    <Typography variant="body2" color={'primary.main'}>*/}
        {/*      {' '}*/}
        {/*      {productToView.exciseDutyGl}*/}
        {/*    </Typography>*/}
        {/*  </Stack>*/}
        {/*</Stack>*/}
      </Stack>
    </Stack>
  )
}
