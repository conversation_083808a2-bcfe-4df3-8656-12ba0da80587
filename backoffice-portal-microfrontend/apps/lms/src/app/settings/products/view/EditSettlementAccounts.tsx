import CloseIcon from '@mui/icons-material/Close'
import { Button, Stack, TextField, Typography } from '@mui/material'
import React from 'react'
import { FormikProps } from 'formik'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import { updateLoanProduct } from '@/store/actions'
import { useAppDispatch } from '@/store'
import { ILoanProductCreate } from '@/store/interfaces'

type ISettlementAccountsProps = {
  formik: FormikProps<ILoanProductCreate>
  setCurrentStep: (step: string) => void
  productId: string
}
const gls = [
  {
    key: 'interestGl',
    label: 'Interest GL',
  },

  {
    key: 'prepaymentGl',
    label: 'Prepayment GL',
  },
  {
    key: 'disbursementGl',
    label: 'Disbursement GL',
  },
]
const SettlementAccountFields: string[] = [
  'interestGl',
  'facilityFeeGl',
  'exciseDutyGl',
  'rollOverGl',
  'penaltyGl',
  'prepaymentGl',
  'disbursementGl',
  'repaymentGl',
]
export const EditSettlementAccounts = ({
  formik,
  setCurrentStep,
  productId,
}: ISettlementAccountsProps) => {
  const dispatch = useAppDispatch()
  const handleSave = () => {
    formik.validateForm().then(async (errors) => {
      const filteredErrors = Object.keys(errors)
        .filter((key) => SettlementAccountFields.includes(key))
        .reduce((obj: { [key: string]: string }, key) => {
          if (errors[key] !== undefined) {
            obj[key] = errors[key] || ''
          }
          return obj
        }, {})
      if (Object.keys(filteredErrors).length === 0) {
        await updateLoanProduct(productId, formik.values, dispatch)
        setCurrentStep('View')
      } else {
        SettlementAccountFields.forEach((field) => {
          formik.setFieldTouched(field, true)
        })
      }
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Typography variant="subtitle1">Settlement Accounts</Typography>
      <Typography variant="subtitle2">
        Add the settlement accounts associated with this product
      </Typography>
      {gls.map((gl) => (
        <Stack
          sx={{
            flexDirection: 'column',
            width: '60%',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
          }}
          key={gl.key}
        >
          <Typography variant="subtitle2" color={'primary.main'}>
            {gl.label}
          </Typography>
          <Stack
            direction="row"
            justifyContent={'space-between'}
            alignItems={'center'}
            alignContent={'center'}
            gap={'2%'}
          >
            <TextField
              margin={'normal'}
              fullWidth
              size="small"
              label="Enter Account Number"
              {...formik.getFieldProps(gl.key)}
              error={Boolean(formik.touched[gl.key] && formik.errors[gl.key])}
              helperText={formik.touched[gl.key] && formik.errors[gl.key]}
            />
            {/*<Button*/}
            {/*  variant="contained"*/}
            {/*  disabled*/}
            {/*  startIcon={<AddOutlinedIcon />}*/}
            {/*  sx={{ marginTop: '5px' }}*/}
            {/*>*/}
            {/*  Save*/}
            {/*</Button>*/}
          </Stack>
        </Stack>
      ))}
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          gap: '3%',
          width: '58%',
        }}
      >
        <Button
          variant="outlined"
          type={'button'}
          fullWidth
          sx={{
            my: '2%',
          }}
          endIcon={<CloseIcon />}
          onClick={() => setCurrentStep('View')}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          type={'button'}
          fullWidth
          sx={{
            my: '2%',
          }}
          endIcon={<ArrowForwardIcon />}
          onClick={handleSave}
        >
          Save Changes
        </Button>
      </Stack>
    </Stack>
  )
}
