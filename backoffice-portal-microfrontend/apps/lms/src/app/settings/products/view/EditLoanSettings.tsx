import CloseIcon from '@mui/icons-material/Close'
import { FormikProps } from 'formik'
import {
  Autocomplete,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React from 'react'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { updateLoanProduct } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { ILoanProductCreate } from '@/store/interfaces'

export const tenureValues = ['Days', 'Weeks', 'Months', 'Quarters']
export const interestRateTypes = ['Static', 'Dynamic']
export const prepaymentTypes = ['Inclusive', 'Exclusive']
export const interestRecoveryTypes = ['Upfront', 'Spread']
export const prepaymentCalculationMethods = [
  'FlatAmount',
  'FlatPercentage',
  'TieredAmount',
  'TieredPercentage',
  'Installment Value',
]
export const interestCalculationMethods = ['Simple', 'Accrued']
export const amortizationModes = [
  'FlatRate',
  'ReducingBalance',
  'BulletPayment',
]
export const repaymentCycles = ['Days', 'Weeks', 'Months', 'Quarters']
export const disbursementCreditAccountTypes = [
  'TenantCbsAccount',
  'CustomerValueStore',
  'SpecifiedAccount',
]
export const gracePeriodTypes = ['None', 'Principal', 'Pure']
export const minimumInterestCalculationModes = [
  'Flat Amount',
  'Principal-Based Flat Percentage',
  'Principal-Based Tiered Amount',
  'Principal-Based Tiered Percentage',
]
export const minimumInterestRecoveryTypes = ['Upfront', 'Spread']
type ILoanSettingsProps = {
  formik: FormikProps<ILoanProductCreate>
  setCurrentStep: (step: string) => void
  productId: string
}
const LoanSettingsFields: string[] = [
  'minimumAmount',
  'maximumAmount',
  'measureOfTenure',
  'minimumTenure',
  'maximumTenure',
  'repaymentCycle',
  'amortizationMode',
  'isManaged',
  'earlyPaymentsAllowed',
  'multipleDrawDown',
  'hasRecoveryTracking',
  'interestRateType',
  'interestCalculation',
  'minimumInterestRecoveryType',
  'minimumInterestCalculationMode',
  'interestRate',
  'interestRecoveryType',
  'prepaymentType',
  'prepaymentCalculation',
  'prepaymentValue',
  'gracePeriodType',
  'disbursementCreditAccountType',
  'disbursementCreditAccount',
  'disbursementCreditAccountBranch',
  'minimumInterestValue',
]
export const EditLoanSettings = ({
  formik,
  setCurrentStep,
  productId,
}: ILoanSettingsProps) => {
  const dispatch = useAppDispatch()
  const { bankBranches } = useAppSelector((state) => state.loans)
  const handleSave = () => {
    formik.validateForm().then(async (errors) => {
      const filteredErrors = Object.keys(errors)
        .filter((key) => LoanSettingsFields.includes(key))
        .reduce((obj: { [key: string]: string }, key) => {
          if (errors[key] !== undefined) {
        obj[key] = errors[key] || ''
          }
          return obj
        }, {})
      if (Object.keys(filteredErrors).length === 0) {
        await updateLoanProduct(productId, formik.values, dispatch)
        setCurrentStep('View')
      } else {
        LoanSettingsFields.forEach((field) => {
          formik.setFieldTouched(field, true)
        })
      }
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Typography variant="subtitle1">Loan Settings</Typography>
      <Typography variant="subtitle2" sx={{ textAlign: 'center' }}>
        You are Updating a loan product for {formik.values.customerType}{' '}
        customers. Please configure the attributes of the loans to be granted to
        customers <br />
        under this product.
      </Typography>
      <Stack
        sx={{
          flexDirection: 'column',
          width: '60%',
          background: '#FFFFFF',
          padding: '2%',
          borderRadius: '12px',
        }}
      >
        <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
          <TextField
            margin={'normal'}
            fullWidth
            label="Minimum loan amount"
            {...formik.getFieldProps('minimumAmount')}
            error={Boolean(
              formik.touched.minimumAmount && formik.errors.minimumAmount
            )}
            helperText={
              formik.touched.minimumAmount && formik.errors.minimumAmount
            }
          />
          <TextField
            margin={'normal'}
            fullWidth
            label="Maximum loan amount"
            {...formik.getFieldProps('maximumAmount')}
            error={Boolean(
              formik.touched.maximumAmount && formik.errors.maximumAmount
            )}
            helperText={
              formik.touched.maximumAmount && formik.errors.maximumAmount
            }
          />
        </Stack>
        <FormControl
          fullWidth
          margin={'normal'}
          error={Boolean(
            formik.touched.measureOfTenure && formik.errors.measureOfTenure
          )}
        >
          <InputLabel>Select Measure of Tenure</InputLabel>
          <Select
            {...formik.getFieldProps('measureOfTenure')}
            name={'measureOfTenure'}
            label={'Select Measure of Tenure'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {tenureValues.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
          <TextField
            margin={'normal'}
            fullWidth
            label="Minimum Tenure"
            {...formik.getFieldProps('minimumTenure')}
            error={Boolean(
              formik.touched.minimumTenure && formik.errors.minimumTenure
            )}
            helperText={
              formik.touched.minimumTenure && formik.errors.minimumTenure
            }
          />
          <TextField
            margin={'normal'}
            fullWidth
            label="Maximum Tenure"
            {...formik.getFieldProps('maximumTenure')}
            error={Boolean(
              formik.touched.maximumTenure && formik.errors.maximumTenure
            )}
            helperText={
              formik.touched.maximumTenure && formik.errors.maximumTenure
            }
          />
        </Stack>
        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Select Repayment Cycle</InputLabel>
          <Select
            {...formik.getFieldProps('repaymentCycle')}
            name={'repaymentCycle'}
            error={Boolean(
              formik.touched.repaymentCycle && formik.errors.repaymentCycle
            )}
            label={'Select Repayment Cycle'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {repaymentCycles.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl
          fullWidth
          margin={'normal'}
          error={Boolean(
            formik.touched.amortizationMode && formik.errors.amortizationMode
          )}
        >
          <InputLabel>Select Amortization Mode</InputLabel>
          <Select
            {...formik.getFieldProps('amortizationMode')}
            name={'amortizationMode'}
            label={'Select Amortization Mode'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {amortizationModes.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Typography variant="subtitle1">Loan Interest Settings</Typography>
        <FormControl
          fullWidth
          margin={'normal'}
          error={Boolean(
            formik.touched.interestRateType && formik.errors.interestRateType
          )}
        >
          <InputLabel>Select Interest Rate Type</InputLabel>
          <Select
            {...formik.getFieldProps('interestRateType')}
            name={'interestRateType'}
            label={'Select Interest Rate Type'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {interestRateTypes.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl
          fullWidth
          margin={'normal'}
          error={Boolean(
            formik.touched.interestCalculation &&
              formik.errors.interestCalculation
          )}
        >
          <InputLabel>Select Interest Calculation Method</InputLabel>
          <Select
            {...formik.getFieldProps('interestCalculation')}
            name={'interestCalculation'}
            label={'Select Interest Calculation Method'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {interestCalculationMethods.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
          <TextField
            margin={'normal'}
            fullWidth
            label="Interest Rate"
            {...formik.getFieldProps('interestRate')}
            error={Boolean(
              formik.touched.interestRate && formik.errors.interestRate
            )}
            helperText={
              formik.touched.interestRate && formik.errors.interestRate
            }
          />
          <FormControl fullWidth margin={'normal'}>
            <InputLabel>Select Interest Recovery Type</InputLabel>
            <Select
              {...formik.getFieldProps('interestRecoveryType')}
              name={'interestRecoveryType'}
              error={Boolean(
                formik.touched.interestRecoveryType &&
                  formik.errors.interestRecoveryType
              )}
              label={'Select Interest Recovery Type'}
              IconComponent={() => <KeyboardArrowDownIcon />}
            >
              {interestRecoveryTypes.map((val) => (
                <MenuItem key={val} value={val}>
                  {val}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>
        <Typography variant="subtitle1">
          Minimum Loan Interest Settings
        </Typography>

        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Select Minimum Interest Recovery Type</InputLabel>
          <Select
            {...formik.getFieldProps('minimumInterestRecoveryType')}
            name={'minimumInterestRecoveryType'}
            label={'Select Minimum Interest Recovery Type'}
            error={Boolean(
              formik.touched.minimumInterestRecoveryType &&
                formik.errors.minimumInterestRecoveryType
            )}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {minimumInterestRecoveryTypes.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Select Minimum Interest Calculation Mode</InputLabel>
          <Select
            {...formik.getFieldProps('minimumInterestCalculationMode')}
            name={'minimumInterestCalculationMode'}
            label={'Select Minimum Interest Calculation Mode'}
            error={Boolean(
              formik.touched.minimumInterestCalculationMode &&
                formik.errors.minimumInterestCalculationMode
            )}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {minimumInterestCalculationModes.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          margin={'normal'}
          fullWidth
          label="Minimum Interest"
          {...formik.getFieldProps('minimumInterestValue')}
          error={Boolean(
            formik.touched.minimumInterestValue &&
              formik.errors.minimumInterestValue
          )}
          helperText={
            formik.touched.minimumInterestValue &&
            formik.errors.minimumInterestValue
          }
        />

        <Typography variant="subtitle1">Loan Prepayment Settings</Typography>
        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Select Prepayment Type</InputLabel>
          <Select
            {...formik.getFieldProps('prepaymentType')}
            name={'prepaymentType'}
            error={Boolean(
              formik.touched.prepaymentType && formik.errors.prepaymentType
            )}
            label={'Select Prepayment Type'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {prepaymentTypes.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
          <FormControl fullWidth margin={'normal'}>
            <InputLabel>Prepayment Calculation method</InputLabel>
            <Select
              {...formik.getFieldProps('prepaymentCalculation')}
              name={'prepaymentCalculation'}
              error={Boolean(
                formik.touched.prepaymentCalculation &&
                  formik.errors.prepaymentCalculation
              )}
              label={'Prepayment Calculation method'}
              IconComponent={() => <KeyboardArrowDownIcon />}
            >
              {prepaymentCalculationMethods.map((val) => (
                <MenuItem key={val} value={val}>
                  {val}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <TextField
            margin={'normal'}
            fullWidth
            label="Prepayment Value"
            {...formik.getFieldProps('prepaymentValue')}
            error={Boolean(
              formik.touched.prepaymentValue && formik.errors.prepaymentValue
            )}
            helperText={
              formik.touched.prepaymentValue && formik.errors.prepaymentValue
            }
          />
        </Stack>
        <Typography variant="subtitle1">Loan Repayment Settings</Typography>
        <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
          <FormControl fullWidth margin={'normal'}>
            <InputLabel>Select Grace Period Type</InputLabel>
            <Select
              {...formik.getFieldProps('gracePeriodType')}
              name={'gracePeriodType'}
              error={Boolean(
                formik.touched.gracePeriodType && formik.errors.gracePeriodType
              )}
              label={'Select Grace Period Type'}
              IconComponent={() => <KeyboardArrowDownIcon />}
            >
              {gracePeriodTypes.map((val) => (
                <MenuItem key={val} value={val}>
                  {val}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>

        <Typography variant="subtitle1">Loan Disbursement Settings</Typography>
        <FormControl fullWidth margin={'normal'}>
          <InputLabel>Select Disbursement Credit Account Type</InputLabel>
          <Select
            {...formik.getFieldProps('disbursementCreditAccountType')}
            name={'disbursementCreditAccountType'}
            error={Boolean(
              formik.touched.disbursementCreditAccountType &&
                formik.errors.disbursementCreditAccountType
            )}
            label={'Select Disbursement Credit Account Type'}
            IconComponent={() => <KeyboardArrowDownIcon />}
          >
            {disbursementCreditAccountTypes.map((val) => (
              <MenuItem key={val} value={val}>
                {val}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          margin={'normal'}
          fullWidth
          label="Disbursement Credit Account"
          {...formik.getFieldProps('disbursementCreditAccount')}
          error={Boolean(
            formik.touched.disbursementCreditAccount &&
              formik.errors.disbursementCreditAccount
          )}
          helperText={
            formik.touched.disbursementCreditAccount &&
            formik.errors.disbursementCreditAccount
          }
        />
        <FormControl fullWidth margin={'normal'}>
          <Autocomplete
            disablePortal
            id="combo-box-demo"
            options={bankBranches}
            getOptionLabel={(option) => option.branchName}
            value={bankBranches.find(
              (branch) =>
                formik.values.disbursementCreditAccountBranch ===
                branch.branchCode
            )}
            onChange={(event, newValue) => {
              formik.setFieldValue(
                'disbursementCreditAccountBranch',
                newValue?.branchCode
              )
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label={'Disbursement Credit Account Branch'}
              />
            )}
          />
        </FormControl>
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '3%',
          }}
        >
          <Button
            variant="outlined"
            type={'button'}
            fullWidth
            sx={{
              my: '2%',
            }}
            endIcon={<CloseIcon />}
            onClick={() => setCurrentStep('View')}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type={'button'}
            fullWidth
            sx={{
              my: '2%',
            }}
            endIcon={<ArrowForwardIcon />}
            onClick={handleSave}
          >
            Save Changes
          </Button>
        </Stack>
      </Stack>
    </Stack>
  )
}
