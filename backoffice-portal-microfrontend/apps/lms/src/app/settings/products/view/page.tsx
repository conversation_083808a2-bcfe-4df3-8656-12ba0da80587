'use client'
import { Person2Outlined } from '@mui/icons-material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { IconButton, Stack, Typography } from '@mui/material'
import { useFormik } from 'formik'
import React, { useState } from 'react'
import { useAppSelector } from '@/store'
import { CustomSuccessChip } from '@dtbx/ui/components/Chip'

import { EditBasicDetails } from '@/app/settings/products/view/EditBasicDetails'
import { EditLoanSettings } from '@/app/settings/products/view/EditLoanSettings'
import { EditCostOfCredit } from '@/app/settings/products/view/EditCostofCredit'
import { EditSettlementAccounts } from '@/app/settings/products/view/EditSettlementAccounts'
import { ViewProductPage } from '@/app/settings/products/view/View'

const ViewProduct = () => {
  const { productToView } = useAppSelector((state) => state.loans)
  const [currentStep, setCurrentStep] = useState('View')
  const {
    organization,
    type,
    id,
    dateCreated,
    dateModified,
    createdBy,
    modifiedBy,
    expiryDate,
    exposureLimit,
    numberOfInstallments,
    ...rest
  } = productToView
  const initialValues = {
    ...rest,
    organizationId: organization.id,
    typeId: type.id,
  }
  const formik = useFormik({
    initialValues,
    onSubmit: (_values) => {},
  })
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '1vh',
        px: '2%',
        py: '1%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          gap: '16%',
        }}
      >
        <IconButton
          sx={{
            background: '#FFFFFF',
            borderRadius: '8px',
            border: '1px solid #D0D5DD',
            width: '3vw',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          onClick={() => window.history.back()}
        >
          <ArrowBackIosNewOutlinedIcon />
        </IconButton>
        <Stack flexDirection="column" gap={'10px'}>
          <Typography
            variant="h6"
            color={'primary.main'}
            sx={{
              textAlign: 'left',
              fontWeight: 700,
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignContent: 'center',
              alignItems: 'center',
            }}
          >
            {productToView.name}
            <CustomSuccessChip label={productToView.status.toUpperCase()} />
          </Typography>
          <Typography
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignContent: 'center',
              alignItems: 'center',
            }}
            variant="body1"
          >
            <Person2Outlined />
            Maker: {productToView.createdBy}
          </Typography>
        </Stack>
      </Stack>
      {(() => {
        switch (currentStep) {
          case 'Basic Details':
            return (
              <EditBasicDetails
                formik={formik}
                setCurrentStep={setCurrentStep}
                productId={productToView.id}
              />
            )
          case 'Loan Settings':
            return (
              <EditLoanSettings
                formik={formik}
                setCurrentStep={setCurrentStep}
                productId={productToView.id}
              />
            )
          case 'Cost of Credit':
            return (
              <EditCostOfCredit
                formik={formik}
                setCurrentStep={setCurrentStep}
                productId={productToView.id}
              />
            )
          case 'Settlement Accounts':
            return (
              <EditSettlementAccounts
                formik={formik}
                setCurrentStep={setCurrentStep}
                productId={productToView.id}
              />
            )
          case 'View':
            return (
              <ViewProductPage
                productToView={productToView}
                setCurrentStep={setCurrentStep}
              />
            )
          default:
            return (
              <ViewProductPage
                productToView={productToView}
                setCurrentStep={setCurrentStep}
              />
            )
        }
      })()}
    </Stack>
  )
}
export default ViewProduct
