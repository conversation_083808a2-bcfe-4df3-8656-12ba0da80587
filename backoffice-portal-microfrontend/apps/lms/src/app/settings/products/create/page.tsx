'use client'

import { I<PERSON><PERSON><PERSON>on, Stack } from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import React, { useEffect, useState } from 'react'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  createLoanProduct,
  getBankBranches,
  getLoanProductCategories,
  getOrganizations,
} from '@/store/actions'

import { BasicDetails } from '@/app/settings/products/create/BasicDetails'
import { LoanSettings } from '@/app/settings/products/create/LoanSettings'
import { CostOfCredit } from '@/app/settings/products/create/CostOfCredit'
import { Confirmation } from '@/app/settings/products/create/Confirmation'
import { SettlementAccounts } from '@/app/settings/products/create/SettlementAccounts'
import { ValidationAssignment } from '@/app/settings/products/create/ValidationAssignment'
import { HasAccessToRights } from '@dtbx/store/utils'

const CreateProductPage = () => {
  const dispatch = useAppDispatch()
  const { organizations } = useAppSelector((state) => state.loans)
  const productValidationSchema = Yup.object().shape({
    disbursementCreditAccount: Yup.string().required(
      'Disbursement Credit Account is required'
    ),
    code: Yup.string().required('Product Code is required'),
    name: Yup.string().required('Name is required'),
    country: Yup.string().required('Country is required'),
    currency: Yup.string().required('Currency is required'),
    typeId: Yup.string().required('Product Category Type is required'),
    organizationId: Yup.string().required('Organization is required'),
    status: Yup.string().required('Status is required'),
    customerType: Yup.string().required('Customer Type is required'),
    minimumAmount: Yup.number().required('Minimum Amount is required'),
    maximumAmount: Yup.number().required('Maximum Amount is required'),
    measureOfTenure: Yup.string().required('Measure of Tenure is required'),
    minimumTenure: Yup.number().required('Minimum Tenure is required'),
    maximumTenure: Yup.number().required('Maximum Tenure is required'),
    interestRateType: Yup.string().required('Interest Rate Type is required'),
    interestRate: Yup.number().required('Interest Rate is required'),
    facilityFee: Yup.string().required('Facility Fee is required'),
    exciseDuty: Yup.number().required('Excise Duty is required'),
    interestRecoveryType: Yup.string().required(
      'Interest Recovery Type is required'
    ),
    earlyPaymentsAllowed: Yup.boolean().required(
      'Are early payments allowed is required'
    ),
    prepaymentValue: Yup.number().required('Prepayment Value is required'),
    interestCalculation: Yup.string().required(
      'Interest Calculation is required'
    ),
    amortizationMode: Yup.string().required('Amortization Mode is required'),
    multipleDrawDown: Yup.boolean().required('Multiple Draw Down is required'),
    tranches: Yup.boolean().required('Tranches is required'),
    repaymentCycle: Yup.string().required('Repayment Cycle is required'),
    periodInArrears: Yup.number().required('Period in Arrears is required'),
    disbursementGl: Yup.string().required('Disbursement GL is required'),
    disbursementCreditAccountType: Yup.string().required(
      'Disbursement Credit Account Type is required'
    ),
    repaymentGl: Yup.string().required('Repayment GL is required'),
    gracePeriodType: Yup.string().required('Grace Period Type is required'),
    hasRecoveryTracking: Yup.boolean().required(
      'Has Recovery Tracking is required'
    ),
  })
  const formik = useFormik({
    initialValues: {
      //string values
      name: '',
      organizationId: '',
      country: 'KE',
      currency: 'KES',
      // expiryDate: '',
      disbursementCreditAccount: '',
      disbursementCreditAccountBranch: '',
      code: `PRO${Math.random().toString().slice(2, 11)}`,
      externalProductCode: '',
      typeId: '',
      status: 'Active',
      customerType: '',
      measureOfTenure: 'Months',
      interestRateType: 'Static',
      facilityFeeRecoveryType: '',
      interestRecoveryType: 'Spread',
      prepaymentType: 'Inclusive',
      prepaymentCalculation: 'Installment Value',
      interestCalculation: 'Simple',
      amortizationMode: 'FlatRate',
      trancheInterval: '',
      repaymentCycle: 'Months',
      disbursementCreditAccountType: 'TenantCbsAccount',
      gracePeriodType: 'None',
      minimumInterestRecoveryType: 'Upfront',
      upfrontInterestRecognitionType: '', // required if minimumInterestRecoveryType is Upfront
      minimumInterestCalculationMode: 'Flat Amount',
      externalProductName: '',
      //boolean values
      isManaged: true,
      earlyPaymentsAllowed: false,
      multipleDrawDown: false,
      tranches: false,
      hasRecoveryTracking: false,
      //number values
      // exposureLimit: 0,
      manualApprovalAmount: 0,
      minimumAmount: 1,
      maximumAmount: 1,
      minimumTenure: 1,
      maximumTenure: 1,
      dynamicInterestFixedComponent: 0,
      interestRate: 0,
      facilityFee: 0,
      exciseDuty: 0,
      rollOverFee: 0,
      rollOverPeriod: 0,
      maxRollOverCount: 0,
      prepaymentValue: 0,
      penalty: 0,
      // numberOfInstallments: 0,
      periodInArrears: 1,
      minimumInterestValue: 0,
      //gl accounts
      interestGl: '',
      facilityFeeGl: '',
      exciseDutyGl: '',
      rollOverGl: '',
      penaltyGl: '',
      prepaymentGl: '',
      disbursementGl: '',
      upfrontInterestLiabilityGl: '', //required if minimumInterestRecoveryType is Upfront and upfrontInterestRecognitionType is Amortized
      repaymentGl: 'N/A',
    },
    validationSchema: productValidationSchema,
    onSubmit: async (values) => {
      const data = await createLoanProduct(values, dispatch)
      if (!HasAccessToRights(['SUPER_CREATE_PRODUCT'])) {
        return
      }
      if (data.status === 400) {
        return
      } else {
        setCurrentStage('KYC')
      }
    },
  })
  const { handleSubmit } = formik
  useEffect(() => {
    getOrganizations(dispatch, 'page=1&size=30')
    getLoanProductCategories(dispatch, 1, 10)
    getBankBranches(dispatch)
  }, [])
  const [currentStage, setCurrentStage] = useState<string>('Basic Details')
  return (
    <Stack
      sx={{
        px: '2%',
        background: '#F7F7F7',
        minHeight: '92vh',
        py: '2%',
        flexDirection: 'column',
        gap: '3vh',
      }}
    >
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          width: '3vw',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => window.history.back()}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>

      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit} noValidate>
          {(() => {
            switch (currentStage) {
              case 'Basic Details':
                return (
                  <BasicDetails
                    formik={formik}
                    organizations={organizations}
                    setCurrentStep={setCurrentStage}
                  />
                )
              case 'Loan Settings':
                return (
                  <LoanSettings
                    formik={formik}
                    setCurrentStep={setCurrentStage}
                  />
                )
              case 'Cost of Credit':
                return (
                  <CostOfCredit
                    formik={formik}
                    setCurrentStep={setCurrentStage}
                  />
                )
              case 'Settlement Accounts':
                return (
                  <SettlementAccounts
                    formik={formik}
                    setCurrentStep={setCurrentStage}
                  />
                )
              case 'Confirmation':
                return (
                  <Confirmation
                    formik={formik}
                    setCurrentStep={setCurrentStage}
                  />
                )
              case 'KYC':
                return <ValidationAssignment setCurrentStep={setCurrentStage} />
              default:
                return <Stack>Default</Stack>
            }
          })()}
        </Form>
      </FormikProvider>
    </Stack>
  )
}
export default CreateProductPage
