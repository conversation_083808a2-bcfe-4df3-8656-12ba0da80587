'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON><PERSON>, Drawer, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, Paper } from '@mui/material'
import { CloseRounded } from '@mui/icons-material'
import { CommentIcon } from '@dtbx/ui/components/SvgIcons'
import { IApprovalRequest } from '@/store/interfaces'
import { Status } from '@/store/interfaces/loans'
import { useAppSelector } from '@/store'
import { formatTimestamp } from '@dtbx/store/utils'

export interface CommentProps {
  CheckerEmail?: string
  timestamp: string
  content: string
}

export interface NotificationProps {
  date: string
  MakerEmail?: string
  Orgname: string
  request?: IApprovalRequest
  brokerStatus?: Status
  comments: CommentProps[]
}

interface ViewCommentsProps {
  commentCount: number
  request?: IApprovalRequest
}
const getValidBrokerStatus = (status: string | undefined): Status => {
  switch (status) {
    case 'Active':
    case 'Inactive':
    case 'Pending Approval':
    case 'Blocked':
      return status as Status
    default:
      return 'Active'
  }
}

const Comment: React.FC<CommentProps> = ({
  CheckerEmail,
  timestamp,
  content,
}) => (
  <Paper
    elevation={1}
    sx={{
      p: 2,
      mt: 2,
      width: '100%',
      borderRadius: 2,
      border: '1px solid',
      borderColor: 'divider',
      minHeight: 104,
      backgroundColor: 'background.paper',
    }}
  >
    <Stack spacing={1}>
      <Stack direction="row" spacing={2} alignItems="center">
        <Typography variant="body2" fontWeight="medium" color="text.primary">
          {CheckerEmail} left a comment
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {timestamp}
        </Typography>
      </Stack>
      <Typography variant="body2" color="text.secondary">
        {content}
      </Typography>
    </Stack>
  </Paper>
)

const Notification: React.FC<NotificationProps> = ({
  date,
  MakerEmail,
  brokerStatus,
  Orgname,
  comments,
}) => {
  return (
    <Stack spacing={2} px={2} maxWidth={431}>
      <Stack spacing={1}>
        <Typography
          variant="body1"
          fontWeight="700"
          color="#101828"
          fontSize="1rem"
        >
          {date}:
        </Typography>
        <Typography variant="body2" color="text.secondary" fontWeight="400">
          {MakerEmail} has requested approval to move {Orgname} to an{' '}
          <Typography component="span" color="success.main" fontWeight="400">
            {getValidBrokerStatus(brokerStatus)} Status
          </Typography>
        </Typography>
      </Stack>

      <Stack spacing={1} pt={2}>
        <Typography
          variant="body1"
          fontWeight="700"
          color="#101828"
          fontSize="1rem"
        >
          Comments
        </Typography>
        {comments.map((comment, index) => (
          <Comment key={index} {...comment} />
        ))}
      </Stack>
    </Stack>
  )
}

const ViewComments: React.FC<ViewCommentsProps> = ({}) => {
  const [isCommentOpen, setIsCommentOpen] = useState(false)
  const { selectedApprovalRequestComments } = useAppSelector(
    (state) => state.approvalRequests
  )

  const handleCommentOpen = (event: React.MouseEvent) => {
    event.stopPropagation()
    if (selectedApprovalRequestComments.length === 0) {
      return
    }
    setIsCommentOpen(true)
  }

  const handleCommentClose = () => {
    setIsCommentOpen(false)
  }

  const notificationsData = selectedApprovalRequestComments.map((request) => ({
    date: formatTimestamp(request?.dateCreated) || 'Unknown date',
    MakerEmail: request?.maker || 'Unknown Maker email',
    Orgname: request?.entity
      ? JSON.parse(request.entity)?.name
      : 'Unknown Organization',
    brokerStatus: getValidBrokerStatus(request?.status) || 'Unknown status',
    comments: [
      {
        CheckerEmail: request?.checker || 'Unknown Checker email',
        timestamp:
          formatTimestamp(request?.dateModified) || 'Unknown timestamp',
        content: request?.checkerComments || 'No comment',
      },
    ],
    request: request,
  }))

  return (
    <>
      <IconButton onClick={handleCommentOpen}>
        <CommentIcon />
        <Typography
          variant="body2"
          color="#1570EF"
          fontSize="16px"
          fontWeight="500"
          sx={{ marginLeft: 1 }}
        >
          {selectedApprovalRequestComments.length} comment
          {selectedApprovalRequestComments?.length <= 1 ? '' : 's'}
        </Typography>
      </IconButton>

      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '35%',
          },
        }}
        variant="temporary"
        open={isCommentOpen}
        anchor="right"
        onClose={handleCommentClose}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          sx={{
            px: 2,
            py: 1,
            borderBottom: '1px solid lightgray',
            backgroundColor: '#F9FAFB',
            alignItems: 'center',
          }}
        >
          <Typography
            variant="subtitle1"
            fontSize="16px"
            fontWeight="700"
            color="#2A3339"
          >
            Comments
          </Typography>
          <IconButton
            sx={{
              border: '1px solid #CBD5E1',
              backgroundColor: '#F1F5F9',
            }}
            onClick={handleCommentClose}
          >
            <CloseRounded sx={{ fontSize: 20 }} />
          </IconButton>
        </Stack>

        <Stack spacing={2} sx={{ px: 2, py: 1, mb: 1 }}>
          {notificationsData.map((notificationData, index) => (
            <Notification key={index} {...notificationData} />
          ))}
        </Stack>
      </Drawer>
    </>
  )
}

export default ViewComments
