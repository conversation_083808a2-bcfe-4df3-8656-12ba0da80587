import React from 'react'
import { Avatar, Stack, Typography } from '@mui/material'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'

interface BrokerOptionProps {
  imageSrc: React.ReactNode
  title: string
  description: string
  onClick: () => void
}

const BrokerOption: React.FC<BrokerOptionProps> = ({
  imageSrc,
  title,
  description,
  onClick,
}) => (
  <Stack
    onClick={onClick}
    direction="row"
    sx={{
      alignItems: 'center',
      justifyContent: 'center',
      width: { xs: '100%', sm: '80%', md: '640px' },
      p: 2,
      bgcolor: 'white',
      borderRadius: 2,
      border: 1,
      borderColor: 'grey.300',
      mx: 'auto',
      cursor: 'pointer',
    }}
  >
    <Avatar
      sx={{
        width: 64,
        height: 64,
        marginRight: 2,
        backgroundColor: 'transparent',
        border: 'none',
      }}
    >
      {imageSrc}
    </Avatar>
    <Stack flexGrow={1} maxWidth={{ xs: '100%', sm: '80%', md: '640px' }}>
      <Typography variant="h6" fontWeight="700" fontSize="20px" color="#101828">
        {title}
      </Typography>
      <Typography
        variant="body1"
        fontSize="16px"
        fontWeight="400"
        color="#475467"
        mt={1}
      >
        {description}
      </Typography>
    </Stack>
    <ChevronRightIcon />
  </Stack>
)

export default BrokerOption
