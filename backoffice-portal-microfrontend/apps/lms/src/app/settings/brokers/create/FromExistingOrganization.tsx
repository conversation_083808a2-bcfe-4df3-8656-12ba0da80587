import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import React, { ChangeEvent, useEffect, useState } from 'react'
import { FormikProps } from 'formik'
import { Button, FormControlLabel, FormControlLabelProps, IconButton, Radio, RadioGroup, Stack, styled, Typography } from '@mui/material'
import { SearchRounded } from '@mui/icons-material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { CustomSearchInput } from '@dtbx/ui/components/Input'

import { ICreateBrokerForm, IOrganization } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { getOrganizations } from '@/store/actions'

const CustomFormControlLabel = styled((props: FormControlLabelProps) => (
  <FormControlLabel {...props} />
))(({ theme }) => ({
  '&.MuiFormControlLabel-root': {
    justifyContent: 'space-between',
    background: '#FFFFFF',
    borderRadius: '8px',
    padding: '1.5%',
    marginBottom: '16px',
    marginLeft: 0,
  },
  '.MuiFormControlLabel-label': {
    color: theme.palette.primary.main,
    fontWeight: 600,
    fontSize: '16px',
  },
}))
interface IExistingOrganization {
  setCurrentStage: (step: string) => void
  formik: FormikProps<ICreateBrokerForm>
}

export const SelectExistingOrganization: React.FC<IExistingOrganization> = ({
  setCurrentStage,
  formik,
}) => {
  const dispatch = useAppDispatch()

  useEffect(() => {
    const params = `page=1&size=5`
    getOrganizations(dispatch, params)
  }, [])

  const handleInputChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const params = `size=5&page=1&name=${event.target.value}`
    await getOrganizations(dispatch, params)
  }
  const [selectedOrg, setSelectedOrg] = useState<string>('')

  const { organizations } = useAppSelector((state) => state.loans)
  const handleOrgSelect = (
    _event: React.ChangeEvent<HTMLInputElement>,
    value: string
  ) => {
    setSelectedOrg(value)
    const org =
      organizations.find((org) => org.id === value) || ({} as IOrganization)
    const keys: (keyof IOrganization)[] = [
      'name',
      'email',
      'mobile',
      'bankName',
      'bankCode',
      'swiftCode',
      'bankAccountNumber',
      'branchCode',
      'accountBranchName',
      'status',
    ]
    keys.forEach((item) => {
      formik.setFieldValue(item, org[item])
    })
    formik.setFieldValue('physicalAddress.country', org.physicalAddress.country)
    formik.setFieldValue('physicalAddress.town', org.physicalAddress.town)
    formik.setFieldValue(
      'physicalAddress.physicalAddress',
      org.physicalAddress.physicalAddress
    )
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        p: '2%',
        height: '95vh',
        gap: '3vh',
        background: '#F7F7F7',
      }}
    >
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          width: '3vw',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => setCurrentStage('GetStarted')}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>
      <Stack
        sx={{
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '0.5vh',
        }}
      >
        <Typography variant="h6" fontWeight="700" color="primary.main">
          Broker Details
        </Typography>
        <Typography
          variant="body1"
          color="primary.primary3"
          fontWeight="400"
          fontSize="1rem"
        >
          You are creating a new broker from existing organization details
        </Typography>
      </Stack>
      <Stack justifyContent="center" alignItems="center">
        <Stack
          sx={{
            flexDirection: 'column',
            borderRadius: '6px',
            p: '2%',
            border: '1px solid #D0D5DD',
            backgroundColor: '#F7F7F7',
            width: '35vw',
            gap: '1vh',
          }}
        >
          <Typography variant="subtitle2" fontWeight="700" color="primary.main">
            Select the organization that this broker is to be linked with.
          </Typography>
          <CustomSearchInput
            placeholder="Search organization by Name"
            onChange={handleInputChange}
            endAdornment={
              <SearchRounded
                sx={{
                  color: 'black',
                }}
              />
            }
            sx={{
              background: '#FFFFFF',
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              width: '100%',
            }}
          />
          <RadioGroup onChange={handleOrgSelect}>
            {organizations.map((org) => (
              <CustomFormControlLabel
                key={org.id}
                value={org.id}
                control={<Radio />}
                label={org.name}
                labelPlacement={'start'}
              />
            ))}
          </RadioGroup>
          <Stack
            sx={{
              maxHeight: '50vh ',
              flexDirection: 'column',
              gap: 2,
            }}
          >
            <Button
              variant="contained"
              endIcon={<ArrowForwardIcon />}
              disabled={!selectedOrg}
              onClick={() => setCurrentStage('CreateBrokerForm')}
              fullWidth
              sx={{
                my: '1%',
              }}
            >
              Continue
            </Button>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}
