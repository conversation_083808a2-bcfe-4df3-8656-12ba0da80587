'use client'
import {
  Autocomplete,
  Button,
  FormControl,
  FormHelperText,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import React, { useEffect, useState } from 'react'
import { FormikProps } from 'formik'
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input'
import { useAppDispatch, useAppSelector } from '@/store'
import { getBankBranches, getLoanProducts } from '@/store/actions'
import { ICreateBrokerForm, ILoanProduct} from '@/store/interfaces'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { MultiSelectAutocomplete } from '@dtbx/ui/components/Input'


interface BrokerDetailsProps {
  formik: FormikProps<ICreateBrokerForm>
  setCurrentStage: (step: string) => void
}

export const CreateBrokerForm = ({
  setCurrentStage,
  formik,
}: BrokerDetailsProps) => {
  const { isLoadingCreateBroker, bankBranches, loanProducts } = useAppSelector(
    (state) => state.loans
  )
  const dispatch = useAppDispatch()
  const [phone, setPhone] = useState<string>(formik.values.mobile)
  const [selectedProducts, setSelectedProducts] = useState<ILoanProduct[]>([])

  useEffect(() => {
    getBankBranches(dispatch)
    getLoanProducts(dispatch)
    formik.validateForm()
  }, [])
  const handlePhoneChange = (value: string) => {
    setPhone(value)
    formik.setFieldTouched('mobile', true, false)
    if (!matchIsValidTel(value)) {
      formik.setFieldError('mobile', 'Invalid mobile number')
    }
    formik.setFieldValue('mobile', value.replace(/\s/g, ''))
  }

  const handleProductChange = (
    _event: unknown,
    newSelectedProducts: ILoanProduct[]
  ) => {
    const selectedProductIds = newSelectedProducts.map((product) => product.id)
    setSelectedProducts(newSelectedProducts)
    formik.setFieldValue('productIds', selectedProductIds)
  }
  return (
    <Stack
      sx={{
        px: '2%',
        background: '#F7F7F7',
        py: '2vh',
        flexDirection: 'column',
        gap: '3vh',
        // height: '95vh',
      }}
    >
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          width: '3vw',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => setCurrentStage('GetStarted')}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>
      <Stack direction="column" alignItems="center" justifyContent="center">
        <Typography variant="subtitle1" align="center" fontWeight="bold">
          Broker Details
        </Typography>
        <Typography variant="subtitle2" align="center" mt={2}>
          You are creating a new broker
        </Typography>
      </Stack>
      <Stack justifyContent="center" alignItems="center">
        <Stack
          sx={{
            flexDirection: 'column',
            width: '40vw',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            border: '1px solid #D0D5DD',
          }}
        >
          <TextField
            autoComplete="name"
            type="text"
            label="Broker’s Name"
            margin={'normal'}
            {...formik.getFieldProps('name')}
            fullWidth
            error={Boolean(formik.touched.name && formik.errors.name)}
            helperText={formik.touched.name && formik.errors.name}
          />
          <TextField
            autoComplete="email"
            type="email"
            label="Broker’s Email address"
            margin={'normal'}
            {...formik.getFieldProps('email')}
            fullWidth
            error={Boolean(formik.touched.email && formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
          />
          <FormControl
            fullWidth
            sx={{
              marginTop: '16px',
            }}
            error={Boolean(formik.touched.mobile && formik.errors.mobile)}
          >
            <MuiTelInput
              value={phone}
              name="mobile"
              label="Broker’s Phone number"
              defaultCountry="KE"
              onlyCountries={['KE', 'UG', 'TZ', 'BI']}
              onChange={handlePhoneChange}
            />
            <FormHelperText>
              {formik.touched.mobile && formik.errors.mobile && (
                <FormHelperText error>{formik.errors.mobile}</FormHelperText>
              )}
            </FormHelperText>
          </FormControl>
          <FormControl fullWidth margin="normal">
            <TextField
              id="bankName"
              autoComplete="bankName"
              label="Bank Name"
              {...formik.getFieldProps('bankName')}
              error={formik.touched.bankName && Boolean(formik.errors.bankName)}
              helperText={formik.touched.bankName && formik.errors.bankName}
            />
          </FormControl>

          <Stack direction="row" justifyContent="space-between" gap={'2%'}>
            <TextField
              autoComplete="bankCode"
              type="text"
              label="Bank Code"
              margin={'normal'}
              {...formik.getFieldProps('bankCode')}
              fullWidth
              error={Boolean(formik.touched.bankCode && formik.errors.bankCode)}
              helperText={formik.touched.bankCode && formik.errors.bankCode}
            />
            <TextField
              autoComplete="swiftCode"
              type="text"
              label="Swift Code"
              margin={'normal'}
              {...formik.getFieldProps('swiftCode')}
              fullWidth
              error={Boolean(
                formik.touched.swiftCode && formik.errors.swiftCode
              )}
              helperText={formik.touched.swiftCode && formik.errors.swiftCode}
            />
          </Stack>
          <TextField
            autoComplete="bankAccountNumber"
            type="text"
            label="Bank Account Number"
            margin={'normal'}
            {...formik.getFieldProps('bankAccountNumber')}
            fullWidth
            error={Boolean(
              formik.touched.bankAccountNumber &&
                formik.errors.bankAccountNumber
            )}
            helperText={
              formik.touched.bankAccountNumber &&
              formik.errors.bankAccountNumber
            }
          />
          <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
            <TextField
              autoComplete="branchCode"
              type="text"
              label="Branch Code"
              margin={'normal'}
              {...formik.getFieldProps('branchCode')}
              fullWidth
              //   inputProps={{ readOnly: true }}
              error={Boolean(
                formik.touched.branchCode && formik.errors.branchCode
              )}
              helperText={formik.touched.branchCode && formik.errors.branchCode}
            />
            <FormControl fullWidth margin="normal">
              <Autocomplete
                disablePortal
                id="bankBranches"
                options={bankBranches}
                getOptionLabel={(option) => option.branchName}
                value={bankBranches.find(
                  (branch) => formik.values.branchCode === branch.branchCode
                )}
                onChange={(event, newValue) => {
                  formik.setFieldValue(
                    'accountBranchName',
                    newValue?.branchName
                  )
                  formik.setFieldValue('branchCode', newValue?.branchCode)
                }}
                renderInput={(params) => (
                  <TextField {...params} label={'Account Branch Name'} />
                )}
              />
              {formik.touched.accountBranchName &&
                formik.errors.accountBranchName && (
                  <FormHelperText error>
                    {formik.errors.accountBranchName}
                  </FormHelperText>
                )}
            </FormControl>
          </Stack>

          <Stack direction="row" justifyContent={'space-between'} gap={'2%'}>
            <TextField
              autoComplete="country"
              type="text"
              label="Country"
              margin="normal"
              {...formik.getFieldProps('physicalAddress.country')}
              fullWidth
              error={Boolean(
                formik.touched.physicalAddress?.country &&
                  formik.errors.physicalAddress?.country
              )}
              helperText={
                formik.touched.physicalAddress?.country &&
                formik.errors.physicalAddress?.country
              }
            />

            <TextField
              autoComplete="town"
              type="text"
              label="Town"
              margin="normal"
              {...formik.getFieldProps('physicalAddress.town')}
              fullWidth
              error={Boolean(
                formik.touched.physicalAddress?.town &&
                  formik.errors.physicalAddress?.town
              )}
              helperText={
                formik.touched.physicalAddress?.town &&
                formik.errors.physicalAddress?.town
              }
            />

            <TextField
              autoComplete="physicalAddress"
              type="text"
              label="Physical Address"
              margin="normal"
              {...formik.getFieldProps('physicalAddress.physicalAddress')}
              fullWidth
              error={Boolean(
                formik.touched.physicalAddress?.physicalAddress &&
                  formik.errors.physicalAddress?.physicalAddress
              )}
              helperText={
                formik.touched.physicalAddress?.physicalAddress &&
                formik.errors.physicalAddress?.physicalAddress
              }
            />
          </Stack>

          <TextField
            autoComplete="bankAccountNumber"
            type="text"
            label="Callback url"
            margin={'normal'}
            {...formik.getFieldProps('callBackUrl')}
            fullWidth
            error={Boolean(
              formik.touched.callBackUrl && formik.errors.callBackUrl
            )}
            helperText={formik.touched.callBackUrl && formik.errors.callBackUrl}
          />
          <MultiSelectAutocomplete
            label="Select a Product"
            options={loanProducts}
            selectedItems={selectedProducts}
            getOptionLabel={(option) => option.name}
            onChange={handleProductChange}
            onDelete={(item) => {
              const updatedProducts = selectedProducts.filter(
                (p) => p.id !== item.id
              )
              setSelectedProducts(updatedProducts)
              const updatedProductIds = updatedProducts.map(
                (product) => product.id
              )
              formik.setFieldValue('productIds', updatedProductIds)
            }}
            isOptionEqualToValue={(option, value) => option.id === value.id}
          />
          {formik.touched.productIds && formik.errors.productIds && (
            <FormHelperText error>{formik.errors.productIds}</FormHelperText>
          )}

          {isLoadingCreateBroker ? (
            <LoadingButton />
          ) : (
            <Button
              variant="contained"
              type="submit"
              disabled={!(formik.isValid && formik.dirty)}
              fullWidth
              sx={{
                my: '1%',
              }}
            >
              Save
            </Button>
          )}
        </Stack>
      </Stack>
    </Stack>
  )
}
