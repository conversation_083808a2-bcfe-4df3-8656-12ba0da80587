'use client'
import React, { useEffect } from 'react'
import { Grid, IconButton, Typography } from '@mui/material'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { EditIcon } from '@dtbx/ui/components/SvgIcons'
import { useAppDispatch } from '@/store'
import { getApprovalRequestByEntityId } from '@/store/actions'
import { IOrganization } from '@/store/interfaces'

interface BasicDetailsProps {
  details: {
    id: string
    brokerName: string
    email: string
    phone: string
    bankName: string
    bankCode: string
    swiftCode: string
    accountNumber: string
    branchCode: string
    branchName: string
    address: string
    exposureLimit: string
    organization: IOrganization | null
    callbackurl: string
  }
}

const BasicDetails: React.FC<BasicDetailsProps> = ({ details }) => {
  const dispatch = useAppDispatch()
  useEffect(() => {
    const fetchBrokerApprovalRequestDetails = async () => {
      const params = `?size=10&page=1`
      await getApprovalRequestByEntityId(dispatch, details.id, params)
    }
    if (details.id) {
      fetchBrokerApprovalRequestDetails()
    }
  }, [dispatch, details.id])

  const router = useCustomRouter()
  return (
    <Grid
      container
      justifyContent="center"
      alignItems="center"
      sx={{
        backgroundColor: '#F7F7F7',
        maxWidth: '68.75%',
        width: '100%',
        mx: 'auto',
      }}
    >
      <Grid
        item
        xs={12}
        sx={{
          p: 4,
          mt: 2,
          borderRadius: '8px',
          border: '1px solid #EAECF0',
          backgroundColor: 'white',
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={6}>
            <Typography variant="h6" fontWeight="600" fontSize="1.25rem">
              Basic Details
            </Typography>
          </Grid>

          <Grid item xs={6} container justifyContent="flex-end">
            <IconButton onClick={() => router.push('/settings/brokers/edit/')}>
              <EditIcon />
            </IconButton>
          </Grid>
        </Grid>

        <Grid container spacing={3} mt={3}>
          <Grid item xs={12}>
            <Typography
              variant="body2"
              color="textSecondary"
              fontWeight="400"
              fontSize="1rem"
            >
              Broker Name
            </Typography>
            <Typography variant="h6" fontWeight="590">
              {details.brokerName}
            </Typography>
          </Grid>

          <Grid container spacing={3} item lg={12}>
            <Grid item xs={12} md={12} lg={6}>
              <Typography
                variant="body2"
                color="textSecondary"
                fontWeight="400"
                fontSize="1rem"
              >
                Email Address
              </Typography>
              <Typography
                variant="h6"
                fontWeight="590"
                sx={{
                  fontSize: {
                    xs: '1rem',
                    sm: '1.25rem',
                  },
                }}
              >
                {details.email}
              </Typography>
            </Grid>
            <Grid item xs={12} md={12} lg={6}>
              <Typography
                variant="body2"
                color="textSecondary"
                fontWeight="400"
                fontSize="1.25rem"
                sx={{
                  fontSize: '1rem',
                }}
              >
                Phone Number
              </Typography>
              <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                {details.phone}
              </Typography>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  fontWeight="400"
                  fontSize="1rem"
                >
                  Bank Name
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.bankName}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  fontWeight="400"
                  fontSize="1rem"
                >
                  Bank Code
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.bankCode}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  fontWeight="400"
                  fontSize="1rem"
                >
                  Swift Code
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.swiftCode}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  fontWeight="400"
                  fontSize="1rem"
                >
                  Bank Account Number
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.accountNumber}
                </Typography>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  fontWeight="400"
                  fontSize="1rem"
                >
                  Branch Code
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.branchCode}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  fontWeight="400"
                  fontSize="1rem"
                >
                  Account Branch Name
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.branchName}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  fontWeight="400"
                  fontSize="1rem"
                >
                  Physical Address
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.address}
                </Typography>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12} lg={12}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" color="textSecondary">
                  Exposure Limit
                </Typography>
                <Typography variant="h6" fontWeight="590" fontSize="1.25rem">
                  {details.exposureLimit}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" color="textSecondary">
                  Broker Call Back URL
                </Typography>
                <Typography
                  variant="h6"
                  fontWeight="590"
                  sx={{
                    fontSize: {
                      xs: '0.9rem',
                      sm: '1rem',
                    },
                  }}
                >
                  {details.callbackurl}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="body2" color="textSecondary">
                  Broker Organization
                </Typography>
                <Typography
                  variant="h6"
                  fontWeight="590"
                  sx={{
                    fontSize: {
                      xs: '0.9rem',
                      sm: '1rem',
                    },
                  }}
                >
                  {details.organization?.name || 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default BasicDetails
