'use client'
import React, { useEffect, useState } from 'react'
import {
  Button,
  FormControl,
  FormHelperText,
  Stack,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { useAppDispatch, useAppSelector } from '@/store'
import { ILoanProduct } from '@/store/interfaces'
import { assignProductToBroker, getBrokerProducts } from '@/store/actions'
import { MultiSelectAutocomplete } from '@dtbx/ui/components/Input'



const ManageProductsValidation = Yup.object({
  product: Yup.array()
    .of(Yup.string().required('Product is required'))
    .min(1, 'At least one product must be selected'),
})

const ManageProducts: React.FC = () => {
  const dispatch = useAppDispatch()
  const [selectedProducts, setSelectedProducts] = useState<ILoanProduct[]>([])
  const { selectedBroker, loanProducts, brokerProducts } = useAppSelector(
    (state) => state.loans
  )

  const formik = useFormik({
    initialValues: {
      product: [] as string[],
    },
    validationSchema: ManageProductsValidation,
    onSubmit: () => {
      assignProductToBroker(
        selectedBroker.id ? selectedBroker.id : '',
        selectedProducts,
        dispatch
      )
    },
  })

  useEffect(() => {
    getBrokerProducts(selectedBroker.id ? selectedBroker.id : '', dispatch)

    if (brokerProducts && brokerProducts.length > 0) {
      setSelectedProducts(brokerProducts)
      formik.setFieldValue(
        'product',
        brokerProducts.map((p) => p.id)
      )
    }
    formik.validateForm()
  }, [selectedBroker.id])

  const handleProductChange = (_event: unknown, newValue: ILoanProduct[]) => {
    setSelectedProducts(newValue)
    formik.setFieldValue(
      'product',
      newValue.map((p) => p.id)
    )
  }

  return (
    <Stack
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#F7F7F7',
      }}
    >
      <Stack
        sx={{
          p: 4,
          mt: 4,
          width: '68.75%',
          bgcolor: 'white',
          borderRadius: '8px',
          border: '1px solid #EAECF0',
        }}
      >
        <Stack sx={{ mb: 2 }}>
          <Typography
            variant="h5"
            fontWeight="700"
            fontSize="1.25rem"
            color="textPrimary"
          >
            Manage Products
          </Typography>
          <Typography
            variant="body1"
            fontWeight="400"
            color="#2A3339"
            sx={{ mt: 1 }}
          >
            Linked products assigned to this broker.
          </Typography>
        </Stack>

        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <FormControl
              fullWidth
              margin="normal"
              error={Boolean(formik.touched.product && formik.errors.product)}
            >
              <MultiSelectAutocomplete
                label="Start typing to search for a product and select"
                options={loanProducts}
                selectedItems={selectedProducts}
                getOptionLabel={(option) => option.name}
                onChange={handleProductChange}
                onDelete={(item) => {
                  const updatedProducts = selectedProducts.filter(
                    (p) => p.id !== item.id
                  )
                  setSelectedProducts(updatedProducts)
                  const updatedProductIds = updatedProducts.map(
                    (product) => product.id
                  )
                  formik.setFieldValue('product', updatedProductIds)
                }}
                isOptionEqualToValue={(option, value) => option.id === value.id}
              />
              {formik.touched.product && formik.errors.product && (
                <FormHelperText error>{formik.errors.product}</FormHelperText>
              )}
            </FormControl>
            <Button
              variant="contained"
              type="submit"
              disabled={!(formik.isValid && formik.dirty)}
              fullWidth
              sx={{
                my: '1%',
              }}
            >
              Save
            </Button>
          </Form>
        </FormikProvider>
      </Stack>
    </Stack>
  )
}

export default ManageProducts
