'use client'
import React, { useState } from 'react'
import { Stack, Typography } from '@mui/material'
import Person2OutlinedIcon from '@mui/icons-material/Person2Outlined'
import {
  CustomActiveBrokerChip,
  CustomBlockedChip,
  CustomInactiveBrokerChip,
  CustomPendingApprovalChip,
} from '@dtbx/ui/components/Chip'

import ViewComments from '../commentsDrawer'
import { Status } from '@/store/interfaces/loans'

interface pageHeaderProps {
  Orgname: string
  Maker: string
  MakerEmail: string
  Checker: string
  CheckerEmail: string
  brokerStatus?: Status
}



const DetailsHeader: React.FC<pageHeaderProps> = ({
  Orgname,
  Maker,
  MakerEmail,
  Checker,
  CheckerEmail,
  brokerStatus,
}) => {
  const [commentCount] = useState(1)

  const renderStatusChip = (status: Status | undefined) => {
    switch (status) {
      case 'Active':
        return <CustomActiveBrokerChip label="Active" />
      case 'Inactive':
        return <CustomInactiveBrokerChip label="Inactive" />
      case 'Blocked':
        return <CustomBlockedChip label="Blocked" />
      case 'Pending Approval':
        return <CustomPendingApprovalChip label="Pending Approval" />
      default:
        return <CustomActiveBrokerChip label="Active" />
    }
  }

  return (
    <Stack
      component="main"
      sx={{
        flexDirection: 'column',
        maxWidth: '68.75%',
        width: '100%',
        margin: '0 auto',
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        spacing={3}
        sx={{ flexWrap: 'wrap', mb: 2 }}
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <Typography
            variant="h6"
            color="textSecondary"
            fontWeight="700"
            fontSize="24px"
          >
            {Orgname}
          </Typography>
          {renderStatusChip(brokerStatus)}
        </Stack>

        <Stack direction="row" spacing={1} sx={{ alignItems: 'center' }}>
        <ViewComments commentCount={commentCount} />
        </Stack>
      </Stack>

      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>
          <Person2OutlinedIcon />
          <Typography
            variant="body2"
            color="#344054"
            fontWeight="500"
            fontSize="16px"
          >
            {Maker}
          </Typography>
          <Typography
            variant="body2"
            color="#344054"
            fontWeight="500"
            fontSize="16px"
          >
            {MakerEmail}
          </Typography>
        </Stack>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={1}>
          <Person2OutlinedIcon />
          <Typography
            variant="body2"
            color="#344054"
            fontWeight="500"
            fontSize="16px"
          >
            {Checker}
          </Typography>
          <Typography
            variant="body2"
            color="#344054"
            fontWeight="500"
            fontSize="16px"
          >
            {CheckerEmail}
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default DetailsHeader
