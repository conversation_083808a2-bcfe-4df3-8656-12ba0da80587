import React, { useEffect } from 'react'
import {
  Button,
  Dialog,
  FormControl,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { Close } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { generateBrokerSecret } from '@/store/actions'

interface GenerateAPIFormProps {
  email: string
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  open: boolean
  onClose: () => void
}

const GenerateApiFormValidation = Yup.object({
  nominatedEmailAddress: Yup.string()
    .email('Invalid email')
    .required('Email must not be empty'),
})
/*
GENERATE, RESET, TERMINATE
*/

const GenerateAPIForm: React.FC<GenerateAPIFormProps> = ({
  setOpen,
  open,
  onClose,
}) => {
  const dispatch = useAppDispatch()
  const { selectedBroker, isGenerateBrokerSecretLoading } = useAppSelector(
    (state) => state.loans
  )

  const formik = useFormik({
    initialValues: {
      nominatedEmailAddress: '',
      requestType:
        selectedBroker.credentialStatus !== 'SET' ? 'GENERATE' : 'RESET',
    },
    validationSchema: GenerateApiFormValidation,
    onSubmit: async (values) => {
      await generateBrokerSecret(
        selectedBroker.id ? selectedBroker.id : '',
        dispatch,
        values
      )
    },
  })
  const { handleSubmit, errors } = formik
  useEffect(() => {
    if (!isGenerateBrokerSecretLoading) {
      setOpen(false)
    }
  }, [isGenerateBrokerSecretLoading, setOpen])

  const handleClose = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth={'xs'} fullWidth>
      <Stack
        sx={{
          p: 3,
          backgroundColor: 'background.paper',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: 1,
          position: 'relative',
        }}
      >
        <Stack mb={2}>
          <Typography
            variant="h6"
            color="#344054"
            fontWeight="700"
            fontSize="20px"
            gutterBottom
          >
            Generate New API
          </Typography>
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{
              position: 'absolute',
              right: 8,
              border: '0.8px solid #CBD5E1',
              borderRadius: '50%',
              padding: '4px',
              '&:hover': {
                backgroundColor: '#E2E8F0',
              },
            }}
          >
            <Close fontSize="small" />
          </IconButton>
          <Typography
            variant="body2"
            color="#344054"
            fontWeight="400"
            fontSize="14px"
          >
            Please enter the email address where the generated API credentials
            will be sent.
          </Typography>
        </Stack>
        <FormikProvider value={formik}>
          <Form onSubmit={handleSubmit}>
            <FormControl
              fullWidth
              margin="normal"
              error={Boolean(
                formik.touched.nominatedEmailAddress &&
                  formik.errors.nominatedEmailAddress
              )}
            >
              <TextField
                autoComplete="email"
                type="email"
                label="Enter Email address"
                margin={'normal'}
                {...formik.getFieldProps('nominatedEmailAddress')}
                fullWidth
                error={Boolean(
                  formik.touched.nominatedEmailAddress &&
                    formik.errors.nominatedEmailAddress
                )}
                helperText={
                  formik.touched.nominatedEmailAddress &&
                  formik.errors.nominatedEmailAddress
                }
              />
              <Typography
                variant="body2"
                color="#344054"
                fontWeight="400"
                fontSize="14px"
                mt={2}
              >
                Once you click generate, your request will be submitted to your
                manager for approval.
              </Typography>
              &nbsp;
              <Button
                variant="contained"
                type="submit"
                disabled={Object.keys(errors).length > 0}
                fullWidth
                sx={{
                  my: '1%',
                }}
              >
                {isGenerateBrokerSecretLoading
                  ? 'Generating...'
                  : 'Generate Credentials'}
              </Button>
            </FormControl>
          </Form>
        </FormikProvider>
      </Stack>
    </Dialog>
  )
}

export default GenerateAPIForm
