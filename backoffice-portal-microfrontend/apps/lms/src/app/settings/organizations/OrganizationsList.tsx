'use client'
import {
  Button,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import React, { useState } from 'react'
import { ArrowForwardIos } from '@mui/icons-material'
import { sentenceCase } from 'tiny-case'
import { getOrganizations } from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import { formatCurrency } from '@dtbx/store/utils'
import { setEditOrganization } from '@/store/reducers'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import {
  CustomActiveBrokerChip,
  CustomBlockedChip,
  CustomInactiveBrokerChip,
} from '@dtbx/ui/components/Chip'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'

const headerList = [
  { id: 'name', label: 'Organization', alignRight: false },
  { id: 'status', label: 'Status', alignRight: false },
  { id: 'limit', label: 'Limit', alignRight: false },
  { id: 'bankAccount', label: 'Bank Account', alignRight: false },
  { id: 'contact', label: 'Contact', alignRight: false },
  { id: 'actions', label: '', alignRight: false },
]
export const OrganizationsList = () => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { organizations, organizationsSummary } = useAppSelector(
    (state) => state.loans
  )
  const [selected, setSelected] = useState<readonly string[]>([])
  const [paginationOptions, setPaginationOptions] = useState({
    page: organizationsSummary.pageNumber,
    size: 10,
    totalPages: organizationsSummary.totalNumberOfPages,
  })

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newSelected: readonly string[] = []
    if (event.target.checked) {
      newSelected = organizations.map((org) => org.id)
    }
    setSelected(newSelected)
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []
    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }
  /********* start pagination handlers***********/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    const params = `page=${newOptions.page}&size=${newOptions.size}`
    await getOrganizations(dispatch, params)
  }
  /************* end pagination handlers ********/
  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: '8px',
        border: '1px solid #EAECF0',
        background: '#FFFFFF',
        my: '1%',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          padding: '2%',
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Organizations</Typography>
          <Typography variant="subtitle3">
            Showing {organizations.length} organizations
          </Typography>
        </Stack>
        <Button variant="outlined" startIcon={<FilterListOutlinedIcon />}>
          Filter
        </Button>
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="designations table"
          size="small"
        >
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={headerList}
            showCheckbox={true}
            rowCount={10}
            numSelected={0}
            onRequestSort={() => {}}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {organizations &&
              organizations.map((row) => {
                const {
                  id,
                  limit,
                  bankAccountNumber,
                  bankName,
                  mobile,
                  name,
                  email,
                  status,
                  cbsIdentifier,
                } = row
                const isItemSelected = selected.indexOf(id) !== -1
                return (
                  <TableRow
                    hover
                    key={id}
                    tabIndex={-1}
                    role="checkbox"
                    onClick={(event) => handleSelectOne(event, row.id)}
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        checked={isItemSelected}
                        inputProps={{
                          'aria-labelledby': id,
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography
                          variant="subtitle2"
                          sx={{ color: 'primary.main' }}
                        >
                          {name}
                        </Typography>
                        <Typography variant="subtitle3">
                          ID: {cbsIdentifier}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      {status === 'Active' ? (
                        <CustomActiveBrokerChip label={sentenceCase(status)} />
                      ) : status === 'Inactive' ? (
                        <CustomInactiveBrokerChip
                          label={sentenceCase(status)}
                        />
                      ) : status === 'Pending Approval' ? (
                        <CustomInactiveBrokerChip
                          label={sentenceCase(status)}
                        />
                      ) : status === 'Rejected' ? (
                        <CustomBlockedChip
                          label={sentenceCase(status)}
                        />
                      ) : null}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatCurrency(limit)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack>
                        <Typography variant="subtitle3" color={'primary.main'}>
                          {bankAccountNumber || 'N/A'}
                        </Typography>
                        <Typography variant="subtitle3">
                          {bankName || 'N/A'}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography variant="subtitle3" color={'primary.main'}>
                          {mobile || 'N/A'}
                        </Typography>
                        <Typography variant="body2">{email}</Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={() => {
                          dispatch(setEditOrganization(row))
                          router.push('/settings/organizations/edit')
                        }}
                      >
                        <ArrowForwardIos />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>
      {organizationsSummary.totalNumberOfPages > 0 && (
        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: organizationsSummary.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
        />
      )}
    </Paper>
  )
}
