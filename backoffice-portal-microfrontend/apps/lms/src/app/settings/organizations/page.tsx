'use client'
import { Button, IconButton, Paper, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch } from '@/store'
import { getOrganizations } from '@/store/actions'

import { OrganizationsList } from '../../settings/organizations/OrganizationsList'

const OrganizationsPage = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  useEffect(() => {
    const params = 'page=0&size=10'
    getOrganizations(dispatch, params)
  }, [])
  return (
    <Paper
      elevation={0}
      sx={{
        px: '2%',
        background: '#F7F7F7',
        minHeight: '94vh',
        py: '2%',
      }}
    >
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => window.history.back()}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>
      <Typography
        variant="h5"
        sx={{
          textAlign: 'left',
          py: '1%',
        }}
      >
        Manage Organizations
      </Typography>
      <Stack direction="row" justifyContent={'flex-end'}>
        {/*<CustomSearchInput*/}
        {/*  placeholder={'Search Organization'}*/}
        {/*  endAdornment={*/}
        {/*    <SearchRounded*/}
        {/*      sx={{*/}
        {/*        color: 'black',*/}
        {/*      }}*/}
        {/*    />*/}
        {/*  }*/}
        {/*  sx={{*/}
        {/*    width: '30%',*/}
        {/*    background: '#FFFFFF',*/}
        {/*    borderRadius: '8px',*/}
        {/*    border: '1px solid #D0D5DD',*/}
        {/*    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',*/}
        {/*  }}*/}
        {/*/>*/}
        <Button
          variant="contained"
          endIcon={<AddOutlinedIcon />}
          onClick={() => router.push('/settings/organizations/create')}
        >
          Add New Organization
        </Button>
      </Stack>

      <OrganizationsList />
    </Paper>
  )
}
export default OrganizationsPage
