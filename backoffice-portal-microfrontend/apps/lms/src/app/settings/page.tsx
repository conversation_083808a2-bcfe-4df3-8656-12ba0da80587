'use client'
import React, { useEffect } from 'react'
import { Paper, Typography } from '@mui/material'
import { getLoanProductCategories } from '@/store/actions'
import { useAppDispatch } from '@/store'

import { SettingsList } from '@/app/settings/SettingsList'

const SettingsPage = () => {
  const dispatch = useAppDispatch()

  useEffect(() => {
    getLoanProductCategories(dispatch, 1, 10)
  }, [])
  return (
    <Paper
      elevation={0}
      sx={{
        px: '2%',
        background: '#F7F7F7',
        minHeight: '92vh',
      }}
    >
      <Typography
        variant="h5"
        sx={{
          textAlign: 'left',
          py: '2%',
        }}
      >
        Settings
      </Typography>
      <SettingsList />
    </Paper>
  )
}

export default SettingsPage
