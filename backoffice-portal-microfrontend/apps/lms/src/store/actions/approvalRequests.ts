import { Dispatch } from '@reduxjs/toolkit'

import {
  setApprovalRequestResponse,
  setApprovals,
  setLoadingApprovals,
  setLoadingRequestTypes,
  setMakerCheckerTypes,
  setRequestTypes,
  setRequestTypesSuccess,
  setSelectedApprovalRequestComments,
} from '../reducers'

import { setNotification } from '@dtbx/store/reducers'

import { secureapi } from '@dtbx/store/utils'

export const getApprovals = async (dispatch: Dispatch, params?: string) => {
  dispatch(setLoadingApprovals(true))
  try {
    const resp = await secureapi.get(
      `/backoffice-auth/maker-checker/approvals${params || ''}`
    )
    dispatch(setApprovals(resp.data.data))
    dispatch(setApprovalRequestResponse(resp.data))
    dispatch(setLoadingApprovals(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setLoadingApprovals(false))
  }
}

export const getApprovalRequestByEntityId = async (
  dispatch: Dispatch,
  entityId: string,
  params: string
) => {
  dispatch(setLoadingApprovals(true))
  try {
    const resp = await secureapi.get(
      `/backoffice-bff/dbp/brokers/${entityId}/changes${params || ''}`
    )
    dispatch(setSelectedApprovalRequestComments(resp.data.data))
    dispatch(setLoadingApprovals(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setLoadingApprovals(false))
  }
}

export const getApprovalRequestTypes = async (
  dispatch: Dispatch,
  channel?: 'DBP' | 'LMS'
) => {
  let url = `/backoffice-auth/maker-checker/types`
  url += channel ? `?channel=${channel}` : ''
  dispatch(setLoadingRequestTypes(true))
  dispatch(setRequestTypesSuccess(false))
  try {
    const response = await secureapi.get(url)
    dispatch(setRequestTypes(response.data))
    dispatch(setRequestTypesSuccess(true))
    dispatch(setLoadingRequestTypes(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setLoadingRequestTypes(false))
    dispatch(setRequestTypesSuccess(false))
  }
}

export const getMakerCheckerTypes = async (
  dispatch: Dispatch,
  params: string
) => {
  dispatch(setMakerCheckerTypes([]))
  try {
    const response = await secureapi.get(
      `/backoffice-auth/maker-checker/types${params || ''}`
    )
    dispatch(setMakerCheckerTypes(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}
