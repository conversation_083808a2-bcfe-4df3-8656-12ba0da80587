import { combineReducers } from 'redux'

import approvalRequests from './ApprovalRequests'
import loansReducer from './loansReducer'
import {navigation, authReducer, overlays, notifications} from '@dtbx/store/reducers'

const rootReducer = combineReducers({
  approvalRequests: approvalRequests,
  loans: loansReducer,
  navigation: navigation,
  notifications: notifications,
  auth: authReducer,
  overlay: overlays
})
export default rootReducer
