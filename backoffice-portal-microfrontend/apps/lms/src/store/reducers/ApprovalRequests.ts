import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import {
  IApprovalRequest,
  IApprovalRequestsResponse,
  IMakerCheckerType,
  IProfileApprovalRequests,
  RequestType,
} from '../interfaces'
export interface IState {
  loading: boolean
  openReviewRequest: {
    open: boolean
    request: IApprovalRequest | null
  }
  error: boolean | null
  isLoadingRequests: boolean
  approvalRequests: IApprovalRequest[]
  approvalRequestsCount: number
  selectedApprovalRequest: IApprovalRequest
  selectedApprovalRequestComments: IApprovalRequest[]
  userApprovalRequestFilters: Record<string, string | string[]>
  approvalRequestSearch: {
    searchBy: Array<keyof IApprovalRequest>
    searchTerm: string
  }
  approvalRequestResponse: IApprovalRequestsResponse
  requestTypes: RequestType[]
  isRequestTypesLoading: boolean
  isRequestTypesSuccess: boolean
  makerCheckerTypes: IMakerCheckerType[]
  pendingCustomerApprovalRequests: IApprovalRequest[]
  pendingCustomerApprovalRequestResponse: IApprovalRequestsResponse
  pendingSingleCustomerApprovalRequests: IApprovalRequest[]
  customersWithPendingApprovals: IProfileApprovalRequests[]
  pendingSingleCustomerApprovalRequestResponse: IApprovalRequestsResponse
  approvalDrawerOpen: boolean
  approvalActions: boolean
  openDialog: boolean
}

const initialState: IState = {
  loading: false,
  openReviewRequest: {
    open: false,
    request: null,
  },
  error: null,
  isLoadingRequests: false,
  approvalRequests: [],
  selectedApprovalRequest: {} as IApprovalRequest,
  selectedApprovalRequestComments: [],
  approvalRequestsCount: 0,
  userApprovalRequestFilters: {},
  approvalRequestSearch: {
    searchBy: ['status'],
    searchTerm: '',
  },
  approvalRequestResponse: {} as IApprovalRequestsResponse,
  requestTypes: [],
  isRequestTypesLoading: false,
  isRequestTypesSuccess: false,
  makerCheckerTypes: [],
  pendingCustomerApprovalRequests: [],
  pendingCustomerApprovalRequestResponse: {} as IApprovalRequestsResponse,
  pendingSingleCustomerApprovalRequests: [],
  pendingSingleCustomerApprovalRequestResponse: {} as IApprovalRequestsResponse,
  customersWithPendingApprovals: [],
  approvalDrawerOpen: false,
  approvalActions: false,
  openDialog: false,
}
const ApprovalRequestsSlice = createSlice({
  name: 'approvalRequests',
  initialState,
  reducers: {
    setOpenReviewRequest: (state, action) => {
      state.openReviewRequest = action.payload
    },
    setLoadingApprovals: (state, action: PayloadAction<boolean>) => {
      state.isLoadingRequests = action.payload
    },
    setApprovals: (state, action: PayloadAction<IApprovalRequest[]>) => {
      state.approvalRequests = action.payload
    },
    setApprovalDrawerOpen: (state, action: PayloadAction<boolean>) => {
      state.approvalDrawerOpen = action.payload
    },
    setPendingCustomerApprovals: (
      state,
      action: PayloadAction<IApprovalRequest[]>
    ) => {
      state.pendingCustomerApprovalRequests = action.payload
    },
    setPendingSingleCustomerApprovalsResponse: (
      state,
      action: PayloadAction<IApprovalRequestsResponse>
    ) => {
      state.pendingSingleCustomerApprovalRequestResponse = action.payload
    },
    setPendingSingleCustomerApprovals: (
      state,
      action: PayloadAction<IApprovalRequest[]>
    ) => {
      state.pendingSingleCustomerApprovalRequests = action.payload
    },
    setPendingCustomerApprovalsResponse: (
      state,
      action: PayloadAction<IApprovalRequestsResponse>
    ) => {
      state.pendingCustomerApprovalRequestResponse = action.payload
    },
    setCustomersWithPendingApprovals: (
      state,
      action: PayloadAction<IProfileApprovalRequests[]>
    ) => {
      state.customersWithPendingApprovals = action.payload
    },
    setSelectedApprovalRequestComments: (
      state,
      action: PayloadAction<IApprovalRequest[]>
    ) => {
      state.selectedApprovalRequestComments = action.payload ?? []
    },
    setSelectedApprovalRequest: (
      state,
      action: PayloadAction<IApprovalRequest>
    ) => {
      state.selectedApprovalRequest = action.payload
    },
    setUserApprovalRequestsFilters: (
      state,
      action: PayloadAction<Record<string, string | string[]>>
    ) => {
      state.userApprovalRequestFilters = action.payload
    },
    setUserApprovalRequestSearch: (state, action) => {
      state.approvalRequestSearch = action.payload
    },
    setApprovalRequestResponse: (
      state,
      action: PayloadAction<IApprovalRequestsResponse>
    ) => {
      state.approvalRequestResponse = action.payload
      state.approvalRequestsCount = action.payload.totalElements
    },
    setRequestTypes: (state, action: PayloadAction<RequestType[]>) => {
      state.requestTypes = action.payload
    },
    setLoadingRequestTypes: (state, action: PayloadAction<boolean>) => {
      state.isRequestTypesLoading = action.payload
    },
    setRequestTypesSuccess: (state, action: PayloadAction<boolean>) => {
      state.isRequestTypesSuccess = action.payload
    },
    setMakerCheckerTypes: (
      state,
      action: PayloadAction<IMakerCheckerType[]>
    ) => {
      state.makerCheckerTypes = action.payload
    },
    setApprovalActions: (state, action: PayloadAction<boolean>) => {
      state.approvalActions = action.payload
    },
    setOpenDialog: (state, action: PayloadAction<boolean>) => {
      state.openDialog = action.payload
    },
  },
})
export const {
  setOpenReviewRequest,
  setApprovals,
  setApprovalDrawerOpen,
  setPendingCustomerApprovals,
  setPendingCustomerApprovalsResponse,
  setPendingSingleCustomerApprovals,
  setPendingSingleCustomerApprovalsResponse,
  setCustomersWithPendingApprovals,
  setLoadingApprovals,
  setSelectedApprovalRequest,
  setSelectedApprovalRequestComments,
  setUserApprovalRequestsFilters,
  setUserApprovalRequestSearch,
  setApprovalRequestResponse,
  setRequestTypes,
  setLoadingRequestTypes,
  setRequestTypesSuccess,
  setMakerCheckerTypes,
  setApprovalActions,
  setOpenDialog,
} = ApprovalRequestsSlice.actions
export default ApprovalRequestsSlice.reducer
