export type Status = 'Active' | 'Inactive' | 'Pending Approval' | 'Blocked'
export interface IIpfRequest {
  policyNumber: string
  policyType: string
  policyNo: string
  provider: string
  customerName: string
  customerId: string
  status: string
  amount: number
  tenure: string
  customerProfile: ICustomerLoanProfile
}
export interface IBrokerDetails {
  accountBranchName?: string
  bankAccountNumber?: string
  bankCode?: string
  bankName?: string
  branchCode?: string
  callBackUrl: string
  email?: string
  id?: string
  mobile?: string
  name?: string
  physicalAddress?: IPhysicalAddress
  product?: ILoanProduct[]
  status: string
  swiftCode?: string
  dateCreated?: string
  dateModified?: string
  createdBy?: string | null
  modifiedBy?: string | null
  comments?: string
}
export interface ILoanRequest {
  customer: ILoanRequestCustomer
  id: string
  loan: ILoanObject | null
  loanRequest: ILoanRequestObject
  productId: string
  reference: string
  requestReference: string
  status: string
  cancelled: boolean
}
export interface ILoanOptinRequest {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  reference: string
  status: string
  customer: ILoanRequestCustomer
  productId: string
  cancelled: boolean
}
export interface ILoanRequestObject {
  loanAmount: number
  loanPurpose: string
  loanTenure: string
  providerItemReference: string
}
export interface ILoanObject {
  cbsDisbursementRef: string
  externalLoanId: string
  frequency: string
  interestAmount: number
  issueDate: string
  lastRepaymentUpdate: string
  loanAmount: number
  loanId: string
  loanTenure: string
  maturityDate: string
  outstandingInterestAmount: number
  outstandingPrincipalAmount: number
  paidPrepaymentAmount: number
  prepaymentAmount: number
  principalAmount: number
  settlementAccount: number
  status: string
}
export interface ILoanRequestCustomer {
  firstName: string
  lastName: string
  id: string
  status: string
  idDocumentNumber: string
}
export interface ILoanRequestsSummary {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
}
export interface ICustomerLoanProfile {
  branch: string | null
  cif: string | null
  pepPipRemarks: string | null
  customerType: string
  dateOfBirth: string
  email: string
  firstName: string
  gender: string
  idDocumentExpiryDate: string
  idDocumentNumber: string
  kraPinNumber: string
  lastName: string
  middleName: string
  mobile: string
  nationality: string
  pepPipDeclarationStatus: string
  pipPepCategoryId: string
  physicalAddress: ICustomerPhysicalAddress
  pipPepCategory: string
  ssn: string | null
  status: string
  occupation: { id: string; value: string; description: string }
  sourceOfFunds: { id: string; value: string; description: string }
  sourceOfWealth: { id: string; value: string; description: string }
  usCitizen: boolean
}
export interface ICustomerPhysicalAddress {
  street: string
  city: string
  country: string
}
export interface ICustomerDocument {
  id: string
  type: string
  file: string
}
export interface ICustomerDocumentUpload {
  documentType: string
  document: string
  comments: string
}
export interface ICustomerCheck {
  code: string
  dateCreated: string
  dateModified: string
  providerReference?: string | null
  id: string
  message: string
  additionalDetails: {
    fullName: string
  }
  status: string
}
export interface IKYCData {
  customerId: string
  iprs: {
    comments: string
    status: string
  }
  kra: {
    comments: string
    status: string
  }
  worldCheck: {
    comments: string
    status: string
  }
}

export interface IRepaymentHistory {
  amount: number
  currency: string
  transactionDate: string
  transactionDesc: string
  transactionReference: string
  transactionType: string
  valueDate: string
}
export interface ITransactionData {
  amountPaid: string
  date: string
  balance: string
  transactionRef: string
  DTBRef: string
  service: string
  paymentMethod: string
}
export interface ILoanProduct {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  code: string
  name: string
  country: string
  currency: string
  organization: IProductOrganization
  type: IProductType
  expiryDate: string
  exposureLimit: number
  status: string
  customerType: string
  minimumAmount: number
  maximumAmount: number
  measureOfTenure: string
  minimumTenure: number
  maximumTenure: number
  interestRateType: string
  dynamicInterestFixedComponent: number
  interestRate: number
  facilityFee: number
  exciseDuty: number
  facilityFeeRecoveryType: string
  interestRecoveryType: string
  minimumInterestRecoveryType: string
  upfrontInterestRecognitionType: string
  minimumInterestCalculationMode: string
  externalProductName: string
  rollOverFee: number
  rollOverPeriod: number
  maxRollOverCount: number
  prepaymentType: string
  prepaymentCalculation: string
  prepaymentValue: number
  penalty: number
  interestCalculation: string
  amortizationMode: string
  multipleDrawDown: boolean
  tranches: boolean
  trancheInterval: string
  repaymentCycle: string
  numberOfInstallments: number
  earlyPaymentsAllowed: boolean
  periodInArrears: number
  minimumInterestValue: number
  interestGl: string
  facilityFeeGl: string
  exciseDutyGl: string
  rollOverGl: string
  penaltyGl: string
  prepaymentGl: string
  disbursementGl: string
  upfrontInterestLiabilityGl: string
  disbursementCreditAccountType: string
  repaymentGl: string
  gracePeriodType: string
  hasRecoveryTracking: boolean
  externalProductCode: string
  disbursementCreditAccount: string
  disbursementCreditAccountBranch: string
  isManaged: boolean
  manualApprovalAmount: number
  typeId?: string
  organizationId?: string
  loanCreationBranch?: string
}
export interface IProductOrganization {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  name: string
  cbsIdentifier: string
  limit: string
}
export interface IProductType {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  code: string
  name: string
}

export interface IWorldCheckOverride {
  id: string
  status: string
  comments: string
}
export interface IOrganization {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  name: string
  status: string
  cbsIdentifier: string
  email: string
  mobile: string
  bankName: string
  bankCode: string
  swiftCode: string
  bankAccountNumber: string
  branchCode: string
  accountBranchName: string
  physicalAddress: {
    country: string
    town: string
    physicalAddress: string
  }
  limit: number
  limitCurrency: string
}

export interface ISelectedBrokerExisting extends IOrganization {
  productIds?: string[]
  callBackUrl?: string
}

export interface IOrganizationCreate {
  name: string
  email: string
  mobile: string
  bankName: string
  bankCode: string
  status: string
  swiftCode: string
  bankAccountNumber: string
  branchCode: string
  accountBranchName: string
  physicalAddress: {
    country: string
    town: string
    physicalAddress: string
  }
  limit: number
  limitCurrency: string
}

export interface IOrganizationMake extends IOrganizationCreate {
  cbsIdentifier?: string
  comments?: string
}

export interface ILoanProductCreate {
  name: string
  organizationId: string
  country: string
  currency: string
  // expiryDate: string
  disbursementCreditAccount: string
  disbursementCreditAccountBranch: string
  code: string
  externalProductCode: string
  typeId: string
  status: string
  customerType: string
  measureOfTenure: string
  interestRateType: string
  facilityFeeRecoveryType: string
  interestRecoveryType: string
  prepaymentType: string
  prepaymentCalculation: string
  interestCalculation: string
  amortizationMode: string
  trancheInterval: string
  repaymentCycle: string
  disbursementCreditAccountType: string
  gracePeriodType: string
  minimumInterestRecoveryType: string
  upfrontInterestRecognitionType: string
  minimumInterestCalculationMode: string
  externalProductName: string
  //boolean values
  isManaged: boolean
  earlyPaymentsAllowed: boolean
  multipleDrawDown: boolean
  tranches: boolean
  hasRecoveryTracking: boolean
  //number values
  // exposureLimit: number
  manualApprovalAmount: number
  minimumAmount: number

  maximumAmount: number
  minimumTenure: number
  maximumTenure: number
  dynamicInterestFixedComponent: number
  interestRate: number
  facilityFee: number
  exciseDuty: number
  rollOverFee: number
  rollOverPeriod: number
  maxRollOverCount: number
  prepaymentValue: number
  penalty: number
  // numberOfInstallments: number
  periodInArrears: number
  minimumInterestValue: number
  //gl accounts
  interestGl: string
  facilityFeeGl: string
  exciseDutyGl: string
  rollOverGl: string
  penaltyGl: string
  prepaymentGl: string
  disbursementGl: string
  repaymentGl: string
  upfrontInterestLiabilityGl: string
  [key: string]: string | number | boolean
}

export interface ILoanValidation {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  code: string
  type: string
}
export interface ISingleProductValidation {
  createdBy: string | null
  customerType: string
  dateCreated: string
  dateModified: string
  id: string
  modifiedBy: string | null
  stage: string
  validation: ILoanValidation
  validDays: number
  optional: boolean
}
export interface IProductCategoryRequest {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IProductCategory[]
}
export interface IProductCategory {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  code: string
  name: string
  parentCategory: string
}
export interface IProductCategoryType {
  code: string
  createdBy: string
  dateCreated: string
  dateModified: string
  id: string
  name: string
  parentCategory: IProductCategory
}
export interface IProductValidationCreate {
  stage: string
  validationId: string
  validDays: number
  customerType: string
  optional: boolean
}

export interface IBankBranch {
  branchName: string
  branchCode: string
}

export interface IOrganizationLimitResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IOrganizationLimit[]
}

export interface IOrganizationLimit {
  organizationName: string
  misCode: string
  exposureLimit: number
  currentExposure: number
  availableLimit: number
}

export interface ILoanRequestReportResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: ILoanRequestReport[]
}

export interface ILoanRequestReport {
  productID: string
  productName: string
  loanRequestId: string
  organizationName: string
  firstName: string
  middleName: string
  lastName: string
  customerID: string
  requestDate: string
  requestAmount: number
  loanTenor: number
  interestRate: number
  requestStatus: string
}

export interface ILoanReportResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: ILoanReport[]
}

export interface ILoanReport {
  productID: string
  productName: string
  organizationName: string
  loanID: string
  flexCubeLoanReference: string
  customerID: string
  flexCubeCustomerCIF: string
  firstName: string
  middleName: string
  lastName: string
  interestRate: number
  flexCubeLoanSettlementAccount: string
  prePaymentAmount: number
  loanPrincipalAmount: number
  loanCreationDate: Date
  loanTenor: number
  loanMaturityDate: Date
  loanStatus: string
  totalOutstandingPrincipal: number
  totalOutstandingInterest: number
  totalOutstandingBalance: number
  overduePrincipal: number
  overdueInterest: number
  penalPrincipalAmountOverdue: number
  penalInterestAmountOverdue: number
  totalOverdueAmount: number
}

export interface IRequestCheckReportsResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IRequestCheck[]
}

export interface IRequestCheck {
  checkDate: string
  productId: string
  productName: string
  organizationName: string
  loanRequestId: string
  channelReference: string
  checkName: string
  status: string
  message: string
}

export interface ICustomerRequestCheckReport {
  checkType: string
  customerId: string
  customerName: string
  dateCreated: string
  dateModified: string
  loanRequestId: string
  message: string
  reference: string
  status: string
}
export interface ICustomerRequestCheckReportResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: ICustomerRequestCheckReport[]
}
//Broker management
export interface IBrokerCreate {
  id?: string
  dateCreated?: string
  dateModified?: string
  createdBy?: string
  modifiedBy?: string
  name: string
  status?: string
  email: string
  mobile: string
  bankName: string
  bankCode: string
  swiftCode: string
  bankAccountNumber: string
  branchCode: string
  organizationId?: string
  accountBranchName: string
  physicalAddress: {
    country: string
    town: string
    physicalAddress: string
  }
  credentialStatus?: string
  limit?: number
  limitCurrency?: string
  totalDisbursements?: string
  callBackUrl?: string
  productIds: string[]
}
export interface IBroker {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  modifiedBy: string
  name: string
  status: string
  email: string
  mobile: string
  bankName: string
  bankCode: string
  swiftCode: string
  bankAccountNumber: string
  branchCode: string
  organizationId: string
  accountBranchName: string
  physicalAddress: {
    country: string
    town: string
    physicalAddress: string
  }
  credentialStatus: string
  organization: IOrganization | null
  limit: number
  limitCurrency: string
  totalDisbursements: string
  callBackUrl: string
  productIds: string[]
}
export interface IBrokerPhysicalAddress {
  country: string
  town: string
  physicalAddress: string
}

export interface ICreateBrokerForm {
  name: string
  email: string
  mobile: string
  bankName: string
  status: string
  bankCode: string
  swiftCode: string
  bankAccountNumber: string
  branchCode: string
  accountBranchName: string
  physicalAddress: IBrokerPhysicalAddress
  productIds: string[]
  callBackUrl: string
}
export interface IGenerateBrokerCreds {
  nominatedEmailAddress: string
  requestType: string
}
export interface IPhysicalAddress {
  country: string
  town: string
  physicalAddress: string
}
export interface IDecodeToken {
  last_name: string
  first_name: string
  user_id: string
  authorities: string[]
  sub: string
  iat: number
  exp: number
  resources?: IResource[]
}
export interface IResource {
  resourceType: string
  resourceIds: string[]
}
