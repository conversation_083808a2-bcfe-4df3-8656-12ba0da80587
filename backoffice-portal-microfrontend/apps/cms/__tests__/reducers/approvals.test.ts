import { describe, it, expect, beforeEach } from 'vitest'
import approvalsReducer, {
  setIsLoadingApprovals,
  setCardApprovalRequests,
  setSelectedCardApprovalRequest,
  setCardApprovalsPagination,
  resetCardApprovalRequests,
} from '@/store/reducers/approvals'
import { ICardApprovalRequest, IPagination } from '@/store/interfaces/Approvals'
import { createMockCardApprovalRequest } from '../mocks/data'

interface IInitialState {
  isLoadingApprovals: boolean
  cardApprovalRequests: ICardApprovalRequest[]
  selectedCardApprovalRequest: ICardApprovalRequest
  cardApprovalsPagination: IPagination
}

describe('Approvals Reducer', () => {
  let initialState: IInitialState

  beforeEach(() => {
    initialState = {
      isLoadingApprovals: false,
      cardApprovalRequests: [],
      selectedCardApprovalRequest: {} as ICardApprovalRequest,
      cardApprovalsPagination: {} as IPagination,
    }
  })

  describe('initial state', () => {
    it('should return the initial state', () => {
      const result = approvalsReducer(undefined, { type: 'unknown' })
      expect(result).toEqual(initialState)
    })
  })

  describe('loading state management', () => {
    it('should handle setIsLoadingApprovals true', () => {
      const action = setIsLoadingApprovals(true)
      const result = approvalsReducer(initialState, action)
      expect(result.isLoadingApprovals).toBe(true)
    })

    it('should handle setIsLoadingApprovals false', () => {
      const stateWithLoading = { ...initialState, isLoadingApprovals: true }
      const action = setIsLoadingApprovals(false)
      const result = approvalsReducer(stateWithLoading, action)
      expect(result.isLoadingApprovals).toBe(false)
    })
  })

  describe('approval requests management', () => {
    it('should handle setCardApprovalRequests with empty array', () => {
      const action = setCardApprovalRequests([])
      const result = approvalsReducer(initialState, action)
      expect(result.cardApprovalRequests).toEqual([])
    })

    it('should handle setCardApprovalRequests with multiple requests', () => {
      const mockRequests = [
        createMockCardApprovalRequest({ id: '1', status: 'PENDING' }),
        createMockCardApprovalRequest({ id: '2', status: 'APPROVED' }),
        createMockCardApprovalRequest({ id: '3', status: 'REJECTED' }),
      ]
      const action = setCardApprovalRequests(mockRequests)
      const result = approvalsReducer(initialState, action)
      expect(result.cardApprovalRequests).toEqual(mockRequests)
      expect(result.cardApprovalRequests).toHaveLength(3)
    })

    it('should replace existing approval requests when setting new ones', () => {
      const existingRequests = [createMockCardApprovalRequest({ id: '1' })]
      const stateWithRequests = { ...initialState, cardApprovalRequests: existingRequests }
      
      const newRequests = [
        createMockCardApprovalRequest({ id: '2' }),
        createMockCardApprovalRequest({ id: '3' }),
      ]
      const action = setCardApprovalRequests(newRequests)
      const result = approvalsReducer(stateWithRequests, action)
      
      expect(result.cardApprovalRequests).toEqual(newRequests)
      expect(result.cardApprovalRequests).toHaveLength(2)
    })
  })

  describe('selected approval request management', () => {
    it('should handle setSelectedCardApprovalRequest', () => {
      const mockRequest = createMockCardApprovalRequest({
        id: 'selected-1',
        status: 'PENDING',
        maker: 'John Doe',
      })
      const action = setSelectedCardApprovalRequest(mockRequest)
      const result = approvalsReducer(initialState, action)
      expect(result.selectedCardApprovalRequest).toEqual(mockRequest)
    })

    it('should replace existing selected request', () => {
      const existingRequest = createMockCardApprovalRequest({ id: 'existing' })
      const stateWithSelected = { ...initialState, selectedCardApprovalRequest: existingRequest }
      
      const newRequest = createMockCardApprovalRequest({ id: 'new' })
      const action = setSelectedCardApprovalRequest(newRequest)
      const result = approvalsReducer(stateWithSelected, action)
      
      expect(result.selectedCardApprovalRequest).toEqual(newRequest)
      expect(result.selectedCardApprovalRequest.id).toBe('new')
    })
  })

  describe('pagination management', () => {
    it('should handle setCardApprovalsPagination', () => {
      const mockPagination: IPagination = {
        pageNumber: 2,
        pageSize: 20,
        totalElements: 100,
        totalNumberOfPages: 5,
      }
      const action = setCardApprovalsPagination(mockPagination)
      const result = approvalsReducer(initialState, action)
      expect(result.cardApprovalsPagination).toEqual(mockPagination)
    })

    it('should handle pagination with zero values', () => {
      const mockPagination: IPagination = {
        pageNumber: 0,
        pageSize: 0,
        totalElements: 0,
        totalNumberOfPages: 0,
      }
      const action = setCardApprovalsPagination(mockPagination)
      const result = approvalsReducer(initialState, action)
      expect(result.cardApprovalsPagination).toEqual(mockPagination)
    })

    it('should replace existing pagination data', () => {
      const existingPagination: IPagination = {
        pageNumber: 1,
        pageSize: 10,
        totalElements: 50,
        totalNumberOfPages: 5,
      }
      const stateWithPagination = { ...initialState, cardApprovalsPagination: existingPagination }
      
      const newPagination: IPagination = {
        pageNumber: 3,
        pageSize: 25,
        totalElements: 200,
        totalNumberOfPages: 8,
      }
      const action = setCardApprovalsPagination(newPagination)
      const result = approvalsReducer(stateWithPagination, action)
      
      expect(result.cardApprovalsPagination).toEqual(newPagination)
    })
  })

  describe('reset functionality', () => {
    it('should handle resetCardApprovalRequests', () => {
      const stateWithData = {
        isLoadingApprovals: true,
        cardApprovalRequests: [createMockCardApprovalRequest()],
        selectedCardApprovalRequest: createMockCardApprovalRequest(),
        cardApprovalsPagination: {
          pageNumber: 2,
          pageSize: 20,
          totalElements: 100,
          totalNumberOfPages: 5,
        },
      }
      
      const action = resetCardApprovalRequests()
      const result = approvalsReducer(stateWithData, action)
      expect(result).toEqual(initialState)
    })
  })

  describe('state immutability', () => {
    it('should not mutate the original state when setting loading', () => {
      const action = setIsLoadingApprovals(true)
      const result = approvalsReducer(initialState, action)
      expect(result).not.toBe(initialState)
      expect(initialState.isLoadingApprovals).toBe(false)
    })

    it('should not mutate the original state when setting approval requests', () => {
      const mockRequests = [createMockCardApprovalRequest()]
      const action = setCardApprovalRequests(mockRequests)
      const result = approvalsReducer(initialState, action)
      expect(result).not.toBe(initialState)
      expect(initialState.cardApprovalRequests).toEqual([])
    })

    it('should not mutate the original state when setting pagination', () => {
      const mockPagination: IPagination = {
        pageNumber: 1,
        pageSize: 10,
        totalElements: 50,
        totalNumberOfPages: 5,
      }
      const action = setCardApprovalsPagination(mockPagination)
      const result = approvalsReducer(initialState, action)
      expect(result).not.toBe(initialState)
      expect(initialState.cardApprovalsPagination).toEqual({})
    })
  })

  describe('multiple actions', () => {
    it('should handle multiple actions in sequence', () => {
      const mockRequests = [createMockCardApprovalRequest()]
      const mockPagination: IPagination = {
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      }
      
      let state = approvalsReducer(initialState, setIsLoadingApprovals(true))
      state = approvalsReducer(state, setCardApprovalRequests(mockRequests))
      state = approvalsReducer(state, setCardApprovalsPagination(mockPagination))
      state = approvalsReducer(state, setIsLoadingApprovals(false))
      
      expect(state.isLoadingApprovals).toBe(false)
      expect(state.cardApprovalRequests).toEqual(mockRequests)
      expect(state.cardApprovalsPagination).toEqual(mockPagination)
    })
  })

  describe('edge cases', () => {
    it('should handle setting selected request to empty object', () => {
      const action = setSelectedCardApprovalRequest({} as ICardApprovalRequest)
      const result = approvalsReducer(initialState, action)
      expect(result.selectedCardApprovalRequest).toEqual({})
    })

    it('should handle large pagination numbers', () => {
      const largePagination: IPagination = {
        pageNumber: 999999,
        pageSize: 100000,
        totalElements: 99999999,
        totalNumberOfPages: 999999,
      }
      const action = setCardApprovalsPagination(largePagination)
      const result = approvalsReducer(initialState, action)
      expect(result.cardApprovalsPagination).toEqual(largePagination)
    })
  })
})
