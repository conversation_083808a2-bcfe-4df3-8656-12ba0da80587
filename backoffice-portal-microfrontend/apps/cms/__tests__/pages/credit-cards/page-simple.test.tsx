import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '../../test-utils'
import CreditCardsPage from '../../../src/app/credit-cards/page'

// Mock the child components to avoid complex dependencies
vi.mock('../../../src/app/credit-cards/List', () => ({
  CreditCardList: () => <div data-testid="credit-card-list">Credit Card List Component</div>
}))

vi.mock('../../../src/app/credit-cards/BranchViewList', () => ({
  BranchViewList: () => <div data-testid="branch-view-list">Branch View List Component</div>
}))

// Mock MUI components that might cause issues
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Chip: ({ children, ...props }: any) => <span data-testid="chip" {...props}>{children}</span>,
    Divider: () => <hr data-testid="divider" />,
    Stack: ({ children, ...props }: any) => <div data-testid="stack" {...props}>{children}</div>,
    Typography: ({ children, ...props }: any) => <div data-testid="typography" {...props}>{children}</div>,
  }
})

vi.mock('@dtbx/ui/components/Tabs', () => ({
  AntTab: ({ children, ...props }: any) => <button data-testid="ant-tab" {...props}>{children}</button>,
  AntTabs: ({ children, ...props }: any) => <div data-testid="ant-tabs" {...props}>{children}</div>,
  TabPanel: ({ children, ...props }: any) => <div data-testid="tab-panel" {...props}>{children}</div>,
}))

describe('CreditCardsPage - Basic Structure', () => {
  const defaultState = {
    cards: {
      creditCardResponse: {
        pageNumber: 1,
        pageSize: 10,
        totalElements: 25,
        totalNumberOfPages: 3,
        data: [],
      },
      selectedCardStatus: 'inactive' as const,
      isBranchListView: false,
      branchCardListPagination: {
        pageNumber: 1,
        pageSize: 10,
        totalElements: 15,
        totalNumberOfPages: 2,
      },
      currentTabIndex: 0,
      isLoadingCards: false,
      isLoadingSingleCard: false,
      isLoadingActivateCard: false,
      isLoadingSetPinCard: false,
      isLoadingResetPinRetries: false,
      cardsError: false,
      cardsSuccess: false,
      creditCardsList: [],
      selectedCardToView: {} as any,
      isLoadingBranchCards: false,
      branchCardsList: [],
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render without crashing', () => {
    expect(() => {
      render(<CreditCardsPage />, { preloadedState: defaultState })
    }).not.toThrow()
  })

  it('should render basic tab structure', () => {
    render(<CreditCardsPage />, { preloadedState: defaultState })
    
    expect(screen.getByTestId('ant-tabs')).toBeInTheDocument()
  })

  it('should render CreditCardList when not in branch view', () => {
    render(<CreditCardsPage />, { preloadedState: defaultState })
    
    // There are multiple credit-card-list elements, so use getAllByTestId
    const creditCardLists = screen.getAllByTestId('credit-card-list')
    expect(creditCardLists.length).toBeGreaterThan(0)
    expect(screen.queryByTestId('branch-view-list')).not.toBeInTheDocument()
  })

  it('should render BranchViewList when in branch view mode', () => {
    const branchViewState = {
      ...defaultState,
      cards: {
        ...defaultState.cards,
        isBranchListView: true,
      },
    }
    
    render(<CreditCardsPage />, { preloadedState: branchViewState })
    
    // There are multiple branch-view-list elements, so use getAllByTestId
    const branchViewLists = screen.getAllByTestId('branch-view-list')
    expect(branchViewLists.length).toBeGreaterThan(0)
    expect(screen.queryByTestId('credit-card-list')).not.toBeInTheDocument()
  })

  it('should render tab panels', () => {
    render(<CreditCardsPage />, { preloadedState: defaultState })
    
    const tabPanels = screen.getAllByTestId('tab-panel')
    expect(tabPanels.length).toBeGreaterThan(0)
  })

  it('should handle different card statuses', () => {
    const activeState = {
      ...defaultState,
      cards: {
        ...defaultState.cards,
        selectedCardStatus: 'active' as const,
        currentTabIndex: 1,
      },
    }
    
    render(<CreditCardsPage />, { preloadedState: activeState })
    
    // There are multiple credit-card-list elements, so use getAllByTestId
    const creditCardLists = screen.getAllByTestId('credit-card-list')
    expect(creditCardLists.length).toBeGreaterThan(0)
  })

  it('should handle blocked card status', () => {
    const blockedState = {
      ...defaultState,
      cards: {
        ...defaultState.cards,
        selectedCardStatus: 'blocked' as const,
        currentTabIndex: 2,
      },
    }
    
    render(<CreditCardsPage />, { preloadedState: blockedState })
    
    // There are multiple credit-card-list elements, so use getAllByTestId
    const creditCardLists = screen.getAllByTestId('credit-card-list')
    expect(creditCardLists.length).toBeGreaterThan(0)
  })

  it('should render chips for status counts', () => {
    render(<CreditCardsPage />, { preloadedState: defaultState })

    // The mocked tabs don't render the actual chip content, so we check for the tab structure
    // The tabs should be rendered with the mocked components
    expect(screen.getByTestId('ant-tabs')).toBeInTheDocument()
    expect(screen.getAllByTestId('ant-tab')).toHaveLength(3) // Three tabs for inactive, active, blocked
  })

  it('should handle missing creditCardResponse', () => {
    const stateWithoutResponse = {
      ...defaultState,
      cards: {
        ...defaultState.cards,
        creditCardResponse: null as any,
      },
    }
    
    expect(() => {
      render(<CreditCardsPage />, { preloadedState: stateWithoutResponse })
    }).not.toThrow()
  })

  it('should handle missing branchCardListPagination', () => {
    const stateWithoutPagination = {
      ...defaultState,
      cards: {
        ...defaultState.cards,
        isBranchListView: true,
        branchCardListPagination: null as any,
      },
    }
    
    expect(() => {
      render(<CreditCardsPage />, { preloadedState: stateWithoutPagination })
    }).not.toThrow()
  })

  it('should render dividers', () => {
    render(<CreditCardsPage />, { preloadedState: defaultState })
    
    expect(screen.getByTestId('divider')).toBeInTheDocument()
  })

  it('should render stack containers', () => {
    render(<CreditCardsPage />, { preloadedState: defaultState })
    
    const stacks = screen.getAllByTestId('stack')
    expect(stacks.length).toBeGreaterThan(0)
  })
})
