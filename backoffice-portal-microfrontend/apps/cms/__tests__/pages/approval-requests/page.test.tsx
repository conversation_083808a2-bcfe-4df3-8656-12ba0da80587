import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '../../test-utils'
import ApprovalsPage from '../../../src/app/approval-requests/page'
import * as approvalActions from '@/store/actions/ApprovalRequests'
import { setSelectedCardApprovalRequest } from '@/store/reducers/approvals'

// Mock the child components
vi.mock('../../../src/app/approval-requests/Pending', () => ({
  default: () => <div data-testid="pending-component">Pending Component</div>
}))

vi.mock('../../../src/app/approval-requests/Rejected', () => ({
  default: () => <div data-testid="rejected-component">Rejected Component</div>
}))

// Mock the approval actions
vi.mock('@/store/actions/ApprovalRequests', () => ({
  getAllCardsApprovalRequests: vi.fn(),
}))

// Mock the store hooks
const mockDispatch = vi.fn()
vi.mock('@/store', () => ({
  useAppDispatch: () => mockDispatch,
}))

describe('ApprovalsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('rendering', () => {
    it('should render the approval requests page with tabs', () => {
      render(<ApprovalsPage />)
      
      expect(screen.getByText('Pending Requests')).toBeInTheDocument()
      expect(screen.getByText('All Requests')).toBeInTheDocument()
    })

    it('should render the Pending component by default', () => {
      render(<ApprovalsPage />)
      
      expect(screen.getByTestId('pending-component')).toBeInTheDocument()
      expect(screen.queryByTestId('rejected-component')).not.toBeInTheDocument()
    })

    it('should have correct tab structure', () => {
      render(<ApprovalsPage />)
      
      const tabs = screen.getAllByRole('tab')
      expect(tabs).toHaveLength(2)
      expect(tabs[0]).toHaveTextContent('Pending Requests')
      expect(tabs[1]).toHaveTextContent('All Requests')
    })
  })

  describe('tab functionality', () => {
    it('should switch to Rejected component when All Requests tab is clicked', async () => {
      render(<ApprovalsPage />)
      
      const allRequestsTab = screen.getByText('All Requests')
      fireEvent.click(allRequestsTab)
      
      await waitFor(() => {
        expect(screen.getByTestId('rejected-component')).toBeInTheDocument()
        expect(screen.queryByTestId('pending-component')).not.toBeInTheDocument()
      })
    })

    it('should switch back to Pending component when Pending Requests tab is clicked', async () => {
      render(<ApprovalsPage />)
      
      // First switch to All Requests
      const allRequestsTab = screen.getByText('All Requests')
      fireEvent.click(allRequestsTab)
      
      await waitFor(() => {
        expect(screen.getByTestId('rejected-component')).toBeInTheDocument()
      })
      
      // Then switch back to Pending
      const pendingRequestsTab = screen.getByText('Pending Requests')
      fireEvent.click(pendingRequestsTab)
      
      await waitFor(() => {
        expect(screen.getByTestId('pending-component')).toBeInTheDocument()
        expect(screen.queryByTestId('rejected-component')).not.toBeInTheDocument()
      })
    })

    it('should maintain tab state correctly', async () => {
      render(<ApprovalsPage />)
      
      const tabs = screen.getAllByRole('tab')
      
      // Initially first tab should be selected
      expect(tabs[0]).toHaveAttribute('aria-selected', 'true')
      expect(tabs[1]).toHaveAttribute('aria-selected', 'false')
      
      // Click second tab
      fireEvent.click(tabs[1])
      
      await waitFor(() => {
        expect(tabs[0]).toHaveAttribute('aria-selected', 'false')
        expect(tabs[1]).toHaveAttribute('aria-selected', 'true')
      })
    })
  })

  describe('initialization effects', () => {
    it('should dispatch setSelectedCardApprovalRequest with empty object on mount', () => {
      render(<ApprovalsPage />)
      
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCardApprovalRequest({})
      )
    })

    it('should call getAllCardsApprovalRequests with correct parameters on mount', () => {
      render(<ApprovalsPage />)
      
      expect(approvalActions.getAllCardsApprovalRequests).toHaveBeenCalledWith(
        mockDispatch,
        'status=PENDING&page=1&size=10'
      )
    })

    it('should call initialization effects only once', () => {
      render(<ApprovalsPage />)
      
      expect(mockDispatch).toHaveBeenCalledTimes(1)
      expect(approvalActions.getAllCardsApprovalRequests).toHaveBeenCalledTimes(1)
    })
  })

  describe('component structure', () => {
    it('should have proper Stack layout', () => {
      render(<ApprovalsPage />)
      
      const stackElement = screen.getByRole('tablist').closest('div')
      expect(stackElement).toBeInTheDocument()
    })

    it('should have a divider between tabs and content', () => {
      render(<ApprovalsPage />)
      
      const divider = document.querySelector('hr')
      expect(divider).toBeInTheDocument()
    })

    it('should have correct tab panel structure', () => {
      render(<ApprovalsPage />)
      
      const tabPanels = screen.getAllByRole('tabpanel', { hidden: true })
      expect(tabPanels).toHaveLength(2)
    })
  })

  describe('accessibility', () => {
    it('should have proper ARIA attributes for tabs', () => {
      render(<ApprovalsPage />)

      const tabList = screen.getByRole('tablist')
      expect(tabList).toHaveAttribute('aria-label', 'approval requests tabs')

      const pendingTab = screen.getByRole('tab', { name: /pending requests/i })
      expect(pendingTab).toHaveAttribute('id', 'approval-tab-0')

      const allTab = screen.getByRole('tab', { name: /all requests/i })
      expect(allTab).toHaveAttribute('id', 'approval-tab-1')
    })

    it('should have proper ARIA attributes for tab panels', () => {
      render(<ApprovalsPage />)
      
      const tabPanels = screen.getAllByRole('tabpanel', { hidden: true })
      tabPanels.forEach((panel, index) => {
        expect(panel).toHaveAttribute('aria-labelledby')
        expect(panel).toHaveAttribute('id')
      })
    })
  })

  describe('styling', () => {
    it('should apply correct styles to tabs', () => {
      render(<ApprovalsPage />)

      const tabList = screen.getByRole('tablist')
      // The marginLeft style is applied via sx prop, so we check for the presence of the tablist
      expect(tabList).toBeInTheDocument()
    })
  })

  describe('error handling', () => {
    it('should handle dispatch errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      mockDispatch.mockImplementation(() => {
        throw new Error('Dispatch error')
      })
      
      expect(() => render(<ApprovalsPage />)).not.toThrow()
      
      consoleSpy.mockRestore()
    })

    it('should handle tab change with invalid values', async () => {
      render(<ApprovalsPage />)
      
      const tabs = screen.getAllByRole('tab')
      
      // Simulate invalid tab change
      fireEvent.click(tabs[0])
      
      await waitFor(() => {
        expect(screen.getByTestId('pending-component')).toBeInTheDocument()
      })
    })
  })

  describe('integration', () => {
    it('should work with different store states', () => {
      const customState = {
        approvals: {
          isLoadingApprovals: true,
          cardApprovalRequests: [],
          selectedCardApprovalRequest: null,
          cardApprovalsPagination: {},
        },
      }
      
      render(<ApprovalsPage />, { preloadedState: customState })
      
      expect(screen.getByText('Pending Requests')).toBeInTheDocument()
      expect(screen.getByText('All Requests')).toBeInTheDocument()
    })
  })
})
