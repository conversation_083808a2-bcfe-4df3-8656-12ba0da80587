import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import React, { ReactNode } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { AppStore, RootState } from '@/store'
import { Provider } from 'react-redux'
import { vi } from 'vitest'
import { configureStore } from '@reduxjs/toolkit'
import rootReducer from '@/store/reducers/store'

// Create router mocks
const baseRouterMock = {
  push: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  pathname: '/approval-requests',
}

const customRouterMock = {
  ...baseRouterMock,
  pushWithTrailingSlash: vi.fn((url) => {
    const urlWithTrailingSlash = url.endsWith('/') ? url : `${url}/`
    baseRouterMock.push(urlWithTrailingSlash)
  }),
}

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => baseRouterMock,
  usePathname: () => '/approval-requests',
  useSearchParams: () => new URLSearchParams(),
  useParams: () => ({}),
  useServerInsertedHTML: vi.fn((callback) => callback()),
}))

// Mock the CustomRouter hook
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => customRouterMock,
  useDebounce: vi.fn((value) => value),
}))

// Mock store state
const mockStoreState = {
  cards: {
    isLoadingCards: false,
    isLoadingSingleCard: false,
    isLoadingActivateCard: false,
    isLoadingSetPinCard: false,
    isLoadingResetPinRetries: false,
    cardsError: false,
    cardsSuccess: false,
    creditCardsList: [],
    creditCardResponse: {
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 1,
      totalElements: 0,
      data: [],
    },
    selectedCardStatus: 'inactive' as const,
    selectedCardToView: {} as any,
    isBranchListView: false,
    isLoadingBranchCards: false,
    branchCardsList: [],
    branchCardListPagination: {
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 1,
      totalElements: 0,
    },
    currentTabIndex: 0,
  },
  approvals: {
    isLoadingApprovals: false,
    cardApprovalRequests: [],
    cardApprovalsPagination: {
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 1,
      totalElements: 0,
    },
    selectedCardApprovalRequest: null,
  },
  auth: {
    decodedToken: {
      permissions: [],
      sub: 'test-user',
      email: '<EMAIL>',
    },
    channelModules: [
      {
        channel: 'CARDS',
        modules: ['CARDS_MANAGEMENT', 'APPROVALS'],
      },
    ],
  },
  navigation: {
    isSidebarCollapsed: false,
    documentToggle: {
      open: false,
    },
  },
  notifications: {
    localNotification: null,
    localNotificationType: null,
  },
}

// Create a mock store
const createMockStore = (preloadedState = {}) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState: {
      ...mockStoreState,
      ...preloadedState,
    },
  })
}

interface ExtendedRenderOptions
  extends Omit<RenderOptions, 'queries' | 'wrapper'> {
  preloadedState?: Partial<RootState>
  store?: AppStore
}

const renderWithProviders = (
  ui: ReactNode,
  {
    preloadedState = {},
    store = createMockStore(preloadedState),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) => {
  const Wrapper = ({ children }: { children: ReactNode }) => {
    return (
      <Provider store={store}>
        <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
          <ThemeConfig themeType={'main'}>{children}</ThemeConfig>
        </NextAppDirEmotionCacheProvider>
      </Provider>
    )
  }
  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

export * from '@testing-library/react'
export { 
  renderWithProviders as render, 
  customRouterMock, 
  baseRouterMock,
  createMockStore,
  mockStoreState
}
