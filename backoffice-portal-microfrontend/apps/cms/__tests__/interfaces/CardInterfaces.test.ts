import { describe, it, expect } from 'vitest'
import { 
  ICreditCard, 
  IListCreditCard, 
  ICardResponse, 
  ICardStatus,
  InitialState,
  CardsQueryParams 
} from '../../src/store/interfaces/CardInterfaces'
import { createMockCreditCard, createMockListCreditCard, createMockCardResponse } from '../mocks/data'

describe('Card Interfaces Type Validation', () => {
  describe('ICreditCard interface', () => {
    it('should accept valid credit card data', () => {
      const validCard: ICreditCard = createMockCreditCard({
        cardId: 'test-card-id',
        customerName: 'John <PERSON>',
        active: true,
        pan: '****************',
        phoneNumber: '+**********',
        cif: 'CIF123456',
        domicileBranch: 'Main Branch',
        cardName: 'John Doe Card',
        account: 'ACC123456',
        cardType: 'Credit',
        productName: 'Premium Card',
        isPrimary: true,
        isSupplementary: false,
        isStaff: false,
        email: '<EMAIL>',
        idNumber: 'ID123456',
        postalAddress: '123 Main St',
        dateOfBirth: '1990-01-01',
      })

      expect(validCard).toBeDefined()
      expect(validCard.cardId).toBe('test-card-id')
      expect(validCard.customerName).toBe('John Doe')
      expect(validCard.active).toBe(true)
      expect(validCard.pan).toBe('****************')
      expect(validCard.phoneNumber).toBe('+**********')
      expect(validCard.cif).toBe('CIF123456')
      expect(validCard.domicileBranch).toBe('Main Branch')
      expect(validCard.cardName).toBe('John Doe Card')
      expect(validCard.account).toBe('ACC123456')
      expect(validCard.cardType).toBe('Credit')
      expect(validCard.productName).toBe('Premium Card')
      expect(validCard.isPrimary).toBe(true)
      expect(validCard.isSupplementary).toBe(false)
      expect(validCard.isStaff).toBe(false)
      expect(validCard.email).toBe('<EMAIL>')
      expect(validCard.idNumber).toBe('ID123456')
      expect(validCard.postalAddress).toBe('123 Main St')
      expect(validCard.dateOfBirth).toBe('1990-01-01')
    })

    it('should handle optional fields', () => {
      const cardWithOptionalFields: ICreditCard = createMockCreditCard({
        cardId: 'test-card-id',
        customerName: 'Jane Doe',
        active: false,
        pan: '****************',
        phoneNumber: '+**********',
        cif: 'CIF654321',
        domicileBranch: 'West Branch',
        cardName: 'Jane Doe Card',
        account: 'ACC654321',
        cardType: 'Debit',
        productName: 'Standard Card',
        isPrimary: false,
        isSupplementary: true,
        isStaff: true,
        email: '<EMAIL>',
        idNumber: 'ID654321',
        postalAddress: undefined,
        dateOfBirth: null,
      })

      expect(cardWithOptionalFields).toBeDefined()
      expect(cardWithOptionalFields.postalAddress).toBeUndefined()
      expect(cardWithOptionalFields.dateOfBirth).toBeNull()
    })
  })

  describe('IListCreditCard interface', () => {
    it('should accept valid list credit card data', () => {
      const validListCard: IListCreditCard = createMockListCreditCard({
        cardId: 'list-card-id',
        customerName: 'Alice Smith',
        active: true,
        pan: '****************',
        domicileBranch: 'North Branch',
        cif: 'CIF111222',
        email: '<EMAIL>',
        postalAddress: '456 North St',
        dateOfBirth: '1985-05-15',
      })

      expect(validListCard).toBeDefined()
      expect(validListCard.cardId).toBe('list-card-id')
      expect(validListCard.customerName).toBe('Alice Smith')
      expect(validListCard.active).toBe(true)
      expect(validListCard.pan).toBe('****************')
      expect(validListCard.domicileBranch).toBe('North Branch')
      expect(validListCard.cif).toBe('CIF111222')
      expect(validListCard.email).toBe('<EMAIL>')
      expect(validListCard.postalAddress).toBe('456 North St')
      expect(validListCard.dateOfBirth).toBe('1985-05-15')
    })

    it('should handle null optional fields', () => {
      const listCardWithNulls: IListCreditCard = createMockListCreditCard({
        cardId: 'list-card-id-2',
        customerName: 'Bob Wilson',
        active: false,
        pan: '****************',
        domicileBranch: 'South Branch',
        cif: 'CIF555666',
        email: '<EMAIL>',
        postalAddress: null,
        dateOfBirth: null,
      })

      expect(listCardWithNulls).toBeDefined()
      expect(listCardWithNulls.postalAddress).toBeNull()
      expect(listCardWithNulls.dateOfBirth).toBeNull()
    })
  })

  describe('ICardResponse interface', () => {
    it('should accept valid card response data', () => {
      const validResponse: ICardResponse = createMockCardResponse({
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 5,
        totalElements: 50,
        data: [
          createMockListCreditCard({ cardId: 'card-1' }),
          createMockListCreditCard({ cardId: 'card-2' }),
        ],
      })

      expect(validResponse).toBeDefined()
      expect(validResponse.pageNumber).toBe(1)
      expect(validResponse.pageSize).toBe(10)
      expect(validResponse.totalNumberOfPages).toBe(5)
      expect(validResponse.totalElements).toBe(50)
      expect(validResponse.data).toHaveLength(2)
      expect(validResponse.data[0].cardId).toBe('card-1')
      expect(validResponse.data[1].cardId).toBe('card-2')
    })

    it('should handle empty data array', () => {
      const emptyResponse: ICardResponse = createMockCardResponse({
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 0,
        totalElements: 0,
        data: [],
      })

      expect(emptyResponse).toBeDefined()
      expect(emptyResponse.data).toHaveLength(0)
      expect(emptyResponse.totalElements).toBe(0)
      expect(emptyResponse.totalNumberOfPages).toBe(0)
    })
  })

  describe('ICardStatus type', () => {
    it('should accept valid card status values', () => {
      const validStatuses: ICardStatus[] = ['active', 'inactive', 'blocked', 'unblocked']

      validStatuses.forEach(status => {
        const cardStatus: ICardStatus = status
        expect(cardStatus).toBe(status)
      })
    })

    it('should validate specific status values', () => {
      const activeStatus: ICardStatus = 'active'
      const inactiveStatus: ICardStatus = 'inactive'
      const blockedStatus: ICardStatus = 'blocked'
      const unblockedStatus: ICardStatus = 'unblocked'

      expect(activeStatus).toBe('active')
      expect(inactiveStatus).toBe('inactive')
      expect(blockedStatus).toBe('blocked')
      expect(unblockedStatus).toBe('unblocked')
    })
  })

  describe('InitialState interface', () => {
    it('should accept valid initial state structure', () => {
      const validInitialState: InitialState = {
        isLoadingCards: false,
        isLoadingSingleCard: false,
        isLoadingActivateCard: false,
        isLoadingSetPinCard: false,
        isLoadingResetPinRetries: false,
        cardsError: false,
        cardsSuccess: false,
        creditCardsList: [],
        creditCardResponse: createMockCardResponse(),
        selectedCardStatus: 'inactive',
        selectedCardToView: createMockCreditCard(),
        isBranchListView: false,
        isLoadingBranchCards: false,
        branchCardsList: [],
        branchCardListPagination: {
          pageNumber: 1,
          pageSize: 10,
          totalElements: 0,
          totalNumberOfPages: 0,
        },
        currentTabIndex: 0,
      }

      expect(validInitialState).toBeDefined()
      expect(validInitialState.isLoadingCards).toBe(false)
      expect(validInitialState.selectedCardStatus).toBe('inactive')
      expect(validInitialState.currentTabIndex).toBe(0)
      expect(validInitialState.creditCardsList).toEqual([])
      expect(validInitialState.branchCardsList).toEqual([])
    })

    it('should handle different loading states', () => {
      const loadingState: InitialState = {
        isLoadingCards: true,
        isLoadingSingleCard: true,
        isLoadingActivateCard: true,
        isLoadingSetPinCard: true,
        isLoadingResetPinRetries: true,
        cardsError: false,
        cardsSuccess: false,
        creditCardsList: [],
        creditCardResponse: createMockCardResponse(),
        selectedCardStatus: 'active',
        selectedCardToView: createMockCreditCard(),
        isBranchListView: true,
        isLoadingBranchCards: true,
        branchCardsList: [],
        branchCardListPagination: {
          pageNumber: 1,
          pageSize: 10,
          totalElements: 0,
          totalNumberOfPages: 0,
        },
        currentTabIndex: 1,
      }

      expect(loadingState.isLoadingCards).toBe(true)
      expect(loadingState.isLoadingSingleCard).toBe(true)
      expect(loadingState.isLoadingActivateCard).toBe(true)
      expect(loadingState.isLoadingSetPinCard).toBe(true)
      expect(loadingState.isLoadingResetPinRetries).toBe(true)
      expect(loadingState.isBranchListView).toBe(true)
      expect(loadingState.isLoadingBranchCards).toBe(true)
      expect(loadingState.selectedCardStatus).toBe('active')
      expect(loadingState.currentTabIndex).toBe(1)
    })
  })

  describe('CardsQueryParams interface', () => {
    it('should accept valid query parameters', () => {
      const validParams: CardsQueryParams = {
        page: 1,
        size: 10,
        cardType: 'CREDIT',
        active: true,
        isBlocked: false,
        customerName: 'John Doe',
        domicileBranch: 'Main Branch',
        pan: '1234',
        cif: 'CIF123',
        email: '<EMAIL>',
        idNumber: 'ID123',
      }

      expect(validParams).toBeDefined()
      expect(validParams.page).toBe(1)
      expect(validParams.size).toBe(10)
      expect(validParams.cardType).toBe('CREDIT')
      expect(validParams.active).toBe(true)
      expect(validParams.isBlocked).toBe(false)
      expect(validParams.customerName).toBe('John Doe')
      expect(validParams.domicileBranch).toBe('Main Branch')
      expect(validParams.pan).toBe('1234')
      expect(validParams.cif).toBe('CIF123')
      expect(validParams.email).toBe('<EMAIL>')
      expect(validParams.idNumber).toBe('ID123')
    })

    it('should handle minimal query parameters', () => {
      const minimalParams: CardsQueryParams = {
        page: 1,
        size: 10,
        cardType: 'DEBIT',
        active: false,
        isBlocked: true,
      }

      expect(minimalParams).toBeDefined()
      expect(minimalParams.page).toBe(1)
      expect(minimalParams.size).toBe(10)
      expect(minimalParams.cardType).toBe('DEBIT')
      expect(minimalParams.active).toBe(false)
      expect(minimalParams.isBlocked).toBe(true)
    })

    it('should handle optional search parameters', () => {
      const paramsWithOptionals: CardsQueryParams = {
        page: 2,
        size: 20,
        cardType: 'CREDIT',
        active: true,
        isBlocked: false,
        customerName: undefined,
        domicileBranch: undefined,
        pan: undefined,
        cif: undefined,
        email: undefined,
        idNumber: undefined,
      }

      expect(paramsWithOptionals).toBeDefined()
      expect(paramsWithOptionals.customerName).toBeUndefined()
      expect(paramsWithOptionals.domicileBranch).toBeUndefined()
      expect(paramsWithOptionals.pan).toBeUndefined()
      expect(paramsWithOptionals.cif).toBeUndefined()
      expect(paramsWithOptionals.email).toBeUndefined()
      expect(paramsWithOptionals.idNumber).toBeUndefined()
    })
  })
})
