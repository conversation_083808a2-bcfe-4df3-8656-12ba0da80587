import { faker } from '@faker-js/faker'
import { ICreditCard, IListCreditCard, ICardResponse } from '@/store/interfaces/CardInterfaces'
import { ICardApprovalRequest, IDiffValues } from '@/store/interfaces/Approvals'

// Mock Credit Card Data
export const createMockCreditCard = (overrides: Partial<ICreditCard> = {}): ICreditCard => ({
  cardId: faker.string.uuid(),
  customerName: faker.person.fullName(),
  active: faker.datatype.boolean(),
  pan: faker.finance.creditCardNumber(),
  phoneNumber: faker.phone.number(),
  cif: faker.string.numeric(10),
  domicileBranch: faker.location.city(),
  cardName: faker.person.fullName(),
  account: faker.finance.accountNumber(),
  cardType: faker.helpers.arrayElement(['Credit', 'Debit']),
  productName: faker.helpers.arrayElement(['Premium Card', 'Standard Card', 'Gold Card']),
  isPrimary: faker.datatype.boolean(),
  isSupplementary: faker.datatype.boolean(),
  isStaff: faker.datatype.boolean(),
  email: faker.internet.email(),
  idNumber: faker.string.numeric(8),
  postalAddress: faker.location.streetAddress(),
  dateOfBirth: faker.date.past({ years: 50 }).toISOString(),
  ...overrides,
})

export const createMockListCreditCard = (overrides: Partial<IListCreditCard> = {}): IListCreditCard => ({
  cardId: faker.string.uuid(),
  customerName: faker.person.fullName(),
  active: faker.datatype.boolean(),
  pan: faker.finance.creditCardNumber(),
  domicileBranch: faker.location.city(),
  cif: faker.string.numeric(10),
  email: faker.internet.email(),
  postalAddress: faker.location.streetAddress(),
  dateOfBirth: faker.date.past({ years: 50 }).toISOString(),
  ...overrides,
})

export const createMockCardResponse = (overrides: Partial<ICardResponse> = {}): ICardResponse => ({
  pageNumber: 1,
  pageSize: 10,
  totalNumberOfPages: 1,
  totalElements: 5,
  data: Array.from({ length: 5 }, () => createMockListCreditCard()),
  ...overrides,
})

// Mock Approval Request Data
export const createMockDiffValues = (overrides: Partial<IDiffValues> = {}): IDiffValues => ({
  field: faker.database.column(),
  name: faker.lorem.word(),
  oldValue: faker.lorem.word(),
  newValue: faker.lorem.word(),
  ...overrides,
})

export const createMockCardApprovalRequest = (overrides: Partial<ICardApprovalRequest> = {}): ICardApprovalRequest => ({
  id: faker.string.uuid(),
  maker: faker.person.fullName(),
  makerFirstName: faker.person.firstName(),
  makerLastName: faker.person.lastName(),
  dateCreated: faker.date.recent().toISOString(),
  dateModified: faker.date.recent().toISOString(),
  makerCheckerType: {
    channel: 'CARDS',
    checkerPermissions: ['APPROVE_CARD_ACTIVATION'],
    description: faker.lorem.sentence(),
    makerPermissions: ['REQUEST_CARD_ACTIVATION'],
    module: 'CARDS',
    name: faker.lorem.words(3),
    overridePermissions: ['OVERRIDE_CARD_ACTIVATION'],
    type: faker.helpers.arrayElement(['ACTIVATE', 'DEACTIVATE', 'SET_PIN']),
  },
  entityId: faker.string.uuid(),
  entity: faker.lorem.word(),
  diff: Array.from({ length: 3 }, () => createMockDiffValues()),
  makerComments: faker.lorem.sentence(),
  status: faker.helpers.arrayElement(['PENDING', 'APPROVED', 'REJECTED']),
  checker: faker.person.fullName(),
  checkerComments: faker.lorem.sentence(),
  ...overrides,
})

// Mock API Response Helpers
export const createMockApiResponse = <T>(data: T) => ({
  data: {
    data,
    pageNumber: 1,
    pageSize: 10,
    totalElements: Array.isArray(data) ? data.length : 1,
    totalNumberOfPages: 1,
  },
})

export const createMockApiError = (message = 'API Error') => {
  const error = new Error(message)
  error.name = 'ApiError'
  return error
}

// Bulk data generators
export const createMockCreditCards = (count = 5): ICreditCard[] =>
  Array.from({ length: count }, () => createMockCreditCard())

export const createMockListCreditCards = (count = 5): IListCreditCard[] =>
  Array.from({ length: count }, () => createMockListCreditCard())

export const createMockCardApprovalRequests = (count = 5): ICardApprovalRequest[] =>
  Array.from({ length: count }, () => createMockCardApprovalRequest())

// Specific test scenarios
export const createPendingApprovalRequest = (): ICardApprovalRequest =>
  createMockCardApprovalRequest({ status: 'PENDING' })

export const createApprovedApprovalRequest = (): ICardApprovalRequest =>
  createMockCardApprovalRequest({ status: 'APPROVED' })

export const createRejectedApprovalRequest = (): ICardApprovalRequest =>
  createMockCardApprovalRequest({ status: 'REJECTED' })

export const createActiveCreditCard = (): ICreditCard =>
  createMockCreditCard({ active: true })

export const createInactiveCreditCard = (): ICreditCard =>
  createMockCreditCard({ active: false })
