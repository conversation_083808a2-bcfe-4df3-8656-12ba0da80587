import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  basePath: '/cms',
  output: 'standalone',
  images: {
    unoptimized: true,
  },
  /*experimental: {
    swcPlugins: process.env.NEXT_PUBLIC_TEST === 'e2e' ? [
      [
        'swc-plugin-coverage-instrument',
        {
          unstableExclude: ['**!/node_modules/!**'],
        },
      ],
    ]: [] ,
  }*/
}

export default nextConfig
