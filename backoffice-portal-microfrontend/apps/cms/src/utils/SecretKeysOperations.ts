'use client'

import forge from 'node-forge'

let keyPairPromise: Promise<{
  publicKey: forge.pki.rsa.PublicKey
  privateKey: forge.pki.rsa.PrivateKey
}> | null = null

async function generateKeyPair(): Promise<{
  publicKey: forge.pki.rsa.PublicKey
  privateKey: forge.pki.rsa.PrivateKey
}> {
  if (!keyPairPromise) {
    keyPairPromise = new Promise((resolve, reject) => {
      forge.pki.rsa.generateKeyPair(
        { bits: 4096, workers: 2 },
        (
          err: Error,
          keypair: {
            publicKey: forge.pki.rsa.PublicKey
            privateKey: forge.pki.rsa.PrivateKey
          }
        ) => {
          if (err) {
            console.error(err)
            reject(new Error(`Key generation failed: ${err.message}`))
          } else {
            resolve({
              publicKey: keypair.publicKey,
              privateKey: keypair.privateKey,
            })
          }
        }
      )
    })
  }

  return keyPairPromise
}

export async function getPublicKey() {
  const keyPair = await generateKeyPair()
  const publicKeyDer = forge.asn1
    .toDer(forge.pki.publicKeyToAsn1(keyPair.publicKey))
    .getBytes()
  const publicKeyBase64 = forge.util.encode64(publicKeyDer)
  return publicKeyBase64
}

export async function decryptData(encryptedBase64: string): Promise<string> {
  const keyPair = await generateKeyPair()
  const encryptedBytes = forge.util.decode64(encryptedBase64)

  const decrypted = keyPair.privateKey.decrypt(
    encryptedBytes,
    'RSAES-PKCS1-V1_5'
  )

  return decrypted
}

export function clearKeys() {
  keyPairPromise = null
}
