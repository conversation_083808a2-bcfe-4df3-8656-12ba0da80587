import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { ICardApprovalRequest, IPagination } from '@/store/interfaces/Approvals'

interface IInitialState {
  isLoadingApprovals: boolean
  cardApprovalRequests: ICardApprovalRequest[]
  selectedCardApprovalRequest: ICardApprovalRequest
  cardApprovalsPagination: IPagination
}
const initialState: IInitialState = {
  isLoadingApprovals: false,
  cardApprovalRequests: [],
  selectedCardApprovalRequest: {} as ICardApprovalRequest,
  cardApprovalsPagination: {} as IPagination,
}

const ApprovalsSlice = createSlice({
  name: 'approvals',
  initialState: initialState,
  reducers: {
    setIsLoadingApprovals: (state, action: PayloadAction<boolean>) => {
      state.isLoadingApprovals = action.payload
    },
    setCardApprovalRequests: (
      state,
      action: PayloadAction<ICardApprovalRequest[]>
    ) => {
      state.cardApprovalRequests = action.payload
    },
    setSelectedCardApprovalRequest: (
      state,
      action: PayloadAction<ICardApprovalRequest>
    ) => {
      state.selectedCardApprovalRequest = action.payload
    },
    setCardApprovalsPagination: (state, action: PayloadAction<IPagination>) => {
      state.cardApprovalsPagination = action.payload
    },
    resetCardApprovalRequests: () => initialState,
  },
})
export const {
  setIsLoadingApprovals,
  setCardApprovalRequests,
  setSelectedCardApprovalRequest,
  setCardApprovalsPagination,
  resetCardApprovalRequests,
} = ApprovalsSlice.actions
export default ApprovalsSlice.reducer
