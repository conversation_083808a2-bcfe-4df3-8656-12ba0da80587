export interface ICardApprovalRequest {
  checker?: string
  checkerComments?: string
  id: string
  maker: string
  makerFirstName?: string
  makerLastName?: string
  dateCreated: string
  dateModified: string
  makerCheckerType: {
    channel: string
    checkerPermissions: string[]
    description?: string
    makerPermissions: string[]
    module: string
    name: string
    overridePermissions: string[]
    type: string
  }
  entityId?: string
  entity?: string
  diff: IDiffValues[]
  makerComments?: string
  status: string
}
export interface IDiffValues {
  field: string
  name?: string
  oldValue: IDiffValues[] | string
  newValue: IDiffValues[] | string
}
export interface IApprovalPaginationData {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
}

export interface IPagination {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
}
