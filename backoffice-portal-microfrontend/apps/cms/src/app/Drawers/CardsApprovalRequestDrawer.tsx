'use client'
import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogTitle,
  IconButton,
  Box,
  Typography,
  TextField,
  Avatar,
  TableContainer,
  Paper,
  Table,
  TableBody,
  TableRow,
} from '@mui/material'
import { useAppSelector, useAppDispatch } from '@/store'
import { AccessControlWrapper, formatTimestamp } from '@dtbx/store/utils'
import { ReadOnlyTypography } from '@dtbx/ui/components'
import { Check } from '@mui/icons-material'
import { CardsIcon, BackIcon, InfoIcon } from '@dtbx/ui/components/SvgIcons'
import { ICardApprovalRequest } from '@/store/interfaces'
import { CheckerRequestsApiHandler } from '@/app/approval-requests/CheckerRequestsApiHandler'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { CustomTableHeader, CustomTableCell } from '@dtbx/ui/components/Table'
import { IHeadCell } from '@dtbx/store/interfaces'

interface CardsApprovalRequestDrawerProps {
  open: boolean
  setOpen: (value: boolean) => void
  origin: string
  selectedCardApprovalRequest: ICardApprovalRequest
}

export const CardsApprovalRequestDrawer = ({
  open,
  setOpen,
  origin,
  selectedCardApprovalRequest,
}: CardsApprovalRequestDrawerProps) => {
  const dispatch = useAppDispatch()
  const [checkerComments, setCheckerComments] = useState('')
  const [checkerCommentsError, setCheckerCommentsError] = useState(false)
  const router = useCustomRouter()
  const { isLoadingActivateCard } = useAppSelector((state) => state.cards)

  const resolvedHeader: IHeadCell[] = [
    { id: 'field', label: 'Field', alignCenter: false, alignRight: false },

    {
      id: 'oldValue',
      label: 'Old Value',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'newValue',
      label: 'New Value',
      alignCenter: false,
      alignRight: false,
    },
  ]

  const handleClose = (e: React.SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
  }

  const handleReject = async () => {
    if (checkerComments === '') {
      setCheckerCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      selectedCardApprovalRequest,
      dispatch,
      router,
      `REJECT_${selectedCardApprovalRequest.makerCheckerType.type}`,
      checkerComments
    )
    setOpen(false)
  }

  const handleApprove = async () => {
    if (checkerComments === '') {
      setCheckerCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      selectedCardApprovalRequest,
      dispatch,
      router,
      `ACCEPT_${selectedCardApprovalRequest.makerCheckerType.type}`,
      checkerComments
    )
    setOpen(false)
  }
  useEffect(() => {
    if (origin === 'card-approvals') {
      setOpen(true)
    }
  }, [origin])

  return (
    <>
      {' '}
      <Button
        variant="outlined"
        sx={{
          border: '1px solid #D0D5DD',
          height: '2.5rem',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          color: '#555C61',
          fontSize: '15px',
          fontWeight: '500',
        }}
        onClick={() => setOpen(!open)}
      >
        View Approval Request Details{' '}
        <Typography
          sx={{
            border: '1px solid #EAECF0',
            background: '#F9FAFB',
            width: '25px',
            height: '25px',
            borderRadius: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          1
        </Typography>
      </Button>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '30%',
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '35%',
          },
        }}
      >
        {' '}
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: 0,
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                alignContent: 'center',
                px: '0.2rem',
                py: '0.2rem',
              }}
            >
              <IconButton onClick={(e) => handleClose(e, 'close')}>
                <BackIcon />
              </IconButton>
              <Typography variant="subtitle2" color={'primary.main'}>
                Approval Request details
              </Typography>
            </DialogTitle>
          </Stack>
        </Box>
        <Stack sx={{ px: '1vw', py: '2vh', gap: '1rem' }}>
          <Stack
            sx={{
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            <ReadOnlyTypography
              fullWidth
              label="Approval request type"
              sx={{}}
              value={selectedCardApprovalRequest?.makerCheckerType?.name}
            />
            <Stack
              direction="row"
              spacing={1}
              width="100%"
              p={'0.2rem'}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '0.3rem',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
            >
              <Stack
                direction="row"
                alignItems="start"
                width={'100%'}
                p={1}
                gap={2}
              >
                <Avatar
                  sx={{
                    backgroundColor: '#E7E8E9',
                    borderRadius: 1,
                    width: '35px',
                    height: '35px',
                  }}
                  alt={` Icon`}
                >
                  <CardsIcon />
                </Avatar>
                <Stack direction="column" justifyContent="center" spacing={0.5}>
                  <Typography
                    variant="body1"
                    sx={{ fontWeight: '700', fontSize: '1rem' }}
                  >
                    Credit Card
                  </Typography>
                  <Typography variant="body1">
                    &nbsp; &#8226;{' '}
                    {selectedCardApprovalRequest?.makerCheckerType?.name}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
            <Stack
              sx={{
                padding: '0.5rem',
                border: '1px solid #D0D5DD',
                gap: '0.5rem',
                borderRadius: '0.3rem',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
            >
              <Typography variant="body2" fontWeight={600}>
                Changes Made
              </Typography>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <CustomTableHeader
                    order={'desc'}
                    orderBy={''}
                    rowCount={0}
                    headLabel={resolvedHeader}
                    numSelected={0}
                  />
                  <TableBody>
                    {selectedCardApprovalRequest?.diff?.map((diff, index) => (
                      <TableRow key={index}>
                        <CustomTableCell>{diff.field}</CustomTableCell>
                        <CustomTableCell>
                          {String(diff.oldValue ?? 'N/A')}
                        </CustomTableCell>
                        <CustomTableCell>
                          {String(diff.newValue ?? 'N/A')}
                        </CustomTableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Stack>

            <ReadOnlyTypography
              fullWidth
              label="Maker"
              sx={{}}
              value={selectedCardApprovalRequest?.maker}
            />
            <ReadOnlyTypography
              fullWidth
              label="Maker timestamp"
              sx={{}}
              value={formatTimestamp(selectedCardApprovalRequest?.dateCreated)}
            />
            <ReadOnlyTypography
              fullWidth
              label="Maker comment"
              sx={{}}
              value={selectedCardApprovalRequest?.makerComments}
            />
            <TextField
              fullWidth
              multiline
              rows={5}
              label="Checker comment"
              sx={{}}
              value={checkerComments}
              error={checkerCommentsError}
              helperText={
                checkerCommentsError && 'Checker comments are required'
              }
              onChange={(e) => {
                if (e.target.value !== '') setCheckerCommentsError(false)
                setCheckerComments(e.target.value)
              }}
            />
          </Stack>
          <Stack direction="row" gap={2}>
            <AccessControlWrapper
              rights={selectedCardApprovalRequest?.makerCheckerType?.checkerPermissions?.filter(
                (perm) => perm.includes('REJECT')
              )}
              makerId={selectedCardApprovalRequest?.maker}
            >
              <Button
                variant="outlined"
                fullWidth
                sx={{
                  height: '40px',
                  background: '#E3E4E4',
                  border: '1px solid #AAADB0',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
                disabled={checkerComments === ''}
                loading={isLoadingActivateCard}
                onClick={handleReject}
              >
                Reject
              </Button>
            </AccessControlWrapper>
            <AccessControlWrapper
              rights={selectedCardApprovalRequest?.makerCheckerType?.checkerPermissions?.filter(
                (perm) => perm.includes('ACCEPT')
              )}
              makerId={selectedCardApprovalRequest?.maker}
            >
              <Button
                variant="contained"
                loading={isLoadingActivateCard}
                fullWidth
                sx={{
                  height: '40px',
                }}
                disabled={checkerComments === ''}
                onClick={handleApprove}
              >
                Approve
                <Check sx={{ marginLeft: 1 }} />
              </Button>
            </AccessControlWrapper>
          </Stack>
          <Stack
            direction="row"
            gap={'2%'}
            sx={{
              alignItems: 'center',
              alignContent: 'center',
            }}
          >
            <InfoIcon />
            <Typography variant="body2" sx={{ color: '#555C61' }}>
              You are approving or rejecting all the changes made as listed
              above.
            </Typography>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
