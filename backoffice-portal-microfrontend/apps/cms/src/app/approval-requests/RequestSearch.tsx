import { sentenceCase } from 'tiny-case'
import {
  <PERSON><PERSON>,
  ClickAwayListener,
  <PERSON>row,
  InputBase,
  Paper,
  Popper,
  <PERSON>ack,
  Typography,
} from '@mui/material'
import React, { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'

interface RequestSearchProps {
  onSetSearch: (makerName: string) => void
  buttonVariant?: 'contained' | 'outlined'
  buttonStyle?: React.CSSProperties
  searchByItems?: Array<{
    label: string
    value: string
  }>
}

const RequestSearch: React.FC<RequestSearchProps> = (params) => {
  const dispatch = useAppDispatch()
  const { onSetSearch, buttonVariant, buttonStyle, searchByItems } = params
  const [open, setOpen] = React.useState<boolean>(false)
  const [searchValue, setSearchValue] = React.useState<string>('')
  const [searchBy, setSearchBy] = React.useState<{
    label: string
    value: string
  }>({
    label: 'Maker First Name',
    value: 'firstName',
  })

  const anchorRef = React.useRef<HTMLButtonElement>(null)
  const anchorInputRef = React.useRef<HTMLInputElement>(null)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }

  const handleSearchBy = (value: { label: string; value: string }) => {
    setSearchBy(value)

    setOpen(false)
  }

  useEffect(() => {}, [])

  useEffect(() => {
    if (searchValue) {
      onSetSearch(searchValue)
    }
  }, [])

  return (
    <Stack
      sx={{
        flexDirection: 'row',
        width: 'auto',
      }}
    >
      <Stack>
        <Button
          variant={buttonVariant || 'outlined'}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            padding: '8px 42px',
            borderRadius: '6px 0px 0px 6px',
            border: buttonStyle?.border || '1.5px solid #D0D5DD',
            boxShadow:
              buttonStyle?.boxShadow ||
              '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          ref={anchorRef}
          id="composition-button"
          aria-controls={open ? 'composition-menu' : undefined}
          aria-expanded={open ? 'true' : undefined}
          aria-haspopup="true"
          onClick={handleToggle}
        >
          <Typography>{sentenceCase(searchBy.label)}</Typography>
        </Button>
        <Popper
          open={open}
          anchorEl={anchorRef.current}
          role={undefined}
          placement="bottom-start"
          transition
          disablePortal
          sx={{
            zIndex: '2000',
          }}
        >
          {({ TransitionProps, placement }) => (
            <Grow
              {...TransitionProps}
              style={{
                transformOrigin:
                  placement === 'bottom-start' ? 'left top' : 'left bottom',
              }}
            >
              <Paper
                sx={{
                  padding: '10px',
                  maxWidth: '200px',
                }}
              >
                {searchByItems &&
                  searchByItems.map((item, index) => {
                    return (
                      <ClickAwayListener
                        onClickAway={() => {
                          setOpen(false)
                        }}
                      >
                        <Stack>
                          <Button
                            key={index}
                            sx={{
                              padding: '10px',
                              width: '100%',
                              justifyContent: 'flex-start',
                            }}
                            onClick={() => handleSearchBy(item)}
                          >
                            <Typography>{sentenceCase(item.label)}</Typography>
                          </Button>
                        </Stack>
                      </ClickAwayListener>
                    )
                  })}
              </Paper>
            </Grow>
          )}
        </Popper>
      </Stack>

      <Stack
        sx={{
          borderRadius: '0px 6px 6px 0px',
          border: '1px solid #D0D5DD',
          padding: '8px 16px',
          height: '40px',
          minWidth: '500px',
          justifyContent: 'flex-start',
          alignItems: 'center',
          background: '#ffffff',
        }}
      >
        <InputBase
          placeholder={`Search by ${sentenceCase(searchBy.label)}`}
          ref={anchorInputRef}
          aria-haspopup="true"
          value={searchValue}
          onChange={(e) => {
            setSearchValue(e.target.value)
            onSetSearch(e.target.value)
          }}
          sx={{
            width: '100%',
          }}
        />
      </Stack>
    </Stack>
  )
}

export default RequestSearch
