import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import {
  completeCardActivation,
  completeCardSetPin,
  completeResetCardPinRetryCounter,
} from '@/store/actions/CardsActions'
import { ICardApprovalRequest } from '@/store/interfaces/Approvals'

//This function takes the action and approval request and makes the api call to the selected api call and routes to the final route
export const CheckerRequestsApiHandler = async (
  request: ICardApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  action: string,
  comments?: string
) => {
  const handler = handlers[request.makerCheckerType.module]
  if (handler) {
    if (typeof handler === 'function') {
      await handler(request, dispatch, router)
    } else {
      const subHandler = handler[action]
      if (subHandler) {
        await subHandler(request, dispatch, router, comments)
      }
    }
  }
}
type Handler = (
  request: ICardApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  comments?: string
) => Promise<void>
type Handlers = {
  [key: string]: Handler | { [key: string]: Handler }
}
const handlers: Handlers = {
  Cards: {
    //accept actions
    ACCEPT_ACTIVATE_CARDS: async (request, dispatch, router, comments) => {
      await completeCardActivation(dispatch, request.id, 'approve', {
        comments: comments || 'approved',
      })
      router.push('/credit-cards')
    },
    ACCEPT_RESET_CARD_PIN: async (request, dispatch, router, comments) => {
      await completeCardSetPin(dispatch, request.id, 'approve', {
        comments: comments || 'approved',
      })
      router.push('/credit-cards')
    },
    ACCEPT_CARDS_RESET_PIN_TRY_COUNTER: async (
      request,
      dispatch,
      router,
      comments
    ) => {
      await completeResetCardPinRetryCounter(
        dispatch,
        { comments: comments || 'approved' },
        request.id,
        'approve'
      )
      router.push('/credit-cards')
    },
    //reject actions
    REJECT_ACTIVATE_CARDS: async (request, dispatch, router, comments) => {
      await completeCardActivation(dispatch, request.id, 'reject', {
        comments: comments || 'rejected',
      })
      router.push('/credit-cards')
    },
    REJECT_RESET_CARD_PIN: async (request, dispatch, router, comments) => {
      await completeCardSetPin(dispatch, request.id, 'reject', {
        comments: comments || 'rejected',
      })
      router.push('/credit-cards')
    },
    REJECT_CARDS_RESET_PIN_TRY_COUNTER: async (
      request,
      dispatch,
      router,
      comments
    ) => {
      await completeResetCardPinRetryCounter(
        dispatch,
        { comments: comments || 'rejected' },
        request.id,
        'reject'
      )
      router.push('/credit-cards')
    },
  },
}
