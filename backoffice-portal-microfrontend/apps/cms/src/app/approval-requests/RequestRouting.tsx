'use client'

import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { ICardApprovalRequest } from '@/store/interfaces/Approvals'
import { getCardById } from '@/store/actions/CardsActions'
import { setSelectedCardApprovalRequest } from '@/store/reducers'

export const ApprovalRequestRouting = async (
  request: ICardApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  const handler = handlers[request.makerCheckerType.module]
  if (handler) {
    if (typeof handler === 'function') {
      await handler(request, dispatch, router)
    } else {
      const subHandler = handler[request.makerCheckerType.type]
      if (subHandler) {
        await subHandler(request, dispatch, router)
      }
    }
  }
}
type Handler = (
  request: ICardApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance
) => Promise<void>
type Handlers = {
  [key: string]: Handler | { [key: string]: Handler }
}

const handlers: Handlers = {
  Cards: async (request, dispatch, router) => {
    if (request.entityId) await getCardById(dispatch, request.entityId)
    dispatch(setSelectedCardApprovalRequest(request))
    router.push('/credit-cards/c-card')
  },
}
