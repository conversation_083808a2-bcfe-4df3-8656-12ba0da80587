FROM node:23-alpine AS base
LABEL authors="<PERSON><PERSON>"

FROM base AS builder
RUN apk update
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
#Set up pnpm
RUN npm install -g pnpm turbo
ENV PNPM_HOME=/app/.pnpm
ENV PATH=$PNPM_HOME:$PATH

# Set working directory
WORKDIR /app

COPY ../.. .

RUN turbo prune eatta --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer

ARG BUILD_ENV
RUN echo $BUILD_ENV

RUN apk update
RUN apk add --no-cache libc6-compat
#Set up pnpm
RUN npm install -g pnpm turbo

WORKDIR /app

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN pnpm install

# Build the project
COPY --from=builder /app/out/full/ .

COPY /apps/eatta/.env.$BUILD_ENV /app/apps/eatta/.env.production

RUN pnpm turbo build --filter=eatta...

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/apps/eatta/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/eatta/.next/static ./apps/eatta/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/eatta/public ./apps/eatta/public

EXPOSE 3003
ENV PORT=3003

CMD [ "node", "apps/eatta/server.js" ]