import { <PERSON><PERSON>, Table<PERSON>ell, TableFooter, TableRow } from '@mui/material'
import { TableBody } from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { Table } from '@mui/material'
import { Paper } from '@mui/material'
import { TableContainer } from '@mui/material'
import { useState } from 'react'
import {
  DisbursementsEntry,
  Order,
  ProducerInvoiceEntryFilters,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import TableSkeleton from '@/components/TableSkeleton'

export interface DisbursementsTableProps {
  filters: ProducerInvoiceEntryFilters
  onPageChange?: (page: number, size: number) => void
}
export const DisbursementsTable: React.FC<DisbursementsTableProps> = ({
  filters,
  onPageChange,
}) => {
  const { disbursementsEntryResponse, isLoading } = useAppSelector(
    (state) => state.invoices
  )
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')
  const [paginationOptions, setPaginationOptions] = useState({
    page: filters.page,
    size: filters.size,
    totalPages: disbursementsEntryResponse.totalNumberOfPages,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    if (onPageChange) {
      onPageChange(newOptions.page, newOptions.size)
    }
  }
  /*************************end pagination handlers**************************/

  return (
    <Stack sx={{ height: '100%' }}>
      {isLoading ? (
        <TableSkeleton rowCount={8} columnCount={7} />
      ) : disbursementsEntryResponse.data.length === 0 ? (
        <EmptyPage
          title="No records found"
          message="No disbursement data available for the selected auction."
          bgUrl={'/combo.svg'}
        />
      ) : (
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            '& .MuiTableCell-root': {
              paddingInline: '1.5rem',
              paddingBlock: '0.5rem',
              textAlign: 'left',
            },
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="Disbursement Summary"
            size="small"
          >
            <CustomTableHeader
              order={order}
              orderBy={orderBy}
              headLabel={[
                { id: 'factory', label: 'Mark', alignRight: false },
                { id: 'amount', label: 'Amount', alignRight: false },
                { id: 'bank', label: 'Bank', alignRight: false },
                { id: 'branch', label: 'Branch', alignRight: false },
                {
                  id: 'transactionReference',
                  label: 'Transaction Reference',
                  alignRight: false,
                },
                {
                  id: 'dateDisbursed',
                  label: 'Date Disbursed',
                  alignRight: false,
                },
                { id: 'comments', label: 'Comments', alignRight: false },
              ]}
              showCheckbox={false}
              rowCount={0}
              numSelected={0}
            />
            <TableBody>
              {disbursementsEntryResponse.data.map(
                (row: DisbursementsEntry) => (
                  <TableRow hover={true} key={row.factory}>
                    <TableCell sx={{ fontWeight: 500, color: '#475467' }}>
                      {row.factory}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 500, color: '#475467' }}>
                      ${new Intl.NumberFormat('en-US').format(row.amount)}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 500, color: '#475467' }}>
                      {row.bank}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 500, color: '#475467' }}>
                      {row.branch}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 500, color: '#475467' }}>
                      {row.coreReference}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 500, color: '#475467' }}>
                      {row.dateCreated}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 500, color: '#475467' }}>
                      {row.narration}
                    </TableCell>
                  </TableRow>
                )
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  sx={{ paddingInline: 0 }}
                  align="center"
                  height={40}
                  colSpan={4}
                >
                  {disbursementsEntryResponse.totalNumberOfPages > 0 && (
                    <CustomPagination
                      options={{
                        ...paginationOptions,
                        totalPages:
                          disbursementsEntryResponse.totalNumberOfPages,
                      }}
                      handlePagination={handlePagination}
                    />
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      )}
    </Stack>
  )
}
