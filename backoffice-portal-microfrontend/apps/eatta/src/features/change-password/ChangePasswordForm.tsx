'use client'

import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import React, { useState } from 'react'
import { VisibilityOffOutlined, VisibilityOutlined } from '@mui/icons-material'
import NavigateNextIcon from '@mui/icons-material/NavigateNext'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'

import { useCustomRouter } from '@dtbx/ui/hooks'
import { CustomRadioButton } from '@/components/CustomRadioButton'
import { handleChangePassword } from '@/store/actions/eattaAuth'
import { useAppDispatch, useAppSelector } from '@/store'

const validationSchema = Yup.object({
  oldpassword: Yup.string()
    .required('Old password is required')
    .min(8, 'Password should be at least 8 characters')
    .matches(
      /[!@#$%^&*(),.?":{}|<>]/,
      'Password should contain at least one special character'
    ),
  newpassword: Yup.string()
    .required('New password is required')
    .min(8, 'Password should be at least 8 characters')
    .matches(
      /[!@#$%^&*(),.?":{}|<>]/,
      'Password should contain at least one special character'
    ),
  confirmpassword: Yup.string()
    .oneOf([Yup.ref('newpassword')], 'Passwords must match')
    .required('Confirm password is required'),
})
export const ChangePasswordForm = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [togglePass, setTogglePass] = useState<boolean>(false)
  const { isLoadingLogin } = useAppSelector((store) => store.auth)

  const formik = useFormik({
    initialValues: {
      oldpassword: '',
      newpassword: '',
      confirmpassword: '',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      await handleChangePassword(
        values.oldpassword,
        values.confirmpassword,
        dispatch,
        router
      )
    },
  })

  const handleCancel = () => {
    router.back()
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: '2px',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Stack
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          mt: '5rem',
          maxWidth: 600,
          width: '100%',
        }}
      >
        <IconButton
          sx={{
            borderRadius: '8px',
            border: '1px solid #D0D5DD',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          onClick={() => router.back()}
        >
          <ArrowBackIosNewOutlinedIcon />
        </IconButton>
        <Stack sx={{ mt: 2 }}>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Change Password
          </Typography>
        </Stack>
      </Stack>
      <Stack
        sx={{
          maxWidth: 600,
          mx: 'auto',
          p: 3,
          bgcolor: 'white',
          borderRadius: 2,
          mt: 3,
          width: '100%',
        }}
      >
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={2} paddingBlock={2}>
              <Stack direction="row" spacing={1}></Stack>

              <Stack spacing={1}>
                <Typography
                  variant="body1"
                  sx={{ color: '#344054', fontWeight: 500 }}
                >
                  Enter old password{' '}
                  <span style={{ color: '#00BC2D', fontWeight: 'bolder' }}>
                    *
                  </span>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type={togglePass ? 'text' : 'password'}
                  sx={{ marginBlock: '0' }}
                  margin={'normal'}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment
                          sx={{ cursor: 'pointer' }}
                          position="end"
                          onClick={() => setTogglePass(!togglePass)}
                        >
                          {togglePass ? (
                            <VisibilityOffOutlined fontSize={'small'} />
                          ) : (
                            <VisibilityOutlined fontSize={'small'} />
                          )}
                        </InputAdornment>
                      ),
                    },
                  }}
                  {...formik.getFieldProps('oldpassword')}
                  fullWidth
                  onKeyDown={(e) => e.stopPropagation()}
                  error={Boolean(
                    formik.touched.oldpassword && formik.errors.oldpassword
                  )}
                  helperText={
                    formik.touched.oldpassword && formik.errors.oldpassword
                  }
                />
              </Stack>
              <Stack spacing={1}>
                <Typography
                  variant="body1"
                  sx={{ color: '#344054', fontWeight: 500 }}
                >
                  Enter new password{' '}
                  <span style={{ color: '#00BC2D', fontWeight: 'bolder' }}>
                    *
                  </span>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type={togglePass ? 'text' : 'password'}
                  sx={{ marginBlock: '0' }}
                  margin={'normal'}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment
                          sx={{ cursor: 'pointer' }}
                          position="end"
                          onClick={() => setTogglePass(!togglePass)}
                        >
                          {togglePass ? (
                            <VisibilityOffOutlined fontSize={'small'} />
                          ) : (
                            <VisibilityOutlined fontSize={'small'} />
                          )}
                        </InputAdornment>
                      ),
                    },
                  }}
                  {...formik.getFieldProps('newpassword')}
                  fullWidth
                  onKeyDown={(e) => e.stopPropagation()}
                  error={Boolean(
                    formik.touched.newpassword && formik.errors.newpassword
                  )}
                  helperText={
                    formik.touched.newpassword && formik.errors.newpassword
                  }
                />
              </Stack>

              <Stack spacing={1}>
                <Typography
                  variant="body1"
                  sx={{ color: '#344054', fontWeight: 500 }}
                >
                  Confirm password{' '}
                  <span style={{ color: '#00BC2D', fontWeight: 'bolder' }}>
                    *
                  </span>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type={togglePass ? 'text' : 'password'}
                  sx={{ marginBlock: '0' }}
                  margin={'normal'}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment
                          sx={{ cursor: 'pointer' }}
                          position="end"
                          onClick={() => setTogglePass(!togglePass)}
                        >
                          {togglePass ? (
                            <VisibilityOffOutlined fontSize={'small'} />
                          ) : (
                            <VisibilityOutlined fontSize={'small'} />
                          )}
                        </InputAdornment>
                      ),
                    },
                  }}
                  {...formik.getFieldProps('confirmpassword')}
                  fullWidth
                  error={Boolean(
                    formik.touched.confirmpassword &&
                      formik.errors.confirmpassword
                  )}
                  helperText={
                    formik.touched.confirmpassword &&
                    formik.errors.confirmpassword
                      ? formik.errors.confirmpassword
                      : ''
                  }
                />
              </Stack>
              <Stack direction="column" useFlexGap spacing={2}>
                <Stack direction="row" useFlexGap spacing={1}>
                  <CustomRadioButton
                    checked={formik.isValid}
                    customColor={'#26b43b'}
                    style={{
                      padding: '2px',
                    }}
                  />
                  <Typography variant="body2" sx={{ color: '#475467' }}>
                    Must be at least 8 characters
                  </Typography>
                </Stack>

                <Stack direction="row" useFlexGap spacing={1}>
                  <CustomRadioButton
                    checked={formik.isValid}
                    customColor={'#26b43b'}
                    style={{
                      padding: '2px',
                    }}
                  />
                  <Typography variant="body2" sx={{ color: '#475467' }}>
                    Must contain one special character
                  </Typography>
                </Stack>
              </Stack>

              <Stack direction="row" spacing={1}>
                <Button
                  type="button"
                  variant="contained"
                  sx={{
                    backgroundColor: '#D92D20',
                    '&:hover': {
                      backgroundColor: '#CC3333',
                    },
                  }}
                  fullWidth
                  onClick={handleCancel}
                >
                  Cancel
                </Button>

                <Button
                  disabled={!formik.isValid || isLoadingLogin}
                  type="submit"
                  variant="contained"
                  fullWidth
                  endIcon={
                    isLoadingLogin ? (
                      <CircularProgress size={20} thickness={3.0} />
                    ) : (
                      <NavigateNextIcon />
                    )
                  }
                >
                  Next
                </Button>
              </Stack>
            </Stack>
          </Form>
        </FormikProvider>
      </Stack>
    </Box>
  )
}
