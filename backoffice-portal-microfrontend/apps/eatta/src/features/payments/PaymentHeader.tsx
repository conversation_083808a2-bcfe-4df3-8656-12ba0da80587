/**
 * <AUTHOR> on 22/05/2025
 */
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack, Typo<PERSON> } from '@mui/material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import React, { FC, useEffect, useMemo, useState } from 'react'
import {
  CompanyFilters,
  InvoicesFilters,
  PageFilters,
} from '@/store/interfaces'
import { resetOnboardingState } from '@/store/reducers'
import { useAppDispatch } from '@/store'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { PaymentFilters } from '@/store/interfaces/transactions'
import { SearchByValueConfig } from '@/components/PageFilters'
import { DECIMAL_NUMERIC_REGEX } from '@/utils/validators'
import { <PERSON><PERSON><PERSON>alRequestFilters } from '@/store/interfaces/maker<PERSON><PERSON><PERSON>'

export interface PaymentHeaderProps {
  filters: PaymentFilters
  onSearchChange: (
    key: keyof PaymentFilters | keyof ApprovalRequestFilters,
    value: string
  ) => void
  searchByValues:
    | SearchByValueConfig<PaymentFilters>[]
    | SearchByValueConfig<ApprovalRequestFilters>[]
  initialSearchBy: PaymentSearchByType
}

type PaymentSearchByType =
  | keyof Omit<PaymentFilters, keyof PageFilters>
  | keyof Omit<ApprovalRequestFilters, keyof PageFilters>

export const PaymentHeader: FC<PaymentHeaderProps> = ({
  initialSearchBy,
  onSearchChange,
  searchByValues,
}) => {
  const [searchBy, setSearchBy] = useState<PaymentSearchByType>(initialSearchBy)

  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  const currentSearchConfig = useMemo(
    () => searchByValues.find((item) => item.filterKey === searchBy),
    [searchByValues, searchBy]
  )

  const validateSearchValue = (value: string): string => {
    if (currentSearchConfig?.type === 'numeric') {
      return DECIMAL_NUMERIC_REGEX.test(value) ? value : ''
    }
    return value
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(validateSearchValue(value))
  }

  useEffect(() => {
    const value = validateSearchValue(debouncedSearchValue)
    if (!value) {
      setSearchValue('')
    }
    onSearchChange(searchBy, value)
  }, [debouncedSearchValue, searchBy])

  useEffect(() => {
    setSearchBy(initialSearchBy)
  }, [initialSearchBy])

  return (
    <Stack>
      <Stack paddingInline={3} paddingBlock={2}>
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            color: '#000A12',
          }}
        >
          Money In
        </Typography>

        <Typography
          variant="subtitle2"
          sx={{
            color: '#000A12',
          }}
        >
          A record of all tea E-slip payments received into the EATTA DTB escrow
          account number ***********
        </Typography>
      </Stack>

      <Divider />

      <Stack
        paddingInline={3}
        paddingBlock={2}
        spacing={1}
        flexWrap="wrap"
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValues.map(
            (item) => item.filterKey as string
          )}
          searchByLabel={searchByLabel}
          selectedSearchBy={searchBy}
          onHandleSearch={handleSearch}
          setSearchByValue={(value: string) =>
            setSearchBy(value as PaymentSearchByType)
          }
          searchPlaceHolder={`Search ${searchByLabel?.[searchBy] || searchBy || 'payment'}`}
          prependSearchBy={true}
        />
      </Stack>
      <Divider />
    </Stack>
  )
}
