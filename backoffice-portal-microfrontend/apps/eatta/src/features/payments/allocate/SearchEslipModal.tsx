/**
 * <AUTHOR> on 05/06/2025
 */
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import React, { FC, useEffect, useMemo, useState } from 'react'
import { EslipDetails } from '@/features/payments/allocate/EslipDetails'
import { InvoiceEslip, InvoicesFilters } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { getInvoiceEslips } from '@/store/actions'
import { resetInvoiceEslips } from '@/store/reducers'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { useDebounce } from '@dtbx/ui/hooks'
import { EslipDetailsSkeleton } from '@/features/payments/allocate/EslipDetailsSkeleton'
import { SearchByValueConfig } from '@/components/PageFilters'

interface SearchEslipModalProps {
  open: boolean
  onClose: () => void
  onEslipSelected?: (eslip: InvoiceEslip) => void
}

type EslipSearchByType = keyof Partial<InvoicesFilters>

const searchByValues: SearchByValueConfig<InvoicesFilters>[] = [
  {
    filterLabel: 'Eslip Number',
    filterKey: 'invoiceNumber',
    type: 'string',
  },
  {
    filterLabel: 'Buyer Code',
    filterKey: 'buyerCode',
    type: 'string',
  },
]

export const SearchEslipModal: FC<SearchEslipModalProps> = ({
  open,
  onClose,
  onEslipSelected,
}) => {
  const dispatch = useAppDispatch()
  const [searchValue, setSearchValue] = useState<string>('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const [searchBy, setSearchBy] = useState<EslipSearchByType>('invoiceNumber')
  const { invoiceEslipsResponse, isLoading } = useAppSelector(
    (state) => state.invoices
  )

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  const searchEslip = async () => {
    dispatch(resetInvoiceEslips())
    await getInvoiceEslips(dispatch, {
      [searchBy]: searchValue,
      ascending: false,
      status: ['NEW', 'PARTIAL'],
      page: 1,
      size: 10,
    })
  }

  useEffect(() => {
    if (!debouncedSearchValue) return
    searchEslip()
  }, [debouncedSearchValue, searchBy])

  return (
    <Dialog fullWidth maxWidth="md" open={open} onClose={onClose}>
      <DialogTitle sx={{ backgroundColor: 'primary.main' }} fontWeight={600}>
        <Stack spacing={2}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography color={'#FFFFFF'} variant="subtitle1" fontWeight="bold">
              Find e-slip
            </Typography>
            <IconButton
              sx={{
                height: '2rem',
                width: '2rem',
                border: '1px solid #FFFFFF',
                borderRadius: '0.5rem',
              }}
              onClick={onClose}
            >
              <CloseIcon sx={{ color: '#FFFFFF' }} />
            </IconButton>
          </Stack>

          <Stack spacing={2} direction="row" justifyContent="space-between">
            <CustomFilterUserBox
              searchValue={searchValue}
              searchByValues={searchByValues.map(
                (item) => item.filterKey as string
              )}
              selectedSearchBy={searchBy}
              searchByLabel={searchByLabel}
              onHandleSearch={(e) => setSearchValue(e.target.value)}
              setSearchByValue={(value: string) =>
                setSearchBy(value as EslipSearchByType)
              }
              searchPlaceHolder={`Search ${searchByLabel?.[searchBy] || searchBy || ''}`}
              prependSearchBy={true}
            />
          </Stack>
        </Stack>
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Stack spacing={2}>
          {isLoading ? (
            <EslipDetailsSkeleton />
          ) : invoiceEslipsResponse.data.length === 0 ? (
            <>
              <EmptyPage
                title="No Eslip found"
                message="Start typing e-slip number on the search input above and click search"
                bgUrl={'/eatta/combo.svg'}
              />
            </>
          ) : (
            <>
              <Stack spacing={1} justifyContent="space-between">
                <Typography variant="body1" fontWeight={600}>
                  Found {invoiceEslipsResponse.totalElements} Item
                </Typography>
                <Typography variant="body1">
                  Select E-Slip to allocate.
                </Typography>
              </Stack>
              {invoiceEslipsResponse.data.map((eslip) => (
                <EslipDetails
                  key={eslip.id}
                  eslip={eslip}
                  canSelect={true}
                  onEslipSelected={onEslipSelected}
                />
              ))}
            </>
          )}
        </Stack>
      </DialogContent>
    </Dialog>
  )
}
