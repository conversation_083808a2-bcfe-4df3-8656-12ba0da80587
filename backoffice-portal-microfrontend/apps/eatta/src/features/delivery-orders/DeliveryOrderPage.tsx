/**
 * <AUTHOR> on 16/06/2025
 */
'use client'

import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { getAuctionWeek } from '@dtbx/store/utils'
import { Divider, Stack } from '@mui/material'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { MainPageHeader } from '@/components/MainPageHeader'
import { DeliveryOrdersTable } from '@/features/delivery-orders/DeliveryOrdersTable'
import { DeliveryOrderFilters } from '@/store/interfaces/Edo'
import { resetDeliveryOrders, setDeliveryOrderFilters } from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'

const TABS: TabType<string>[] = [
  {
    title: 'New',
    status: 'NEW',
    canSelect: true,
  },
  {
    title: 'Pending Approval',
    status: 'PENDING_APPROVAL',
    canSelect: false,
  },
  {
    title: 'Dispatched',
    status: 'SIGNED',
    canSelect: false,
  },
]

export const DeliveryOrderPage = () => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const week = useMemo(() => getAuctionWeek().toString(), [])

  const { filters } = useAppSelector((state) => state.edo)

  const handleSearch = useCallback(
    (newFilters: DeliveryOrderFilters) => {
      dispatch(
        setDeliveryOrderFilters({
          ...newFilters,
          page: 1,
          saleDate: newFilters.saleDate || week,
          year: newFilters.year || new Date().getFullYear(),
        })
      )
    },
    [dispatch, week]
  )

  const handleTabChange = (index: number) => {
    dispatch(resetDeliveryOrders())
    setSelectedTab(index)
  }

  const handleCountChange = (count: number, tab: TabType<string>) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab, filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <MainPageHeader />

      <Divider />

      <PageFilters
        title="Delivery Orders"
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showStatus: false,
          showExport: false,
          showDateFilter: true,
        }}
        onSearch={handleSearch}
        filters={filters}
        searchByValues={[
          {
            filterLabel: 'Document Number',
            filterKey: 'docNumber',
            type: 'string',
          },
          {
            filterLabel: 'TRD Number',
            filterKey: 'trdDocNumber',
            type: 'string',
          },
          { filterLabel: 'Buyer', filterKey: 'buyerCode', type: 'string' },
          { filterLabel: 'Warehouse', filterKey: 'warehouse', type: 'string' },
        ]}
        onExport={() => {}}
      />
      <Divider />
      <CustomTabs
        isLoading={false}
        tabs={tabs}
        selectedTab={selectedTab}
        onTabSelected={handleTabChange}
      />
      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            <DeliveryOrdersTable
              status={tab.status}
              filters={filters}
              canSelect={tabs[selectedTab].canSelect}
              onDeliveryOrderCountChange={(count) =>
                handleCountChange(count, tabs[selectedTab])
              }
            />
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}
