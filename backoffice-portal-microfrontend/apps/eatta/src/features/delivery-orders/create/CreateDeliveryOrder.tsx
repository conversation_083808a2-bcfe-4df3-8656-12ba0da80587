'use client'
import {
  <PERSON>,
  CircularProgress,
  But<PERSON>,
  Di<PERSON><PERSON>,
  <PERSON>ack,
  Typo<PERSON>,
} from '@mui/material'
import { BackButton } from '@dtbx/ui/components/Button'
import { useRouter, useSearchParams } from 'next/navigation'
import React, { FC, useState, useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import Grid from '@mui/material/Grid2'
import { CustomStepper } from '@/components/CustomStepper'
import DeliveryOrderSetup from './DeliveryOrderSetup'
import CheckerVerification from './CheckerVerification'
import DispatchConfirmation from './DispatchConfirmation'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { DELIVERY_ORDER_STATUS_COLOR } from '@/utils/statusChips'
import { MainPageHeader } from '@/components/MainPageHeader'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import CheckerSignature from './CheckerSignature'
import { DeliveryOrderStatus } from '@/store/interfaces/Edo'
import { getDeliveryOrderById } from '@/store/actions/Edo'

type Step = {
  title: string
  description?: string
}

const MAKER_STEPS: Step[] = [
  {
    title: 'Set up delivery order',
    description:
      'Verify payment has been received, ensure all details are correct.',
  },
]

const CHECKER_STEPS: Step[] = [
  {
    title: 'Checker Verification',
    description:
      'Checker verifies EDO and approves dispatch to buyer and warehouse.',
  },
  {
    title: 'Dispatch',
    description: 'Email sent to buyers and godowns.',
  },
]

export enum CreateDeliveryOrderSteps {
  SETUP = 1,
  CHECKER_VERIFICATION,
  DISPATCH,
}

interface CreateDeliveryOrderPageProps {
  orderId: string
  actionType?: 'MAKER' | 'CHECKER'
}

const CreateDeliveryOrderPage: FC<CreateDeliveryOrderPageProps> = ({
  orderId,
  actionType,
}) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const dispatch = useAppDispatch()
  const [currentStep, setCurrentStep] = useState<CreateDeliveryOrderSteps>(1)
  const [isLoading, setIsLoading] = useState(false)
  const [showDeliveryOrderDialog, setShowDeliveryOrderDialog] =
    useState<boolean>(false)
  const { selectedDeliveryOrderEntries } = useAppSelector((state) => state.edo)
  const status = selectedDeliveryOrderEntries?.status || ''

  useEffect(() => {
    if (orderId) {
      getDeliveryOrderById(dispatch, orderId)
    }
  }, [dispatch, orderId])

  // Determine user role based on status and query parameters
  const stepParam = searchParams.get('step')
  const isCheckerFlow = stepParam === 'checker' || status === 'PENDING_APPROVAL'
  const userRole = isCheckerFlow ? 'CHECKER' : 'MAKER'

  // Set initial step based on query parameter and status
  useEffect(() => {
    if (stepParam === 'checker' && status === 'PENDING_APPROVAL') {
      setCurrentStep(CreateDeliveryOrderSteps.CHECKER_VERIFICATION)
    } else if (userRole === 'MAKER') {
      setCurrentStep(CreateDeliveryOrderSteps.SETUP)
    }
  }, [searchParams, status, userRole])

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
        height: '100%',
      }}
    >
      <MainPageHeader />

      <Divider />
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{ backgroundColor: '#FFFFFF' }}
      >
        <Stack
          direction="row"
          paddingInline={3}
          paddingBlock={2}
          spacing={2}
          alignItems="center"
        >
          <BackButton onClick={() => router.back()} />
          <Stack>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#000A12',
              }}
            >
              Delivery Order No. {selectedDeliveryOrderEntries?.docNumber || ''}
            </Typography>
          </Stack>
          <StatusChip
            label={status}
            status={DELIVERY_ORDER_STATUS_COLOR[status as DeliveryOrderStatus]}
          />
        </Stack>
        {currentStep === CreateDeliveryOrderSteps.DISPATCH && (
          <Button
            variant="contained"
            disabled={isLoading}
            onClick={() => setShowDeliveryOrderDialog(true)}
            sx={{
              marginRight: 3,
              textWrap: 'nowrap',
            }}
            endIcon={
              isLoading ? (
                <CircularProgress size={20} thickness={3.0} />
              ) : (
                <ChevronRightIcon stroke="#FFFFFF" />
              )
            }
          >
            Sign and Dispatch
          </Button>
        )}
      </Stack>

      <Divider />

      <Box sx={{ flexGrow: 1, height: '100%', overflow: 'hidden' }}>
        <Grid
          container
          spacing={2}
          sx={{ height: '100%', overflowY: { sm: 'auto', md: 'hidden' } }}
        >
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 4, lg: 3 }}
            sx={{ height: { md: '100%' }, overflowY: 'auto' }}
          >
            <CustomStepper
              steps={userRole === 'MAKER' ? MAKER_STEPS : CHECKER_STEPS}
              currentStep={userRole === 'MAKER' ? 1 : currentStep - 1}
              setStep={(step) => {
                if (userRole === 'CHECKER') {
                  setCurrentStep(step + 1)
                } else {
                  setCurrentStep(step)
                }
              }}
              disablePreviousSteps={status === 'PENDING_APPROVAL'}
            />
          </Grid>
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 8, lg: 9 }}
            sx={{ height: { md: '100%' }, overflowY: { md: 'auto' } }}
          >
            <Box width={{ sm: '100%', md: '80%', lg: '60%' }}>
              {(() => {
                switch (currentStep) {
                  case CreateDeliveryOrderSteps.SETUP:
                    return (
                      <DeliveryOrderSetup
                        onNext={() =>
                          setCurrentStep(
                            CreateDeliveryOrderSteps.CHECKER_VERIFICATION
                          )
                        }
                        actionType={userRole}
                      />
                    )
                  case CreateDeliveryOrderSteps.CHECKER_VERIFICATION:
                    return (
                      <CheckerVerification
                        onNext={() =>
                          setCurrentStep(CreateDeliveryOrderSteps.DISPATCH)
                        }
                      />
                    )
                  case CreateDeliveryOrderSteps.DISPATCH:
                    return <DispatchConfirmation />
                  default:
                    return null
                }
              })()}
            </Box>
          </Grid>
        </Grid>
      </Box>
      <CheckerSignature
        open={showDeliveryOrderDialog}
        onClose={() => setShowDeliveryOrderDialog(false)}
      />
    </Stack>
  )
}

export default CreateDeliveryOrderPage
