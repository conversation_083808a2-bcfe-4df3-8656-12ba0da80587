import { Divider, Stack, Typography } from '@mui/material'
import { Logo } from '@/components/SvgIcons/Logo'
import Image from 'next/image'
import React, { FC, JSX } from 'react'
import DeliveryDetails from './DeliveryDetails'
import { LotInfoCardList } from './LotInfoCard'
import DeliveryOrderInfo from './DeliveryOrderInfo'
import { useAppSelector } from '@/store'
import { fetchLogoImage } from '@/store/actions/Edo'
import { useAppDispatch } from '@/store'
import { useState, useEffect } from 'react'

interface DeliveryOrderTemplateProps {
  actionType?: 'MAKER' | 'CHECKER'
}

const DeliveryOrderTemplate: FC<DeliveryOrderTemplateProps> = ({
  actionType,
}) => {
  const dispatch = useAppDispatch()
  const [logoUrl, setLogoUrl] = useState<string | null>(null)
  const { selectedDeliveryOrderEntries, selectedAuctionEntry } = useAppSelector(
    (state) => state.edo
  )

  const currentEntry = selectedDeliveryOrderEntries?.entries?.find(
    (entry) => entry.lotNumber === selectedAuctionEntry?.lotNo
  )

  useEffect(() => {
    const fetchLogo = async () => {
      if (selectedDeliveryOrderEntries?.brokerLogoUrl) {
        const blobUrl = await fetchLogoImage(
          dispatch,
          selectedDeliveryOrderEntries.brokerLogoUrl
        )
        setLogoUrl(blobUrl)
      }
    }

    fetchLogo()

    return () => {
      if (logoUrl) {
        window.URL.revokeObjectURL(logoUrl)
      }
    }
  }, [selectedDeliveryOrderEntries?.brokerLogoUrl, dispatch])

  const isCheckerView = actionType === 'CHECKER'

  const BROKER_DETAILS = [
    {
      label: 'Initiated by',
      value: selectedDeliveryOrderEntries?.submittedBy || '_',
    },
    ...(isCheckerView
      ? [
          {
            label: 'Approved By',
            value: selectedDeliveryOrderEntries?.approvedBy || '_',
          },
          {
            label: 'Digitally Signed on [Date & Time] by:',
            value: selectedDeliveryOrderEntries?.signedBy || '_',
          },
        ]
      : []),
  ]

  return (
    <>
      <Stack
        sx={{
          background: '#FFF',
          width: 'fit-content',
          padding: '2rem',
          height: '100%',
          mt: 2,
        }}
      >
        <Stack sx={{ gap: '1rem', width: '100%' }}>
          <Stack
            sx={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}
          >
            <Stack sx={{ width: '310px', height: '135px' }}>
              <Image
                src={logoUrl || ''}
                alt="Logo"
                width={310}
                height={135}
                style={{
                  width: '310px',
                  height: '135px',
                  aspectRatio: '62/27',
                  objectFit: 'contain',
                }}
              />
            </Stack>

            <Typography
              sx={{
                fontWeight: 600,
                fontSize: '18px',
                color: '#000',
                marginRight: '2rem',
              }}
            >
              Delivery Order
            </Typography>
          </Stack>
          <Stack
            sx={{ alignItems: 'flex-start', color: '#000', gap: '0.5rem' }}
          >
            <Typography
              sx={{ fontWeight: 700, fontSize: '18px', color: '#000' }}
            >
              To: {selectedDeliveryOrderEntries?.warehouse}
            </Typography>
            <Typography sx={{ color: '#000' }}>
              Please deliver this tea to{' '}
              <b>
                {currentEntry?.buyerName ||
                  selectedAuctionEntry?.buyer ||
                  selectedDeliveryOrderEntries?.buyer ||
                  '_'}
              </b>{' '}
              on payment of all charges after{' '}
              <b>{selectedDeliveryOrderEntries?.promptDate || ''}</b>
            </Typography>
          </Stack>

          <Stack sx={{ width: '100%' }}>
            <Divider />
            <DeliveryOrderInfo />
            <Divider />
          </Stack>

          {/* Delivery Details */}

          <Stack sx={{ width: '100%' }}>
            <Typography
              sx={{
                color: '#344054',
                fontWeight: '600',
                textAlign: 'left',
                marginBottom: '0.5rem',
              }}
            >
              Delivery Details
            </Typography>
            <DeliveryDetails />
          </Stack>

          {/* Tea Details */}
          <Stack sx={{ width: '100%' }}>
            <Typography
              sx={{
                color: '#344054',
                fontWeight: '600',
                textAlign: 'left',
                marginBottom: '0.5rem',
              }}
            >
              Tea Details
            </Typography>
            <LotInfoCardList showExpandButton={false} />
          </Stack>

          {/* Broker Approval and Signature */}
          <Stack>
            <Stack direction="row" alignItems="center">
              <Typography
                sx={{
                  fontWeight: 600,
                  color: '#344054',
                  marginBottom: '0.5rem',
                }}
              >
                Broker Approval and Signature
              </Typography>
            </Stack>

            <Stack
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: 2,
                padding: '1rem',
                gap: '0.5rem',
              }}
            >
              {BROKER_DETAILS.map(({ label, value }, index) => (
                <Stack key={label}>
                  <Stack
                    sx={{
                      flexDirection: 'row',
                      gap: '2rem',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Typography>{label}</Typography>
                    <Typography sx={{ color: '#344054', fontWeight: 600 }}>
                      {value}
                    </Typography>
                  </Stack>
                  {index < BROKER_DETAILS.length - 1 && <Divider />}
                </Stack>
              ))}
            </Stack>
          </Stack>
          <Stack sx={{ alignItems: 'center', gap: '1rem' }}>
            <Typography>Facilitated by Diamond Trust Bank</Typography>
            <Logo />
          </Stack>
        </Stack>
      </Stack>
    </>
  )
}

export default DeliveryOrderTemplate
