'use client'

import { Divider, Stack } from '@mui/material'
import ApprovalRequestsTable from './ApprovalRequestTable'
import { ApprovalRequestHeader } from './ApprovalRequestsHeader'
import {
  ApprovalRequestFilters,
  ApprovalRequestStatus,
} from '@/store/interfaces/makerChecker'
import React, { useEffect, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { useAppDispatch, useAppSelector } from '@/store'
import { resetApprovalRequest } from '@/store/reducers/approvalRequestsReducer'
import { TabPanel } from '@dtbx/ui/components/Tabs'

const TABS: TabType<ApprovalRequestStatus>[] = [
  {
    title: 'Pending Requests',
    canSelect: true,
    status: 'PENDING',
  },
  {
    title: 'All Requests',
    canSelect: true,
    status: 'APPROVED',
  },
]

const initialFilters: ApprovalRequestFilters = {
  page: 1,
  size: 10,
  makerFirstName: '',
  makerLastName: '',
  requestType: '',
  createDateFrom: '',
  createDateTo: '',
  status: '',
  channel: '',
  module: '',
}

const ApprovalRequestPage = () => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<ApprovalRequestStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)
  const [filters, setFilters] = useState<ApprovalRequestFilters>(initialFilters)

  const { isLoadingApprovals } = useAppSelector((state) => state.approvals)

  const handleChange = (field: keyof ApprovalRequestFilters, value: string) => {
    setFilters({
      ...filters,
      [field]: value,
    })
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  const handleTabSelected = (index: number) => {
    dispatch(resetApprovalRequest())
    setFilters((prev) => ({
      ...prev,
      page: 1,
    }))
    setSelectedTab(index)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab])

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
        }}
      >
        <ApprovalRequestHeader
          filters={filters}
          onSearchChange={handleChange}
        />

        <CustomTabs
          tabs={tabs}
          selectedTab={selectedTab}
          onTabSelected={handleTabSelected}
          isLoading={isLoadingApprovals}
        />
      </Stack>

      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            <ApprovalRequestsTable
              filters={filters}
              status={tabs[selectedTab].status}
            />
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}

export default ApprovalRequestPage
