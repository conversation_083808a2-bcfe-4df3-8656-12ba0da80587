'use client'

import React, { useEffect, useState } from 'react'
import {
  Chip,
  IconButton,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
} from '@mui/material'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { ChipProps, StatusChip } from '@dtbx/ui/components/Chip'
import { getApprovalRequests } from '@/store/actions/ApprovalRequests'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  ApprovalRequestFilters,
  ApprovalRequestStatus,
  IApprovalRequest,
  MakerCheckerTypes,
} from '@/store/interfaces/makerChecker'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'

import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { sentenceCase } from 'tiny-case'
import { formatTimestamp } from '@dtbx/store/utils'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'
import { ArrowForwardIos } from '@mui/icons-material'
import { safeJsonParse } from '@/utils/objectUtil'
import { PaymentApprovalEntity } from '@/store/interfaces/transactions'
import { SelectedApprovalRequests } from '@/store/interfaces/makerChecker'
import {
  resetAllocatePayment,
  setAllocateEslip,
  setSelectedPayments,
  setSelectedTransactionApprovalsRequests,
} from '@/store/reducers/transactionsReducer'
import { AllocationModal } from '../payments/allocate/AllocationModal'
import { setSelectedApprovalRequests } from '@/store/reducers/brokerCommissionsReducer'
import BrokerCommissionForm, {
  ApprovalRequestDetails,
} from '../settings/commisions/CommissionDialog'

export const RequestChip = styled(Chip)<ChipProps>(() => ({
  padding: '2px 8px',
  borderRadius: '16px',
  background: '#F3F5F5',
  height: '24px',
  width: 'auto',
  minWidth: '0',
}))

interface ApprovalRequestsTableProps {
  status: ApprovalRequestStatus
  filters: ApprovalRequestFilters
}
const ApprovalRequestsTable: React.FC<ApprovalRequestsTableProps> = ({
  status,
  filters,
}) => {
  const dispatch = useAppDispatch()
  const [order, _setOrder] = useState<'asc' | 'desc'>('desc')
  const [orderBy, _setOrderBy] = useState<string>('dateCreated')
  const { approvalRequests, isLoadingApprovals } = useAppSelector(
    (state) => state.approvals
  )
  const { selectedApprovalRequests } = useAppSelector(
    (state) => state.brokerCommissions
  )
  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const [openAllocationModal, setOpenAllocationModal] = useState<boolean>(false)
  const [openCommissionsModal, setOpenCommissionsModal] =
    useState<boolean>(false)

  const [selectedRequest, setSelectedRequest] =
    useState<ApprovalRequestDetails>()
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )
  const router = useCustomRouter()

  const handleCloseAllocationModal = () => {
    setOpenAllocationModal(false)
    dispatch(resetAllocatePayment())
  }
  const handleOpenCommissionModal = (row: IApprovalRequest) => {
    const selectedApprovalRequests = JSON.parse(
      row.entity || '{}'
    ) as SelectedApprovalRequests
    dispatch(setSelectedApprovalRequests(selectedApprovalRequests))

    const requestDetails: ApprovalRequestDetails = {
      id: row.id,
      requestType: row.makerCheckerType?.type || '',
      createdAt: row.dateCreated,
      createdBy: row.maker,
      checker: row.checker,
      checkerComments: row.checkerComments,
      maker: row.maker,
      dateCreated: row.dateCreated,
      dateModified: row.dateModified,
      makerCheckerType: row.makerCheckerType,
      entityId: row.entityId || '',
      entity: row.entity || '',
      diff: row.diff,
      makerComments: row.makerComments,
      status: row.status,
    }

    setSelectedRequest(requestDetails)
    setOpenCommissionsModal(true)
  }

  const handleViewRequest = (row: IApprovalRequest) => {
    switch (row.makerCheckerType.type) {
      case MakerCheckerTypes.CreateUser:
      case MakerCheckerTypes.UpdateUser:
        router.push(`/backoffice/members/approve/${row.id}`)
        break
      case MakerCheckerTypes.UpdateOrganization:
      case MakerCheckerTypes.CreateOrganization:
        router.push(`/backoffice/companies/approve/${row.id}`)
        break
      case MakerCheckerTypes.CreateCommission:
        handleOpenCommissionModal(row)
        break
      case MakerCheckerTypes.AllocateInvoicePayment:
        const approval = safeJsonParse<PaymentApprovalEntity>(row.entity)
        if (!approval) {
          return
        }
        dispatch(setSelectedPayments(approval.invoicePayment))
        dispatch(setAllocateEslip(approval.invoice))
        dispatch(setSelectedTransactionApprovalsRequests(row))
        setOpenAllocationModal(true)
        break
      default:
        break
    }
  }
  const getRightsByMakerCheckerType = (row: IApprovalRequest) => {
    switch (row.makerCheckerType.type) {
      case MakerCheckerTypes.CreateUser:
      case MakerCheckerTypes.UpdateUser:
        return ACCESS_CONTROLS.ACCEPT_CREATE_USER

      case MakerCheckerTypes.UpdateOrganization:
      case MakerCheckerTypes.CreateOrganization:
        return ACCESS_CONTROLS.ACCEPT_CREATE_ORGANIZATION
      case MakerCheckerTypes.CreateCommission:
        return ACCESS_CONTROLS.ACCEPT_COMMISSION

      case MakerCheckerTypes.AllocateInvoicePayment:
        return ACCESS_CONTROLS.ACCEPT_ALLOCATE_PAYMENT
      default:
        return []
    }
  }

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    const fetchApprovals = async () => {
      await getApprovalRequests(dispatch, {
        ...filters,
        status: status,
        page: paginationOptions.page,
        size: paginationOptions.size,
        channel: 'EATTA',
      })
    }
    fetchApprovals()
  }, [dispatch, filters, paginationOptions.page, status])

  const APPROVALS_HEADER = [
    { id: 'requestType', label: 'Request Type', alignRight: false },
    { id: 'maker', label: 'Maker', alignRight: false },
    { id: 'makerTimestamp', label: 'Maker Timestamp', alignRight: false },
  ]

  if (status === 'APPROVED') {
    APPROVALS_HEADER.push({
      id: 'status',
      label: 'Status',
      alignRight: false,
    })
  } else {
    APPROVALS_HEADER.push({
      id: 'action',
      label: 'Action',
      alignRight: false,
    })
  }

  return isLoadingApprovals ? (
    <TableSkeleton rowCount={15} columnCount={4} />
  ) : approvalRequests.data.length === 0 ? (
    <EmptyPage title="No requests found" bgUrl={'/eatta/combo.svg'} />
  ) : (
    <TableContainer
      component={Paper}
      sx={{
        boxShadow: 'none',
        '& .MuiTableCell-root': {
          paddingInline: '1.5rem',
          paddingBlock: '0.5rem',
          textAlign: 'left',
        },
      }}
    >
      <Table
        sx={{ minWidth: 650 }}
        aria-label="approval requests table"
        size="small"
      >
        <CustomTableHeader
          order={order}
          orderBy={orderBy}
          headLabel={APPROVALS_HEADER}
          showCheckbox={false}
          rowCount={approvalRequests.data.length}
          numSelected={0}
        />
        <TableBody>
          {approvalRequests.data.map((row, index) => (
            <TableRow hover key={index} tabIndex={-1}>
              <TableCell>
                <RequestChip
                  label={sentenceCase(row.makerCheckerType.type)}
                  sx={{ width: 'auto' }}
                />
              </TableCell>
              <TableCell>{row.maker}</TableCell>
              <TableCell>{formatTimestamp(row.dateCreated)}</TableCell>
              {status === 'APPROVED' && (
                <TableCell>
                  <StatusChip status="success" label={row.status} />
                </TableCell>
              )}
              {status !== 'APPROVED' && (
                <TableCell>
                  <AccessControlWrapper
                    rights={getRightsByMakerCheckerType(row)}
                    makerId={row.maker}
                  >
                    <IconButton onClick={() => handleViewRequest(row)}>
                      <ArrowForwardIos />
                    </IconButton>
                  </AccessControlWrapper>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell
              sx={{ paddingInline: 0 }}
              align="center"
              height={40}
              colSpan={12}
            >
              {approvalRequests.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: approvalRequests.totalNumberOfPages,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
      {openAllocationModal && (
        <AllocationModal
          open={openAllocationModal}
          onClose={handleCloseAllocationModal}
          actionType={'CHECKER'}
        />
      )}
      {openCommissionsModal && (
        <BrokerCommissionForm
          open={openCommissionsModal}
          handleClose={() => {
            setOpenCommissionsModal(false)
            setSelectedRequest(undefined)
          }}
          actionType="CHECKER"
          requestDetails={selectedRequest}
        />
      )}
    </TableContainer>
  )
}

export default ApprovalRequestsTable
