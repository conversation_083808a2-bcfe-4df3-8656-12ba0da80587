/**
 * <AUTHOR> on 23/10/2024
 */

'use client'
import { Stack } from '@mui/material'
import React, { useEffect, useMemo, useState } from 'react'
import { CompanyFilters, OrganizationStatus } from '@/store/interfaces'
import CompaniesTable from '@/features/companies/CompaniesTable'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import { CompanyHeader } from '@/features/companies/CompanyHeader'
import CompanyApprovalsTable from '@/features/companies/CompanyApprovalsTable'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { useAppSelector } from '@/store'
import { SearchByValueConfig } from '@/components/PageFilters'
import { ApprovalRequestFilters } from '@/store/interfaces/makerChecker'

const TABS: TabType<OrganizationStatus>[] = [
  {
    title: 'Companies',
    canSelect: true,
    status: 'ACTIVE',
  },
  {
    title: 'Registration Ongoing',
    canSelect: true,
    status: 'STAGING',
  },
  {
    title: 'Awaiting Approval',
    canSelect: false,
    status: 'PENDING',
  },
]

const initialFilters = {
  page: 1,
  size: 10,
  name: '',
  code: '',
  type: '',
}

const companiesSearchByValues: SearchByValueConfig<CompanyFilters>[] = [
  {
    filterLabel: 'Name',
    filterKey: 'name',
    type: 'string',
  },
  {
    filterLabel: 'Code',
    filterKey: 'code',
    type: 'string',
  },
  {
    filterLabel: 'Type',
    filterKey: 'type',
    type: 'string',
  },
]

const approvalsSearchByValues: SearchByValueConfig<ApprovalRequestFilters>[] = [
  {
    filterLabel: 'Maker Firstname',
    filterKey: 'makerFirstName',
    type: 'string',
  },
  {
    filterLabel: 'Maker Lastname',
    filterKey: 'makerLastName',
    type: 'string',
  },
]

const CompaniesPage = () => {
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<OrganizationStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const [filters, setFilters] = useState<
    CompanyFilters | ApprovalRequestFilters
  >(initialFilters)

  const { isLoading } = useAppSelector((state) => state.companies)

  const isCompanyTabSelected = useMemo(() => {
    return tabs[selectedTab].title === 'Companies'
  }, [selectedTab, tabs])

  const searchByValues = useMemo(() => {
    return isCompanyTabSelected
      ? companiesSearchByValues
      : approvalsSearchByValues
  }, [isCompanyTabSelected])

  const handleTabSelected = (index: number) => {
    setSelectedTab(index)
  }

  const handleChange = (
    field: keyof CompanyFilters | keyof ApprovalRequestFilters,
    value: string
  ) => {
    setFilters({
      ...initialFilters,
      [field]: value,
    })
  }

  const handleCountChange = (
    count: number,
    tab: TabType<OrganizationStatus>
  ) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab])

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
        }}
      >
        <CompanyHeader
          onSearchChange={handleChange}
          searchByValues={searchByValues}
          initialSearchBy={isCompanyTabSelected ? 'name' : 'makerFirstName'}
        />

        <CustomTabs
          tabs={tabs}
          selectedTab={selectedTab}
          onTabSelected={handleTabSelected}
          isLoading={isLoading}
        />
      </Stack>

      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            {tab.title === 'Companies' ? (
              <CompaniesTable
                filters={filters}
                onCompanyCountChange={(count) => handleCountChange(count, tab)}
              />
            ) : (
              <CompanyApprovalsTable
                status={tab.status}
                filters={filters as ApprovalRequestFilters}
                onApprovalCountChange={(count) => handleCountChange(count, tab)}
              />
            )}
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}

export default CompaniesPage
