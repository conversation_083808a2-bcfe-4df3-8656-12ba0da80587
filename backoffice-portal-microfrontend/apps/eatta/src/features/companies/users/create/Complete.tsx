import { Button, Stack } from '@mui/material'
import { useRouter } from 'next/navigation'
import ConfirmationCard from '@/components/ConfirmationCard'
import { useAppSelector } from '@/store'

const Complete = () => {
  const router = useRouter()
  const { onboardedUser } = useAppSelector((state) => state.companies)
  return (
    <ConfirmationCard
      title="Success!"
      description={`An email with a link to activate this account is on its way to ${onboardedUser?.email}`}
    >
      <Stack
        spacing={3}
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{ width: '100%' }}
      >
        <Button
          fullWidth
          variant="contained"
          type="button"
          onClick={() => router.back()}
        >
          Okay
        </Button>
      </Stack>
    </ConfirmationCard>
  )
}

export default Complete
