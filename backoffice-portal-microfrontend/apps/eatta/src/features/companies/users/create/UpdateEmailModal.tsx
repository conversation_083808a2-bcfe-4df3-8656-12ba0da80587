import {
    <PERSON>ton,
    CircularProgress,
    Dialog,
    DialogContent,
    DialogContentText,
    DialogTitle,
    Stack,
    TextField,
  } from '@mui/material'
  import * as Yup from 'yup'
  import { Form, FormikProvider, useFormik } from 'formik'
  import {
    CompanyUser,
    EditCompanyUser,
  } from '@/store/interfaces'
  import { useAppDispatch, useAppSelector } from '@/store'
  
  const validationSchema = Yup.object({
    email: Yup.string()
      .required('Email is required')
      .email('Enter a valid email address'),
  })
  
  type UpdateEmailModalProps = {
    user: CompanyUser
    open: boolean
    handleClose: () => void
  }
  
  export const UpdateEmailModal = ({
    open,
    handleClose,
    user,
  }: UpdateEmailModalProps) => {
  
    const dispatch = useAppDispatch()
    const { isEditingUser } = useAppSelector((state) => state.companies)
  
    const formik = useFormik({
      initialValues: {
        email: user.email ?? '',
      },
      validateOnMount: true,
      validationSchema,
      onSubmit: async (values) => {
        const editUser: EditCompanyUser = {
          ...user,
          email: values.email,
        }
  
        try {
        //   await makeUpdateUser(user.id, editUser, dispatch)
          handleClose()
        } catch (error) {
          console.error('Failed to update user email:', error)
        }
      },
    })
  
    return (
      <Dialog fullWidth open={open} onClose={handleClose}>
        <DialogTitle
          sx={{ fontWeight: 600, fontSize: 24, color: '#000A12' }}
        >
          Update Email
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ color: '#000A12' }}>
          Current email: {user.email} 
          </DialogContentText>
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <Stack spacing={2} paddingBlock={2}>
                <Stack spacing={1}>
                  <TextField
                    label="Enter New Email"
                    size="small"
                    type="email"
                    {...formik.getFieldProps('email')}
                    onKeyDown={(e) => e.stopPropagation()}
                    fullWidth
                    error={Boolean(formik.touched.email && formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Stack>
  
                <Stack direction="row">
                  <Button
                    disabled={isEditingUser}
                    type="submit"
                    variant="contained"
                    fullWidth
                    endIcon={
                      isEditingUser ? (
                        <CircularProgress
                          color="primary"
                          size={20}
                          thickness={3.0}
                        />
                      ) : undefined
                    }
                  >
                   Submit Changes for Approval
                  </Button>
                </Stack>
              </Stack>
            </Form>
          </FormikProvider>
        </DialogContent>
      </Dialog>
    )
  }
  