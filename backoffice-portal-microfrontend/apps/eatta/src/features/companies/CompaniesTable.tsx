import React, { useEffect, useState } from 'react'
import {
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { Company, CompanyFilters, Order, PageFilters } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { getCompanies } from '@/store/actions'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import AddIcon from '@mui/icons-material/Add'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { CompanyMoreMenu } from '@/features/companies/MoreMenu'
import { AccessControlWrapper, formatDate } from '@dtbx/store/utils'
import { sortData } from '@/utils/sortTableData'
import TableSkeleton from '@/components/TableSkeleton'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'

interface CompaniesTableProps {
  filters: CompanyFilters
  onCompanyCountChange?: (count: number) => void
}

const initialPageFilters: PageFilters = {
  page: 1,
  size: 10,
}

const CompaniesTable: React.FC<CompaniesTableProps> = ({
  filters,
  onCompanyCountChange,
}) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { companiesResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof Company>('dateCreated')

  const handleCreateCompany = () => {
    router.push('/backoffice/companies/create')
  }

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof Company)
  }

  const sortKey = orderBy as keyof Company
  const companiesDataSorted = sortData(
    [...companiesResponse.data],
    sortKey,
    order
  )

  const [paginationOptions, setPaginationOptions] = useState(initialPageFilters)
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }

    const fetchCompanies = async () => {
      await getCompanies(dispatch, {
        ...filters,
        page: paginationOptions.page,
        size: paginationOptions.size,
        ascending: false,
      })
    }
    fetchCompanies()
  }, [dispatch, filters, paginationOptions.page])

  useEffect(() => {
    onCompanyCountChange &&
      onCompanyCountChange(companiesResponse.totalElements)
  }, [companiesResponse.totalElements])

  return isLoading ? (
    <TableSkeleton rowCount={15} columnCount={6} />
  ) : companiesResponse.data.length === 0 ? (
    <EmptyPage
      title="No companies found"
      message="Please create a new company to get started"
      bgUrl={'/eatta/combo.svg'}
      action={
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ORGANIZATION}>
          <Button
            variant="contained"
            type="submit"
            startIcon={<AddIcon />}
            onClick={handleCreateCompany}
          >
            New Company
          </Button>
        </AccessControlWrapper>
      }
    />
  ) : (
    <TableContainer
      component={Paper}
      sx={{
        boxShadow: 'none',
        '& .MuiTableCell-root': {
          paddingInline: '1.5rem',
          paddingBlock: '0.5rem',
          textAlign: 'left',
        },
      }}
    >
      <Table sx={{ minWidth: 650 }} aria-label="company table" size="small">
        <CustomTableHeader
          order={order}
          orderBy={orderBy}
          headLabel={[
            {
              id: 'code',
              label: 'EATTA Member Code',
              alignRight: false,
            },
            { id: 'name', label: 'Company Name', alignRight: false },
            { id: 'type', label: 'Company Type', alignRight: false },
            {
              id: 'dateCreated',
              label: 'Date Created',
              alignRight: false,
            },
            {
              id: 'status',
              label: 'Status',
              alignRight: false,
            },
            { id: '', label: '', alignRight: false },
          ]}
          showCheckbox={false}
          rowCount={companiesResponse.data.length}
          numSelected={0}
          onRequestSort={handleRequestSort}
        />
        <TableBody>
          {companiesDataSorted.map((row: Company) => {
            const { id, name, type, dateCreated, code } = row

            return (
              <TableRow hover key={id} tabIndex={-1} role="checkbox">
                <TableCell component="th" scope="row" id={code}>
                  {code}
                </TableCell>
                <TableCell>{name}</TableCell>
                <TableCell>
                  <StatusChip
                    status={
                      type === 'Broker' || type === 'Buyer'
                        ? 'success'
                        : 'error'
                    }
                    label={type}
                  />
                </TableCell>
                <TableCell>{formatDate(dateCreated)}</TableCell>
                <TableCell>
                  <StatusChip label="ACTIVE"></StatusChip>
                </TableCell>
                <TableCell>
                  <CompanyMoreMenu company={row} />
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell
              sx={{ paddingInline: 0 }}
              align="center"
              height={40}
              colSpan={12}
            >
              {companiesResponse.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: companiesResponse.totalNumberOfPages,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </TableContainer>
  )
}

export default CompaniesTable
