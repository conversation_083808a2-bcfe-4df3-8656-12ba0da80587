/**
 * <AUTHOR> on 01/07/2025
 */
'use client'
import { Company, OnboardingStep } from '@/store/interfaces'
import React, { FC, useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Box, Divider, Stack, Typography } from '@mui/material'
import { BackButton } from '@dtbx/ui/components/Button'
import Grid from '@mui/material/Grid2'
import { CustomStepper } from '@/components/CustomStepper'
import DetailsVerification from '@/features/companies/create/DetailsVerification'
import Complete from '@/features/companies/create/Complete'
import { useAppDispatch, useAppSelector } from '@/store'
import { getApprovalById } from '@/store/actions'
import { setOnboardingCompany } from '@/store/reducers'
import ApprovalSkeleton from '@/components/ApprovalSkeleton'

export enum ApproveCompanySteps {
  CHECKER = 1,
  COMPLETE,
}

const STEPS: OnboardingStep[] = [
  {
    title: 'Checker Verification',
    description: 'Company profile is submitted to compliance for verification.',
  },
  {
    title: 'Complete',
    description: 'Company is onboarded successfully.',
  },
]

interface ApproveCompanyPageProps {
  approvalId: string
}

const ApproveCompanyPage: FC<ApproveCompanyPageProps> = ({ approvalId }) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const [currentStep, setCurrentStep] = useState<ApproveCompanySteps>(1)

  const { onboardingCompany } = useAppSelector((state) => state.companies)
  const company: Company | null = useMemo(
    () =>
      onboardingCompany?.entity ? JSON.parse(onboardingCompany.entity) : null,
    [onboardingCompany]
  )
  const type = company?.type
  const isFactory = type === 'Factory'

  const getApprovals = async () => {
    await getApprovalById(dispatch, approvalId, (approvalRequest) => {
      dispatch(setOnboardingCompany(approvalRequest))
    })
  }

  useEffect(() => {
    getApprovals()
  }, [approvalId])

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
        height: '100%',
      }}
    >
      <Stack
        sx={{ backgroundColor: '#FFFFFF' }}
        direction="row"
        paddingInline={3}
        paddingBlock={2}
        spacing={2}
        alignItems="flex-start"
      >
        <BackButton onClick={() => router.back()} />
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            color: '#000A12',
          }}
        >
          Approve {type ? type : 'Company'}
        </Typography>
      </Stack>

      <Divider />

      <Box sx={{ flexGrow: 1, height: '100%', overflow: 'hidden' }}>
        <Grid
          container
          spacing={2}
          sx={{ height: '100%', overflowY: { sm: 'auto', md: 'hidden' } }}
        >
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 4, lg: 3 }}
            sx={{ height: { md: '100%' }, overflowY: 'auto' }}
          >
            <CustomStepper
              steps={STEPS}
              currentStep={currentStep}
              setStep={(step: ApproveCompanySteps) => setCurrentStep(step)}
            />
          </Grid>
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 8, lg: 9 }}
            sx={{ height: { md: '100%' }, overflowY: { md: 'auto' } }}
          >
            <Box width={{ sm: '100%', md: '100%', lg: '70%' }}>
              {!onboardingCompany ? (
                <ApprovalSkeleton />
              ) : (
                (() => {
                  switch (currentStep) {
                    case ApproveCompanySteps.CHECKER:
                      return (
                        <DetailsVerification
                          actionType="CHECKER"
                          setStep={setCurrentStep}
                        />
                      )
                    case ApproveCompanySteps.COMPLETE:
                      return <Complete isFactory={isFactory} />
                  }
                })()
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  )
}

export default ApproveCompanyPage
