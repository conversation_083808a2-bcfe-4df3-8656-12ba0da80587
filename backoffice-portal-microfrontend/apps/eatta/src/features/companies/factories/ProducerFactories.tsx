import React, { useState } from 'react'
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useAppDispatch, useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  Order,
  ProducerFactory,
  ProducerFactoryFilters,
} from '@/store/interfaces'
import TableSkeleton from '@/components/TableSkeleton'

export interface ProducerFactoriesTableProps {
  filters: ProducerFactoryFilters
  onPageChange?: (page: number, size: number) => void
}
const ProducerFactoriesTable = ({
  filters,
  onPageChange,
}: ProducerFactoriesTableProps) => {
  const dispatch = useAppDispatch()
  const { producerFactoryResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')

  const [paginationOptions, setPaginationOptions] = useState({
    page: filters.page,
    size: filters.size,
    totalPages: producerFactoryResponse.totalNumberOfPages,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    if (onPageChange) {
      onPageChange(newOptions.page, newOptions.size)
    }
  }
  /*************************end pagination handlers**************************/

  return isLoading ? (
    <TableSkeleton rowCount={4} columnCount={4} />
  ) : producerFactoryResponse.data.length === 0 ? (
    <EmptyPage
      title="No records found"
      message="No factory data is currently available."
      bgUrl={'/combo.svg'}
    />
  ) : (
    <>
      <Typography
        sx={{
          width: '100%',
          textAlign: 'center',
          padding: '1rem',
        }}
      >
        {`Showing ${producerFactoryResponse.totalElements} factories`}
      </Typography>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="company table" size="small">
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={[
              {
                id: 'factory',
                label: 'Factory',
                alignRight: false,
              },
              {
                id: 'totalWeight',

                label: 'Total Weight',

                alignRight: false,
              },
              {
                id: 'totalearnings',

                label: 'Total Earnings(USD)',

                alignRight: false,
              },
            ]}
            rowCount={producerFactoryResponse.data.length}
            numSelected={0}
          />
          <TableBody>
            {producerFactoryResponse.data.map((row: ProducerFactory) => {
              return (
                <TableRow hover tabIndex={-1}>
                  <TableCell component="th" scope="row">
                    {row.factory}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">{row.totalWeight}</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">
                      $
                      {new Intl.NumberFormat('en-US').format(row.totalEarnings)}
                    </Typography>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                sx={{ paddingInline: 0 }}
                align="center"
                height={40}
                colSpan={12}
              >
                {producerFactoryResponse.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: producerFactoryResponse.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    </>
  )
}

export default ProducerFactoriesTable
