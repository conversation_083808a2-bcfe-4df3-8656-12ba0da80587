import React, { useEffect } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import {
  Bank,
  BankBranch,
  CHANNELS,
  CURRENCIES,
  Currency,
  PaymentChannel,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'
import { NUMERIC_REGEX, ORG_NAME_REGEX } from '@/utils/validators'
import CloseIcon from '@mui/icons-material/Close'
import { useAppDispatch } from '@dtbx/store'
import { createFactory, fetchBanks } from '@/store/actions'
import { AddFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { FactoryCreationRequest } from '@/store/interfaces/factory'

const initialSchema = Yup.object({
  name: Yup.string()
    .required('Factory Name is required')
    .matches(
      ORG_NAME_REGEX,
      'Only letters, numbers, spaces, and brackets are allowed. No leading or trailing spaces.'
    )
    .test(
      'alpha',
      'Name must contain at least three letters.',
      (value) => (value.match(/[a-zA-Z]/g) || []).length >= 3
    ),
  bankName: Yup.string().required('Bank is required'),
  accountCurrency: Yup.string().required('Currency is required'),
  accountNumber: Yup.string()
    .required('Account number should not be empty')
    .matches(NUMERIC_REGEX, 'Account number should be a number')
    .min(5, 'Account number should be at least 5 digits')
    .max(16, 'Account number should have max of 16 digits'),
  channel: Yup.string().required('channel is required'),
  swiftCode: Yup.string().required('Swift Code is required'),
})

export const CreateFactoryForm = ({
  organizationId,
  onCreateFactorySuccess,
  onCancel,
}: {
  organizationId: string
  onCreateFactorySuccess: () => void
  onCancel: () => void
}) => {
  const { isCreatingUser } = useAppSelector((state) => state.companies)
  const dispatch = useAppDispatch()
  const { banks, bankBranches } = useAppSelector((state) => state.companies)

  const [validationSchema, updateValidationSchema] =
    React.useState(initialSchema)

  const [bank, setBank] = React.useState<Bank | null>(null)
  const [branch, setBranch] = React.useState<BankBranch | null>(null)
  const [currency, setCurrency] = React.useState<Currency | null>(null)

  const formik = useFormik({
    initialValues: {
      name: '',
      bankBranchName: '',
      bankCode: '',
      bankName: '',
      bankBranchCode: '',
      accountNumber: '',
      accountCurrency: '',
      channel: '',
      swiftCode: '',
    },
    validateOnMount: true,
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      const factory: FactoryCreationRequest = {
        name: values.name,
        bankName: values.bankName,
        bankCode: values.bankCode,
        bankBranchName: values.bankBranchName,
        bankBranchCode: values.bankBranchCode,
        accountNumber: values.accountNumber,
        accountCurrency: values.accountCurrency,
        channel: values.channel,
        swiftCode: values.swiftCode,
        type: 'Factory',
        parentOrganizationId: organizationId,
      }
      await createFactory(dispatch, factory, () => {
        onCreateFactorySuccess()
      })
    },
  })

  useEffect(() => {
    setBank(banks.find((b) => b.bankName === formik.values.bankName) ?? null)
    setBranch(
      bankBranches.find((b) => b.code === formik.values.bankBranchCode) ?? null
    )
    setCurrency(
      CURRENCIES.find((c) => c.code === formik.values.accountCurrency) ?? null
    )
  }, [banks, bankBranches, formik.values])

  useEffect(() => {
    fetchBanks(dispatch)
  }, [])

  useEffect(() => {
    const schema = initialSchema

    if (bank?.bicCode !== 'DTKEKENA') {
      schema.shape({
        bankBranchName: Yup.string().required(
          'Branch name should not be empty'
        ),
        bankBranchCode: Yup.string().required(
          'Branch code should not be empty'
        ),
      })
    }
    updateValidationSchema(schema)
  }, [bank?.bicCode])

  const handleBankChange = (value: Bank | null) => {
    setBank(value)
    formik.setFieldValue('bankCode', value?.bankCode ?? '')
    formik.setFieldValue('bankName', value?.bankName ?? '')
    formik.setFieldValue('swiftCode', value?.bicCode ?? '')
    if (value?.bicCode === 'DTKEKENA') {
      formik.setFieldValue('channel', 'IFT')
    } else {
      formik.setFieldValue('channel', '')
    }
  }

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit}>
        <Stack spacing={2} paddingBlock={2}>
          <Stack spacing={1}>
            <Typography fontWeight={600}>Name of factory:</Typography>
            <TextField
              hiddenLabel
              size="small"
              type="text"
              sx={{ marginBlock: '0' }}
              margin={'normal'}
              {...formik.getFieldProps('name')}
              fullWidth
              onKeyDown={(e) => e.stopPropagation()}
              error={Boolean(formik.touched.name && formik.errors.name)}
              helperText={formik.touched.name && formik.errors.name}
            />
          </Stack>
          <Stack spacing={1}>
            <Typography fontWeight={600}>Enter payment details:</Typography>
            <Typography>
              Please provide a bank account through which the factory will
              receive funds a sale. The can be paid either through SWIFT or
              RTGS.You can always update this later.
            </Typography>
            <Stack spacing={1}>
              <Typography variant="body1">Select Bank</Typography>
              <Autocomplete
                disablePortal
                size="small"
                id="bankCode"
                options={banks}
                value={bank}
                onChange={(_, value) => handleBankChange(value)}
                getOptionLabel={(option) => option.bankName}
                isOptionEqualToValue={(option, value) =>
                  option.bankCode === value.bankCode
                }
                renderInput={(params) => (
                  <TextField
                    hiddenLabel
                    {...params}
                    placeholder="Select bank"
                    error={Boolean(
                      formik.touched.bankName && formik.errors.bankName
                    )}
                    helperText={
                      formik.touched.bankName && formik.errors.bankName
                        ? formik.errors.bankName
                        : ''
                    }
                  />
                )}
              />
            </Stack>
          </Stack>
          {bank?.bicCode === 'DTKEKENA' && (
            <Stack direction="row" spacing={2}>
              <Stack sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Branch Name
                </Typography>
                <Autocomplete
                  fullWidth
                  disablePortal
                  size="small"
                  id="bankBranchName"
                  options={bankBranches}
                  value={branch}
                  onChange={(_, value) => {
                    setBranch(value)
                    formik.setFieldValue('bankBranchName', value?.name ?? '')
                    formik.setFieldValue('bankBranchCode', value?.code ?? '')
                  }}
                  getOptionLabel={(option) => option.name}
                  isOptionEqualToValue={(option, value) =>
                    option.code === value.code
                  }
                  renderInput={(params) => (
                    <TextField
                      hiddenLabel
                      {...params}
                      placeholder="Select branch"
                      error={Boolean(
                        formik.touched.bankBranchName &&
                          formik.errors.bankBranchName
                      )}
                      helperText={
                        formik.touched.bankBranchName &&
                        formik.errors.bankBranchName
                          ? formik.errors.bankBranchName
                          : ''
                      }
                    />
                  )}
                />
              </Stack>

              <Stack sx={{ width: '50%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Branch Code
                </Typography>
                <TextField
                  disabled
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="001"
                  sx={{ marginBlock: '0' }}
                  margin={'normal'}
                  {...formik.getFieldProps('bankBranchCode')}
                  fullWidth
                  error={Boolean(
                    formik.touched.bankBranchCode &&
                      formik.errors.bankBranchCode
                  )}
                  helperText={
                    formik.touched.bankBranchCode &&
                    formik.errors.bankBranchCode
                  }
                />
              </Stack>
            </Stack>
          )}

          <Stack direction="row" spacing={2}>
            <Stack width="100%">
              <Typography fontWeight={600}>
                Account Number:{' '}
                <Box component="span" sx={{ color: 'primary.main' }}>
                  *
                </Box>
              </Typography>
              <TextField
                hiddenLabel
                size="small"
                type="text"
                sx={{ marginBlock: '0' }}
                margin={'normal'}
                {...formik.getFieldProps('accountNumber')}
                fullWidth
                onKeyDown={(e) => e.stopPropagation()}
                error={Boolean(
                  formik.touched.accountNumber && formik.errors.accountNumber
                )}
                helperText={
                  formik.touched.accountNumber && formik.errors.accountNumber
                }
              />
            </Stack>
            <Stack sx={{ width: '100%' }}>
              <Typography variant="body1">Currency</Typography>
              <Autocomplete
                fullWidth
                disablePortal
                size="small"
                id="accountCurrency"
                options={CURRENCIES}
                value={currency}
                onChange={(_, value) => {
                  setCurrency(value)
                  formik.setFieldValue(
                    'accountCurrency',
                    value?.code ?? '',
                    true
                  )
                }}
                getOptionLabel={(option) => option.name}
                isOptionEqualToValue={(option, value) =>
                  option.code === value.code
                }
                renderInput={(params) => (
                  <TextField
                    hiddenLabel
                    {...params}
                    placeholder="Select currency"
                    error={Boolean(
                      formik.touched.accountCurrency &&
                        formik.errors.accountCurrency
                    )}
                    helperText={
                      formik.touched.accountCurrency &&
                      formik.errors.accountCurrency
                        ? formik.errors.accountCurrency
                        : ''
                    }
                  />
                )}
              />
            </Stack>
          </Stack>
          <Stack sx={{ width: '100%' }}>
            <Typography variant="body1">Channel</Typography>
            <Autocomplete
              fullWidth
              disablePortal
              size="small"
              id="channel"
              options={CHANNELS.filter((c) =>
                bank?.bicCode === 'DTKEKENA' ? c === 'IFT' : c !== 'IFT'
              )}
              {...formik.getFieldProps('channel')}
              onChange={(_, value: PaymentChannel) => {
                formik.setFieldValue('channel', value)
              }}
              renderInput={(params) => (
                <TextField
                  hiddenLabel
                  {...params}
                  placeholder="Select channel"
                  error={Boolean(
                    formik.touched.channel && formik.errors.channel
                  )}
                  helperText={
                    formik.touched.channel && formik.errors.channel
                      ? formik.errors.channel
                      : ''
                  }
                />
              )}
            />
          </Stack>
          <Stack>
            <Typography fontWeight={600}>SWIFT Code</Typography>
            <TextField
              disabled
              hiddenLabel
              size="small"
              type="text"
              placeholder="KENXBNK"
              sx={{ marginBlock: '0' }}
              margin={'normal'}
              {...formik.getFieldProps('swiftCode')}
              fullWidth
              onKeyDown={(e) => e.stopPropagation()}
              error={Boolean(
                formik.touched.swiftCode && formik.errors.swiftCode
              )}
              helperText={formik.touched.swiftCode && formik.errors.swiftCode}
            />
          </Stack>

          <Stack
            direction="row"
            spacing={2}
            sx={{
              mt: 3,
              width: '100%',
            }}
          >
            <Button
              sx={{ width: '40%' }}
              disabled={isCreatingUser}
              variant="outlined"
              onClick={onCancel}
            >
              Back
            </Button>

            <Button
              fullWidth
              variant="contained"
              type="submit"
              disabled={!formik.isValid}
              endIcon={
                isCreatingUser ? (
                  <CircularProgress size={20} thickness={3.0} />
                ) : undefined
              }
            >
              Submit
            </Button>
          </Stack>
        </Stack>
      </Form>
    </FormikProvider>
  )
}

type CreateFactoryDialogPros = {
  organizationName: string
  organizationId: string
  open: boolean
  handleClose: () => void
}

export const CreateFactoryDialog = ({
  organizationName,
  organizationId,
  open,
  handleClose,
}: CreateFactoryDialogPros) => {
  return (
    <Dialog maxWidth={'sm'} open={open} onClose={handleClose}>
      <DialogTitle fontWeight={600}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            spacing={1}
          >
            <AddFactoryIcon height={32} width={32} />
            <Typography variant="subtitle1" fontWeight="bold">
              Add new factory or mark.
            </Typography>
          </Stack>
          <IconButton
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          Factory will be registered under <b>{organizationName}</b>
        </DialogContentText>

        <CreateFactoryForm
          organizationId={organizationId}
          onCreateFactorySuccess={handleClose}
          onCancel={handleClose}
        />
      </DialogContent>
    </Dialog>
  )
}
