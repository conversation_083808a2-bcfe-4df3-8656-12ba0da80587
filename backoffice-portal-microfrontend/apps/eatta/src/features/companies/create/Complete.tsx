import { But<PERSON>, Stack } from '@mui/material'
import { useRouter } from 'next/navigation'
import { useAppDispatch, useAppSelector } from '@/store'
import { resetOnboardingState, setSelectedCompany } from '@/store/reducers'
import AddIcon from '@mui/icons-material/Add'
import ConfirmationCard from '../../../components/ConfirmationCard'
import { Company } from '@/store/interfaces'
import { FC, useMemo } from 'react'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'

interface CompleteProps {
  isFactory: boolean
}
const Complete: FC<CompleteProps> = ({ isFactory }) => {
  const router = useRouter()
  const dispatch = useAppDispatch()

  const { onboardingCompany, onboardedCompany } = useAppSelector(
    (state) => state.companies
  )
  const company: Company | null = useMemo(
    () =>
      onboardingCompany?.entity ? JSON.parse(onboardingCompany.entity) : null,
    [onboardingCompany]
  )

  const handleRegistrationComplete = () => {
    router.replace('/backoffice/companies')
    dispatch(resetOnboardingState())
  }

  const handleCreateUser = () => {
    dispatch(setSelectedCompany(company))
    router.replace(`/backoffice/companies/${onboardedCompany?.id}/users/create`)
  }

  return (
    <ConfirmationCard
      title="Company account registered successfully!"
      description={`An account for ${company?.name} has been created successfully.`}
    >
      <Stack
        spacing={3}
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{ width: '100%' }}
      >
        <Button
          fullWidth
          variant="outlined"
          type="button"
          onClick={() => handleRegistrationComplete()}
        >
          Go to Companies
        </Button>
        {!isFactory && (
          <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
            <Button
              fullWidth
              variant="contained"
              type="button"
              onClick={handleCreateUser}
              startIcon={<AddIcon />}
            >
              Add User
            </Button>
          </AccessControlWrapper>
        )}
      </Stack>
    </ConfirmationCard>
  )
}

export default Complete
