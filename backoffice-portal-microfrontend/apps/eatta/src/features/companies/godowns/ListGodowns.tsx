import React, { useEffect, useState } from 'react'
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useAppDispatch, useAppSelector } from '@/store'
import { EditGodownDialog } from './EditGodownDialog'
import { EditFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { WarehouseResponse } from '@/store/interfaces/warehouse'
import { getGodowns } from '@/store/actions'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { setSelectedCompany, setSelectedWarehouse } from '@/store/reducers'
import { Order } from '@/store/interfaces'

const ListGodowns = () => {
  const dispatch = useAppDispatch()
  const isBackOffice = checkIfBackOffice()
  const [showEditWarehouse, setShowEditWarehouse] = useState(false)
  const { warehousesResponse, selectedCompany, selectedWarehouse, isLoading } =
    useAppSelector((state) => state.companies)
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }
  /*************************end pagination handlers**************************/

  const fetchGodowns = async () => {
    getGodowns(dispatch, {
      page: paginationOptions.page,
      size: 10,
      warehouseId: selectedCompany?.id,
    })
  }

  useEffect(() => {
    if (selectedCompany) {
      fetchGodowns()
    }
  }, [selectedCompany, paginationOptions.page])

  const handleEditWarehouse = (warehouse: WarehouseResponse) => {
    dispatch(setSelectedWarehouse(warehouse))
    if (selectedCompany) {
      dispatch(setSelectedCompany(selectedCompany))
    }
    setShowEditWarehouse(true)
  }

  const handleCloseEditWarehouse = (refresh?: boolean) => {
    setShowEditWarehouse(false)
    if (refresh) {
      fetchGodowns()
    }
  }

  return isLoading ? (
    <CustomSkeleton
      animation="pulse"
      variant="rectangular"
      width={'100%'}
      height={'60vh'}
    />
  ) : warehousesResponse.data.length === 0 ? (
    <EmptyPage
      title="No records found"
      message="No godowns found. Please create a new warehouse to get started."
      bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
    />
  ) : (
    <>
      <Typography
        sx={{
          width: '100%',
          textAlign: 'center',
          padding: '1rem',
        }}
      >
        {`Showing ${warehousesResponse?.totalElements} godowns`}
      </Typography>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="warehouses table"
          size="small"
        >
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={[
              {
                id: 'godownCode',
                label: 'Warehouse Code',
                alignRight: false,
              },
              {
                id: 'name',
                label: 'Name/Location',
                alignRight: false,
              },
              {
                id: 'icon',
                label: '',
                alignRight: false,
              },
            ]}
            rowCount={warehousesResponse.data.length}
            numSelected={0}
          />
          <TableBody>
            {warehousesResponse.data.map((row: WarehouseResponse) => {
              return (
                <TableRow hover tabIndex={-1} key={row.warehouseId}>
                  <TableCell component="th" scope="row">
                    {row.godownCode}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">{row.name}</Typography>
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditWarehouse(row)}>
                      <EditFactoryIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                sx={{ paddingInline: 0 }}
                align="center"
                height={40}
                colSpan={3}
              >
                {warehousesResponse.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: warehousesResponse.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
      <EditGodownDialog
        open={showEditWarehouse}
        handleClose={handleCloseEditWarehouse}
        warehouse={selectedWarehouse as WarehouseResponse}
      />
    </>
  )
}

export default ListGodowns
