/**
 * <AUTHOR> on 05/03/2025
 */
import { Stack, Typography } from '@mui/material'
import React from 'react'
import { Insights } from '@mui/icons-material'

type EmptyInsightsProps = {
  title?: string
  message?: string
}
export const EmptyInsights = ({
  title = 'No Insights found',
  message = 'Insights will appear here',
}: EmptyInsightsProps) => {
  return (
    <Stack
      spacing={3}
      sx={{ height: '10.9375rem' }}
      justifyContent="center"
      alignItems="center"
    >
      <Insights />
      <Stack spacing={1} justifyContent="center" alignItems="center">
        <Typography variant="subtitle2">{title}</Typography>
        <Typography variant="caption">{message}</Typography>
      </Stack>
    </Stack>
  )
}
