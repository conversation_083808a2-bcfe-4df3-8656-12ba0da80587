import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Typography } from '@mui/material'
import Grid from '@mui/material/Grid2'
import { StatusChip } from '@dtbx/ui/components/Chip'
import {
  Insight,
  InsightResponse,
  InsightsSummary,
  Stat,
} from '@/store/interfaces/insight'
import { useAppSelector } from '@/store'
import { InsightSkeleton } from '@/features/insights/InsightSkeleton'
import { EmptyInsights } from '@/features/insights/EmptyInsights'

/**
 * <AUTHOR> on 17/02/2025
 */

export const SummaryCard = ({ insight }: { insight: Insight }) => {
  const { name, value, percentage, link, linkText } = insight

  return (
    <Stack
      sx={{
        borderRadius: '0.375rem',
        backgroundColor: '#FFFFFF',
        border: '1px solid  #E4E7EC',
        boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
      }}
    >
      <Stack spacing={1} paddingBlock={3} paddingInline={2}>
        <Typography variant="body2">{name}</Typography>

        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="h5" fontWeight="600">
            {value}
          </Typography>
          {percentage !== undefined && (
            <StatusChip
              status={name.includes('Unsold') ? 'error' : 'success'}
              sx={{
                fontSize: '0.8rem',
                height: '1.5rem',
                width: '3.5rem',
                padding: '0',
              }}
              label={`${percentage}%`}
            />
          )}
        </Stack>
      </Stack>

      <Divider />

      <Stack alignItems="end" justifyContent="center" padding={2}>
        <Link
          href={link}
          sx={{
            padding: '0',
            textDecoration: 'none',
            fontSize: '0.8rem',
            fontWeight: 'bold',
          }}
          color="primary"
        >
          {' '}
          {linkText}
        </Link>
      </Stack>
    </Stack>
  )
}

const transformSummaryData = (data: Stat): InsightsSummary => {
  return {
    weight: [
      {
        name: 'All Tea (Kgs)',
        value: data.totalWeight.toLocaleString(),
        linkText: 'View Sales',
        link: '/_sales',
      },
      {
        name: 'Tea Sold (Kgs)',
        value: data.weightSold.toLocaleString(),
        percentage: (data.weightSold / data.totalWeight) * 100,
        linkText: 'View Report',
        link: '/_sales',
      },
      {
        name: 'Unsold (Kgs)',
        value: data.weightUnsold.toLocaleString(),
        percentage: (data.weightUnsold / data.totalWeight) * 100,
        linkText: 'View Report',
        link: '/_sales',
      },
    ],
    value: [
      {
        name: 'Expected Income (USD)',
        value: data.totalValue.toLocaleString(),
        linkText: 'View Sales',
        link: '/_sales',
      },
      {
        name: 'Total Value Sold (USD)',
        value: data.valueSold.toLocaleString(),
        percentage: (data.valueSold / data.totalValue) * 100,
        linkText: 'View Report',
        link: '/_sales',
      },
      {
        name: 'Total Value Unsold (USD)',
        value: data.valueUnsold.toLocaleString(),
        percentage: (data.valueUnsold / data.totalValue) * 100,
        linkText: 'View Report',
        link: '/_sales',
      },
    ],
  }
}

export const InsightSummaries = () => {
  const { insights, loadingInsights } = useAppSelector(
    (state) => state.insights
  )
  const summaryData = insights?.stats
    ? transformSummaryData(insights.stats)
    : null

  const renderSection = (title: string, data: Insight[]) => (
    <>
      <Typography>{title}</Typography>
      {loadingInsights ? (
        <InsightSkeleton />
      ) : !insights ? (
        <EmptyInsights />
      ) : (
        <Grid container spacing={2}>
          {data.map((insight) => (
            <Grid key={insight.name} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
              <SummaryCard insight={insight} />
            </Grid>
          ))}
        </Grid>
      )}
    </>
  )
  return (
    <Stack spacing={2}>
      {renderSection('WEIGHT', summaryData?.weight ?? [])}
      {renderSection('VALUE (USD)', summaryData?.value ?? [])}
    </Stack>
  )
}
