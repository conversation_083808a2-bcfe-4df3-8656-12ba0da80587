/**
 * <AUTHOR> on 05/03/2025
 */
import { Divider, Skeleton, Stack } from '@mui/material'
import Grid from '@mui/material/Grid2'

export const InsightCardSkeleton = () => {
  return (
    <Stack
      sx={{
        borderRadius: '0.375rem',
        backgroundColor: '#FFFFFF',
        border: '1px solid  #E4E7EC',
        boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
      }}
    >
      <Stack spacing={1} paddingBlock={3} paddingInline={2}>
        <Skeleton sx={{ height: '1.5rem', width: '5rem' }}></Skeleton>

        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Skeleton sx={{ height: '2.25rem', width: '8rem' }}></Skeleton>

          <Skeleton sx={{ height: '2.25rem', width: '3rem' }}></Skeleton>
        </Stack>
      </Stack>

      <Divider />

      <Stack alignItems="end" justifyContent="center" padding={2}>
        <Skeleton sx={{ height: '1.5rem', width: '5rem' }}></Skeleton>
      </Stack>
    </Stack>
  )
}

export const InsightSkeleton = () => {
  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
        <InsightCardSkeleton />
      </Grid>
      <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
        <InsightCardSkeleton />
      </Grid>
      <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
        <InsightCardSkeleton />
      </Grid>
    </Grid>
  )
}

export const TopMetricSkeleton = () => {
  return (
    <Stack
      sx={{ height: '2.5rem' }}
      direction="row"
      alignItems="center"
      spacing={1}
    >
      <Skeleton sx={{ height: '1.5rem', width: '7rem' }}></Skeleton>
      <Skeleton sx={{ height: '1.5rem', width: '1.5rem' }}></Skeleton>

      <Skeleton sx={{ height: '0.5rem', width: '100%' }}></Skeleton>

      <Skeleton sx={{ height: '1.5rem', width: '5rem' }}></Skeleton>
    </Stack>
  )
}
export const TopInsightSkeleton = () => {
  return (
    <Grid container>
      <Grid size={{ xs: 12, sm: 12, md: 12, lg: 9 }}>
        <Stack
          paddingBlock={3}
          paddingInline={2}
          spacing={2}
          sx={{
            backgroundColor: '#FFFFFF',
            borderRadius: '0.375rem',
          }}
        >
          <TopMetricSkeleton />
          <TopMetricSkeleton />
          <TopMetricSkeleton />
          <TopMetricSkeleton />
          <TopMetricSkeleton />
          <TopMetricSkeleton />
        </Stack>
      </Grid>
    </Grid>
  )
}
