/**
 * <AUTHOR> on 17/02/2025
 */
'use client'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { InsightsFilters } from '@/features/insights/InsightsFilters'
import { ArrowOutward } from '@mui/icons-material'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { InsightSummaries } from '@/features/insights/InsightSummaries'
import { TopInsights } from '@/features/insights/TopInsights'
import { useAppDispatch, useAppSelector } from '@/store'
import { getInsights, getTopMetrics } from '@/store/actions/insights'
import { InsightFilters } from '@/store/interfaces/insight'
import { resetInsightState } from '@/store/reducers/insightsReducer'

export const InsightsPage = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { insights } = useAppSelector((state) => state.insights)

  useEffect(() => {
    fetchData({})
  }, [])

  const fetchData = (filters: InsightFilters) => {
    dispatch(resetInsightState())
    getInsights(dispatch, filters)
    getTopMetrics(dispatch, filters)
  }

  const handleSearchFilters = (filters: InsightFilters) => {
    fetchData(filters)
  }

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack sx={{ backgroundColor: '#FFFFFF' }}>
        <Stack paddingInline={3} paddingBlock={2} alignItems="flex-start">
          <Typography variant="h5">Insights</Typography>
        </Stack>
        <Divider />

        <InsightsFilters onFilterChange={handleSearchFilters} />
      </Stack>

      <Divider />

      <Stack
        paddingInline={3}
        paddingBlock={2}
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <Typography fontWeight="600">
          Showing data from {insights?.stats.lotCount} lots {insights?.saleCode}
        </Typography>

        <Button
          variant="contained"
          color="primary"
          size="small"
          endIcon={<ArrowOutward />}
          onClick={() => router.push('/sales')}
        >
          View All Sales
        </Button>
      </Stack>

      <Divider />
      <Stack
        sx={{ overflowY: 'scroll' }}
        spacing={4}
        paddingInline={3}
        paddingBlock={2}
      >
        <InsightSummaries />

        <TopInsights />
      </Stack>
    </Stack>
  )
}
