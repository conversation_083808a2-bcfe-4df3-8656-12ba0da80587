/**
 * <AUTHOR> on 01/07/2025
 */

import React, { FC } from 'react'
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { AutorenewOutlined } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  AccessControlWrapper,
  formatCurrency,
  formatDate,
} from '@dtbx/store/utils'
import { TRANSACTION_STATUS_MAP } from '@/utils/statusChips'
import { retryTransaction } from '@/store/actions/transactions'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { FileDownload } from '@/components/FileDownload'

interface SettlementDetailsModalProps {
  open: boolean
  onClose: (tabIndex?: number) => void
}

export const TransactionDetailsModal: FC<SettlementDetailsModalProps> = ({
  open,
  onClose,
}) => {
  const dispatch = useAppDispatch()
  const { selectedTransaction, isRetrying } = useAppSelector(
    (state) => state.transactions
  )

  if (!selectedTransaction) {
    return null
  }

  const {
    channel,
    coreReference,
    amount,
    sourceAccount,
    destinationAccount,
    status,
    breakdownFileLink,
    message,
  } = selectedTransaction

  const handleTransactionRetry = async (transactionId: string) => {
    await retryTransaction(dispatch, transactionId, handleRetrySuccess)
  }

  const handleRetrySuccess = () => {
    onClose(0) // Close the modal and reset to the first tab
  }

  return (
    <Dialog fullWidth maxWidth="sm" open={open}>
      <DialogTitle fontWeight={600}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="h5" fontWeight="bold">
            {channel ?? 'Transaction Details'}
          </Typography>
          {coreReference && (
            <StatusChip
              label={`Corebanking Ref: ${coreReference}`}
              status="default"
              sx={{
                borderRadius: '1rem',
                fontSize: '0.75rem',
              }}
            />
          )}

          <IconButton
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
            onClick={() => onClose()}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Stack spacing={2} useFlexGap>
          <Typography variant="h5" fontWeight="bold">
            {formatCurrency(amount, 'USD')}
          </Typography>

          <Stack>
            <Typography>From</Typography>
            <Stack
              padding={2}
              spacing={1}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '6px',
              }}
            >
              <Typography fontWeight="600">
                {sourceAccount.accountHolder}
              </Typography>
              <Stack direction="row" spacing={2} alignItems="center">
                <Typography>{sourceAccount.bank} </Typography>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="4"
                  height="4"
                  viewBox="0 0 4 4"
                  fill="none"
                >
                  <circle cx="2" cy="2" r="2" fill="#101828" />
                </svg>
                <Typography>{sourceAccount.accountNumber ?? 'N/A'} </Typography>
              </Stack>
            </Stack>
          </Stack>

          <Stack>
            <Typography>To</Typography>
            <Stack
              padding={2}
              spacing={1}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '6px',
              }}
            >
              <Typography fontWeight="600">
                {destinationAccount.accountHolder}
              </Typography>
              <Stack direction="row" spacing={2} alignItems="center">
                <Typography>{destinationAccount.bank}</Typography>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="4"
                  height="4"
                  viewBox="0 0 4 4"
                  fill="none"
                >
                  <circle cx="2" cy="2" r="2" fill="#101828" />
                </svg>
                <Typography>{destinationAccount.accountNumber} </Typography>
              </Stack>
            </Stack>
          </Stack>

          {breakdownFileLink && (
            <Stack>
              <Typography>Supporting document</Typography>
              <Stack
                padding={2}
                spacing={2}
                direction="row"
                alignItems="center"
                flexWrap="wrap"
                width="100%"
                sx={{
                  border: '1px solid #D0D5DD',
                  borderRadius: '6px',
                }}
              >
                <FileDownload
                  fileUrl={breakdownFileLink}
                  fileName="Transaction Breakdown Document"
                />
              </Stack>
            </Stack>
          )}

          <Stack>
            <Typography>Status</Typography>
            <Stack
              padding={2}
              spacing={2}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '6px',
              }}
            >
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Stack>
                  <StatusChip
                    label={selectedTransaction.status ?? ''}
                    status={TRANSACTION_STATUS_MAP[status]}
                    sx={{
                      borderRadius: '1rem',
                      fontSize: '0.75rem',
                      width: 'fit-content',
                    }}
                  />

                  <Typography>
                    Date:{' '}
                    {formatDate(selectedTransaction?.dateCreated ?? '')}{' '}
                  </Typography>
                </Stack>

                {status === 'FAILED' && (
                  <AccessControlWrapper
                    rights={ACCESS_CONTROLS.RETRY_TRANSACTION}
                  >
                    <Button
                      disabled={isRetrying}
                      variant="outlined"
                      sx={{
                        px: 2,
                        borderRadius: '0.5rem',
                        textTransform: 'none',
                      }}
                      onClick={() =>
                        handleTransactionRetry(selectedTransaction.id)
                      }
                      endIcon={
                        isRetrying ? (
                          <CircularProgress size={20} thickness={3.0} />
                        ) : (
                          <AutorenewOutlined />
                        )
                      }
                    >
                      Retry
                    </Button>
                  </AccessControlWrapper>
                )}
              </Stack>
              {message && status === 'FAILED' && (
                <Typography color="error">
                  Failure Message: {message}
                </Typography>
              )}
            </Stack>
          </Stack>
        </Stack>
      </DialogContent>
    </Dialog>
  )
}
