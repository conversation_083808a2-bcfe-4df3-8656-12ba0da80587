'use client'
import { Stack } from '@mui/material'
import React, { useEffect, useState, useCallback, useMemo } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { OrganizationStatus, UserFilters } from '@/store/interfaces'
import { ApprovalRequestFilters } from '@/store/interfaces/makerChecker'
import { SearchByValueConfig } from '@/components/PageFilters'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import MembersTable from '@/features/members/MembersTable'
import { useAppDispatch, useAppSelector } from '@/store'
import MemberApprovalsTable from '@/features/members/MemberApprovalsTable'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import MemberHeader from './MemberHeader'
import { getMembers, getUsersApprovalRequests } from '@/store/actions/companies'
import { EATTA_MODULES } from '@/utils/constants'
import { setMemberResponse } from '@/store/reducers'

const TABS: TabType<OrganizationStatus>[] = [
  {
    title: 'Members',
    canSelect: true,
    status: 'ACTIVE',
  },
  {
    title: 'Registration Ongoing',
    canSelect: true,
    status: 'STAGING',
  },
  {
    title: 'Awaiting Approval',
    canSelect: true,
    status: 'PENDING',
  },
]

type CombinedFilters = UserFilters & Partial<ApprovalRequestFilters>

const initialFilters: CombinedFilters = {
  page: 1,
  size: 10,
  name: '',
  email: '',
  phoneNumber: '',
  status: '',
  companyCode: undefined,
  companyType: undefined,
}

const membersSearchByValues: SearchByValueConfig<UserFilters>[] = [
  {
    filterLabel: 'Name',
    filterKey: 'name',
    type: 'string',
  },
  {
    filterLabel: 'Email',
    filterKey: 'email',
    type: 'string',
  },
  {
    filterLabel: 'Phone Number',
    filterKey: 'phoneNumber',
    type: 'string',
  },
  {
    filterLabel: 'Status',
    filterKey: 'status',
    type: 'string',
  },
]

const approvalsSearchByValues: SearchByValueConfig<ApprovalRequestFilters>[] = [
  {
    filterLabel: 'Maker Firstname',
    filterKey: 'makerFirstName',
    type: 'string',
  },
  {
    filterLabel: 'Maker Lastname',
    filterKey: 'makerLastName',
    type: 'string',
  },
]

const ManageMembersPage = () => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<OrganizationStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const [filters, setFilters] = useState<CombinedFilters>(initialFilters)

  const isMembersTabSelected = useMemo(() => {
    return tabs[selectedTab].title === 'Members'
  }, [selectedTab, tabs])

  const searchByValues = useMemo(() => {
    return isMembersTabSelected
      ? membersSearchByValues
      : approvalsSearchByValues
  }, [isMembersTabSelected])

  const initialSearchBy = useMemo(() => {
    return isMembersTabSelected ? 'email' : 'makerFirstName'
  }, [isMembersTabSelected])

  const { isLoading } = useAppSelector((state) => state.companies)

  const handleTabSelected = (index: number) => {
    setSelectedTab(index)
  }

  const handleChange = useCallback(
    (
      field: keyof UserFilters | keyof ApprovalRequestFilters,
      value: string
    ) => {
      setFilters((prevFilters) => {
        const searchFields = [
          'name',
          'email',
          'phoneNumber',
          'status',
          'makerFirstName',
          'makerLastName',
        ]
        const clearedSearchFields = searchFields.reduce((acc, searchField) => {
          if (searchField !== field) {
            acc[searchField] = ''
          }
          return acc
        }, {} as any)
        return {
          ...prevFilters,
          ...clearedSearchFields,
          [field]: value,
          page: 1,
        }
      })
    },
    []
  )

  const handleCompanyCodeChange = useCallback(
    (companyCode: string | null, companyType?: string | null) => {
      setFilters((prevFilters) => ({
        ...prevFilters,
        companyCode: companyCode || undefined,
        companyType: companyType || undefined,
        page: 1,
      }))
    },
    []
  )

  const handlePaginationChange = (page: number, size: number) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      page,
      size,
    }))
  }

  const handleCountChange = (
    count: number,
    tab: TabType<OrganizationStatus>
  ) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab, pathname, searchParams, replace])

  useEffect(() => {
    if (isMembersTabSelected) {
      const hasSearchFilters =
        (filters.name && filters.name.trim()) ||
        (filters.email && filters.email.trim()) ||
        (filters.phoneNumber && filters.phoneNumber.trim()) ||
        (filters.status && filters.status.trim())
      const hasCompanyFilters = filters.companyCode && filters.companyType
      const isInitialLoad = !hasSearchFilters && !hasCompanyFilters

      if (hasSearchFilters || hasCompanyFilters || isInitialLoad) {
        const memberFilters: UserFilters = {
          page: filters.page,
          size: filters.size,
          name: filters.name,
          email: filters.email,
          phoneNumber: filters.phoneNumber,
          status: filters.status,
          companyCode: filters.companyCode,
          companyType: filters.companyType,
        }
        getMembers(dispatch, memberFilters)
      }
    }
  }, [
    dispatch,
    isMembersTabSelected,
    filters.page,
    filters.size,
    filters.name,
    filters.email,
    filters.phoneNumber,
    filters.status,
    filters.companyCode,
    filters.companyType,
  ])

  useEffect(() => {
    if (!isMembersTabSelected) {
      const approvalFilters: ApprovalRequestFilters = {
        channel: 'EATTA',
        module: EATTA_MODULES.users,
        status: tabs[selectedTab].status,
        page: filters.page,
        size: filters.size,
        makerFirstName: filters.makerFirstName || undefined,
        makerLastName: filters.makerLastName || undefined,
      }
      getUsersApprovalRequests(dispatch, approvalFilters)
    }
  }, [
    dispatch,
    isMembersTabSelected,
    selectedTab,
    filters.page,
    filters.size,
    filters.makerFirstName,
    filters.makerLastName,
  ])

  useEffect(() => {
    if (isMembersTabSelected) {
      const hasCompanyTypeButNoCompany =
        filters.companyType && !filters.companyCode
      if (hasCompanyTypeButNoCompany) {
        const emptyResponse = {
          data: [],
          size: 0,
          page: 0,
          totalNumberOfPages: 0,
          totalElements: 0,
        }
        dispatch(setMemberResponse(emptyResponse))
      }
    }
  }, [dispatch, isMembersTabSelected, filters.companyType, filters.companyCode])

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
        }}
      >
        <MemberHeader
          filters={filters}
          onSearchChange={handleChange}
          onCompanyCodeChange={handleCompanyCodeChange}
          searchByValues={searchByValues}
          initialSearchBy={initialSearchBy}
          showCompanyFilter={isMembersTabSelected}
        />

        <CustomTabs
          tabs={tabs}
          selectedTab={selectedTab}
          onTabSelected={handleTabSelected}
          isLoading={isLoading}
        />
      </Stack>

      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            {tab.title === 'Members' ? (
              <MembersTable
                filters={filters}
                onMembersCountChange={(count) => handleCountChange(count, tab)}
                onPaginationChange={handlePaginationChange}
              />
            ) : (
              <MemberApprovalsTable
                status={tab.status}
                filters={filters}
                onApprovalsCountChange={(count) =>
                  handleCountChange(count, tab)
                }
              />
            )}
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}

export default ManageMembersPage
