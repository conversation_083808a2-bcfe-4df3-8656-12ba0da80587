'use client'
import { Box, Divider, Stack, Typography } from '@mui/material'
import { BackButton } from '@dtbx/ui/components/Button'
import { useRouter } from 'next/navigation'
import React, { FC, useEffect, useMemo, useState } from 'react'
import { useAppSelector } from '@/store'
import Grid from '@mui/material/Grid2'
import Complete from './Complete'
import MemberProfile from './MemberProfile'
import MemberDetailsVerification from '././MemberDetailsVerification'
import { CustomStepper } from '@/components/CustomStepper'
import MemberIdentityVerification from './MemberIdentityVerification'
import { CompanyUser, OnboardingStep, UserSetUpStage } from '@/store/interfaces'
import { ApprovalRequestStatus } from '@/store/interfaces/makerChecker'
import { safeJsonParse } from '@/utils/objectUtil'
import { checkIsMaker, HasAccessToRights } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'

const STEPS: OnboardingStep[] = [
  {
    title: 'Identity Details',
    description: 'Names and identification details',
  },
  {
    title: 'User Profile Set Up',
    description: 'Contact information and roles.',
  },
  {
    title: 'Confirm and Submit',
    description:
      'Confirm user details and submit profile for checker verification.',
  },
  {
    title: 'Complete',
    description:
      'User is successfully onboarded and receives an activation email.',
  },
]

export enum CreateMemberSteps {
  VERIFICATION = 1,
  PROFILE,
  SUBMISSION,
  COMPLETE,
}

const CreateMemberPage = () => {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState<CreateMemberSteps>(1)

  const { selectedCompany, onboardingUser, onboardedUser } = useAppSelector(
    (state) => state.companies
  )
  const user: CompanyUser | null = useMemo(
    () => safeJsonParse(onboardingUser?.entity),
    [onboardingUser]
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_USER),
    []
  )

  const handleSetNextStep = (
    step: UserSetUpStage,
    status: ApprovalRequestStatus
  ) => {
    //Maker
    if (status === 'STAGING' && !onboardedUser) {
      switch (step) {
        case 'VERIFICATION':
          setCurrentStep(CreateMemberSteps.PROFILE)
          break
        case 'PROFILE':
          setCurrentStep(CreateMemberSteps.SUBMISSION)
          break
      }
      return
    }
    //Checker
    if (onboardedUser && !isSuper) {
      setCurrentStep(CreateMemberSteps.COMPLETE)
    }
  }

  useEffect(() => {
    const step = user?.stepName
    const status = onboardingUser?.status
    if (step && status) {
      handleSetNextStep(step, status)
    }
  }, [onboardingUser, user])

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
        height: '100%',
      }}
    >
      <Stack
        sx={{ backgroundColor: '#FFFFFF' }}
        direction="row"
        paddingInline={3}
        paddingBlock={2}
        spacing={2}
        alignItems="flex-start"
      >
        <BackButton onClick={() => router.back()} />
        <Stack>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Add User
          </Typography>
          <Typography variant="body1" sx={{ color: '#475467' }}>
            Once added, they will be able to log in as members of{' '}
            {selectedCompany?.name}
          </Typography>
        </Stack>
      </Stack>

      <Divider />

      <Box sx={{ flexGrow: 1, height: '100%', overflow: 'hidden' }}>
        <Grid
          container
          spacing={2}
          sx={{ height: '100%', overflowY: { sm: 'auto', md: 'hidden' } }}
        >
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 4, lg: 3 }}
            sx={{ height: { md: '100%' }, overflowY: 'auto' }}
          >
            <CustomStepper
              steps={STEPS}
              currentStep={currentStep}
              setStep={(step: CreateMemberSteps) => setCurrentStep(step)}
              isComplete={currentStep === CreateMemberSteps.COMPLETE}
            />
          </Grid>
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 8, lg: 9 }}
            sx={{ height: { md: '100%' }, overflowY: { md: 'auto' } }}
          >
            <Box width={{ sm: '100%', md: '80%', lg: '60%' }}>
              {(() => {
                switch (currentStep) {
                  case CreateMemberSteps.VERIFICATION:
                    return (
                      <MemberIdentityVerification setStep={setCurrentStep} />
                    )
                  case CreateMemberSteps.PROFILE:
                    return <MemberProfile setStep={setCurrentStep} />
                  case CreateMemberSteps.SUBMISSION:
                    return (
                      <MemberDetailsVerification
                        actionType={'SUBMISSION'}
                        setStep={setCurrentStep}
                      />
                    )
                  case CreateMemberSteps.COMPLETE:
                    return <Complete />
                }
              })()}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  )
}

export default CreateMemberPage
