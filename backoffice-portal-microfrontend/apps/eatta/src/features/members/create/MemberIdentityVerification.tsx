import React, { FC, useEffect, useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import {
  Company,
  CompanyUser,
  EAST_AFRICAN_COUNTRIES,
  UserIdentityRequest,
} from '@/store/interfaces'
import { createUser, getCompanies } from '@/store/actions'
import { useAppDispatch } from '@dtbx/store'
import { useAppSelector } from '@/store'
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight'
import { CreateMemberSteps } from '@/features/members/create/CreateMemberPage'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { ID_REGEX } from '@/utils/validators'
import { useDebounce } from '@dtbx/ui/hooks'

const validationSchema = Yup.object().shape({
  organizationCode: Yup.string().required('Company is required'),
  citizenship: Yup.string().required('Nationality is required'),
  nationalId: Yup.string()
    .matches(ID_REGEX, 'National ID must be up to 8 digits only')
    .required('ID Number is required')
    .min(8, 'National ID must be up to 8 digits only')
    .max(8, 'National ID must be up to 8 digits only'),
  firstName: Yup.string().required('First name is required'),
  middleName: Yup.string().optional().nullable(),
  lastName: Yup.string().required('Last name is required'),
})

type IdentityVerificationProps = {
  setStep: (step: CreateMemberSteps) => void
}

const MemberIdentityVerification: FC<IdentityVerificationProps> = ({
  setStep,
}) => {
  const dispatch = useAppDispatch()
  const {
    isCreatingUser,
    selectedCompany,
    onboardingUser,
    companiesResponse,
    isLoading,
  } = useAppSelector((state) => state.companies)
  const user: CompanyUser | null = useMemo(
    () => (onboardingUser?.entity ? JSON.parse(onboardingUser.entity) : null),
    [onboardingUser]
  )

  const [companySearchTerm, setCompanySearchTerm] = useState('')
  const debouncedCompanySearch = useDebounce(companySearchTerm, 500)
  const [selectedCompanyOption, setSelectedCompanyOption] =
    useState<Company | null>(null)
  const [isAutocompleteOpen, setIsAutocompleteOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_USER),
    []
  )
  const formik = useFormik<UserIdentityRequest>({
    initialValues: {
      organizationCode: selectedCompany?.code ?? '',
      citizenship: user?.citizenship ?? '',
      firstName: user?.firstName ?? '',
      middleName: user?.middleName ?? '',
      lastName: user?.lastName ?? '',
      nationalId: user?.nationalId ?? '',
      stepName: 'VERIFICATION',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      const payload: UserIdentityRequest = {
        ...values,
        approvalId: onboardingUser?.id,
      }
      await createUser(isSuper, dispatch, payload, () =>
        setStep(CreateMemberSteps.PROFILE)
      )
    },
  })

  // Fetch companies on autocomplete focus (first 10 companies)
  const handleAutocompleteFocus = () => {
    setIsAutocompleteOpen(true)
    setCurrentPage(1)
    // Only fetch if we don't have companies or if we need to reset to default list
    if (!companiesResponse.data?.length || companySearchTerm) {
      getCompanies(dispatch, { page: 1, size: 10, ascending: false })
      setCompanySearchTerm('')
    }
  }

  useEffect(() => {
    if (debouncedCompanySearch && isAutocompleteOpen) {
      setCurrentPage(1)
      getCompanies(dispatch, {
        page: 1,
        size: 10,
        name: debouncedCompanySearch,
        ascending: false,
      })
    } else if (isAutocompleteOpen && !debouncedCompanySearch) {
      // When search is cleared, show default first 10 companies
      setCurrentPage(1)
      getCompanies(dispatch, { page: 1, size: 10, ascending: false })
    }
  }, [debouncedCompanySearch, isAutocompleteOpen, dispatch])

  useEffect(() => {
    if (selectedCompany && !selectedCompanyOption) {
      setSelectedCompanyOption(selectedCompany)
      formik.setFieldValue('organizationCode', selectedCompany.code)
    }
  }, [selectedCompany, selectedCompanyOption])

  useEffect(() => {
    if (selectedCompanyOption) {
      formik.setFieldValue('organizationCode', selectedCompanyOption.code)
    }
  }, [selectedCompanyOption])

  return (
    <Stack spacing={2}>
      <Stack
        spacing={3}
        sx={{
          padding: 3,
          backgroundColor: '#FFFFFF',
          borderRadius: '0.5rem',
        }}
      >
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={2}>
              <Stack spacing={1}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Company{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="company-select"
                  options={companiesResponse.data || []}
                  value={selectedCompanyOption}
                  onChange={(_, value) => {
                    setSelectedCompanyOption(value)
                  }}
                  onInputChange={(_, value) => {
                    setCompanySearchTerm(value)
                  }}
                  onOpen={handleAutocompleteFocus}
                  onClose={() => setIsAutocompleteOpen(false)}
                  onBlur={() =>
                    formik.setFieldTouched('organizationCode', true)
                  }
                  getOptionLabel={(option) => option.name}
                  isOptionEqualToValue={(option, value) =>
                    option.id === value.id
                  }
                  loading={isLoading}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Search and select company"
                      error={
                        formik.touched.organizationCode &&
                        Boolean(formik.errors.organizationCode)
                      }
                      helperText={
                        formik.touched.organizationCode &&
                        formik.errors.organizationCode
                          ? formik.errors.organizationCode
                          : ''
                      }
                      slotProps={{
                        input: {
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {isLoading ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        },
                      }}
                    />
                  )}
                />
              </Stack>
              <Stack spacing={1}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Nationality{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="citizenship"
                  options={EAST_AFRICAN_COUNTRIES}
                  value={formik.values.citizenship}
                  onChange={(_, value) => {
                    formik.setFieldValue('citizenship', value)
                  }}
                  onBlur={() => formik.setFieldTouched('citizenship', true)}
                  getOptionLabel={(option) => option}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select Nationality"
                      error={
                        formik.touched.citizenship &&
                        Boolean(formik.errors.citizenship)
                      }
                      helperText={
                        formik.touched.citizenship && formik.errors.citizenship
                          ? formik.errors.citizenship
                          : ''
                      }
                    />
                  )}
                />
              </Stack>
              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  ID Number{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter ID Number"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('nationalId')}
                  error={
                    formik.touched.nationalId &&
                    Boolean(formik.errors.nationalId)
                  }
                  helperText={
                    formik.touched.nationalId && formik.errors.nationalId
                  }
                />
              </Stack>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  First Name{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter first name"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('firstName')}
                  error={
                    formik.touched.firstName && Boolean(formik.errors.firstName)
                  }
                  helperText={
                    formik.touched.firstName && formik.errors.firstName
                  }
                />
              </Stack>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Middle Name(s)
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter middle name(s)"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('middleName')}
                  error={
                    formik.touched.middleName &&
                    Boolean(formik.errors.middleName)
                  }
                  helperText={
                    formik.touched.middleName && formik.errors.middleName
                  }
                />
              </Stack>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Last Name{' '}
                  <Box component="span" sx={{ color: 'primary.main' }}>
                    *
                  </Box>
                </Typography>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  placeholder="Enter last name"
                  sx={{ marginBlock: '0' }}
                  margin="normal"
                  fullWidth
                  {...formik.getFieldProps('lastName')}
                  error={
                    formik.touched.lastName && Boolean(formik.errors.lastName)
                  }
                  helperText={formik.touched.lastName && formik.errors.lastName}
                />
              </Stack>

              <Box mt={2}>
                <AccessControlWrapper
                  rights={ACCESS_CONTROLS.CREATE_USER}
                  makerId={onboardingUser?.maker}
                  isMake={true}
                >
                  <Button
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!formik.isValid || isCreatingUser}
                    endIcon={
                      isCreatingUser ? (
                        <CircularProgress size={20} thickness={3.0} />
                      ) : (
                        <KeyboardArrowRightIcon />
                      )
                    }
                  >
                    Next
                  </Button>
                </AccessControlWrapper>
              </Box>
            </Stack>
          </Form>
        </FormikProvider>
      </Stack>
    </Stack>
  )
}

export default MemberIdentityVerification
