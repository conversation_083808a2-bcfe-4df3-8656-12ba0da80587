import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Footer, TableRow } from '@mui/material'
import { TableBody } from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { Table } from '@mui/material'
import { Paper } from '@mui/material'
import { TableContainer } from '@mui/material'
import { useState } from 'react'
import {
  BrokerCommissionStatements,
  CatalogueFilters,
  InvoiceEntryStatus,
  Order,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { INVOICE_ENTRY_STATUS_MAP } from '@/utils/statusChips'
import { INVOICE_ENTRY_STATUS_COLOR } from '@/utils/statusChips'
import TableSkeleton from '@/components/TableSkeleton'
import { formatCurrency } from '@dtbx/store/utils'

export interface CommissionStatementsTableProps {
  filters: CatalogueFilters
  onPageChange?: (page: number, size: number) => void
}
export const CommissionStatementsTable: React.FC<
  CommissionStatementsTableProps
> = ({ filters, onPageChange }) => {
  const {
    brokerCommissionStatementsResponse,
    isLoadingBrokerCommissionStatements,
  } = useAppSelector((state) => state.brokerCommissions)
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')
  const [paginationOptions, setPaginationOptions] = useState({
    page: filters.page,
    size: filters.size,
    totalPages: brokerCommissionStatementsResponse.totalNumberOfPages,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    if (onPageChange) {
      onPageChange(newOptions.page, newOptions.size)
    }
  }
  /*************************end pagination handlers**************************/

  return (
    <Stack sx={{ height: '100%' }}>
      {isLoadingBrokerCommissionStatements ? (
        <TableSkeleton rowCount={5} columnCount={4} />
      ) : brokerCommissionStatementsResponse.data.length === 0 ? (
        <EmptyPage
          title="No records found"
          message="No statement data available for the selected auction."
          bgUrl={'/combo.svg'}
        />
      ) : (
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            '& .MuiTableCell-root': {
              paddingInline: '1.5rem',
              paddingBlock: '0.5rem',
              textAlign: 'left',
            },
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="Commission Statement"
            size="small"
          >
            <CustomTableHeader
              order={order}
              orderBy={orderBy}
              headLabel={[
                { id: 'lotNo', label: 'Lot No.', alignRight: false },
                {
                  id: 'gardenInvoice',
                  label: 'Garden Invoice',
                  alignRight: false,
                },
                {
                  id: 'buyer',
                  label: 'Buyer',
                  alignRight: false,
                },
                {
                  id: 'totalWeight',
                  label: 'Net Weight (Kgs)',
                  alignRight: false,
                },
                {
                  id: 'pricePerKg',
                  label: 'Final Price (USD)',
                  alignRight: false,
                },
                {
                  id: 'pkgs',
                  label: 'Value (USD)',
                  alignRight: false,
                },
                {
                  id: 'brokerCommissionFromBuyer',
                  label: 'Buyer',
                  alignRight: false,
                },
                {
                  id: 'brokerCommissionFromProducer',
                  label: 'Producer',
                  alignRight: false,
                },
                {
                  id: 'totalCommissions',
                  label: 'Total Commission',
                  alignRight: false,
                },
                {
                  id: 'status',
                  label: 'Status',
                  alignRight: false,
                },
              ]}
              showCheckbox={false}
              rowCount={0}
              numSelected={0}
            />
            <TableBody>
              {brokerCommissionStatementsResponse.data.map(
                (row: BrokerCommissionStatements) => (
                  <TableRow hover={true} key={row.lotNo}>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {row.lotNo}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {row.gardenInvoice}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {row.buyer}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {row.totalWeight}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {formatCurrency(row.pricePerKg, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {formatCurrency(row.totalValue, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {formatCurrency(
                        row.brokerCommissionFromBuyer,
                        'USD',
                        'en-US'
                      )}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {formatCurrency(
                        row.brokerCommissionFromProducer,
                        'USD',
                        'en-US'
                      )}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {formatCurrency(row.totalCommissions, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      <StatusChip
                        label={INVOICE_ENTRY_STATUS_MAP(
                          row.invoiceEntryStatus as InvoiceEntryStatus,
                          row.lateDays
                        )}
                        status={
                          INVOICE_ENTRY_STATUS_COLOR[
                            row.invoiceEntryStatus as InvoiceEntryStatus
                          ]
                        }
                      />
                    </TableCell>
                  </TableRow>
                )
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  sx={{ paddingInline: 0 }}
                  align="center"
                  height={40}
                  colSpan={10}
                >
                  {brokerCommissionStatementsResponse.totalNumberOfPages >
                    0 && (
                    <CustomPagination
                      options={{
                        ...paginationOptions,
                        totalPages:
                          brokerCommissionStatementsResponse.totalNumberOfPages,
                      }}
                      handlePagination={handlePagination}
                    />
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      )}
    </Stack>
  )
}
