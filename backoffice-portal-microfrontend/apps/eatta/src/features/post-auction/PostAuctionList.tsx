/**
 * <AUTHOR> on 29/04/2025
 */
import { AuctionGroup } from '@/store/interfaces'
import React, { FC } from 'react'
import {
  AccordionDetails,
  AccordionSummary,
  Stack,
  Typography,
} from '@mui/material'
import { PostAuctionTable } from '@/features/post-auction/PostAuctionTable'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { CustomAccordion } from '@/components/CustomAccordion'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { useAppSelector } from '@dtbx/store'

interface ListGroupHeaderProps {
  lotsCount: number
  factory: string
  warehouse: string
  buyer: string
}

const ListGroupHeader: FC<ListGroupHeaderProps> = ({
  lotsCount,
  factory,
  warehouse,
  buyer,
}) => {
  const { decodedToken } = useAppSelector((state) => state.auth)
  const isProducer = decodedToken.clientType === 'Producer'
  return (
    <Stack>
      <Stack direction="row" spacing={2}>
        <Typography variant="subtitle2" fontWeight="600" color={'textPrimary'}>
          Buyer: {buyer}
        </Typography>
        <StatusChip
          label={`${lotsCount} Lots`}
          sx={{ width: '5rem', borderRadius: '1rem', fontSize: '0.75rem' }}
        />
      </Stack>

      <Stack direction="row" spacing={3}>
        <Typography>Mark: {factory}</Typography>
        {!isProducer && <Typography>Warehouse: {warehouse}</Typography>}
      </Stack>
    </Stack>
  )
}

interface PostAuctionListGroupProps {
  auctionGroup: AuctionGroup
}

export const PostAuctionListGroup: FC<PostAuctionListGroupProps> = ({
  auctionGroup,
}) => {
  const { buyer, warehouse, factories } = auctionGroup
  return (
    <Stack spacing={3} display="flex" useFlexGap>
      {factories.map((factory, factoryIndex) => (
        <CustomAccordion
          defaultExpanded
          key={`${factory.name}-${buyer}-${warehouse}-${factoryIndex}`}
        >
          <AccordionSummary
            aria-controls={`${factory.name} header`}
            id={`${factory.name}-header`}
            expandIcon={<ExpandMoreIcon />}
          >
            <ListGroupHeader
              factory={factory.name}
              warehouse={warehouse}
              buyer={buyer}
              lotsCount={factory.catalogues.length}
            />
          </AccordionSummary>

          <AccordionDetails>
            <PostAuctionTable catalogues={factory.catalogues} />
          </AccordionDetails>
        </CustomAccordion>
      ))}
    </Stack>
  )
}

interface PostAuctionListProps {
  postAuctionData: AuctionGroup[]
}

export const PostAuctionList: FC<PostAuctionListProps> = ({
  postAuctionData,
}) => {
  return (
    <>
      {postAuctionData.map((auctionGroup: AuctionGroup, groupIndex: number) => (
        <PostAuctionListGroup
          key={`${auctionGroup.producer}-${auctionGroup.buyer}-${groupIndex}`}
          auctionGroup={auctionGroup}
        />
      ))}
    </>
  )
}
