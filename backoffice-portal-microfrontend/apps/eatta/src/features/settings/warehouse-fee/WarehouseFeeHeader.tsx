/**
 * <AUTHOR> on 10/07/2025
 */
import { <PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material'
import React, { FC, useEffect, useMemo, useState } from 'react'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useDebounce } from '@dtbx/ui/hooks'
import { PageFilters } from '@/store/interfaces'
import { SearchByValueConfig } from '@/components/PageFilters'
import { WarehouseFeeFilters } from '@/store/interfaces/warehouse'
import { DECIMAL_NUMERIC_REGEX } from '@/utils/validators'
import ConfigureWarehouseFormDialog from './create/ConfigureWarehouseFormDialog'

export interface WarehouseFeeHeaderProps {
  onSearchChange: (key: keyof WarehouseFeeFilters, value: string) => void
}

type WarehouseFeeSearchByType = keyof Omit<
  WarehouseFeeFilters,
  keyof PageFilters
>

const searchByValues: SearchByValueConfig<WarehouseFeeFilters>[] = [
  { filterLabel: 'Warehouse', filterKey: 'warehouseName', type: 'string' },
  { filterLabel: 'Producer', filterKey: 'producerName', type: 'string' },
  { filterLabel: 'Created By', filterKey: 'createdBy', type: 'string' },
  {
    filterLabel: 'Handling Fees',
    filterKey: 'handlingCommission',
    type: 'numeric',
  },
]

export const WarehouseFeeHeader: FC<WarehouseFeeHeaderProps> = ({
  onSearchChange,
}) => {
  const [searchBy, setSearchBy] =
    useState<WarehouseFeeSearchByType>('warehouseName')
  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)
  const [openModal, setOpenModal] = useState(false)

  const handleSetUpWarehouseFee = () => {
    setOpenModal(true)
  }

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  const currentSearchConfig = useMemo(
    () => searchByValues.find((item) => item.filterKey === searchBy),
    [searchByValues, searchBy]
  )

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(validateSearchValue(value))
  }

  const validateSearchValue = (value: string): string => {
    if (currentSearchConfig?.type === 'numeric') {
      return DECIMAL_NUMERIC_REGEX.test(value) ? value : ''
    }
    return value
  }

  useEffect(() => {
    const value = validateSearchValue(debouncedSearchValue)
    if (!value) {
      setSearchValue('')
    }
    onSearchChange(searchBy, value)
  }, [debouncedSearchValue, searchBy])

  return (
    <Stack
      paddingInline={3}
      paddingBlock={2}
      spacing={2}
      sx={{ backgroundColor: '#FFFFFF' }}
    >
      <Stack>
        <Typography
          sx={{ fontSize: '20px', fontWeight: 600, color: '#000000' }}
        >
          Warehouse Fees Configuration
        </Typography>
      </Stack>

      <Stack
        spacing={1}
        flexWrap="wrap"
        useFlexGap
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValues.map(
            (item) => item.filterKey as string
          )}
          searchByLabel={searchByLabel}
          selectedSearchBy={searchBy}
          onHandleSearch={handleSearch}
          setSearchByValue={(value: string) =>
            setSearchBy(value as WarehouseFeeSearchByType)
          }
          searchPlaceHolder={`Search ${searchByLabel?.[searchBy] || searchBy || 'company'}`}
          prependSearchBy={true}
        />
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddOutlinedIcon />}
          sx={{ borderRadius: '0.5rem', marginTop: 0, textWrap: 'nowrap' }}
          onClick={handleSetUpWarehouseFee}
        >
          Set New Fee Config
        </Button>
        <ConfigureWarehouseFormDialog
          open={openModal}
          onClose={() => setOpenModal(false)}
        />
      </Stack>
    </Stack>
  )
}
