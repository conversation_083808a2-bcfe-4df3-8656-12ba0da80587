/**
 * <AUTHOR> on 10/07/2025
 */
import { Divider, Stack } from '@mui/material'
import React, { useState } from 'react'
import { WarehouseFeeHeader } from '@/features/settings/warehouse-fee/WarehouseFeeHeader'
import { WarehouseFeeFilters } from '@/store/interfaces/warehouse'
import { WarehouseFeeTable } from '@/features/settings/warehouse-fee/WarehouseFeeTable'

const initialFilters: WarehouseFeeFilters = {
  page: 1,
  size: 10,
}

export const WarehouseFee = () => {
  const [filters, setFilters] = useState<WarehouseFeeFilters>(initialFilters)

  const handleSearchChange = (
    field: keyof WarehouseFeeFilters,
    value: string
  ) => {
    setFilters({
      ...initialFilters,
      [field]: value,
    })
  }

  return (
    <Stack sx={{ height: '100%' }}>
      <WarehouseFeeHeader onSearchChange={handleSearchChange} />

      <Divider />

      <Stack sx={{ overflow: 'auto' }}>
        <WarehouseFeeTable filters={filters} />
      </Stack>
    </Stack>
  )
}
