/**
 * <AUTHOR> on 23/12/2024
 */
import React, { useState } from 'react'
import { CircularProgress, IconButton, MenuItem } from '@mui/material'
import { DotsVerticalIcon } from '@dtbx/ui/icons'
import { CustomMenu } from '@/components/CustomMenu'
import { BrokerCommissionResponse } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { activateCommissions } from '@/store/actions/brokerCommission'

export const CommissionMoreMenu = ({
  commission,
}: {
  commission: BrokerCommissionResponse
}) => {
  const dispatch = useAppDispatch()
  const { isActivatingCommission } = useAppSelector(
    (state) => state.brokerCommissions
  )

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleActivate = () => {
    activateCommissions(dispatch, commission.id, handleClose)
  }

  return (
    <>
      <IconButton onClick={handleClick}>
        <DotsVerticalIcon />
      </IconButton>

      <CustomMenu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{
          '& .MuiPaper-root': {
            '& .MuiMenuItem-root': {
              marginTop: 0,
            },
          },
        }}
      >
        {commission.status === 'Expired' && (
          <MenuItem
            disabled={isActivatingCommission}
            sx={{
              marginTop: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            onClick={handleActivate}
          >
            Activate
            {isActivatingCommission && (
              <CircularProgress color="primary" size={20} thickness={3.0} />
            )}
          </MenuItem>
        )}
      </CustomMenu>
    </>
  )
}
