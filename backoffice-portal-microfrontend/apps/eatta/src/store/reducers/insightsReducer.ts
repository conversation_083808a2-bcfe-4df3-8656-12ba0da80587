/**
 * <AUTHOR> on 05/03/2025
 */
import { InsightResponse, TopMetricResponse } from '@/store/interfaces/insight'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export interface InsightState {
  loadingInsights: boolean
  insights: InsightResponse | null
  loadingTopMetrics: boolean
  topMetrics: TopMetricResponse | null
}

const initialState: InsightState = {
  loadingInsights: false,
  insights: null,
  loadingTopMetrics: false,
  topMetrics: null,
}

const insightsSlice = createSlice({
  name: 'insights',
  initialState,
  reducers: {
    setInsights: (state, action: PayloadAction<InsightResponse>) => {
      state.insights = action.payload
    },
    setIsLoadingInsights: (state, action: PayloadAction<boolean>) => {
      state.loadingInsights = action.payload
    },
    setTopMetrics: (state, action: PayloadAction<TopMetricResponse>) => {
      state.topMetrics = action.payload
    },
    setIsLoadingTopMetrics: (state, action: PayloadAction<boolean>) => {
      state.loadingTopMetrics = action.payload
    },
    resetInsightState: (state) => {
      state.insights = null
      state.topMetrics = null
    },
  },
})
export const {
  setInsights,
  setIsLoadingInsights,
  setTopMetrics,
  setIsLoadingTopMetrics,
  resetInsightState,
} = insightsSlice.actions
export default insightsSlice.reducer
