import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  Bank,
  BankBranch,
  Company,
  CompanyKycCheckResponse,
  CompanyKycDetails,
  CompanyUser,
  EditCompanyUser,
  ProducerFactory,
  UserKycResults,
} from '../interfaces'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { FactoryResponse } from '../interfaces/factory'
import { WarehouseResponse } from '../interfaces/warehouse'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'

export interface CompaniesState {
  //companies
  selectedCompanies: Company[]
  selectedCompany: Company | null
  isLoading: boolean
  companiesResponse: PaginatedResponse<Company>
  onboardingCompany: IApprovalRequest | null
  onboardedCompany: Company | null
  banks: Bank[]
  bankBranches: BankBranch[]
  isCheckingKyc: boolean
  companyKycCheckResponse: CompanyKycCheckResponse | null
  companyKycDetails: CompanyKycDetails | null
  companyApprovalRequests: PaginatedResponse<IApprovalRequest>
  uploadDocuments: boolean
  // Company Users
  isCreatingUser: boolean
  isCreatingCompany: boolean
  isEditingUser: boolean
  selectedCompanyUser: EditCompanyUser | null
  companyUsersResponse: PaginatedResponse<CompanyUser>
  onboardingUser: IApprovalRequest | null
  onboardedUser: CompanyUser | null
  userApprovalRequests: PaginatedResponse<IApprovalRequest>
  memberResponse: PaginatedResponse<CompanyUser>

  //factories
  factoryResponse: PaginatedResponse<FactoryResponse>
  isCreatingFactory: boolean
  selectedFactory: FactoryResponse | null
  selectedFactories: FactoryResponse[]
  //producer factories
  producerFactoryResponse: PaginatedResponse<ProducerFactory>
  //warehouse
  isCreatingWarehouses: boolean
  isUpdatingWarehouse: boolean
  isUpdatingCompany: boolean
  selectedWarehouse: WarehouseResponse | null
  warehousesResponse: PaginatedResponse<WarehouseResponse>
  userVerificationResults: UserKycResults | null
}

const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialState: CompaniesState = {
  selectedCompanies: [],
  selectedCompany: null,
  isLoading: false,
  onboardingCompany: null,
  onboardedCompany: null,
  isCreatingUser: false,
  isCreatingCompany: false,
  isEditingUser: false,
  isUpdatingCompany: false,

  banks: [],
  bankBranches: [],
  isCheckingKyc: false,
  companiesResponse: initialPaginatedState,
  companyKycCheckResponse: null,
  companyKycDetails: null,
  companyApprovalRequests: initialPaginatedState,
  uploadDocuments: false,
  memberResponse: initialPaginatedState,

  // Users
  selectedCompanyUser: null,
  companyUsersResponse: initialPaginatedState,
  onboardingUser: null,
  onboardedUser: null,
  userApprovalRequests: initialPaginatedState,

  //factories
  factoryResponse: initialPaginatedState,
  isCreatingFactory: false,
  selectedFactory: null,
  selectedFactories: [],
  //producer factories
  producerFactoryResponse: initialPaginatedState,
  //warehouse
  isCreatingWarehouses: false,
  isUpdatingWarehouse: false,
  selectedWarehouse: null,
  warehousesResponse: initialPaginatedState,
  userVerificationResults: null,
}

const companiesSlice = createSlice({
  name: 'companies',
  initialState,
  reducers: {
    //companies
    setSelectedCompanies: (state, action: PayloadAction<Company[]>) => {
      state.selectedCompanies = action.payload
    },
    setSelectedCompany: (state, action: PayloadAction<Company | null>) => {
      state.selectedCompany = action.payload
    },
    setCompaniesResponse: (
      state,
      action: PayloadAction<PaginatedResponse<Company>>
    ) => {
      state.companiesResponse = action.payload
    },
    setIsLoadingCompanies: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },

    setOnboardingCompany: (state, action: PayloadAction<IApprovalRequest>) => {
      state.onboardingCompany = action.payload
    },
    setOnboardedCompany: (state, action: PayloadAction<Company>) => {
      state.onboardedCompany = action.payload
    },
    setIsCheckingKyc: (state, action: PayloadAction<boolean>) => {
      state.isCheckingKyc = action.payload
    },
    setCompanyKycCheckResponse: (
      state,
      action: PayloadAction<CompanyKycCheckResponse | null>
    ) => {
      state.companyKycCheckResponse = action.payload
    },
    setCompanyKycDetails: (
      state,
      action: PayloadAction<CompanyKycDetails | null>
    ) => {
      state.companyKycDetails = action.payload
    },
    setCompanyApprovalRequest: (
      state,
      action: PayloadAction<PaginatedResponse<IApprovalRequest>>
    ) => {
      state.companyApprovalRequests = action.payload
    },
    setBanks: (state, action: PayloadAction<Bank[]>) => {
      state.banks = action.payload
    },
    setBankBranches: (state, action: PayloadAction<BankBranch[]>) => {
      state.bankBranches = action.payload
    },
    //users
    setIsCreatingUser: (state, action: PayloadAction<boolean>) => {
      state.isCreatingUser = action.payload
    },
    setCompanyUsersResponse: (
      state,
      action: PayloadAction<PaginatedResponse<CompanyUser>>
    ) => {
      state.companyUsersResponse = action.payload
    },

    setMemberResponse: (
      state,
      action: PayloadAction<PaginatedResponse<CompanyUser>>
    ) => {
      state.memberResponse = action.payload
    },
    setIsEditingUser: (state, action: PayloadAction<boolean>) => {
      state.isEditingUser = action.payload
    },
    setOnboardingUser: (state, action: PayloadAction<IApprovalRequest>) => {
      state.onboardingUser = action.payload
    },
    setOnboardedUser: (state, action: PayloadAction<CompanyUser>) => {
      state.onboardedUser = action.payload
    },
    setUsersApprovalRequest: (
      state,
      action: PayloadAction<PaginatedResponse<IApprovalRequest>>
    ) => {
      state.userApprovalRequests = action.payload
    },
    setUserKycResults: (state, action: PayloadAction<UserKycResults>) => {
      state.userVerificationResults = action.payload
    },
    //factories
    setFactoryResponse: (
      state,
      action: PayloadAction<PaginatedResponse<FactoryResponse>>
    ) => {
      state.factoryResponse = action.payload
    },
    //producer factories
    setProducerFactoryResponse: (
      state,
      action: PayloadAction<PaginatedResponse<ProducerFactory>>
    ) => {
      state.producerFactoryResponse = action.payload
    },
    setIsCreatingFactory: (state, action: PayloadAction<boolean>) => {
      state.isCreatingFactory = action.payload
    },
    setIsCreatingCompany: (state, action: PayloadAction<boolean>) => {
      state.isCreatingCompany = action.payload
    },
    setSelectedFactory: (
      state,
      action: PayloadAction<FactoryResponse | null>
    ) => {
      state.selectedFactory = action.payload
    },

    setSelectedFactories: (state, action: PayloadAction<FactoryResponse[]>) => {
      state.selectedFactories = action.payload
    },
    removeSelectedFactories: (state, action: PayloadAction<string>) => {
      state.selectedFactories = state.selectedFactories.filter(
        (factory) => factory.id !== action.payload
      )
    },

    //godowns
    setWarehousesResponse: (
      state,
      action: PayloadAction<PaginatedResponse<WarehouseResponse>>
    ) => {
      state.warehousesResponse = action.payload
    },
    setIsCreatingWarehouses: (state, action: PayloadAction<boolean>) => {
      state.isCreatingWarehouses = action.payload
    },
    setIsUpdatingWarehouse: (state, action: PayloadAction<boolean>) => {
      state.isUpdatingWarehouse = action.payload
    },
    setIsUpdatingCompany: (state, action: PayloadAction<boolean>) => {
      state.isUpdatingCompany = action.payload
    },

    setSelectedWarehouse: (
      state,
      action: PayloadAction<WarehouseResponse | null>
    ) => {
      state.selectedWarehouse = action.payload
    },
    setUploadDocuments: (state, action: PayloadAction<boolean>) => {
      state.uploadDocuments = action.payload
    },
    resetOnboardingState(state) {
      state.onboardingCompany = null
      state.companyKycCheckResponse = null
      state.companyKycDetails = null
      state.onboardingUser = null
      state.onboardedCompany = null
      state.onboardedUser = null
    },
    resetManageUserState(state) {
      state.selectedCompany = null
      state.companyUsersResponse = initialPaginatedState
    },
  },
})

export const {
  //Companies lists
  setSelectedCompanies,
  setSelectedCompany,
  setCompaniesResponse,
  setIsLoadingCompanies,
  setOnboardingCompany,
  setOnboardedCompany,
  resetOnboardingState,
  setBanks,
  setBankBranches,
  setIsCheckingKyc,
  setCompanyKycCheckResponse,
  setCompanyKycDetails,
  setCompanyApprovalRequest,
  setUploadDocuments,
  //user
  setIsCreatingUser,
  setIsEditingUser,
  setIsUpdatingCompany,
  setCompanyUsersResponse,
  setOnboardingUser,
  setOnboardedUser,
  setUsersApprovalRequest,
  setMemberResponse,
  //factories
  setIsCreatingFactory,
  setFactoryResponse,
  setSelectedFactory,
  setSelectedFactories,
  removeSelectedFactories,
  //producer factories
  setProducerFactoryResponse,
  //godowns
  setIsCreatingWarehouses,
  setIsUpdatingWarehouse,
  setWarehousesResponse,
  setSelectedWarehouse,
  //reset
  resetManageUserState,
  setUserKycResults,
} = companiesSlice.actions
export default companiesSlice.reducer
