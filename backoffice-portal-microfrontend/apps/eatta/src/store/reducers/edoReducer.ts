import { PaginatedResponse } from '@dtbx/store/interfaces'
import { AuctionEntry } from '../interfaces/catalogues'
import { DeliveryOrderResponse } from '../interfaces/Edo'
import { IApprovalRequest } from '../interfaces/makerChecker'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { DeliveryOrderFilters } from '../interfaces/Edo'
import { getAuctionWeek } from '@dtbx/store/utils'

export interface EdoState {
  //Delivery Orders
  deliveryOrdersResponse: PaginatedResponse<DeliveryOrderResponse>
  selectedDeliveryOrderEntries: DeliveryOrderResponse | null
  selectedAuctionEntry: AuctionEntry | null
  isSigningDeliveryOrder: boolean
  isSubmittingDeliveryOrder: boolean
  selectedDeliveryOrderApprovalsRequests: IApprovalRequest | null
  isLoading: boolean
  isDownloadingTrdEdo: boolean
  filters: DeliveryOrderFilters
}

const initialPaginatedState: PaginatedResponse<DeliveryOrderResponse> = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialFilters: DeliveryOrderFilters = {
  page: 1,
  size: 10,
  year: new Date().getFullYear(),
  saleDate: getAuctionWeek().toString() || '',
}

const initialState: EdoState = {
  //Delivery Orders
  deliveryOrdersResponse: initialPaginatedState,
  selectedDeliveryOrderEntries: null,
  selectedAuctionEntry: null,
  isSigningDeliveryOrder: false,
  isSubmittingDeliveryOrder: false,
  selectedDeliveryOrderApprovalsRequests: null,
  isLoading: false,
  isDownloadingTrdEdo: false,
  filters: initialFilters,
}

const edoSlice = createSlice({
  name: 'edo',
  initialState: initialState,
  reducers: {
    //Delivery Orders
    setIsLoadingEdo: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setIsDownloadingTrdEdo: (state, action: PayloadAction<boolean>) => {
      state.isDownloadingTrdEdo = action.payload
    },
    setDeliveryOrdersResponse: (
      state,
      action: PayloadAction<PaginatedResponse<DeliveryOrderResponse>>
    ) => {
      state.deliveryOrdersResponse = action.payload
    },
    setSelectedDeliveryOrderEntries: (
      state,
      action: PayloadAction<DeliveryOrderResponse>
    ) => {
      state.selectedDeliveryOrderEntries = action.payload
    },
    setSelectedAuctionEntry: (
      state,
      action: PayloadAction<AuctionEntry | null>
    ) => {
      state.selectedAuctionEntry = action.payload
    },
    setIsSigningDeliveryOrder: (state, action: PayloadAction<boolean>) => {
      state.isSigningDeliveryOrder = action.payload
    },
    setIsSubmittingDeliveryOrder: (state, action: PayloadAction<boolean>) => {
      state.isSubmittingDeliveryOrder = action.payload
    },
    setSelectedDeliveryOrderApprovalsRequests: (
      state,
      action: PayloadAction<IApprovalRequest>
    ) => {
      state.selectedDeliveryOrderApprovalsRequests = action.payload
    },
    setDeliveryOrderFilters: (
      state,
      action: PayloadAction<DeliveryOrderFilters>
    ) => {
      state.filters = action.payload
    },
    resetDeliveryOrderFilters: (state) => {
      state.filters = initialFilters
    },
    resetDeliveryOrders: (state) => {
      state.deliveryOrdersResponse = initialPaginatedState
    },
    resetEdoStore: () => initialState,
  },
})

export const {
  //Delivery Orders
  setIsLoadingEdo,
  setIsDownloadingTrdEdo,
  setDeliveryOrdersResponse,
  setSelectedDeliveryOrderEntries,
  setSelectedAuctionEntry,
  setIsSigningDeliveryOrder,
  setIsSubmittingDeliveryOrder,
  setSelectedDeliveryOrderApprovalsRequests,
  setDeliveryOrderFilters,
  resetDeliveryOrderFilters,
  resetDeliveryOrders,
  resetEdoStore,
} = edoSlice.actions

export default edoSlice.reducer
