/**
 * <AUTHOR> on 10/07/2025
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { WarehouseFee } from '@/store/interfaces'

export interface WarehouseState {
  isLoading: boolean
  warehouseFeesResponse: PaginatedResponse<WarehouseFee>
  isConfiguringWarehouseFee: boolean
}

const initialPaginatedResponse: PaginatedResponse<WarehouseFee> = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialState: WarehouseState = {
  isLoading: false,
  warehouseFeesResponse: initialPaginatedResponse,
  isConfiguringWarehouseFee: false,
}

const warehouseSlice = createSlice({
  name: 'warehouse',
  initialState,
  reducers: {
    setIsLoadingWarehouse: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setIsConfiguringWarehouseFee: (state, action: PayloadAction<boolean>) => {
      state.isConfiguringWarehouseFee = action.payload
    },
    setWarehouseFeesResponse: (
      state,
      action: PayloadAction<PaginatedResponse<WarehouseFee>>
    ) => {
      state.warehouseFeesResponse = action.payload
    },
    resetWarehouseStore: () => initialState,
  },
})
export const {
  setIsLoadingWarehouse,
  setIsConfiguringWarehouseFee,
  setWarehouseFeesResponse,
  resetWarehouseStore,
} = warehouseSlice.actions
export default warehouseSlice.reducer
