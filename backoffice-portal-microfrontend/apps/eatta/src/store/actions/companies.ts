import { Dispatch } from '@reduxjs/toolkit'
import {
  Bank,
  BankBranch,
  Company,
  CompanyFilters,
  CompanyPaymentRequest,
  CompanyProfileRequest,
  CompanyUser,
  FactoryProfileRequest,
  MakeRequest,
  PartialCompanyData,
  PartialCompanyUser,
  ProducerFactory,
  ProducerFactoryFilters,
  UserFilters,
  UserIdentityRequest,
  UserProfileRequest,
} from '../interfaces'
import {
  setBankBranches,
  setBanks,
  setCompaniesResponse,
  setCompanyApprovalRequest,
  setCompanyUsersResponse,
  setMemberResponse,
  setFactoryResponse,
  setIsCreatingFactory,
  setIsCreatingUser,
  setIsCreatingWarehouses,
  setIsEditingUser,
  setIsLoadingCompanies,
  setIsUpdatingWarehouse,
  setOnboardedCompany,
  setOnboardedUser,
  setOnboardingCompany,
  setOnboardingUser,
  setProducerFactoryResponse,
  setSelectedCompanies,
  setSelectedCompany,
  setUploadDocuments,
  setUsersApprovalRequest,
  setWarehousesResponse,
  setIsUpdatingCompany,
} from '../reducers'
import { setNotification } from '@dtbx/store/reducers'
import { ApiResponse, PaginatedResponse } from '@dtbx/store/interfaces'
import { apiclient } from '@/utils/apiclient'
import { mapObjectToUrlParams, removeFalsyValues } from '@/utils/objectUtil'
import {
  EditFactoryDetails,
  FactoryCreationRequest,
  FactoryFilters,
  FactoryResponse,
} from '../interfaces/factory'
import {
  GodownCreationRequest,
  GodownsFilters,
  WarehouseResponse,
  WarehouseUpdateRequest,
} from '@/store/interfaces'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import {
  ApprovalRequestFilters,
  IApprovalRequest,
} from '@/store/interfaces/makerChecker'
import { AxiosRequestConfig } from 'axios'

export const setSelectedCompanyRecord = (
  dispatch: Dispatch,
  companies: Company[]
) => {
  dispatch(setSelectedCompanies(companies))
}

export const getCompanies = async (
  dispatch: Dispatch,
  params: CompanyFilters
) => {
  try {
    const isBackOffice = checkIfBackOffice()
    dispatch(setIsLoadingCompanies(true))

    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })

    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/onboarding/organizations?${searchParams}`
      : `/v1/eatta-service/onboarding/organizations?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<Company>>(url)
    dispatch(setCompaniesResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const getCompanyApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setCompanyApprovalRequest(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const createCompany = async (
  isSuper: boolean,
  dispatch: Dispatch,
  data:
    | CompanyProfileRequest
    | CompanyPaymentRequest
    | MakeRequest
    | FactoryProfileRequest,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const url = `/backoffice-bff/eatta-service/onboarding/register-organization/${isSuper ? '' : 'make'}`
    const res = await apiclient.post(url, removeFalsyValues(data))

    if (isSuper && data.stepName === 'SUBMISSION') {
      dispatch(setOnboardedCompany(res.data.data))
    } else {
      dispatch(setOnboardingCompany(isSuper ? res.data.data : res.data))
    }

    dispatch(
      setNotification({
        message: `Company details saved successfully  ${!isSuper && data.stepName === 'SUBMISSION' ? 'and is pending approval' : ''}`,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const updateCompany = async (
  isSuper: boolean,
  dispatch: Dispatch,
  organizationId: string,
  editCompanyData: PartialCompanyData,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsUpdatingCompany(true))

    const roleSegment = isSuper ? '' : '/make'
    const url = `/backoffice-bff/eatta-service/onboarding/update-organization${roleSegment}/${organizationId}`

    await apiclient.patch(url, { ...editCompanyData })

    dispatch(
      setNotification({
        message: `Company details saved successfully${!isSuper ? ' and is pending approval' : ''}`,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message || 'An error occurred during update'
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsUpdatingCompany(false))
  }
}

export const createUser = async (
  isSuper: boolean,
  dispatch: Dispatch,
  data: UserIdentityRequest | UserProfileRequest | MakeRequest,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsCreatingUser(true))

    const url = `/backoffice-bff/eatta-service/onboarding/register-user/${isSuper ? '' : 'make'}`
    const res = await apiclient.post(url, removeFalsyValues(data))
    dispatch(
      setNotification({
        message: `User details saved successfully  ${!isSuper && data.stepName === 'SUBMISSION' ? 'and is pending approval' : ''}`,
        type: 'success',
      })
    )

    if (isSuper && data.stepName === 'SUBMISSION') {
      dispatch(setOnboardedUser(res.data.data))
    } else {
      dispatch(setOnboardingUser(isSuper ? res.data.data : res.data))
    }

    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingUser(false))
  }
}
export const approveUser = async (
  dispatch: Dispatch,
  approvalId: string,
  comments: string,
  onSuccess: () => void,
  requestType?: string,
  authUserId?: string
) => {
  try {
    dispatch(setIsCreatingUser(true))

    const isUpdate = requestType === 'EATTA_UPDATE_USERS'

    const url = isUpdate
      ? `/backoffice-bff/eatta-service/users/update-user/${authUserId}/approve/${approvalId}`
      : `/backoffice-bff/eatta-service/onboarding/register-user/approve/${approvalId}`

    const res = await apiclient.put(url, { comments })
    dispatch(setOnboardedUser(res.data.data))
    dispatch(
      setNotification({
        message: 'User approved successfully',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingUser(false))
  }
}
export const approveCompany = async (
  dispatch: Dispatch,
  approvalId: string,
  comments: string,
  onSuccess: () => void,
  requestType?: string
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const isUpdate = requestType === 'EATTA_UPDATE_ORGANIZATION'
    const url = isUpdate
      ? `/backoffice-bff/eatta-service/onboarding/update-organization/approve/${approvalId}`
      : `/backoffice-bff/eatta-service/onboarding/register-organization/approve/${approvalId}`

    const res = await apiclient.put(url, { comments })

    dispatch(setOnboardedCompany(res.data.data))
    dispatch(
      setNotification({
        message: 'Company approved successfully',
        type: 'success',
      })
    )

    onSuccess()
  } catch (error: any) {
    dispatch(
      setNotification({
        message: error.message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}
//get organization by organization id
export const getOrganizationById = async (
  dispatch: Dispatch,
  organizationId: string
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const url = `/backoffice-bff/eatta-service/onboarding/${organizationId}`

    const res = await apiclient.get<ApiResponse<Company>>(url)

    dispatch(setSelectedCompany(res.data.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const getUsersApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setUsersApprovalRequest(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}
//company users
export const getUsersByOrganizationCode = async (
  dispatch: Dispatch,
  organizationCode: string,
  params: UserFilters
) => {
  const searchParams = mapObjectToUrlParams(params)

  try {
    dispatch(setIsLoadingCompanies(true))
    const url = `/backoffice-bff/eatta-service/users/${organizationCode}?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<CompanyUser>>(url)
    dispatch(setCompanyUsersResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const getMembers = async (dispatch: Dispatch, params: UserFilters) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const searchParams = mapObjectToUrlParams(params)
    const url = `/backoffice-bff/eatta-service/users/search/all?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<CompanyUser>>(url)
    dispatch(setMemberResponse(res.data))
  } catch (error) {
    const message = (error as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const updateUserDetails = async (
  isSuper: boolean,
  dispatch: Dispatch,
  authUserId: string,
  editUserData: PartialCompanyUser,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsEditingUser(true))
    const roleSegment = isSuper ? '' : '/make'
    const url = `/backoffice-bff/eatta-service/users/update-user/${authUserId}${roleSegment}`
    await apiclient.patch(url, { ...editUserData })
    dispatch( setNotification({
      message: `User updated successfully ${!isSuper ? ' and is pending approval' : ''}`,
      type: 'success',
    }))
    onSuccess()

  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsEditingUser(false))
  }
}

export const fetchBanks = async (dispatch: Dispatch) => {
  try {
    const [banksRes, branchRes] = await Promise.all([
      apiclient.get<Bank[]>('/backoffice-bff/eatta-service/banks'),
      apiclient.get<BankBranch[]>(
        '/backoffice-bff/eatta-service/banks/branches'
      ),
    ])
    dispatch(setBanks(banksRes.data))
    dispatch(setBankBranches(branchRes.data))
  } catch (e: any) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

//Factory
export const createFactory = async (
  dispatch: Dispatch,
  factory: FactoryCreationRequest,
  onCreateFactorySuccess: () => void
) => {
  try {
    const { parentOrganizationId, type, name, ...paymentDetails } = factory
    dispatch(setIsCreatingFactory(true))
    const res = await apiclient.post<ApiResponse<Company>>(
      '/backoffice-bff/eatta-service/onboarding/register-organization/factory',
      { parentOrganizationId, type, name, ...paymentDetails }
    )
    const organizationId = res.data.data.id
    const url = `/backoffice-bff/eatta-service/onboarding/organization/${organizationId}`
    await apiclient.post(url, paymentDetails)

    dispatch(
      setNotification({
        message: 'Factory added successfully',
        type: 'success',
      })
    )
    onCreateFactorySuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingFactory(false))
  }
}

export const getFactories = async (
  dispatch: Dispatch,
  isBackOffice: boolean,
  params: FactoryFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })
    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/onboarding/factories?${searchParams}`
      : `/v1/eatta-service/onboarding/factories?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<FactoryResponse>>(url)
    dispatch(setFactoryResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const getProducerFactories = async (
  dispatch: Dispatch,
  params: ProducerFactoryFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })
    const url = `/v1/eatta-service/catalogue/query/producer-factory-summary?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<ProducerFactory>>(url)
    dispatch(setProducerFactoryResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const updateFactoryDetails = async (
  dispatch: Dispatch,
  organizationId: string,
  editFactory: EditFactoryDetails
) => {
  try {
    dispatch(setIsCreatingFactory(true))
    const url = `/backoffice-bff/eatta-service/onboarding/${organizationId}`
    await apiclient.patch(url, { ...editFactory })
    dispatch(
      setNotification({
        message: 'Factory saved successfully',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingFactory(false))
  }
}
//warehouse
export const createGodown = async (
  dispatch: Dispatch,
  warehouse: GodownCreationRequest,
  onCreateGodownSuccess: () => void
) => {
  try {
    dispatch(setIsCreatingWarehouses(true))
    const url = `/backoffice-bff/eatta-service/onboarding/register-godown`
    const res = await apiclient.post<ApiResponse<WarehouseResponse>>(
      url,
      warehouse
    )
    dispatch(
      setNotification({
        message: res.data.message,
        type: 'success',
      })
    )
    onCreateGodownSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsCreatingWarehouses(false))
  }
}

export const updateGodown = async (
  dispatch: Dispatch,
  warehouse: WarehouseUpdateRequest,
  onUpdateWarehouseSuccess: () => void
) => {
  try {
    dispatch(setIsUpdatingWarehouse(true))
    const url = `/backoffice-bff/eatta-service/onboarding/update-godown/${warehouse.warehouseId}`
    const res = await apiclient.patch<ApiResponse<WarehouseResponse>>(
      url,
      warehouse
    )

    dispatch(
      setNotification({
        message: res.data.message,
        type: 'success',
      })
    )
    onUpdateWarehouseSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsUpdatingWarehouse(false))
  }
}

export const getGodowns = async (
  dispatch: Dispatch,
  params: GodownsFilters
) => {
  try {
    dispatch(setIsLoadingCompanies(true))
    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })
    const url = `/backoffice-bff/eatta-service/onboarding/fetch-godowns?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<WarehouseResponse>>(url)
    dispatch(setWarehousesResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCompanies(false))
  }
}

export const uploadOnboardingDocuments = async (
  dispatch: Dispatch,
  file: File,
  config: AxiosRequestConfig
): Promise<string | undefined> => {
  try {
    dispatch(setUploadDocuments(true))
    const url = `/backoffice-bff/eatta-service/onboarding/documents/upload`
    const res = await apiclient.postForm(url, { file }, config)
    return res.data.data.path
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    return undefined
  } finally {
    dispatch(setUploadDocuments(false))
  }
}

export const downloadOnboardingDocuments = async (
  dispatch: Dispatch,
  resourceUrl: string,
  external: boolean = false
): Promise<File | undefined> => {
  try {
    dispatch(setUploadDocuments(true))
    const fileName = resourceUrl.split('/').pop() || 'Onboarding_form.pdf'
    const url = `/backoffice-bff/eatta-service/onboarding/documents/download?resourceUrl=${resourceUrl}`
    const res = await apiclient.get<File>(url, {
      headers: {
        'Content-Type': 'application/pdf',
      },
      responseType: 'blob',
    })
    const blob = new Blob([res.data])
    if (external) {
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    return new File([blob], fileName)
  } catch (e) {
    console.error(e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    return undefined
  } finally {
    dispatch(setUploadDocuments(false))
  }
}
