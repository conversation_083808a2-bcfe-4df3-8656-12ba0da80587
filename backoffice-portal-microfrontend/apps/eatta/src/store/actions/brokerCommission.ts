import { Dispatch } from '@reduxjs/toolkit'
import {
  BrokerCommissionRequest,
  BrokerCommissionResponse,
  BrokerCommissionStatements,
  CatalogueFilters,
  PageFilters,
} from '@/store/interfaces'
import {
  setBrokerCommissionStatementsResponse,
  setCommissionsResponse,
  setIsActivatingCommissions,
  setIsLoadingBrokerCommissionStatements,
  setIsLoadingCommissions,
  setPendingRequestsResponse,
} from '@/store/reducers'
import { apiclient } from '@/utils/apiclient'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { setNotification } from '@dtbx/store/reducers'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import {
  IApprovalRequest,
  ApprovalRequestFilters,
} from '../interfaces/makerChecker'
import { EATTA_MODULES } from '@/utils/constants'

export const getBrokerCommissions = async (
  dispatch: Dispatch,
  params: PageFilters
) => {
  try {
    dispatch(setIsLoadingCommissions(true))

    const searchParams = mapObjectToUrlParams(params)

    const url = `/backoffice-bff/eatta-service/configuration/commission?${searchParams}`

    const res =
      await apiclient.get<PaginatedResponse<BrokerCommissionResponse>>(url)

    dispatch(setCommissionsResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCommissions(false))
  }
}

export const activateCommissions = async (
  dispatch: Dispatch,
  commissionId: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsActivatingCommissions(true))
    const url =
      '/backoffice-bff/eatta-service/configuration/commission/activate'
    await apiclient.patch(url, { id: commissionId })
    setNotification({
      message: `Commission updated successfully`,
      type: 'success',
    })
    onSuccess()
    await getBrokerCommissions(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsActivatingCommissions(false))
  }
}

//Maker checker actions
export const createBrokerCommissionsMake = async (
  isSuper: boolean,
  dispatch: Dispatch,
  data: BrokerCommissionRequest,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsLoadingCommissions(true))
    const url = `/backoffice-bff/eatta-service/configuration/commission/${isSuper ? '' : 'make'}`
    await apiclient.post(url, data)
    dispatch(
      setNotification({
        message: `Broker configuration created successfully  ${!isSuper ? 'and is pending approval' : ''}`,
        type: 'success',
      })
    )
    onSuccess()
    await getBrokerCommissions(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCommissions(false))
  }
}

export const approveBrokerCommissions = async (
  dispatch: Dispatch,
  comments: string,
  onSuccess: () => void,
  approvalId: string
) => {
  try {
    dispatch(setIsLoadingCommissions(true))
    const url = `/backoffice-bff/eatta-service/configuration/commission/approve/${approvalId}`

    await apiclient.put(url, { comments })

    dispatch(
      setNotification({
        message: `Broker configuration approved successfully`,
        type: 'success',
      })
    )
    onSuccess()
    await getPendingRequests(dispatch, {
      page: 1,
      size: 10,
      status: 'PENDING',
      module: EATTA_MODULES.commissions,
      channel: 'EATTA',
    })
    await getBrokerCommissions(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCommissions(false))
  }
}

export const getPendingRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingCommissions(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setPendingRequestsResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCommissions(false))
  }
}

export const getBrokerCommissionStatements = async (
  dispatch: Dispatch,
  filters: CatalogueFilters
) => {
  try {
    dispatch(setIsLoadingBrokerCommissionStatements(true))
    const searchParams = mapObjectToUrlParams({
      ...filters,
      groupedBy: ['Broker'],
    })

    const url = `/v1/eatta-service/catalogue/query/statement?${searchParams}`
    const response =
      await apiclient.get<PaginatedResponse<BrokerCommissionStatements>>(url)

    dispatch(setBrokerCommissionStatementsResponse(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingBrokerCommissionStatements(false))
  }
}
