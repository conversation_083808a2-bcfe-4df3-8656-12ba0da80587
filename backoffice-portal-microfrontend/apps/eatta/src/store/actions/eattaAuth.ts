import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { jwtDecode } from 'jwt-decode'
import { openapi2, secureapi } from '@dtbx/store/utils'

import {
  clearNotification,
  setCredentials,
  setDecodedToken,
  setIsLoadingLogin,
  setNotification,
} from '@dtbx/store/reducers'
import {
  ApiResponse,
  IDecodeToken,
  TokenResponse,
} from '@dtbx/store/interfaces'
import {
  AuthRequest,
  CustomerSupportPayload,
  KeyResponse,
  LoginResponse,
  OTPResponse,
  ValidateTokenResponse,
  VerifyOTPResponse,
} from '@/store/interfaces/auth'
import { encryptData } from '@/utils/passEncryption'
import { scheduleTokenRefresh } from '@/utils/refreshSchedule'
import { AxiosRequestConfig } from 'axios'
import { mapObjectToUrlParams } from '@/utils/objectUtil'

export const handleLogout = async (
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  dispatch(clearNotification())
  window.localStorage.clear()
  window.sessionStorage.clear()
  router.replace('/auth')
}

export const refreshUserToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken')
    if (!refreshToken) {
      console.error('ERROR NO REFRESH TOKEN FOUND')
      return
    }
    const currentToken: IDecodeToken = jwtDecode(
      localStorage.getItem('accessToken') || ''
    )
    const data = new FormData()
    data.append('grant_type', 'user_refresh_token')
    data.append('refresh_token', refreshToken)
    data.append('client_id', currentToken.aud || '')

    const resp = await openapi2.postForm<TokenResponse>('/auth/token', data)
    localStorage.setItem('accessToken', resp.data.access_token)
    localStorage.setItem('refreshToken', resp.data.refresh_token)
    scheduleTokenRefresh()
  } catch (e) {
    console.error('ERROR ON REFRESH TOKEN', e)
  }
}

export const verifyOTP = async (
  data: { username: string; otp: string },
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  try {
    const response = await openapi2.post<VerifyOTPResponse>(
      '/v1/eatta-service/otp/verify',
      data
    )

    if (response.data.status === 'SUCCESS') {
      const { access_token, refresh_token } = response.data.data
      // Store tokens in localStorage
      localStorage.setItem('accessToken', access_token)
      localStorage.setItem('refreshToken', refresh_token)
      // Decode and store token in redux state
      const decodedToken: IDecodeToken = jwtDecode(access_token)
      dispatch(setDecodedToken(decodedToken))

      dispatch(
        setNotification({
          message: 'OTP verified. Login successful!',
          type: 'success',
        })
      )

      router.replace('/')
      return true
    }
  } catch (e: any) {
    let message = (e as Error).message
    if (e.statusCode === 429) {
      message =
        message ||
        'You have exceeded the threshold of 5 attempts. Please login again'
      dispatch(
        setNotification({
          message: message,
          type: 'error',
        })
      )
      router.replace('/auth')
    } else {
      message = message || 'You entered an invalid or expired code.'
      dispatch(
        setNotification({
          message: message,
          type: 'error',
        })
      )
    }
    return false
  }
}

export const generateOTP = async (
  username: string,
  deliveryMode: 'SMS' | 'EMAIL',
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  try {
    const payload = {
      deliveryMode,
      username,
    }

    const response = await openapi2.post<OTPResponse>(
      '/v1/eatta-service/otp/generate',
      payload
    )

    if (response.data.status === 'SUCCESS') {
      dispatch(
        setNotification({
          message: 'Verification code sent successfully',
          type: 'success',
        })
      )
      return response.data
    }
    return response.data
  } catch (error: any) {
    const errorMessage =
      'Failed to send verification code. Please try to login again.'

    dispatch(
      setNotification({
        message: errorMessage,
        type: 'error',
      })
    )
    router.replace('/auth')
    return false
  }
}

export const handleClientLogin = async (
  data: AuthRequest,
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  try {
    dispatch(setIsLoadingLogin(true))

    // Encrypt password
    const encryptedPassword = await encryptData(data.password)

    const loginPayload = {
      username: data.username,
      password: encryptedPassword,
      organizationCode: data.organizationCode,
      //acceptedTerms
    }

    const response = await openapi2.post<LoginResponse>(
      '/v1/eatta-service/onboarding/login',
      loginPayload
    )

    if (response.data.status === 'SUCCESS') {
      dispatch(
        setCredentials({
          username: data.username,
          email: response.data.data.email,
          phoneNumber: response.data.data.phoneNumber,
        })
      )
      router.replace('/auth/verify')
    }
  } catch (e) {
    const message =
      (e as Error).message || 'Login failed. Please check your credentials.'
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingLogin(false))
  }
}

export const handleResetPassword = async (
  confirmPassword: string,
  token: string,
  dispatch: Dispatch,
  router: AppRouterInstance,
  onSuccess: () => void
) => {
  try {
    const encryptedPassword = await encryptData(confirmPassword)

    await openapi2.post(
      `/v1/eatta-service/password/reset?token=${token}`,

      {
        newPassword: encryptedPassword,
        confirmPassword: encryptedPassword,
      }
    )
    dispatch(
      setNotification({
        message: 'Password reset successfully. You can now log in.',
        type: 'success',
      })
    )
    onSuccess()
    // return router.replace('/auth')
  } catch (e) {
    const message = 'Failed to reset password. Please try again.'
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const handleExpiredResetLink = async (
  email: string,
  resetToken: string,
  dispatch: Dispatch,

  onSuccess: () => void
) => {
  try {
    const url = '/v1/eatta-service/password/resend-reset-link'
    await openapi2.post(url, { email, resetToken })
    dispatch(
      setNotification({
        message: 'Reset link sent successfully to your email',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const handleValidateToken = async (
  token: string,
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  try {
    const url = `/v1/eatta-service/password/validate-token/${token}`
    const response = await openapi2.get<ValidateTokenResponse>(url)
    return response.data.data
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    router.replace('/auth')
    return { status: 'ERROR', message }
  }
}

export const handleForgotPassword = async (
  userEmail: string,
  companyCode: string,
  dispatch: Dispatch,
  onSuccess: () => void
) => {
  try {
    await openapi2.post('/v1/eatta-service/password/forgot', {
      userEmail,
      companyCode,
    })

    dispatch(
      setNotification({
        message: 'Password reset link sent successfully to your email',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const handleChangePassword = async (
  oldPassword: string,
  confirmPassword: string,
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  try {
    const encryptedOldPassword = await encryptData(oldPassword)
    const encryptedPassword = await encryptData(confirmPassword)

    await secureapi.post('/v1/eatta-service/password/change-password', {
      oldPassword: encryptedOldPassword,
      newPassword: encryptedPassword,
      confirmPassword: encryptedPassword,
    })
    await handleLogout(dispatch, router)
    dispatch(
      setNotification({
        message: 'Password updated successfully. Login again to continue.',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const fetchPublicKey = async () => {
  const response = await openapi2.get<ApiResponse<KeyResponse>>(
    '/v1/eatta-service/onboarding/key'
  )
  return response.data.data.key
}

export const handleCustomerSupport = async (
  dispatch: Dispatch,
  payload: CustomerSupportPayload,
  onSuccess: () => void,
  config: AxiosRequestConfig = {}
) => {
  try {
    dispatch(setIsLoadingLogin(true))
    const { membershipType, file, ...values } = payload
    const query = mapObjectToUrlParams({
      membershipType: membershipType.toUpperCase(),
      ...values,
    })

    const url = `/v1/eatta-service/customer-support/raise-issue?${query}`

    const formData = new FormData()
    if (file) {
      formData.append('file', file)
    }
    await openapi2.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers,
      },
      ...config,
    })

    dispatch(
      setNotification({
        message:
          'Thank you for your query. Customer Support has been notified of your issue.',
        type: 'success',
      })
    )
    onSuccess()
  } catch (error: any) {
    let message =
      error?.message.message ||
      error?.message ||
      'Something went wrong. Try again later.'

    dispatch(
      setNotification({
        message: message.toString(),
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingLogin(false))
  }
}
