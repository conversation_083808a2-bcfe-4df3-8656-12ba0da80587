/**
 * <AUTHOR> on 10/07/2025
 */
import { Dispatch } from '@reduxjs/toolkit'
import { WarehouseFee } from '@/store/interfaces'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import { apiclient } from '@/utils/apiclient'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { setNotification } from '@dtbx/store/reducers'
import {
  UpdateWarehouseFeeConfigurationPayload,
  WarehouseFeeConfigurationPayload,
  WarehouseFeeFilters,
} from '@/store/interfaces/warehouse'
import {
  setIsConfiguringWarehouseFee,
  setIsLoadingWarehouse,
  setWarehouseFeesResponse,
} from '@/store/reducers'

export const getWarehouseFeeConfigs = async (
  dispatch: Dispatch,
  params: WarehouseFeeFilters
) => {
  try {
    dispatch(setIsLoadingWarehouse(true))

    const searchParams = mapObjectToUrlParams(params)

    const url = `/backoffice-bff/eatta-service/configuration/commission/warehouse?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<WarehouseFee>>(url)

    dispatch(setWarehouseFeesResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingWarehouse(false))
  }
}

export const configureWarehouseFee = async (
  dispatch: Dispatch,
  payload: WarehouseFeeConfigurationPayload,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsConfiguringWarehouseFee(true))

    const url = `/backoffice-bff/eatta-service/configuration/commission/warehouse`

    const response = await apiclient.post(url, payload)

    dispatch(
      setNotification({
        message: response.data.message,
        type: 'success',
      })
    )
    onSuccess()
    await getWarehouseFeeConfigs(dispatch, {
      page: 1,
      size: 10,
      ascending: false,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsConfiguringWarehouseFee(false))
  }
}

export const updateWarehouseFee = async (
  dispatch: Dispatch,
  payload: UpdateWarehouseFeeConfigurationPayload,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsConfiguringWarehouseFee(true))

    const url = `/backoffice-bff/eatta-service/configuration/commission/warehouse`

    const response = await apiclient.patch(url, payload)

    dispatch(
      setNotification({
        message: response.data.message,
        type: 'success',
      })
    )
    onSuccess()
    await getWarehouseFeeConfigs(dispatch, {
      page: 1,
      size: 10,
      ascending: false,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsConfiguringWarehouseFee(false))
  }
}
