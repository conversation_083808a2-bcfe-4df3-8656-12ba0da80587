import { Dispatch } from '@reduxjs/toolkit'
import {
  CatalogueFilters,
  AuctionResponse,
  CataloguePayload,
} from '../interfaces'
import {
  setInvoiceData,
  setIsDownloadingEslip,
  setIsExportingCatalogues,
  setIsLoadingCatalogues,
  setIsLoadingTRDInfo,
  setIsUploadingCatalogues,
  setPostAuctionCataloguesResponse,
  setPreAuctionCataloguesResponse,
} from '../reducers'
import { setNotification } from '@dtbx/store/reducers'
import { apiclient } from '@/utils/apiclient'
import { AxiosRequestConfig } from 'axios'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import { encryptFile } from '@/app/(client)/(dashboard)/actions'

export const getPostAuctionCatalogues = async (
  dispatch: Dispatch,
  isBackOffice: boolean,
  filters: CatalogueFilters
) => {
  try {
    dispatch(setIsLoadingCatalogues(true))
    // Exclude status from API call since it's only for UI display
    const { status, ...apiFilters } = filters
    const searchParams = mapObjectToUrlParams({
      ...apiFilters,
      auctionType: 'POST_AUCTION',
      groupedBy: ['buyer', 'factory', 'warehouse'],
    })

    //Todo: Update the api
    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/catalogue/pre?${searchParams}`
      : `/v1/eatta-service/catalogue/pre?${searchParams}`

    const res = await apiclient.get<AuctionResponse>(url)
    dispatch(setPostAuctionCataloguesResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCatalogues(false))
  }
}

export const uploadCatalogue = async (
  dispatch: Dispatch,
  payload: CataloguePayload,
  config: AxiosRequestConfig,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsUploadingCatalogues(true))

    const encryptionPayload = new FormData()
    encryptionPayload.append('file', payload.file)
    const { error, encryptedFile } = await encryptFile(encryptionPayload)
    if (error || !encryptedFile) {
      dispatch(
        setNotification({
          message: 'Unable to encrypt the file catalogue: ' + error,
          type: 'error',
        })
      )
      dispatch(setIsUploadingCatalogues(false))
      return
    }
    const file = new File([encryptedFile], `${payload.file.name}.pgp`, {
      type: 'application/octet-stream',
    })
    const url = `/v1/eatta-service/catalogue?saleDate=${payload.saleDate}&auctionType=${payload.auctionType}`
    const res = await apiclient.postForm(url, { file }, config)
    dispatch(
      setNotification({
        message: res.data.message,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsUploadingCatalogues(false))
  }
}

export const getPreAuctionCatalogues = async (
  dispatch: Dispatch,
  isBackOffice: boolean,
  filters: CatalogueFilters
) => {
  try {
    dispatch(setIsLoadingCatalogues(true))
    const searchParams = mapObjectToUrlParams({
      ...filters,
      auctionType: 'PRE_AUCTION',
      groupedBy: ['producer', 'factory'],
    })

    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/catalogue/pre?${searchParams}`
      : `/v1/eatta-service/catalogue/pre?${searchParams}`

    const res = await apiclient.get<AuctionResponse>(url)
    dispatch(setPreAuctionCataloguesResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingCatalogues(false))
  }
}

export const fetchTRD = async (
  dispatch: Dispatch,
  brokerInvoiceEntryId: string
) => {
  try {
    dispatch(setIsLoadingTRDInfo(true))
    const url = `v1/eatta-service/invoice/auction-entry/${brokerInvoiceEntryId}/broker-invoice-entry`
    const res = await apiclient.get(url)
    dispatch(setInvoiceData(res.data.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingTRDInfo(false))
  }
}

export const downloadTRD = async (
  dispatch: Dispatch,
  brokerInvoiceEntryId: string
) => {
  try {
    const url = `/v1/eatta-service/invoice/trd/auction-entry/${brokerInvoiceEntryId}`
    const res = await apiclient.get(url, {
      headers: {
        Accept: 'application/pdf',
      },
      responseType: 'blob',
    })

    const blob = new Blob([res.data], { type: 'application/pdf' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = 'TRD.pdf'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const downloadCatalogues = async (
  dispatch: Dispatch,
  isBackOffice: boolean,
  filters: CatalogueFilters
) => {
  try {
    dispatch(setIsExportingCatalogues(true))
    const searchParams = mapObjectToUrlParams({
      ...filters,
    })
    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/reports/auction?${searchParams}`
      : `/v1/eatta-service/reports/auction?${searchParams}`
    await apiclient.get(url)

    dispatch(
      setNotification({
        message: 'Report will be sent to your email shortly',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsExportingCatalogues(false))
  }
}

export const downloadEslip = async (
  dispatch: Dispatch,
  invoiceId: string
) => {
  try {
    dispatch(setIsDownloadingEslip(true))
    const url = `/v1/eatta-service/reports/eslip/${invoiceId}`
    const res = await apiclient.get(url, {
      headers: {
        Accept: 'application/pdf',
      },
      responseType: 'blob',
    })

    const blob = new Blob([res.data], { type: 'application/pdf' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = 'Eslip.pdf'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsDownloadingEslip(false))
  }
}