/**
 * <AUTHOR> on 05/03/2025
 */
import { Dispatch } from '@reduxjs/toolkit'
import { apiclient } from '@/utils/apiclient'
import { setNotification } from '@dtbx/store/reducers'
import {
  InsightFilters,
  InsightResponse,
  TopMetricResponse,
} from '@/store/interfaces/insight'
import {
  resetInsightState,
  setInsights,
  setIsLoadingInsights,
  setIsLoadingTopMetrics,
  setTopMetrics,
} from '@/store/reducers/insightsReducer'
import { ApiResponse } from '@dtbx/store/interfaces'
import { removeFalsyValues } from '@/utils/objectUtil'

export const getInsights = async (
  dispatch: Dispatch,
  filters: InsightFilters
) => {
  dispatch(setIsLoadingInsights(true))
  try {
    const params = removeFalsyValues(filters)
    const res = await apiclient.get<ApiResponse<InsightResponse>>(
      '/v1/eatta-service/stats',
      { params }
    )
    dispatch(setInsights(res.data.data))
  } catch (e) {
    const errorMessage = (e as Error).message
    dispatch(
      setNotification({
        message: errorMessage,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingInsights(false))
  }
}

export const getTopMetrics = async (
  dispatch: Dispatch,
  filters: InsightFilters
) => {
  dispatch(setIsLoadingTopMetrics(true))
  try {
    const params = removeFalsyValues(filters)
    const res = await apiclient.get<ApiResponse<TopMetricResponse>>(
      '/v1/eatta-service/stats/organization',
      { params }
    )
    dispatch(setTopMetrics(res.data.data))
  } catch (e) {
    const errorMessage = (e as Error).message
    dispatch(
      setNotification({
        message: errorMessage,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingTopMetrics(false))
  }
}
