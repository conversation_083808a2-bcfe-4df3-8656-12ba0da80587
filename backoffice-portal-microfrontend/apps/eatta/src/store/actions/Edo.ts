import {
  setDeliveryOrdersResponse,
  setIsDownloadingTrdEdo,
  setIsLoadingEdo,
  setIsSigningDeliveryOrder,
  setIsSubmittingDeliveryOrder,
  setSelectedAuctionEntry,
  setSelectedDeliveryOrderEntries,
} from '@/store/reducers'
import { apiclient } from '@/utils/apiclient'
import {
  DeliveryOrderFilters,
  DeliveryOrderResponse,
  SignDeliveryOrderPayload,
  ValidateEDODetails,
} from '../interfaces/Edo'
import { Dispatch } from '@reduxjs/toolkit'
import { setNotification } from '@dtbx/store/reducers'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import { ApiResponse, PaginatedResponse } from '@dtbx/store/interfaces'
import { openapi2 } from '@dtbx/store/utils'

//Delivery orders
export const getDeliveryOrders = async (
  dispatch: Dispatch,
  filters: DeliveryOrderFilters
) => {
  try {
    dispatch(setIsLoadingEdo(true))

    const searchParams = mapObjectToUrlParams({ ...filters, ascending: false })

    const res = await apiclient.get<PaginatedResponse<DeliveryOrderResponse>>(
      `/v1/eatta-service/electronic-delivery-order?${searchParams}`
    )
    dispatch(setDeliveryOrdersResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingEdo(false))
  }
}

export const getDeliveryOrderById = async (
  dispatch: Dispatch,
  deliveryOrderId: string
) => {
  try {
    dispatch(setIsLoadingEdo(true))

    const res = await apiclient.get<ApiResponse<DeliveryOrderResponse>>(
      `/v1/eatta-service/electronic-delivery-order/${deliveryOrderId}`
    )
    dispatch(setSelectedDeliveryOrderEntries(res.data.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingEdo(false))
  }
}

export const signDeliveryOrder = async (
  dispatch: Dispatch,
  payload: SignDeliveryOrderPayload,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsSigningDeliveryOrder(true))

    const url = `/v1/eatta-service/electronic-delivery-order/sign`

    const response = await apiclient.post(url, payload)

    dispatch(
      setNotification({
        message: response.data.message,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsSigningDeliveryOrder(false))
  }
}

export const submitDeliveryOrder = async (
  dispatch: Dispatch,
  deliveryOrderId: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsSubmittingDeliveryOrder(true))

    const url = `/v1/eatta-service/electronic-delivery-order/submit/${deliveryOrderId}`

    const response = await apiclient.post(url)

    dispatch(
      setNotification({
        message: response.data.message,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsSubmittingDeliveryOrder(false))
  }
}

export const selectDeliveryOrderEntries = (
  dispatch: Dispatch,
  deliveryOrderId: string,
  deliveryOrders: DeliveryOrderResponse[]
) => {
  const selectedOrder = deliveryOrders.find(
    (order) => order.id === deliveryOrderId
  )
  if (selectedOrder && selectedOrder.entries) {
    dispatch(setSelectedDeliveryOrderEntries(selectedOrder))
  }
}

export const selectAuctionEntry = (
  dispatch: Dispatch,
  lotNumber: number,
  selectedDeliveryOrder: DeliveryOrderResponse | null
) => {
  if (selectedDeliveryOrder?.entries) {
    const entry = selectedDeliveryOrder.entries.find(
      (e) => e.lotNumber === lotNumber
    )
    if (entry?.auctionEntry) {
      dispatch(setSelectedAuctionEntry(entry.auctionEntry))
    }
  }
}

export const downloadTrdEdo = async (
  dispatch: Dispatch,
  resourceUrl: string,
  documentType: 'TRD' | 'EDO' = 'TRD'
) => {
  try {
    dispatch(setIsDownloadingTrdEdo(true))
    const url = `/v1/eatta-service/document?resourceUrl=${resourceUrl}`

    const getFilenameFromUrl = (url: string): string => {
      try {
        const urlParts = url.split('/')
        const fileNameWithExtension = urlParts[urlParts.length - 1]
        const fileName = fileNameWithExtension.replace(/\.pdf$/i, '')
        return fileName || `${documentType.toLowerCase()}`
      } catch (e) {
        return `${documentType.toLowerCase()}`
      }
    }

    const fileName = getFilenameFromUrl(resourceUrl)

    const res = await apiclient.get(url, {
      headers: {
        Accept: 'application/pdf',
      },
      responseType: 'blob',
    })

    const blob = new Blob([res.data], { type: 'application/pdf' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = `${fileName}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsDownloadingTrdEdo(false))
  }
}

export const fetchLogoImage = async (
  dispatch: Dispatch,
  resourceUrl: string
): Promise<string | null> => {
  try {
    const url = `/v1/eatta-service/document?resourceUrl=${resourceUrl}`

    const res = await apiclient.get(url, {
      headers: {
        Accept: '*/*',
      },
      responseType: 'blob',
    })

    const blob = new Blob([res.data], {
      type: res.headers['content-type'] || 'image/png',
    })
    const blobUrl = window.URL.createObjectURL(blob)

    return blobUrl
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: `Error fetching logo: ${message}`,
        type: 'error',
      })
    )
    return null
  }
}

export const getEsoByDocNumber = async (docNumber: string) => {
  const res = await openapi2.get<ApiResponse<ValidateEDODetails>>(
    `/v1/eatta-service/electronic-delivery-order/doc-number/${docNumber}`
  )
  return res.data.data
}
