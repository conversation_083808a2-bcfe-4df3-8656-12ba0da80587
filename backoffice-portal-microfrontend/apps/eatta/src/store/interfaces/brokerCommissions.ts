export type BrokerCommissionStatus = 'Active' | 'Expired' | 'PENDING'

export interface BrokerCommissionRequest {
  buyerCommission: number
  commissionType: string
  producerCommission: number
  centralBankRate: number
  penalty: number
  withHoldingTax: number
  comments: string
}

export interface BrokerCommissionResponse {
  id: string
  brokerBuyerCommissionPercentage: number
  brokerProducerCommissionPercentage: number
  withHoldingTax: number
  cbrRate: number
  buyerPenalty: number
  status: BrokerCommissionStatus
  measure: string
  createdBy: string
  dateCreated: string
  createByFirstName: string
  createByLastName: string
}

export interface BrokerCommissionStatements {
  invoiceNo: string
  gardenInvoice: string
  broker: string
  factory: string
  pkgs: string
  kgs: string
  grade: string
  type: string
  buyer: string
  warehouse: string
  totalWeight: number
  pricePerKg: number
  totalValue: number
  warehouseCharges: number
  brokerCommissionFromBuyer: number
  brokerCommissionFromProducer: number
  totalCommissions: number
  penalties: number
  brokerProducerWithholdingTax: number
  producerExpectedAmount: number
  lotNo: number
  invoiceEntryStatus: string
  lateDays: number
  dateCreated: string
  dateModified: string
}
