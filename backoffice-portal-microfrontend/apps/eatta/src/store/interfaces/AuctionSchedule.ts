import { PaginatedResponse } from '@dtbx/store/interfaces'
import { PageFilters } from './filters'
import { AuctionType } from './catalogues'

export interface AuctionSchedule {
  id: string
  saleCode: string
  dayOneDate: string
  dayOne: string
  dayTwoDate: string
  dayTwo: string
  closingDate: string
  closingDay: string
  promptDate: string
  promptDay: string
  year: number
}

//Auction Schedule
export interface AuctionScheduleFilters extends PageFilters {
  auctionType?: AuctionType
  year: string
}

export interface AuctionScheduleResponse
  extends PaginatedResponse<AuctionSchedule> {}
