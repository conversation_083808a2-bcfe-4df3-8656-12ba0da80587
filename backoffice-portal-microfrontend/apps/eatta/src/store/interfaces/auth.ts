/**
 * <AUTHOR> on 23/12/2024
 */
import { ApiResponse } from '@dtbx/store/interfaces'

export interface AuthRequest {
  username: string
  password: string
  organizationCode: string
}

export interface KeyResponse {
  key: string
}

// Login Response
export interface LoginResponse {
  status: string
  message: string
  data: Credentials
}

export interface Credentials {
  email: string
  phoneNumber: string
}
// OTP Response
export interface OTPResponse {
  status: string
  message: string
  data: OTPData
}
export interface OTPData {
  username: string
  otp: string
}

// Token Response
export interface VerifyOTPResponse {
  status: string
  message: string
  data: TokenResponse
}
export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}
// validate token
export interface ValidateTokenResponse {
  status: string
  message: string
  data: ValidateTokenData
}
export interface ValidateTokenData {
  status: string
  message: string
}
export interface CustomerSupportPayload {
  organizationCode: string
  file?: File
  email: string
  phoneNumber: string
  membershipType: string
  issueDescription: string
}
