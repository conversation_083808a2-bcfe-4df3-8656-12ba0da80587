import { PageFilters } from './filters'
import { DisbursementType, InvoiceEslip } from '@/store/interfaces/invoices'
import { CompanyType, PaymentChannel } from '@/store/interfaces/Company'

export type PaymentStatus = 'PENDING' | 'ALLOCATED' | 'UNALLOCATED'

export type TransactionStatus = 'PENDING' | 'SUCCESS' | 'FAILED'

export interface PaymentFilters extends PageFilters {
  transactionRef?: string
  buyer?: string
  amount?: number
  status?: string
  eslipNumber?: string
}
export interface Payment {
  id: string
  buyerName: string
  amount: number
  coreReference: string
  status: string
  description: string
  eslipNumber: string
  sourceAddress: string
  sourceName: string
  dateCreated?: string
}

export interface AllocatePaymentRequest {
  invoicePaymentId: string
  invoiceId: string
  comments: string
}

export interface PaymentApprovalEntity {
  invoicePayment: Payment
  invoice: InvoiceEslip
}

export interface Account {
  accountHolder: string
  bank: string
  accountNumber: string
  branch: string
}

export interface Transaction {
  dateCreated: string
  dateModified: string
  id: string
  sourceAccount: Account
  destinationAccount: Account
  amount: number
  currency: string
  narration: string
  channel?: any
  status: TransactionStatus
  organizationName?: any
  organizationType?: any
  coreReference: string
  breakdownFileLink?: string
  disbursementDate: string
  message?: string
}

export interface TransactionFilters extends PageFilters {
  accountNumber?: string
  organizationName?: string
  organizationType?: CompanyType
  status?: TransactionStatus
  year: number
  saleDate: string
  disbursementType?: DisbursementType
  channel?: PaymentChannel
}
