/**
 * <AUTHOR> on 17/02/2025
 */
export type ToMetricClient = 'Producers' | 'Buyers' | null

export interface InsightResponse {
  saleCode: string
  stats: Stat
}

export interface Stat {
  totalWeight: number
  lotCount: number
  weightUnsold: number
  valueSold: number
  totalValue: number
  weightSold: number
  valueUnsold: number
}

export interface Insight {
  name: string
  value: string
  linkText: string
  link: string
  percentage?: number
}

export interface InsightsSummary {
  weight: Insight[]
  value: Insight[]
}

export interface TopMetric extends TopStat {
  percentage: number
}

export interface TopStat {
  value: number
  organizationCode: string
}

export interface TopMetricResponse {
  saleCode: string
  totalWeight: number
  totalValue: number
  topProducers: TopStat[]
  topBuyers: TopStat[]
}

export interface InsightFilters {
  saleCode?: string
}
