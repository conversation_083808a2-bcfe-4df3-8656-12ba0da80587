/**
 * <AUTHOR> on 19/12/2024
 */

import { ApiResponse, PaginatedResponse } from '@dtbx/store/interfaces'
import { PageFilters } from '@/store/interfaces/filters'
import { CHIP_COLORS } from '@/utils/statusChips'
import { Catalogue, CatalogueFilters } from '@/store/interfaces/catalogues'

//Broker invoices
export type BrokerInvoiceStatus =
  | 'INVALID'
  | 'READY'
  | 'UNPAID'
  | 'PARTIAL'
  | 'PAID'
  | 'SETTLED'
  | 'NEW'
  | 'BATCHED'
  | 'FAILED'
  | 'INVOICED'

export const INVOICE_ENTRY_STATUS = [
  'PAID',
  'PAID_AT_EQUITY',
  'PAID_AT_STANBIC',
  'UNSOLD',
  'UNPAID',
  'LATE_PAID',
  'LATE_UNPAID',
  'SETTLED',
] as const

export const LATE_UNPAID_OPTIONS = [
  { value: [1, 3], label: 'By 1-3 days' },
  { value: [4, 7], label: 'By 4-7 days' },
  { value: [8, 14], label: 'By 8-14 days' },
  { value: [15], label: 'By 15+ days' },
] as const

export type InvoiceEntryStatus = (typeof INVOICE_ENTRY_STATUS)[number]

export type InvoiceEslipStatus =
  | 'NEW'
  | 'PARTIAL'
  | 'PAID'
  | 'BATCHED'
  | 'FAILED'
  | 'INVOICED'

export interface BrokerInvoicePayload {
  saleNumber: string
  auctionDate: string
  promptDate: string
  file: File
}

export interface BrokerInvoice {
  id: string
  cacheId: string
  invoiceNumber: string
  buyerCode: string
  brokerCode: string
  saleNumber: string
  createdBy: string
  auctionDate: string
  promptDate: string
  fileHash: string
  status: BrokerInvoiceStatus
  soldWeight: number
  totalInvoiceAmount: number
  amountReceived: number
  remainingBalance: number
  lotCount: number
}

export interface BrokerInvoicesResponse
  extends PaginatedResponse<BrokerInvoice> {}

export interface InvoicesFilters extends PageFilters {
  year: number
  producer?: string
  amount?: number
  lotNo?: string
  broker?: string
  invoiceNo?: string
  invoiceNumber?: string
  status?: BrokerInvoiceStatus
  buyerName?: string
  totalPrice?: number
  saleDate: string
  dateCreated?: string
  buyer?: string
  buyerCode?: string
  brokerInvoiceId?: string
  fromAuctionDate?: string
  toAuctionDate?: string
  brokerCode?: string
  total?: number
  paidAmount?: number
  balance?: number
  saleCode?: string
}
export interface EslipFilters extends PageFilters {
  invoiceNumber?: string
  buyerCode?: string
  status?: BrokerInvoiceStatus[]
}

export interface InvoicesEntryFilters extends PageFilters {
  brokerInvoiceId?: string
}

export interface InvoiceEslip {
  id: string
  invoiceNumber: string
  invoiceEntries: InvoiceEntry[]
  status: InvoiceEslipStatus
  totalTeaValue: number
  totalBrokerCommission: number
  totalWithholdingTax: number
  totalAmountToPay: number
  totalSoldWeight: number
  balance: number
  paidAmount: number
  lotCount: number
  penalties: number
  buyer: string
  promptDate: string
  createdBy: string
}

type InvoiceEntryError = Partial<
  Record<
    keyof Omit<
      InvoiceEntry,
      | 'errors'
      | 'teaReleaseDocumentDate'
      | 'auctionEntry'
      | 'datePaid'
      | 'brokerInvoice'
      | 'status'
      | 'id'
      | 'saleDate'
    >,
    string
  >
>

export interface InvoiceEntry {
  id: string
  invoiceNumber: string
  gardenInvoice: string
  lotNumber: number
  auctionDate: string
  buyerCode: string
  buyerName?: any
  broker: string
  mark: string
  grade: string
  totalWeight: number
  saleDate: string
  category?: any
  pricePerKg: number
  netWeight: number
  unitWeight: number
  totalValue: number
  brokerShare: number
  withholdingTax: number
  netAmount: number
  netPayable: number
  warehouse: string
  bags: string
  datePaid?: any
  teaReleaseDocumentDate?: any
  status: string
  brokerCommissionFromProducer: number
  producerShare: number
  producerExpectedAmount: number
  errors: InvoiceEntryError | null
  brokerInvoice?: BrokerInvoice
  auctionEntry?: Catalogue
  totalPenalties: number
}
export interface BrokerInvoicesEntryResponse
  extends PaginatedResponse<InvoiceEntry> {}

export interface TransactionHistory {
  id: string
  transactionReference: string
  ledgerEntryType: string
  dateCreated: string
  channel: string
  source: string
  destination: string
  amount: number
  balance: string
  currency: string
  description: string
  walletName: string
}

export interface BrokerInvoiceResponse extends ApiResponse<BrokerInvoice> {}

export interface TransactionHistoryResponse
  extends PaginatedResponse<TransactionHistory> {}

export type InvoiceTabType = {
  title: string
  status: (typeof CHIP_COLORS)[keyof typeof CHIP_COLORS]
  invoiceStatus: BrokerInvoiceStatus
  itemCounts?: number
  canSelect: boolean
}

export type CheckoutType = 'INVOICE' | 'INVOICE_ENTRY'

export interface CreateInvoicePayload {
  itemIds: string[]
  itemType: CheckoutType
}

export interface RemoveInvoiceEntryPayload {
  entryIds: string[]
  invoiceNumber: string
}
export interface ProducerInvoiceEntryFilters extends CatalogueFilters {
  promptDate: string
  brokerInvoiceId: string
}
export interface AccountSalesFilter {
  broker?: string
  factory?: string
  status?: string
  saleDate?: string
  year?: number
}
export interface ProducerInvoiceEntry {
  factory: string
  totalExpected: number
  amountReceived: number
  amountDisbursed: number
}
export interface ProducerInvoiceEntryResponse
  extends PaginatedResponse<ProducerInvoiceEntry> {}

export interface DisbursementsEntry {
  amount: number
  narration: string
  internalReference: string
  externalReference: string
  currency: string
  coreReference: string
  message: string
  status: string
  bank: string
  branch: string
  brokerCode: string
  factory: string
  dateCreated: string
}
export const DISBURSEMENT_TYPE_OPTIONS = [
  'MINI',
  'MAIN',
  'LATE_PAYMENT',
] as const

export type DisbursementType = (typeof DISBURSEMENT_TYPE_OPTIONS)[number]

export interface ProducerFactoryFilters extends CatalogueFilters {
  invoiceEntryStatus?: InvoiceEntryStatus
}

export interface ProducerFactory {
  factory: string
  totalWeight: number
  totalEarnings: number
}

export interface DisbursementsEntryResponse
  extends PaginatedResponse<DisbursementsEntry> {}
