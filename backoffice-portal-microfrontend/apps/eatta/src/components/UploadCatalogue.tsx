import React, { useState } from 'react'
import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { UploadIcon } from '@dtbx/ui/icons'
import { uploadCatalogue } from '@/store/actions/catalogues'
import { useAppDispatch, useAppSelector } from '@/store'
import { FileUpload } from '@/components/FileUpload'
import { AuctionPicker } from '@/components/AuctionPicker'
import { AuctionSchedule, AuctionType } from '@/store/interfaces'
import { canUploadAuction } from '@/utils/auctionChecker'
import { Form, FormikProvider, useFormik } from 'formik'
import CloseIcon from '@mui/icons-material/Close'


interface UploadCatalogueProps {
  auctionType?: AuctionType
  onClose: (success?: boolean) => void
  open: boolean
}

const validationSchema = Yup.object({
  saleDate: Yup.string().required('sale date is required'),
  file: Yup.mixed<File>().required('catalogue file is required'),
})

export const UploadCatalogue = ({
  auctionType = 'PRE_AUCTION',
  onClose,
  open,
}: UploadCatalogueProps) => {
  const dispatch = useAppDispatch()
  const { isUploadingCatalogues } = useAppSelector((state) => state.catalogues)
  const [uploadProgress, setUploadProgress] = React.useState(0)
  const [dateError, setDateError] = React.useState<string | null>(null)

  const [auctionSchedule, setAuctionSchedule] =
    useState<AuctionSchedule | null>(null)

  const handleClose = (success: boolean) => {
    setUploadProgress(0)
    setDateError(null)
    setAuctionSchedule(null)
    formik.resetForm()
    onClose(success)
  }

  const formik = useFormik({
    initialValues: {
      saleDate: '',
      file: null,
    },
    validationSchema,
    onSubmit: async (values) => {
      if (!values.saleDate) {
        setDateError('Please select a sale date')
        return
      }
      setUploadProgress(0)
      if (!formik.values.file) return

      await uploadCatalogue(
        dispatch,
        {
          file: formik.values.file as File,
          saleDate: formik.values.saleDate,
          auctionType: auctionType,
        },
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
              setUploadProgress(progress)
            }
          },
        },
        () => handleClose(true)
      )
    },
  })

  const handleAuctionScheduleChange = (schedule: AuctionSchedule | null) => {
    setAuctionSchedule(schedule)
    if (!schedule) {
      formik.setFieldValue('saleDate', '')
      return
    }

    const auctionDate = new Date(schedule.dayOneDate)
    auctionDate.setHours(8, 0, 0, 0)
    formik.setFieldValue('saleDate', schedule.saleCode)

    if (auctionDate < new Date() && auctionType === 'PRE_AUCTION') {
      setDateError(
        'The selected sale date is for a past auction. Please choose an upcoming auction date.'
      )
      return
    }

    if (!canUploadAuction(auctionDate) && auctionType === 'PRE_AUCTION') {
      setDateError(
        'Cannot upload catalogue within 1 hour of auction start time (9:00 AM)'
      )
      return
    }

    setDateError(null)
  }

  return (
    <Dialog fullWidth maxWidth="sm" open={open}>
      <DialogTitle fontWeight={600}>
        <Stack spacing={1}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="h5" fontWeight="bold">
              Upload New {auctionType?.toLocaleLowerCase().replace('_', '-')}{' '}
              Catalogue.
            </Typography>
            <IconButton
              sx={{
                height: '2rem',
                width: '2rem',
                border: '1px solid #D0D5DD',
                borderRadius: '0.5rem',
              }}
              onClick={() => handleClose(false)}
            >
              <CloseIcon />
            </IconButton>
          </Stack>
        </Stack>
      </DialogTitle>
      <Divider />
      <DialogContent>
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={3}>
              <Stack spacing={1.5}>
                <AuctionPicker
                  value={auctionSchedule}
                  onChange={handleAuctionScheduleChange}
                  touched={formik.touched.saleDate}
                  error={formik.errors.saleDate}
                  onBlur={() => {
                    formik.setFieldTouched('saleDate', true)
                  }}
                  required
                />
              </Stack>
              {dateError && (
                <Stack>
                  <Typography
                    sx={{
                      color: '#D92D20',
                      fontSize: '0.875rem',
                      textAlign: 'center',
                    }}
                  >
                    {dateError}
                  </Typography>
                </Stack>
              )}
              <FileUpload
                progress={uploadProgress}
                disabled={isUploadingCatalogues || !!dateError}
                onFileChange={(file) => formik.setFieldValue('file', file)}
              />

              <Stack
                direction="row"
                spacing={3}
                sx={{ mt: 4, background: '#FFFFFF' }}
              >
                <Button
                  fullWidth
                  variant="contained"
                  onClick={() => handleClose(false)}
                  sx={{ background: '#D92D20' }}
                >
                  Cancel
                </Button>
                <Button
                  fullWidth
                  type="submit"
                  variant="contained"
                  startIcon={
                    isUploadingCatalogues ? (
                      <CircularProgress size={20} color="inherit" />
                    ) : (
                      <UploadIcon
                        stroke={formik.isValid ? ' #FFFFFF' : '#344054'}
                      />
                    )
                  }
                  sx={{
                    border: '1px solid #E4E7EC',
                    fontWeight: 600,
                    textWrap: 'noWrap',
                    '&:hover': {
                      color: '#FFFFFF',
                      '& svg': {
                        stroke: '#FFFFFF',
                      },
                    },
                  }}
                  disabled={isUploadingCatalogues || !formik.isValid}
                >
                  {isUploadingCatalogues ? 'Uploading...' : 'Upload Catalogue'}
                </Button>
              </Stack>
            </Stack>
          </Form>
        </FormikProvider>
      </DialogContent>
    </Dialog>
  )
}
