/**
 * <AUTHOR> on 22/04/2025
 */
import { ComponentProps } from 'react'

export const AddFactoryIcon = (props: ComponentProps<'svg'>) => {
  const { width = 20, height = 20, stroke = '#101828', ...rest } = props
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      {...rest}
    >
      <path
        d="M27.3333 9.7037L16 16M16 16L4.66662 9.7037M16 16L16 28.6667M18.6667 27.8519L17.036 28.7578C16.6579 28.9679 16.4688 29.0729 16.2686 29.1141C16.0914 29.1505 15.9086 29.1505 15.7314 29.1141C15.5312 29.0729 15.3421 28.9679 14.964 28.7578L5.0973 23.2763C4.69794 23.0544 4.49824 22.9435 4.35284 22.7857C4.22421 22.6461 4.12686 22.4807 4.06731 22.3005C4 22.0967 4 21.8683 4 21.4114V10.5886C4 10.1318 4 9.90334 4.06731 9.69961C4.12686 9.51938 4.22421 9.35393 4.35284 9.21435C4.49825 9.05656 4.69793 8.94563 5.09729 8.72376L14.964 3.24228C15.3421 3.0322 15.5312 2.92716 15.7314 2.88598C15.9086 2.84953 16.0914 2.84953 16.2686 2.88598C16.4688 2.92716 16.6579 3.0322 17.036 3.24227L26.9027 8.72376C27.3021 8.94563 27.5018 9.05656 27.6472 9.21434C27.7758 9.35393 27.8731 9.51937 27.9327 9.69961C28 9.90334 28 10.1318 28 10.5886L28 16.6667M10 6.00003L22 12.6667M25.3333 28V20M21.3333 24H29.3333"
        stroke={stroke}
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ManageFactoryIcon = (props: ComponentProps<'svg'>) => {
  const { width = 20, height = 20, ...otherProps } = props
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      {...otherProps}
    >
      <path
        d="M10.8337 9.16667H14.8337C15.7671 9.16667 16.2338 9.16667 16.5903 9.34832C16.9039 9.50811 17.1589 9.76308 17.3187 10.0767C17.5003 10.4332 17.5003 10.8999 17.5003 11.8333V17.5M10.8337 17.5V5.16667C10.8337 4.23325 10.8337 3.76654 10.652 3.41002C10.4922 3.09641 10.2372 2.84144 9.92364 2.68166C9.56712 2.5 9.10041 2.5 8.16699 2.5H5.16699C4.23357 2.5 3.76686 2.5 3.41034 2.68166C3.09674 2.84144 2.84177 3.09641 2.68198 3.41002C2.50033 3.76654 2.50033 4.23325 2.50033 5.16667V17.5M18.3337 17.5H1.66699M5.41699 5.83333H7.91699M5.41699 9.16667H7.91699M5.41699 12.5H7.91699"
        stroke="#475467"
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const EditFactoryIcon = (props: ComponentProps<'svg'>) => {
  const { width = 20, height = 20, ...otherProps } = props
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...otherProps}
    >
      <path
        d="M9.99998 16.6667H17.5M2.5 16.6667H3.89545C4.3031 16.6667 4.50693 16.6667 4.69874 16.6206C4.8688 16.5798 5.03138 16.5125 5.1805 16.4211C5.34869 16.318 5.49282 16.1739 5.78107 15.8856L16.25 5.41669C16.9404 4.72634 16.9404 3.60705 16.25 2.91669C15.5597 2.22634 14.4404 2.22634 13.75 2.91669L3.28105 13.3856C2.9928 13.6739 2.84867 13.818 2.7456 13.9862C2.65422 14.1353 2.58688 14.2979 2.54605 14.468C2.5 14.6598 2.5 14.8636 2.5 15.2713V16.6667Z"
        stroke="#475467"
        strokeWidth={1.66667}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
