export const Logo = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="177"
      height="32"
      viewBox="0 0 177 32"
      fill="none"
    >
      <path
        d="M115.52 0.583618H114.784V31.9998H115.52V0.583618Z"
        fill="#D92D20"
      />
      <path
        d="M91.5464 14.1622C92.9961 13.2973 94.727 11.6973 94.727 8.04324C94.727 4.36757 93.1475 0.0216229 85.7044 0.0216229H65.3872V31.3946H85.488C93.8615 31.3946 96.0685 25.0378 96.0685 21.5784C96.0685 17.9892 94.5539 15.5243 91.5464 14.1622ZM83.5407 25.0378H72.7654V5.90271H82.9132C86.6348 5.90271 87.3704 6.98378 87.3704 8.73513C87.3704 11.2649 84.8605 11.827 83.995 11.827H78.3261L74.7127 18.1189H84.4494C86.2886 18.1189 88.4956 18.7027 88.4956 21.4486C88.4739 24.4541 86.6564 25.0378 83.5407 25.0378ZM16.8769 0C25.099 0 30.8544 6.35676 30.8544 15.4595C30.8544 24.7135 24.9475 31.3946 16.1629 31.3946H0V12.4108L7.37824 8.64865V25.0162H14.1506C19.733 25.0162 22.9353 21.6432 22.9353 15.7838C22.9353 9.49189 20.0143 5.88108 14.8863 5.88108H0V0H16.8769Z"
        fill="#D92D20"
      />
      <path
        d="M60.9513 0.0864258H33.0828V6.29183H60.9513V0.0864258Z"
        fill="#D92D20"
      />
      <path
        d="M50.7186 8.64856L43.3403 12.4107H43.362V31.3945H50.6969V8.64856"
        fill="#D92D20"
      />
      <path
        d="M172.924 1.85943C174.135 1.85943 174.828 2.7243 174.828 4.23781C174.828 5.57835 174.157 6.46483 172.772 6.46483H171.041V2.24862L169.44 3.07024V7.74051H173.227C175.239 7.74051 176.58 6.31349 176.58 4.15132C176.58 1.98916 175.217 0.562134 173.4 0.562134H169.419V1.83781"
        fill="#D92D20"
      />
      <path
        d="M124.694 1.85943C125.884 1.85943 126.598 2.7243 126.598 4.23781C126.598 5.57835 125.906 6.46483 124.543 6.46483H122.812V2.24862L121.189 3.07024V7.74051H124.997C127.009 7.74051 128.329 6.31349 128.329 4.15132C128.329 1.98916 126.966 0.562134 125.149 0.562134H121.189V1.83781"
        fill="#D92D20"
      />
      <path
        d="M146.354 7.74037L148.323 2.94037V7.74037H149.924V0.583618H147.847L145.618 5.79443L143.411 0.583618H141.312V7.74037H142.913V2.94037H142.935L144.904 7.74037"
        fill="#D92D20"
      />
      <path
        d="M131.532 0.583618H129.931V7.74037H131.532V0.583618Z"
        fill="#D92D20"
      />
      <path
        d="M137.46 0.583618H135.383L132.483 7.74037H134.366L136.421 2.14037L137.46 4.9728H135.967L135.274 6.2701H137.957L138.498 7.74037H140.359"
        fill="#D92D20"
      />
      <path
        d="M166.129 5.57821H166.107L162.624 0.583618H160.677V7.74037H162.278V2.74578H162.299L165.913 7.74037H167.73V0.583618H166.129"
        fill="#D92D20"
      />
      <path
        d="M155.311 0.389038C152.131 0.389038 151.243 2.68093 151.243 4.17282C151.243 5.66471 152.066 7.9566 155.311 7.9566C158.47 7.9566 159.379 5.66471 159.379 4.17282C159.379 2.65931 158.492 0.389038 155.311 0.389038ZM155.311 6.61606C153.667 6.61606 152.974 5.27552 152.974 4.17282C152.974 3.0485 153.645 1.72957 155.311 1.72957C156.977 1.72957 157.648 3.0485 157.648 4.17282C157.648 5.27552 156.956 6.61606 155.311 6.61606Z"
        fill="#D92D20"
      />
      <path
        d="M149.469 15.7838L147.175 15.2433C146.699 15.1568 146.136 14.9838 146.158 14.5081C146.158 14.0325 147.11 13.8379 147.803 13.8379C148.538 13.8379 149.49 13.8379 149.62 14.7676H151.351C151.221 12.7352 149.382 12.4757 147.976 12.4757C145.855 12.4757 144.47 13.146 144.427 14.6379C144.405 15.8487 145.574 16.4541 146.699 16.6919L148.387 17.0811C149.122 17.2325 149.815 17.4271 149.793 17.946C149.771 18.5514 148.884 18.7027 148.041 18.7027C146.418 18.7027 146.007 18.2703 145.963 17.2973L144.341 17.8379C144.427 19.6757 146.266 20.0649 147.997 20.0649C150.55 20.0649 151.546 19.1352 151.567 17.7946C151.611 16.8217 150.983 16.1081 149.469 15.7838Z"
        fill="#D92D20"
      />
      <path
        d="M136.507 14.1189V17.3189C136.507 18.1189 136.983 20.0865 140.034 20.0865C143.02 20.0865 143.518 18.1405 143.518 17.3189V12.7135H141.895V16.8432C141.895 17.5351 141.679 18.7243 140.013 18.7243C138.325 18.7243 138.108 17.5567 138.108 16.8432V12.7135H136.507"
        fill="#D92D20"
      />
      <path
        d="M135.469 19.8704L135.317 18.3353C135.231 17.5353 134.95 16.865 134.149 16.4758C134.82 16.1947 135.188 15.5244 135.188 14.6596C135.188 13.9245 134.517 12.692 133.002 12.692H128.35V19.8704H129.951V13.9893H132.569C133.024 13.9893 133.543 14.2488 133.543 14.9623C133.543 15.6326 133.067 15.892 132.656 15.892H131.033L130.254 17.2542H132.569C133.132 17.2542 133.586 17.4704 133.695 18.5082L133.824 19.8704"
        fill="#D92D20"
      />
      <path
        d="M127.225 12.692H120.582V14.0758H127.225V12.692Z"
        fill="#D92D20"
      />
      <path
        d="M148.344 27.9353V24.8002H146.743V32.0002H148.344V29.8596L149.534 28.7785L151.893 32.0002H154.056L150.768 27.784L153.84 24.8002H151.655"
        fill="#D92D20"
      />
      <path
        d="M133.999 24.8215H131.922L129.022 31.9999H130.883L132.96 26.3783L133.999 29.2107H132.506L131.792 30.508H134.475L135.037 31.9999H136.898"
        fill="#D92D20"
      />
      <path
        d="M143.279 29.8161H143.258L139.796 24.8215H137.827V31.9999H139.45V27.0053H139.471L143.063 31.9999H144.902V24.8215H143.279"
        fill="#D92D20"
      />
      <path
        d="M127.074 28.108C127.42 27.9134 127.875 27.5459 127.875 26.6594C127.875 25.8161 127.529 24.8215 125.841 24.8215H121.189V31.9783H125.798C127.702 31.9783 128.199 30.6161 128.199 29.8378C128.199 28.9945 127.81 28.4107 127.074 28.108ZM125.343 30.7026H122.79V26.0972H125.192C126.079 26.0972 126.252 26.3999 126.252 26.8107C126.252 27.4594 125.603 27.5891 125.43 27.5891H123.894L123.115 28.9513H125.516C125.971 28.9513 126.512 29.1026 126.512 29.7729C126.533 30.5729 126.057 30.7026 125.343 30.7026Z"
        fill="#D92D20"
      />
      <path
        d="M124.716 14.6378L123.028 15.5027V19.8702H124.716V14.6378Z"
        fill="#D92D20"
      />
      <path
        d="M158.882 12.692H152.239V14.0758H158.882V12.692Z"
        fill="#D92D20"
      />
      <path
        d="M156.392 14.6378L154.704 15.5027V19.8702H156.392V14.6378Z"
        fill="#D92D20"
      />
    </svg>
  )
}
