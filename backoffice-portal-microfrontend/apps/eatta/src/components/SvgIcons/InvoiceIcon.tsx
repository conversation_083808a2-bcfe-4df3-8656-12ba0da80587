import { ComponentProps } from 'react'

export const InvoiceIcon = (props: ComponentProps<'svg'>) => {
  const {
    width = 18,
    height = 20,
    strokeWidth = 2,
    stroke = '#667085',
    ...rest
  } = props

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M1 5.8C1 4.11984 1 3.27976 1.32698 2.63803C1.6146 2.07354 2.07354 1.6146 2.63803 1.32698C3.27976 1 4.11984 1 5.8 1H12.2C13.8802 1 14.7202 1 15.362 1.32698C15.9265 1.6146 16.3854 2.07354 16.673 2.63803C17 3.27976 17 4.11984 17 5.8V19L14.25 17L11.75 19L9 17L6.25 19L3.75 17L1 19V5.8Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
