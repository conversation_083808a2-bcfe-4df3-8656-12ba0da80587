import { ComponentProps } from 'react'

export const RemoveIcon = (props: ComponentProps<'svg'>) => {
  const { width = 21, height = 20, stroke = '#475467', ...rest } = props
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="minus-circle">
        <path
          id="Icon"
          d="M7.16663 10.0001H13.8333M18.8333 10.0001C18.8333 14.6025 15.1023 18.3334 10.5 18.3334C5.89759 18.3334 2.16663 14.6025 2.16663 10.0001C2.16663 5.39771 5.89759 1.66675 10.5 1.66675C15.1023 1.66675 18.8333 5.39771 18.8333 10.0001Z"
          stroke={stroke}
          strokeWidth="1.66667"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
    </svg>
  )
}
