/**
 * <AUTHOR> on 22/04/2025
 */
import { ComponentProps } from 'react'

export const AddUserIcon = (props: ComponentProps<'svg'>) => {
  const { width = 20, height = 20, ...otherProps } = props

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      {...otherProps}
    >
      <path
        d="M10.0003 12.9167H6.25033C5.08736 12.9167 4.50587 12.9167 4.03271 13.0602C2.96737 13.3834 2.13369 14.217 1.81053 15.2824C1.66699 15.7555 1.66699 16.337 1.66699 17.5M15.8337 17.5V12.5M13.3337 15H18.3337M12.0837 6.25C12.0837 8.32107 10.4047 10 8.33366 10C6.26259 10 4.58366 8.32107 4.58366 6.25C4.58366 4.17893 6.26259 2.5 8.33366 2.5C10.4047 2.5 12.0837 4.17893 12.0837 6.25Z"
        stroke="#475467"
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ManageUserIcon = (props: ComponentProps<'svg'>) => {
  const { width = 20, height = 20, ...otherProps } = props

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      {...otherProps}
    >
      <path
        d="M9.99955 12.5C7.35782 12.5 5.00855 13.7755 3.51288 15.755C3.19097 16.181 3.03002 16.394 3.03528 16.6819C3.03935 16.9043 3.17902 17.1849 3.35402 17.3222C3.58054 17.5 3.89444 17.5 4.52224 17.5H15.4769C16.1047 17.5 16.4186 17.5 16.6451 17.3222C16.8201 17.1849 16.9598 16.9043 16.9638 16.6819C16.9691 16.394 16.8081 16.181 16.4862 15.755C14.9906 13.7755 12.6413 12.5 9.99955 12.5Z"
        stroke="#475467"
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.99955 10C12.0706 10 13.7496 8.32107 13.7496 6.25C13.7496 4.17893 12.0706 2.5 9.99955 2.5C7.92848 2.5 6.24955 4.17893 6.24955 6.25C6.24955 8.32107 7.92848 10 9.99955 10Z"
        stroke="#475467"
        strokeWidth={1.66667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
