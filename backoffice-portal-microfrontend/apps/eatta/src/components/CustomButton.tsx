/**
 * <AUTHOR> on 18/12/2024
 */
'use client'
import React from 'react'
import { useAppSelector } from '@/store'
import { Button, ButtonProps, CircularProgress } from '@mui/material'
import { UploadIcon } from '@dtbx/ui/icons'

interface CustomButtonProps extends ButtonProps {
  label: string
  isLoading?: boolean
  setCurrentStage?: (stage: string) => void
}

export const CustomButton: React.FC<CustomButtonProps> = ({
  label,
  isLoading = false,
  sx,
  onClick,
  ...props
}) => {
  return (
    <Button
      disabled={isLoading}
      sx={{
        padding: '8px 14px',
        fontWeight: 'bold',
        gap: '1px',
        ...sx,
      }}
      {...props}
      variant="outlined"
      color="secondary"
      onClick={onClick}
      startIcon={
        <UploadIcon
          stroke={
            isLoading
              ? '#00000042'
              : sx &&
                  typeof sx === 'object' &&
                  'backgroundColor' in sx &&
                  sx.backgroundColor === 'primary.main'
                ? '#FFFFFF'
                : '#344054'
          }
        />
      }
      endIcon={
        isLoading ? (
          <CircularProgress color="primary" size={20} thickness={3.0} />
        ) : undefined
      }
    >
      {isLoading ? 'Uploading' : label}
    </Button>
  )
}
