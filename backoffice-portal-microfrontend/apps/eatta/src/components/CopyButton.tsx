/**
 * <AUTHOR> on 13/05/2025
 */

import { FC, useState } from 'react'
import { IconButton, Tooltip } from '@mui/material'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import DoneIcon from '@mui/icons-material/DoneRounded'

interface CopyButtonProps {
  value: string
  tooltipTitle?: string
}

const CopyButton: FC<CopyButtonProps> = ({
  value,
  tooltipTitle = 'Copy to clipboard',
}) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <Tooltip title={copied ? 'Copied!' : tooltipTitle}>
      <IconButton onClick={handleCopy} size="small">
        {copied ? (
          <DoneIcon color="success" fontSize={'small'} />
        ) : (
          <ContentCopyIcon fontSize={'small'} />
        )}
      </IconButton>
    </Tooltip>
  )
}

export default CopyButton
