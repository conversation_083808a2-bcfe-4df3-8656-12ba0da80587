import { useCustomRouter } from '@dtbx/ui/hooks'
import { Button, IconButton, Stack, Tooltip } from '@mui/material'
import LogoutIcon from '@mui/icons-material/Logout'
import { ChangePasswordIcon } from '@/components/SvgIcons/ChangePassword'
import { useAppSelector } from '@/store'
import { handleLogout } from '@/store/actions/eattaAuth'
import { useAppDispatch } from '@/store'
import { useState } from 'react'
import { ConfirmCancel } from './ConfirmCancel'

const SideBarFooter = () => {
  const { isSidebarCollapsed } = useAppSelector((state) => state.navigation)
  const dispatch = useAppDispatch()
  const router = useCustomRouter()

  const [dialogOpen, setDialogOpen] = useState(false)

  const handleChangePassword = () => {
    router.push('/change-password')
  }

  const handleClientLogOut = () => {
    setDialogOpen(true)
  }
  const handleCloseDialog = () => {
    setDialogOpen(false)
  }
  const handleConfirmLogout = () => {
    handleLogout(dispatch, router)
    setDialogOpen(false)
  }
  const handleConfirmCancel = () => {
    setDialogOpen(false)
  }

  return (
    <Stack
      direction="column"
      spacing={2}
      paddingBottom={3}
      sx={{
        width: '100%',
        transition: 'width 0.3s',
      }}
    >
      {isSidebarCollapsed ? (
        <Tooltip title="Log Out" placement={'right'}>
          <IconButton
            onClick={handleClientLogOut}
            sx={{
              '&:hover': {
                backgroundColor: 'transparent',
              },
            }}
          >
            <LogoutIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Button
          type="button"
          variant="contained"
          onClick={handleClientLogOut}
          sx={{
            backgroundColor: '#FF4D38',
            whiteSpace: 'nowrap',
            '&:hover': {
              backgroundColor: '#FF4D38',
            },
          }}
          fullWidth
          endIcon={<LogoutIcon />}
        >
          Log Out
        </Button>
      )}

      {isSidebarCollapsed ? (
        <Tooltip title="Change Password" placement={'right'}>
          <IconButton
            onClick={handleChangePassword}
            sx={{
              '&:hover': {
                backgroundColor: 'transparent',
              },
            }}
          >
            <ChangePasswordIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Button
          type="button"
          variant="text"
          fullWidth
          startIcon={<ChangePasswordIcon />}
          onClick={handleChangePassword}
          sx={{ color: '#344054', whiteSpace: 'nowrap' }}
        >
          Change Password
        </Button>
      )}
      <ConfirmCancel
        open={dialogOpen}
        title="Log out?"
        description="Are you sure you want to log out?"
        confirmLabel="Yes, I'm sure"
        cancelLabel="Cancel"
        onConfirm={handleConfirmLogout}
        onCancel={handleCloseDialog}
      />
    </Stack>
  )
}

export default SideBarFooter
