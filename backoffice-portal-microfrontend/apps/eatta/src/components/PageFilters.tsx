import { DECIMAL_NUMERIC_REGEX } from '@/utils/validators'
import {
  Autocomplete,
  CircularProgress,
  Divider,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect, useMemo, useState } from 'react'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { useAppDispatch, useAppSelector } from '@/store'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import {
  AllCompanyType,
  AuctionSchedule,
  CompanyFilters,
  CompanyType,
  PaymentChannel,
} from '@/store/interfaces'
import { ExportButton } from '@/components/ExportButton'
import { BrokerSelector } from './BrokerSelector'
import { MarkSelector } from './MarkSelector'
import { useDebounce } from '@dtbx/ui/hooks'
import { DisbursementSelector } from './DisbursmentSelector'
import { DisbursementType } from '@/store/interfaces/invoices'
import { generateYears } from '@/utils/numberFormatter'
import { StatusSelector } from './statusSelector'
import { AccessWrapper } from './AccessHelper'
import { LateUnpaidSelector } from './LateUnpaidSelector'
import { CompanyTypeSelector } from '@/components/CompanyTypeSelector'
import { ChannelSelector } from '@/components/ChannelSelector'
import { getAuctionSchedule } from '@/store/actions'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { getAuctionWeek } from '@dtbx/store/utils'

export const DEFAULT_FILTER_CONFIG = {
  showSaleDate: true,
  showSearchBox: true,
  showLateUnpaidStatus: false,
  showStatus: true,
  showBrokerFilter: false,
  showFactory: false,
  showExport: true,
  showDateFilter: false, // This is not used in the component but kept for consistency
  showDisbursementFilter: false,
  showCompanyType: false,
  showChannel: false,
} as const

export type PageFilterConfig = Record<
  keyof typeof DEFAULT_FILTER_CONFIG,
  boolean
>

export interface MainFilters {
  status?: string
  year: number
  lotNo?: string
  saleDate: string
  broker?: string
  factory?: string
  disbursementType?: DisbursementType | null
  organizationType?: CompanyType | null
  channel?: PaymentChannel | null
  lateUnpaidDays?: number[] | undefined
}

export interface SearchByValueConfig<T> {
  filterLabel?: string
  filterKey: keyof T
  type: 'string' | 'numeric'
}

interface PageFiltersProps<T> {
  title?: string
  subtitle?: string
  filters: T
  statuses?: readonly string[]
  companyTypes?: readonly AllCompanyType[]
  filterConfig?: PageFilterConfig
  searchByValues?: SearchByValueConfig<T>[] // Updated type
  onSearch: (filters: T) => void
  onExport: (filters: T) => void
  initialSearchBy?: keyof T
}

export function PageFilters<T extends MainFilters>({
  title = 'Sales',
  subtitle = '',
  filters,
  statuses = [],
  companyTypes = [],
  filterConfig = DEFAULT_FILTER_CONFIG,
  searchByValues = [],
  onSearch,
  onExport,
  initialSearchBy,
}: PageFiltersProps<T>) {
  const dispatch = useAppDispatch()
  const isBackoffice = useMemo(() => checkIfBackOffice(), [])
  const viewingYear = useMemo(() => generateYears(), [])
  const [year, setYear] = useState<string>(
    filters.year?.toString() ||
      viewingYear[0] ||
      String(new Date().getFullYear())
  )
  const { isExportingCatalogues } = useAppSelector((state) => state.catalogues)
  const { auctionScheduleResponse, isLoadingSchedules } = useAppSelector(
    (state) => state.salesSchedule
  )
  const { data: auctions } = auctionScheduleResponse

  const [auctionSchedule, setAuctionSchedule] =
    useState<AuctionSchedule | null>(null)

  const {
    showExport,
    showSearchBox,
    showSaleDate,
    showLateUnpaidStatus,
    showStatus,
    showBrokerFilter,
    showFactory,
    showDisbursementFilter,
    showCompanyType,
    showChannel,
  } = filterConfig
  const [searchBy, setSearchBy] = useState<string>(
    initialSearchBy?.toString() || searchByValues[0]?.filterKey.toString()
  )

  useEffect(() => {
    setSearchBy(
      initialSearchBy?.toString() || searchByValues[0]?.filterKey.toString()
    )
  }, [initialSearchBy])

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const currentSearchConfig = useMemo(
    () => searchByValues.find((item) => item.filterKey === searchBy),
    [searchByValues, searchBy]
  )

  const searchPlaceHolder = `Search ${searchByLabel?.[searchBy] || searchBy || ''}`

  useEffect(() => {
    handleChange(searchBy as keyof T, validateSearchValue(debouncedSearchValue))
  }, [debouncedSearchValue, searchBy])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(validateSearchValue(value))
  }

  const validateSearchValue = (value: string): string => {
    if (currentSearchConfig?.type === 'numeric') {
      return DECIMAL_NUMERIC_REGEX.test(value) ? value : ''
    }
    return value
  }

  const handleChange = (field: keyof T, value: string | number) => {
    if (filters[field as keyof T] === value) {
      return
    }
    let updatedFilters = {
      ...filters,
      [field]: value,
    }

    const searchValuesKeys = searchByValues
      ?.map((v) => v.filterKey)
      .filter((v) => v !== searchBy)
    searchValuesKeys?.forEach((key) => {
      delete updatedFilters[key]
    })
    onSearch(updatedFilters)
  }

  const handleAuctionChange = (schedule: AuctionSchedule) => {
    setAuctionSchedule(schedule)
    handleFilterChange({
      ...filters,
      year: schedule.year,
      saleDate: schedule.saleCode,
    })
  }
  const handleBrokerChange = (brokerName: string | null) => {
    handleChange('broker', brokerName ?? '')
  }
  const handleFactoryChange = (factoryName: string | null) => {
    handleChange('factory', factoryName ?? '')
  }
  const handleDisbursementChange = (
    disbursementType: DisbursementType | null
  ) => {
    handleChange('disbursementType', disbursementType ?? '')
  }

  function handleYearChange(value: string) {
    setYear(value)
  }

  const handleFilterChange = (filters: T) => {
    onSearch(filters)
  }

  const handleLateUnpaidChange = (value: number[] | null) => {
    let updatedFilters = {
      ...filters,
    }

    if (value && value.length > 0) {
      updatedFilters = {
        ...updatedFilters,
        lateUnpaidDays: value,
        status: 'LATE_UNPAID',
      }
    } else {
      updatedFilters = {
        ...updatedFilters,
        lateUnpaidDays: undefined,
      }
    }

    handleFilterChange(updatedFilters)
  }
  const handleStatusChange = (value: string | null) => {
    let updatedFilters = {
      ...filters,
      status: value || '',
    }
    if (value !== 'LATE_UNPAID') {
      updatedFilters = {
        ...updatedFilters,
        lateUnpaidDays: undefined,
      }
    }
    handleFilterChange(updatedFilters)
  }

  useEffect(() => {
    const value = validateSearchValue(debouncedSearchValue)
    if (!value) {
      setSearchValue('')
    }
  }, [searchBy])

  useEffect(() => {
    getAuctionSchedule(dispatch, isBackoffice, {
      year,
      page: 1,
      size: 55,
    })
  }, [year, dispatch, isBackoffice])

  useEffect(() => {
    if (!auctions || auctions.length === 0) return
    const week = getAuctionWeek().toString()
    const currentAuction = auctions.find(
      (schedule) =>
        schedule.saleCode === filters.saleDate &&
        schedule.year.toString() === year
    )
    if (currentAuction) {
      setAuctionSchedule(currentAuction)
      return
    }

    // Try to find auction by week
    const weekAuction = auctions.find(
      (schedule) =>
        schedule.saleCode === week && schedule.year.toString() === year
    )
    if (weekAuction) {
      setAuctionSchedule(weekAuction)
      handleFilterChange({
        ...filters,
        year: weekAuction.year,
        saleDate: weekAuction.saleCode,
      })
      return
    }

    //Default to first auction in the {auctions} list
    const firstAuction = auctions[0]
    setAuctionSchedule(firstAuction)

    handleFilterChange({
      ...filters,
      year: firstAuction.year,
      saleDate: firstAuction.saleCode,
    })
  }, [auctions])

  return (
    <Stack sx={{ backgroundColor: '#FFFFFF' }}>
      <Stack
        flexDirection={{ xs: 'column', md: 'row' }}
        justifyContent="space-between"
        alignItems={{ xs: 'start', md: 'center' }}
        sx={{
          width: '100%',
          gap: '0.5rem',
          paddingInline: 3,
          paddingBlock: 1,
        }}
      >
        <Stack>
          {title && (
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#000A12',
              }}
            >
              {title}
            </Typography>
          )}

          {subtitle && (
            <Typography
              variant="subtitle2"
              sx={{
                color: '#000A12',
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Stack>
        {showSaleDate && (
          <Stack direction="row" spacing={2}>
            <Stack spacing={1} width="6rem">
              <Typography
                variant="body2"
                sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
              >
                Viewing Year
              </Typography>

              <Autocomplete
                disablePortal
                size="small"
                id="disbursement-type-selector"
                options={viewingYear}
                value={year}
                onChange={(_, newValue) => {
                  handleYearChange(newValue)
                }}
                disableClearable
                popupIcon={<KeyboardArrowDownRounded />}
                sx={{
                  '.MuiInputBase-input.MuiOutlinedInput-input': {
                    py: '3px !important',
                  },
                  background: '#FFFFFF',
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      border: `1px solid #D0D5DD !important`,
                    },
                  },
                  borderRadius: '0.5rem',
                  color: '#667085',
                }}
                renderInput={(params) => (
                  <TextField
                    hiddenLabel
                    placeholder="Year"
                    {...params}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '0.5rem',
                      },
                    }}
                  />
                )}
              />
            </Stack>
            <Stack spacing={1} width="8rem">
              <Typography
                variant="body2"
                sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
              >
                Viewing Auction
              </Typography>

              <Autocomplete
                disablePortal
                size="small"
                id="auction-schedule-selector"
                loading={isLoadingSchedules}
                loadingText="Loading..."
                options={auctions}
                value={auctionSchedule ?? auctions[0] ?? null}
                disableClearable
                onChange={(_, auction) => {
                  handleAuctionChange(auction)
                }}
                popupIcon={
                  isLoadingSchedules ? (
                    <CircularProgress color="primary" size={16} />
                  ) : (
                    <KeyboardArrowDownRounded />
                  )
                }
                getOptionKey={(option) => `${option.year}-${option.saleCode}`}
                getOptionLabel={(option) => `${option.year}/${option.saleCode}`}
                isOptionEqualToValue={(option, value) =>
                  option.saleCode === value.saleCode
                }
                renderInput={(params) => (
                  <TextField
                    hiddenLabel
                    {...params}
                    placeholder="Auction"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '0.5rem',
                      },
                    }}
                  />
                )}
                slotProps={{
                  popper: {
                    sx: {
                      '& .MuiAutocomplete-listbox': {
                        maxHeight: '15rem',
                      },
                    },
                  },
                }}
              />
            </Stack>
          </Stack>
        )}
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
          width: '100%',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="flex-end"
        useFlexGap
        flexWrap={{ xs: 'wrap', lg: 'nowrap' }}
      >
        {showSearchBox && (
          <Stack spacing={1} sx={{ width: '100%' }}>
            <Typography
              variant="body2"
              sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
            >
              Select Search Filter
            </Typography>
            <CustomFilterUserBox
              searchValue={searchValue}
              selectedSearchBy={searchBy}
              searchByValues={searchByValues.map(
                (item) => item.filterKey as string
              )}
              searchByLabel={searchByLabel}
              setSearchByValue={setSearchBy}
              onHandleSearch={handleSearch}
              searchPlaceHolder={searchPlaceHolder}
              prependSearchBy={true}
            />
          </Stack>
        )}
        {showBrokerFilter && (
          <BrokerSelector
            value={filters.broker}
            onChange={handleBrokerChange}
          />
        )}

        {showFactory && (
          <MarkSelector
            value={filters.factory}
            onChange={handleFactoryChange}
          />
        )}
        {showCompanyType && (
          <CompanyTypeSelector
            options={companyTypes}
            value={filters.organizationType}
            onChange={(organizationType) =>
              handleChange('organizationType', organizationType ?? '')
            }
          />
        )}
        {showChannel && (
          <ChannelSelector
            value={filters.channel}
            onChange={(channel) => handleChange('channel', channel ?? '')}
          />
        )}
        {showDisbursementFilter && (
          <DisbursementSelector
            value={filters.disbursementType}
            onChange={handleDisbursementChange}
          />
        )}
        {showLateUnpaidStatus && (
          <LateUnpaidSelector
            value={filters.lateUnpaidDays}
            onChange={handleLateUnpaidChange}
          />
        )}
        {showStatus && (
          <StatusSelector
            value={filters.status}
            statuses={statuses}
            onChange={handleStatusChange}
          />
        )}

        {showExport && (
          //Hide for superadmin
          <AccessWrapper
            clientTypes={['Broker', 'Producer', 'Buyer']}
            backofficeAccess={false}
          >
            <ExportButton
              onClick={() => onExport(filters)}
              ButtonText={isExportingCatalogues ? 'Exporting...' : 'Export CSV'}
            />
          </AccessWrapper>
        )}
      </Stack>
    </Stack>
  )
}
