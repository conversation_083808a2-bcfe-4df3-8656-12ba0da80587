import { Autocomplete, Stack, TextField, Typography } from '@mui/material'
import {
  DISBURSEMENT_TYPE_OPTIONS,
  DisbursementType,
} from '@/store/interfaces/invoices'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'
import React from 'react'

export interface DisbursementSelectorProps {
  label?: string
  placeholder?: string
  value?: DisbursementType | null
  onChange: (value: DisbursementType | null) => void
}

export function DisbursementSelector({
  label = 'Select Disbursement',
  placeholder = 'Disbursement',
  value,
  onChange,
}: DisbursementSelectorProps) {
  return (
    <Stack
      spacing={1}
      sx={{
        width: '100%',
        maxWidth: '20rem',
        '.MuiInputBase-input.MuiOutlinedInput-input ': {
          py: '6px !important',
        },
      }}
    >
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
      >
        {label}
      </Typography>
      <Autocomplete
        disablePortal
        size="small"
        id="disbursement-type-selector"
        options={DISBURSEMENT_TYPE_OPTIONS}
        value={value}
        popupIcon={<KeyboardArrowDownRounded />}
        onChange={(_, newValue) => {
          onChange(newValue)
        }}
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '3px !important',
          },
          background: '#FFFFFF',
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          },
          borderRadius: '0.5rem',
          color: '#667085',
        }}
        getOptionLabel={(option) =>
          option
            .replace(/_/g, ' ')
            .toLocaleLowerCase('en-US')
            .replace(/\b\w/g, (c) => c.toUpperCase())
        }
        renderInput={(params) => (
          <TextField
            hiddenLabel
            placeholder={placeholder}
            {...params}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '0.5rem',
              },
            }}
          />
        )}
      />
    </Stack>
  )
}
