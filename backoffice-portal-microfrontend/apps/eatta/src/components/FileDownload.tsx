/**
 * <AUTHOR> on 15/07/2025
 */
import { Box, Button, Stack, Typography } from '@mui/material'
import { ExcelIcon, PdfIcon } from '@dtbx/ui/icons'
import React, { FC } from 'react'
import { downloadOnboardingDocuments } from '@/store/actions'
import { useAppDispatch } from '@/store'
import { DownloadPng } from '@/components/SvgIcons/DownloadPng'

interface FileDownloadProps {
  fileUrl: string
  fileName: string
  fileExtension?: 'pdf' | 'excel' | 'image'
}

export const FileDownload: FC<FileDownloadProps> = ({
  fileUrl,
  fileName,
  fileExtension = 'pdf',
}) => {
  const dispatch = useAppDispatch()

  const handleDownload = async () => {
    await downloadOnboardingDocuments(dispatch, fileUrl, true)
  }

  const renderFilePreview = () => {
    switch (fileExtension) {
      case 'pdf':
        return <PdfIcon />
      case 'excel':
        return <ExcelIcon />
      case 'image':
        return <DownloadPng />
      default:
    }
  }

  return (
    <Stack flexDirection="row" alignItems="center" gap={1}>
      {renderFilePreview()}
      <Box>
        <Typography fontWeight="600">{fileName}</Typography>
        <Button
          variant="text"
          onClick={handleDownload}
          sx={{
            padding: '0px',
            color: '#1976d2 !important',
            textDecoration: 'underline',
            textDecorationColor: '#1976d2',
          }}
        >
          Download
        </Button>
      </Box>
    </Stack>
  )
}
