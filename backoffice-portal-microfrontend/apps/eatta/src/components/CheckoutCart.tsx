/**
 * <AUTHOR> on 05/05/2025
 */
import { Box, Button, CircularProgress } from '@mui/material'
import { LinkExternalIcon, ShoppingCartIcon } from '@dtbx/ui/icons'
import { AccessWrapper } from '@/components/AccessHelper'
import React, { FC, useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getRoutePath } from '@/utils/routePath'
import { CheckoutType, CreateInvoicePayload } from '@/store/interfaces'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { createInvoiceEslip } from '@/store/actions'
import { resetBrokerInvoicesResponse } from '@/store/reducers'
import { resetInvoicesStore } from '@/store/reducers'

interface CheckoutCartProps {
  checkoutType: CheckoutType
}
export const CheckoutCart: FC<CheckoutCartProps> = ({ checkoutType }) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [lotsCount, setLotCount] = useState(0)

  const { selectedInvoices, selectedInvoiceLots, isCheckingOut } =
    useAppSelector((state) => state.invoices)

  const textColor = !lotsCount || isCheckingOut ? '#98A2B3' : '#26b43b'

  const showCheckout = (eslipId: string) => {
    router.push(getRoutePath(`/invoices/checkout/${eslipId}`))
  }

  const handleCheckout = () => {
    let itemIds = selectedInvoiceLots.map((lot) => lot.id)
    if (checkoutType === 'INVOICE') {
      itemIds = selectedInvoices.map((invoice) => invoice.id)
    }
    const payload: CreateInvoicePayload = {
      itemIds,
      itemType: checkoutType,
    }
    createInvoiceEslip(dispatch, payload, showCheckout)
  }

  const setCount = () => {
    let count = 0
    if (checkoutType === 'INVOICE') {
      count = selectedInvoices
        .map((invoice) => invoice.lotCount)
        .reduce((acc, current) => acc + current, 0)
    }

    if (checkoutType === 'INVOICE_ENTRY') {
      count = selectedInvoiceLots.length
    }

    setLotCount(count)
  }

  useEffect(() => {
    setCount()
  }, [selectedInvoices, selectedInvoiceLots, checkoutType])

   //Clear selected invoice
   useEffect(() => {
    return () => {
      dispatch(resetInvoicesStore())
      dispatch(resetBrokerInvoicesResponse())
    }
  }, [])

  return (
    <AccessWrapper clientTypes={['Buyer']} backofficeAccess={false}>
      <Box
        sx={{
          height: 52,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          p: 2,
          border: '1px solid',
          borderRadius: '8px',
          justifyContent: 'space-between',
          color: textColor,
          fontWeight: 'bold',
        }}
      >
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <ShoppingCartIcon stroke={textColor} />
          <span style={{ marginLeft: '4px' }}>{lotsCount} Lots in cart</span>
        </span>
        <Button
          disabled={!lotsCount || isCheckingOut}
          endIcon={
            isCheckingOut ? (
              <CircularProgress
                sx={{ color: textColor }}
                size={20}
                thickness={3.0}
              />
            ) : (
              <LinkExternalIcon stroke={textColor} />
            )
          }
          sx={{
            padding: '8px 14px',
            border: `1px solid ${textColor}`,
            borderWidth: '1px',
            fontWeight: 'bold',
            color: textColor,
          }}
          onClick={handleCheckout}
        >
          {isCheckingOut ? 'Loading' : 'Checkout'}
        </Button>
      </Box>
    </AccessWrapper>
  )
}
