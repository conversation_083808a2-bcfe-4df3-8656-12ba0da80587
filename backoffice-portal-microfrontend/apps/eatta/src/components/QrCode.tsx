import { QRCodeRenderersOptions } from 'qrcode'
import { useEffect, useRef } from 'react'
import QRCode from 'qrcode'

export interface QrCodeProps {
  data: string
  options?: QRCodeRenderersOptions
}

const QrCode = ({ data, options }: QrCodeProps) => {
  const qrCodeRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    if (qrCodeRef.current) {
      QRCode.toCanvas(qrCodeRef.current, data, options)
    }
  }, [qrCodeRef, data, options])
  return <canvas ref={qrCodeRef} />
}

export default QrCode
