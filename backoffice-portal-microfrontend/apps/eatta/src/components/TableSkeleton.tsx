import React from 'react'
import {
  Stack,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Paper,
  TableRow,
} from '@mui/material'

export type TableSkeletonProps = {
  rowCount?: number
  columnCount?: number
}

const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rowCount = 10,
  columnCount = 8, 
}) => {
  return (
    <Stack spacing={3} useFlexGap>
      <Stack>
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            '& .MuiTableCell-root': {
              paddingInline: '1.5rem',
              paddingBlock: '0.5rem',
              textAlign: 'left',
            },
          }}
        >
          <Table sx={{ minWidth: 650 }} size="small">
            <TableBody>
              {Array.from({ length: rowCount }, (_, rowIndex) => (
                <TableRow key={rowIndex}>
                  {Array.from({ length: columnCount }, (_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <Skeleton variant="text" width={80} height={24} />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Stack>
    </Stack>
  )
}

export default TableSkeleton
