/**
 * <AUTHOR> on 10/06/2025
 */
import { Stack, Tooltip, Typography } from '@mui/material'
import { HelpIcon } from '@dtbx/ui/icons'
import React from 'react'

export const TooltipTableCell = ({
  error,
  value,
}: {
  error?: string | null
  value: string | number
}) => {
  if (!error) {
    return <Typography>{value}</Typography>
  }

  return (
    <Tooltip title={error}>
      <Stack
        direction="row"
        alignItems="center"
        spacing={1}
        useFlexGap
        justifyContent="flex-start"
      >
        <HelpIcon stroke="#D92D20" />
        <Typography color="#D92D20">{value}</Typography>
      </Stack>
    </Tooltip>
  )
}
