import { getCompanies } from '@/store/actions'
import { <PERSON>complete, Stack, <PERSON>Field, Typography } from '@mui/material'
import React, { FC, useEffect, useState, JSX } from 'react'
import { useDispatch } from 'react-redux'
import { useAppSelector } from '@/store'
import { Company, CompanyFilters, CompanyType } from '@/store/interfaces'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'

export interface CompanySelectorProps {
  type: CompanyType
  label?: string
  placeholder?: string
  value?: string | null
  onChange: (value: string | null) => void
  disabled?: boolean
  error?: boolean
  helperText?: string
  autoSelectFirst?: boolean
}

export const CompanySelector: FC<CompanySelectorProps> = ({
  type,
  label,
  placeholder,
  value,
  onChange,
  disabled = false,
  error = false,
  helperText = '',
  autoSelectFirst = false,
}) => {
  const dispatch = useDispatch()
  const { companiesResponse } = useAppSelector((state) => state.companies)
  const [companies, setCompanies] = useState<Company[]>([])
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false)

  const defaultLabel = label || `Select ${type}`
  const defaultPlaceholder = placeholder || `Select ${type}..`

  useEffect(() => {
    const filters: CompanyFilters = {
      page: 1,
      size: 100,
      type: type,
    }
    setIsLoadingCompanies(true)
    getCompanies(dispatch, filters)
  }, [dispatch, type])

  useEffect(() => {
    if (isLoadingCompanies && companiesResponse.data.length > 0) {
      const filteredCompanies = companiesResponse.data.filter(
        (company) => company.code && company.name && company.type === type
      )
      if (filteredCompanies.length > 0) {
        setCompanies(filteredCompanies)
        setIsLoadingCompanies(false)

        if (autoSelectFirst && filteredCompanies.length > 0 && !value) {
          const first = filteredCompanies[0]
          setSelectedCompany(first)
          onChange(first.code)
        }
      }
    }
  }, [
    companiesResponse.data,
    isLoadingCompanies,
    type,
    autoSelectFirst,
    value,
    onChange,
  ])

  useEffect(() => {
    if (value) {
      const match = companies.find((company) => company.code === value) || null
      setSelectedCompany(match)
    }
  }, [value, companies])

  return (
    <Stack
      spacing={1}
      sx={{
        width: '100%',
      }}
    >
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 600, fontSize: '1rem' }}
      >
        {defaultLabel}
      </Typography>
      <Autocomplete
        disablePortal
        size="small"
        id={`${type.toLowerCase()}-type-selector`}
        loading={isLoadingCompanies}
        disabled={disabled || isLoadingCompanies}
        options={companies}
        popupIcon={<KeyboardArrowDownRounded />}
        getOptionLabel={(option) => `${option.name}`}
        value={selectedCompany}
        onChange={(_, newValue) => {
          setSelectedCompany(newValue)
          onChange(newValue?.code ?? null)
        }}
        sx={{
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '3px !important',
          },
          background: '#FFFFFF',
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          },
          borderRadius: '0.5rem',
          color: '#667085',
        }}
        renderInput={(params) => (
          <TextField
            hiddenLabel
            placeholder={defaultPlaceholder}
            error={error}
            helperText={helperText}
            {...params}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '0.5rem',
              },
            }}
          />
        )}
        renderOption={(props, option) => (
          <li {...props} key={option.id}>
            <Stack>
              <Typography variant="body2" fontWeight={500}>
                {option.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Code: {option.code}
              </Typography>
            </Stack>
          </li>
        )}
      />
    </Stack>
  )
}
