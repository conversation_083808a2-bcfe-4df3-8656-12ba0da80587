import React, { FocusEvent } from 'react'
import {
  Select,
  MenuItem,
  SelectChangeEvent,
  FormControl,
  FormHelperText,
} from '@mui/material'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'

interface AuctionOption {
  id: string | number
  auctionDate: string
}

interface CustomDateSelectPickerProps {
  value: string
  onChange: (event: SelectChangeEvent) => void
  options: AuctionOption[]
  touched?: boolean
  error?: string
  helperText?: string
  onBlur?: (
    e: FocusEvent<HTMLInputElement | HTMLTextAreaElement, Element>
  ) => void
  required?: boolean
  placeholder?: string
  slotProps?: {
    textField?: {
      sx?: Record<string, any>
      [key: string]: any
    }
  }
  slots?: {
    openPickerIcon?: () => React.JSX.Element
  }
  sx?: Record<string, any>
}

export const CustomDateSelectPicker: React.FC<CustomDateSelectPickerProps> = ({
  value,
  onChange,
  options,
  touched = false,
  error,
  helperText,
  onBlur,
  required = false,
  placeholder = 'Select Sale Date',
  slotProps,
  slots,
  sx,
  ...props
}) => {
  return (
    <FormControl fullWidth error={touched && Boolean(error)}>
      <Select
        required={required}
        fullWidth
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        displayEmpty
        error={touched && Boolean(error)}
        IconComponent={() =>
          slots?.openPickerIcon ? (
            slots.openPickerIcon()
          ) : (
            <KeyboardArrowDownRounded
              sx={{ color: '#667085', marginRight: '0.5rem' }}
            />
          )
        }
        {...props}
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          color: '#667085',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '7px !important',
          },
          ...(sx || {}),
          ...(slotProps?.textField?.sx || {}),
        }}
      >
        <MenuItem value="" disabled>
          {placeholder}
        </MenuItem>
        {options.map((schedule) => (
          <MenuItem key={schedule.id} value={schedule.auctionDate}>
            {schedule.auctionDate}
          </MenuItem>
        ))}
      </Select>
      {touched && error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}
