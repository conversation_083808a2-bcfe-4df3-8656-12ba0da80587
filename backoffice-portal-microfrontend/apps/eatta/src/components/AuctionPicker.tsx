import React, { FocusEvent, useEffect, useMemo } from 'react'
import {
  Autocomplete,
  CircularProgress,
  FormControl,
  FormHelperText,
  TextField,
} from '@mui/material'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'
import { getAuctionSchedule } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { AuctionSchedule } from '@/store/interfaces'
import { getAuctionWeek } from '@dtbx/store/utils'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

export interface CustomDateSelectPickerProps {
  year?: string
  value: AuctionSchedule | null
  onChange: (schedule: AuctionSchedule | null) => void
  touched?: boolean
  error?: string
  helperText?: string
  onBlur?: (
    e: FocusEvent<HTMLInputElement | HTMLTextAreaElement, Element>
  ) => void
  required?: boolean
  placeholder?: string
  slotProps?: {
    textField?: {
      sx?: Record<string, any>
      [key: string]: any
    }
  }
  slots?: {
    openPickerIcon?: () => React.JSX.Element
  }
  sx?: Record<string, any>
  isAutocomplete?: boolean
}

export const AuctionPicker: React.FC<CustomDateSelectPickerProps> = ({
  year = new Date().getFullYear().toString(),
  value,
  onChange,
  touched = false,
  error,
  helperText,
  onBlur,
  required = false,
  placeholder = 'Select Sale Date',
  slotProps,
  slots,
  sx,
  isAutocomplete = false,
  ...props
}) => {
  const { auctionScheduleResponse, isLoadingSchedules } = useAppSelector(
    (state) => state.salesSchedule
  )
  const { data } = auctionScheduleResponse
  const dispatch = useAppDispatch()
  const isBackoffice = useMemo(() => checkIfBackOffice(), [])

  const setInitialSelection = () => {
    const week = getAuctionWeek().toString()
    let currentAuction = data.find(
      (schedule) =>
        schedule.saleCode === (!value ? week : value.saleCode) &&
        schedule.year.toString() === year
    )

    if (currentAuction) {
      onChange(currentAuction)
    }
  }

  useEffect(() => {
    getAuctionSchedule(dispatch, isBackoffice, {
      year,
      page: 1,
      size: 55,
    })
  }, [year, dispatch, isBackoffice])

  useEffect(() => {
    if (!data || data.length === 0) return
    setInitialSelection()
  }, [data])

  return (
    <FormControl fullWidth error={touched && Boolean(error)}>
      <Autocomplete
        disablePortal
        size="small"
        id="auction-schedule-selector"
        loading={isLoadingSchedules}
        loadingText="Loading..."
        options={data}
        value={value}
        onChange={(_, newValue) => {
          onChange(newValue)
        }}
        popupIcon={
          isLoadingSchedules ? (
            <CircularProgress color="primary" size={16} />
          ) : (
            <KeyboardArrowDownRounded />
          )
        }
        getOptionKey={(option) => `${option.year}-${option.saleCode}`}
        getOptionLabel={(option) => `${option.year}/${option.saleCode}`}
        isOptionEqualToValue={(option, value) =>
          option.saleCode === value.saleCode
        }
        renderInput={(params) => (
          <TextField
            hiddenLabel
            {...params}
            placeholder={placeholder}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '0.5rem',
              },
            }}
          />
        )}
        slotProps={{
          popper: {
            sx: {
              '& .MuiAutocomplete-listbox': {
                maxHeight: '12rem',
              },
            },
          },
        }}
      />
      {touched && error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}
