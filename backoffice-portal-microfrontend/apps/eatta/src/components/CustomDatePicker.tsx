'use client'

import React, { FocusEvent } from 'react'
import { DatePicker, DatePickerProps } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import dayjs, { type Dayjs } from 'dayjs'

interface CustomDatePickerProps
  extends Omit<DatePickerProps<Dayjs, false>, 'onChange' | 'value'> {
  onDateChanged: (date: string | null) => void
  label?: string
  required: boolean
  value: string
  error: boolean
  helperText?: string
  hiddenLabel?: boolean
  onBlur?: (
    e: FocusEvent<HTMLInputElement | HTMLTextAreaElement, Element>
  ) => void
  slotProps?: {
    textField?: object
  }
  slots?: {
    openPickerIcon?: () => React.JSX.Element
  }
}

const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  onDateChanged,
  label,
  required,
  value,
  error,
  helperText,
  hiddenLabel,
  onBlur,
  slotProps,
  slots,
  ...props
}) => {
  const [selectedDate, setSelectedDate] = React.useState<Dayjs | null>(
    value ? dayjs(value) : null
  )

  React.useEffect(() => {
    setSelectedDate(value ? dayjs(value) : null)
  }, [value])

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        label={hiddenLabel ? undefined : label}
        value={selectedDate}
        onChange={(date: Dayjs | null) =>
          onDateChanged(date ? date.format('YYYY-MM-DD') : null)
        }
        slotProps={{
          textField: {
            required: required,
            error: error,
            size: 'small',
            fullWidth: true,
            helperText: helperText,
            onBlur: onBlur,
            ...(slotProps?.textField || {}),
          },
        }}
        {...(slots || {})}
        {...props}
      />
    </LocalizationProvider>
  )
}

export default CustomDatePicker
