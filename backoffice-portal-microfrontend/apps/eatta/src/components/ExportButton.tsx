import { useAppSelector } from '@/store'
import { ExportIcon, FileDownloadIcon } from '@dtbx/ui/components/SvgIcons'
import Button from '@mui/material/Button'
import React from 'react'

interface ExportButtonProps {
  onClick: () => void
  ButtonText: string
  disabled?: boolean
  isLoading?: boolean
}

export const ExportButton: React.FC<ExportButtonProps> = ({
  onClick,
  ButtonText,
  isLoading,
}) => {
  const [isHovered, setIsHovered] = React.useState(false)

  return (
    <Button
      onClick={onClick}
      type="submit"
      endIcon={
        isLoading ? (
          <FileDownloadIcon stroke={isHovered ? '#FFFFFF' : '#029327'} />
        ) : (
          <ExportIcon stroke={isHovered ? '#FFFFFF' : '#029327'} />
        )
      }
      size="medium"
      variant="contained"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{
        border: isHovered ? 'transparent' : '1px solid #73FF96',
        borderRadius: '8px',
        backgroundColor: 'transparent',
        color: 'primary.main',
        paddingX: '44px',
        paddingY: '8px',
        fontSize: '1rem',
        fontWeight: 600,
        textTransform: 'none',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textWrap: 'noWrap',
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
        '&:hover': {
          bgcolor: 'primary.main',
          color: '#FFFFFF',
          '& svg': {
            stroke: '#FFFFFF',
          },
        },
      }}
    >
      {ButtonText}
    </Button>
  )
}
