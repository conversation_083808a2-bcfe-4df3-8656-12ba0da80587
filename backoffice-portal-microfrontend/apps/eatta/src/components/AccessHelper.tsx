/**
 * <AUTHOR> on 10/01/2025
 */

import React, { ReactNode } from 'react'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { useAppSelector } from '@/store'
import { ClientType } from '@dtbx/store/interfaces'

type AccessWrapperProps = {
  clientTypes?: ClientType[]
  backofficeAccess: boolean
  children: ReactNode
}

export const AccessWrapper: React.FC<AccessWrapperProps> = ({
  clientTypes = [],
  backofficeAccess,
  children,
}) => {
  const { decodedToken } = useAppSelector((state) => state.auth)
  const clientType = decodedToken.clientType
  const hasAccess = checkIfBackOffice()
    ? backofficeAccess
    : clientType && clientTypes.includes(clientType)
  return hasAccess ? children : null
}
