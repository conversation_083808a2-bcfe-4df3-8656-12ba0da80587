import {
  Stack,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  Typography,
  SelectChangeEvent,
} from '@mui/material'
import React from 'react'
import { UnitType, PER_UNIT_OPTIONS } from '@/store/interfaces/warehouse'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'

export interface PerUnitSelectorProps {
  label?: string
  placeholder?: string
  value?: UnitType | null
  error?: boolean
  helperText?: string
  onChange?: (value: UnitType | null) => void
}

export function PerUnitSelector({
  label = 'Per Unit',
  placeholder = 'Select Unit',
  value,
  error = false,
  helperText = '',
  onChange,
}: PerUnitSelectorProps) {
  const handleChange = (event: SelectChangeEvent<string>) => {
    const selectedValue = event.target.value as UnitType
    onChange?.(selectedValue || null)
  }

  return (
    <Stack
      spacing={1}
      sx={{
        width: '100%',
        maxWidth: '20rem',
      }}
    >
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 600, fontSize: '1rem' }}
      >
        {label}
      </Typography>
      <FormControl
        size="small"
        error={error}
        sx={{
          width: '100%',
          background: '#FFFFFF',
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
              borderRadius: '0.5rem !important',
            },
          },
        }}
      >
        <Select
          IconComponent={(iconProps) => (
            <KeyboardArrowDownRounded
              {...iconProps}
              sx={{
                color: '#667085',
                marginRight: '0.5rem',
                ...(iconProps?.sx || {}),
              }}
            />
          )}
          value={value || ''}
          onChange={handleChange}
          displayEmpty
          disabled={!onChange}
          sx={{
            '.MuiSelect-select': {
              py: '12px !important',
            },
            color: '#667085',
          }}
        >
          <MenuItem value="" disabled>
            <Typography component="em" style={{ color: '#9CA3AF' }}>
              {placeholder}
            </Typography>
          </MenuItem>
          {PER_UNIT_OPTIONS.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>
        {helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>
    </Stack>
  )
}
