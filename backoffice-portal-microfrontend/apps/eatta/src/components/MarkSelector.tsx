import { getFactories } from '@/store/actions'
import { <PERSON>complete, Stack, TextField, Typography } from '@mui/material'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useAppSelector } from '@/store'
import { FactoryFilters, FactoryResponse } from '@/store/interfaces/factory'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

export interface MarkerSelectorProps {
  label?: string
  placeholder?: string
  value?: string | null
  onChange: (value: string | null) => void
}

export function MarkSelector({
  label = 'Select Mark',
  placeholder = 'Select Mark',
  value,
  onChange,
}: MarkerSelectorProps) {
  const dispatch = useDispatch()
  const { factoryResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )

  const isBackOffice = checkIfBackOffice()
  const [marks, setMarks] = useState<FactoryResponse[]>([])
  const [selectedMark, setSelectedMark] = useState<FactoryResponse | null>(null)

  const organizationId = ''

  useEffect(() => {
    const filters: FactoryFilters = {
      page: 1,
      size: 100,
      parentOrganizationId: organizationId,
    }
    getFactories(dispatch, isBackOffice, filters)
  }, [dispatch, isBackOffice, organizationId])

  useEffect(() => {
    const marksOnly = factoryResponse.data.filter((f) => f.factoryName)
    setMarks(marksOnly)

    if (marksOnly.length > 0 && !value) {
      const first = marksOnly[0]
      setSelectedMark(first)
      onChange(first.factoryName)
    }
  }, [factoryResponse.data])

  useEffect(() => {
    if (value) {
      const match = marks.find((m) => m.factoryName === value) || null
      setSelectedMark(match)
    }
  }, [value, marks])

  return (
    <Stack
      spacing={1}
      sx={{
        width: '100%',
        '.MuiInputBase-input.MuiOutlinedInput-input ': {
          py: '6px !important',
        },
      }}
    >
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
      >
        {label}
      </Typography>
      <Autocomplete
        disablePortal
        size="small"
        id="mark-selector"
        loading={isLoading}
        options={marks}
        getOptionLabel={(option) => option.factoryName}
        value={selectedMark}
        onChange={(_, newValue) => {
          setSelectedMark(newValue)
          onChange(newValue?.factoryName ?? null)
        }}
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '3px !important',
          },
          background: '#FFFFFF',
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          },
          borderRadius: '0.5rem',
          color: '#667085',
        }}
        renderInput={(params) => (
          <TextField
            hiddenLabel
            placeholder={placeholder}
            {...params}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '0.5rem',
              },
            }}
          />
        )}
      />
    </Stack>
  )
}
