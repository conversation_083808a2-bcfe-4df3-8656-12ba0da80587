'use client'
import React, { ReactNode } from 'react'
import AppProvider from '@/store/AppProvider'
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import { InActivity } from '@dtbx/ui/components'
import { isLoggedIn } from '@dtbx/store/utils'
import './global.css'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
            <ThemeConfig themeType={'eatta'}>{children}</ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  )
}
