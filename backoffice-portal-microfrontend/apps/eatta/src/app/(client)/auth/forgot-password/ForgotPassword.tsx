import {
  <PERSON>,
  Button,
  CircularProgress,
  <PERSON>ack,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { handleForgotPassword } from '@/store/actions/eattaAuth'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { CheckIcon } from '@dtbx/ui/components/SvgIcons'
import { Logo } from '@/components/SvgIcons/Logo'

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Please enter a valid email')
    .required('Email should not be empty'),
  companyCode: Yup.string().required('Company Code should not be empty'),
})

export const ForgotPassword = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { isLoadingLogin } = useAppSelector((store) => store.auth)
  const [isSuccess, setIsSuccess] = useState(false)

  const handleSuccess = () => {
    setIsSuccess(true)
  }

  const formik = useFormik({
    initialValues: {
      email: '',
      companyCode: '',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      handleForgotPassword(
        values.email,
        values.companyCode,
        dispatch,
        handleSuccess
      )
      setTimeout(() => {
        router.push('/auth')
      }, 5000)
    },
  })
  return (
    <Stack
      sx={{
        gap: '2rem',
      }}
    >
      {isSuccess ? (
        <Stack
          spacing={13}
          justifyContent="center"
          alignItems="center"
          sx={{ mb: 50 }}
        >
          <Logo />
          <Stack
            direction="column"
            alignItems="center"
            spacing={3}
            sx={{ width: '100%' }}
          >
            <CheckIcon />
            <Typography variant="h5" fontWeight="600" sx={{ fontSize: '30px' }}>
              Success!
            </Typography>
            <Typography
              variant="body2"
              fontWeight="500"
              textAlign="center"
              sx={{ color: '#000A12', fontSize: '16px' }}
            >
              If your email exists in our records, you will receive a link to
              reset your password.
            </Typography>
          </Stack>
        </Stack>
      ) : (
        <Stack
          spacing={3}
          sx={{
            width: '100%',
            mb: 35,
          }}
        >
          <Stack
            direction="column"
            alignItems="center"
            spacing={10}
            useFlexGap
            sx={{ mb: 9 }}
          >
            <Logo />

            <Stack
              direction="column"
              alignItems="center"
              spacing={1}
              useFlexGap
            >
              <Typography variant="h5" fontWeight="600">
                Enter your email
              </Typography>
              <Typography variant="body2" fontWeight="300" textAlign="center">
                Please enter your email so we can send you a link to reset your
                password.
              </Typography>
            </Stack>
          </Stack>

          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <Stack sx={{ width: '100%', height: '100%' }}>
                <Box>
                  <Typography
                    variant="body2"
                    component={'label'}
                    sx={{ color: '#344054', fontWeight: 500 }}
                  >
                    Organization Code
                  </Typography>
                  <TextField
                    hiddenLabel
                    size="small"
                    type="text"
                    placeholder=""
                    sx={{ marginBottom: '1rem' }}
                    {...formik.getFieldProps('companyCode')}
                    fullWidth
                    error={Boolean(
                      formik.touched.companyCode && formik.errors.companyCode
                    )}
                    helperText={
                      formik.touched.companyCode && formik.errors.companyCode
                    }
                  />
                </Box>
                <Box>
                  <Typography
                    variant="body2"
                    component={'label'}
                    sx={{ color: '#344054', fontWeight: 500 }}
                  >
                    Email
                  </Typography>
                  <TextField
                    hiddenLabel
                    size="small"
                    type="text"
                    placeholder="<EMAIL>"
                    sx={{ marginBottom: '1rem' }}
                    {...formik.getFieldProps('email')}
                    fullWidth
                    error={Boolean(formik.touched.email && formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Box>

                <Button
                  disabled={isLoadingLogin}
                  variant="contained"
                  type="submit"
                  sx={{
                    width: '100%',
                  }}
                  endIcon={
                    isLoadingLogin ? (
                      <CircularProgress size={20} thickness={3.0} />
                    ) : undefined
                  }
                >
                  Continue
                </Button>
              </Stack>
            </Form>
          </FormikProvider>
        </Stack>
      )}
    </Stack>
  )
}
