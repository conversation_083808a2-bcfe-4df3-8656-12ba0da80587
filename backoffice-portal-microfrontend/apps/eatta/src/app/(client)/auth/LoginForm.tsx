'use client'

import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  Box,
  Button,
  CircularProgress,
  InputAdornment,
  Link,
  Stack,
  TextField,
  Typography,
} from '@mui/material'

import { CustomCheckBox } from '@/components/CustomInputs'
import { Logo } from '@/components/SvgIcons/Logo'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import React, { useState } from 'react'
import { VisibilityOffOutlined, VisibilityOutlined } from '@mui/icons-material'
import { handleClientLogin } from '@/store/actions/eattaAuth'
import { useAppDispatch, useAppSelector } from '@/store'
import { ALPHA_NUMERIC_REGEX } from '@/utils/validators'
import { ChevronRightIconSingle } from '@dtbx/ui/components/SvgIcons'

const validationSchema = Yup.object({
  organizationCode: Yup.string()
    .required('Organization Code should not be empty')
    .matches(ALPHA_NUMERIC_REGEX, 'Only alphanumeric characters are allowed'),
  username: Yup.string().required('Username should not be empty'),
  password: Yup.string()
    .required('Password should not be empty')
    .min(8, 'Password should be at least 8 characters')
    .matches(
      /[!@#$%^&*(),.?":{}|<>]/,
      'Password contain one special character'
    ),
})

export const LoginForm = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { isLoadingLogin } = useAppSelector((store) => store.auth)

  const [togglePass, setTogglePass] = useState<boolean>(false)

  const formik = useFormik({
    initialValues: {
      organizationCode: '',
      username: '',
      password: '',
      acceptedTerms: false,
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      handleClientLogin(values, dispatch, router)
    },
  })

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        maxWidth: '22.5rem',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '2rem',
      }}
    >
      <Stack direction="column" alignItems="center" spacing={5} useFlexGap>
        <Logo />

        <Stack direction="column" alignItems="center" spacing={1} useFlexGap>
          <Typography variant="h5" fontWeight="600">
            Log in to your account
          </Typography>
          <Typography variant="body2" fontWeight="300" textAlign="center">
            Welcome back! Please enter your details.
          </Typography>
        </Stack>
      </Stack>

      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <Stack spacing={3} useFlexGap>
            <Box>
              <Typography variant="body2" component={'label'}>
                Organization Code
              </Typography>
              <TextField
                hiddenLabel
                size="small"
                type="text"
                sx={{ marginBottom: '1rem' }}
                {...formik.getFieldProps('organizationCode')}
                fullWidth
                error={Boolean(
                  formik.touched.organizationCode &&
                    formik.errors.organizationCode
                )}
                helperText={
                  formik.touched.organizationCode &&
                  formik.errors.organizationCode
                }
              />

              <Typography variant="body2" component={'label'}>
                Email
              </Typography>
              <TextField
                hiddenLabel
                size="small"
                type="text"
                sx={{ marginBottom: '1rem' }}
                {...formik.getFieldProps('username')}
                fullWidth
                error={Boolean(
                  formik.touched.username && formik.errors.username
                )}
                helperText={formik.touched.username && formik.errors.username}
              />

              <Typography variant="body2" component={'label'}>
                Password
              </Typography>
              <TextField
                hiddenLabel
                size="small"
                type={togglePass ? 'text' : 'password'}
                margin={'none'}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment
                        sx={{ cursor: 'pointer' }}
                        position="end"
                        onClick={() => setTogglePass(!togglePass)}
                      >
                        {togglePass ? (
                          <VisibilityOffOutlined fontSize={'small'} />
                        ) : (
                          <VisibilityOutlined fontSize={'small'} />
                        )}
                      </InputAdornment>
                    ),
                  },
                }}
                {...formik.getFieldProps('password')}
                fullWidth
                error={Boolean(
                  formik.touched.password && formik.errors.password
                )}
                helperText={formik.touched.password && formik.errors.password}
              />
            </Box>

            <Box
              sx={{
                flexDirection: 'column',
                display: 'flex',
                alignItems: 'start',
                gap: '0.625rem',
                width: '100%',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '5px',
                  cursor: 'pointer',
                }}
              >
                <CustomCheckBox
                  checked={formik.values.acceptedTerms}
                  onChange={() =>
                    formik.setFieldValue(
                      'acceptedTerms',
                      !formik.values.acceptedTerms
                    )
                  }
                  customColor={'#26b43b'}
                  style={{ padding: '2px' }}
                />
                <Typography
                  variant="body2"
                  onClick={() =>
                    formik.setFieldValue(
                      'acceptedTerms',
                      !formik.values.acceptedTerms
                    )
                  }
                >
                  I confirm that I have read and accept the{' '}
                  <Link
                    href="/terms"
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{ textDecoration: 'none' }}
                  >
                    Terms & Conditions
                  </Link>{' '}
                  and{' '}
                  <Link
                    href="https://dtbk.dtbafrica.com/privacy-policy"
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{ textDecoration: 'none' }}
                  >
                    Privacy Policy
                  </Link>
                  .
                </Typography>
              </Box>

              <Link
                href="/auth/forgot-password"
                variant="body2"
                sx={{ textDecoration: 'none' }}
              >
                Forgot password?
              </Link>
            </Box>
            <Button
              disabled={
                !formik.isValid ||
                isLoadingLogin ||
                !formik.values.acceptedTerms
              }
              variant="contained"
              type="submit"
              sx={{
                width: '100%',
              }}
              endIcon={
                isLoadingLogin ? (
                  <CircularProgress size={20} thickness={3.0} />
                ) : undefined
              }
            >
              Login
            </Button>
            <Button
              component="a"
              href="/customer-support"
              variant="outlined"
              type="submit"
              sx={{
                width: '100%',
                marginTop: '4px',
              }}
            >
              Contact Support <ChevronRightIconSingle />
            </Button>
          </Stack>
        </Form>
      </FormikProvider>
    </Box>
  )
}
