/**
 * <AUTHOR> on 02/07/2025
 */
import { ReactNode } from 'react'
import { Box } from '@mui/material'
import { ValidateFooter } from '@/features/delivery-orders/validate/ValidateFooter'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <Box
      height="100%"
      justifyContent="center"
      alignItems="center"
      display="flex"
      flexDirection="column"
    >
      <Box
        paddingInline={3}
        paddingBlock={5}
        display="flex"
        flexDirection="column"
        flex={'auto'}
        height="100%"
        maxWidth="425px"
        width="100%"
        sx={{ gap: '1rem' }}
      >
        {children}
        <ValidateFooter />
      </Box>
    </Box>
  )
}
