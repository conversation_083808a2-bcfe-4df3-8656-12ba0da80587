import { ValidatePage } from '@/features/delivery-orders/validate/ValidatePage'
import { getEsoByDocNumber } from '@/store/actions/Edo'

/**
 * <AUTHOR> on 02/07/2025
 */
const Page = async ({ params }: { params: Promise<{ edoNumber: string }> }) => {
  const { edoNumber } = await params
  const edoDetails = await getEsoByDocNumber(edoNumber)
  return <ValidatePage edo={edoDetails} />
}

export default Page
