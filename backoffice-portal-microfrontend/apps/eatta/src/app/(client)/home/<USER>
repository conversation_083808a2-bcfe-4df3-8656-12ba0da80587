'use client'

import React from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
} from '@mui/material'
import {
  Timeline as TimelineIcon,
  AttachMoney as MoneyIcon,
  Analytics as AnalyticsIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material'
import { useRouter } from 'next/navigation'
const features = [
  {
    icon: <TimelineIcon sx={{ fontSize: 40 }} />,
    title: 'Automated Settlements',
    description:
      'Upload post-sale catalogues and let Buni handle invoice generation and payment distribution automatically.',
  },
  {
    icon: <AnalyticsIcon sx={{ fontSize: 40 }} />,
    title: 'Real-time Tracking',
    description:
      'Track payments for every kilogram of tea sold with complete transparency and accuracy.',
  },
  {
    icon: <MoneyIcon sx={{ fontSize: 40 }} />,
    title: 'Cost Effective',
    description:
      'Reduce transaction costs and eliminate manual reconciliation processes.',
  },
]

const testimonials = [
  {
    quote:
      "With Buni, I no longer worry about payment delays. It's all automated, and I can trace every payment down to the last kilogram of tea.",
    author: '— KTDA Representative',
  },
  {
    quote:
      'The cost and time savings are incredible. And the analytics Buni provides help me make smarter decisions.',
    author: '— Tea Broker',
  },
]

export default function HomePage() {
  const router = useRouter()
  return (
    <Box>
      {/* Hero Section */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h1"
            gutterBottom
            sx={{ fontSize: '3.5rem', mb: 3 }}
          >
            Revolutionizing Tea Payment Settlements
          </Typography>
          <Typography variant="h5" sx={{ mb: 4, fontWeight: 300 }}>
            Streamlining payments for buyers, brokers, and producers in the East
            African Tea Trade Association.
          </Typography>
          <Button
            variant="contained"
            color="secondary"
            size="large"
            endIcon={<ArrowForwardIcon />}
            sx={{ py: 1.5, px: 4 }}
            onClick={() => router.push('/auth')} // Navigate to /auth on button click
          >
            Get Started
          </Button>
        </Container>
      </Box>

      {/* Features Section */}
      <Box sx={{ py: 8, bgcolor: 'grey.50' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            textAlign="center"
            gutterBottom
            sx={{ mb: 6 }}
          >
            Why Choose Us?
          </Typography>
          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card sx={{ height: '100%' }}>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Box sx={{ color: 'primary.main', mb: 2 }}>
                      {feature.icon}
                    </Box>
                    <Typography variant="h6" gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Testimonials Section */}
      <Box sx={{ py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            textAlign="center"
            gutterBottom
            sx={{ mb: 6 }}
          >
            What Our Users Say
          </Typography>
          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    bgcolor: 'secondary.light',
                    opacity: 0.9,
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Typography
                      variant="body1"
                      sx={{ mb: 2, fontStyle: 'italic' }}
                    >
                      {testimonial.quote}
                    </Typography>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      {testimonial.author}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
    </Box>
  )
}
