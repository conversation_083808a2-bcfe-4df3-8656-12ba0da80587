'use client'

import React, { ReactNode } from 'react'
import { ThemeProvider, createTheme } from '@mui/material/styles'

const theme = createTheme({
  palette: {
    primary: {
      main: '#2e7d32',
      light: '#4caf50',
      dark: '#1b5e20',
    },
    secondary: {
      main: '#f57c00',
      light: '#ffb74d',
      dark: '#e65100',
    },
  },
  typography: {
    fontFamily: '"Source Serif Pro", "DM Sans", sans-serif',
    h1: {
      fontFamily: '"Source Serif Pro", serif',
      fontWeight: 700,
    },
    body1: {
      fontFamily: '"DM Sans", sans-serif',
    },
  },
})

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <ThemeProvider theme={theme}>
      <>{children}</>
    </ThemeProvider>
  )
}
