'use client'

import { Box } from '@mui/material'
import React, { ReactNode } from 'react'
import { AuthWrapper, LocalNotification } from '@dtbx/ui/components'
import { isLoggedIn } from '@dtbx/store/utils'
import { clearNotification } from '@dtbx/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'

export default function Layout({ children }: { children: ReactNode }) {
  const dispatch = useAppDispatch()
  const { localNotification, localNotificationType } = useAppSelector(
    (state) => state.notifications
  )
  return (
    <AuthWrapper
      requiresAuth={false}
      isLoggedIn={isLoggedIn}
      loginUrl="/auth"
      homeUrl="/"
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-start',
          minHeight: '100vh',
          px: 2,
        }}
      >
        <LocalNotification
          clearNotification={() => dispatch(clearNotification())}
          notification={localNotification}
          notificationType={localNotificationType}
        />

        <Box sx={{ mt: 6, height: '100%', width: '100%', flexGrow: 1 }}>
          {children}
        </Box>
      </Box>
    </AuthWrapper>
  )
}
