'use client'
import { CommissionStatementsPage } from '@/features/statements/CommissionStatementsPage'
import { StatementsPage } from '@/features/statements/StatementsPage'
import { useAppSelector } from '@/store'

const Page = () => {
  const { decodedToken } = useAppSelector((state) => state.auth)
  const clientType = decodedToken?.clientType
  if (clientType === 'Broker') {
    return <CommissionStatementsPage />
  }
  return <StatementsPage />
}

export default Page
