'use client'
import React, { useEffect } from 'react'
import { But<PERSON>, <PERSON>ack } from '@mui/material'
import Grid from '@mui/material/Grid2'
import TrdInfoCard from './TrdInfoCard'
import { FolderDownloadIcon } from '@dtbx/ui/components/SvgIcons'
import { ArrowBackIos } from '@mui/icons-material'
import { getRoutePath } from '@/utils/routePath'
import { useAppDispatch } from '@dtbx/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useParams } from 'next/navigation'
import { downloadTRD, fetchTRD } from '@/store/actions'

const TRDPage = () => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const params = useParams()
  const brokerInvoiceEntryId = Array.isArray(params.brokerInvoiceEntryId)
    ? params.brokerInvoiceEntryId[0]
    : params.brokerInvoiceEntryId

  function handleNavigateToCatalogue() {
    router.replace(getRoutePath('/_sales'))
  }
  function triggerDownloadTRD(brokerInvoiceEntryId: string) {
    downloadTRD(dispatch, brokerInvoiceEntryId)
  }
  useEffect(() => {
    if (brokerInvoiceEntryId) {
      fetchTRD(dispatch, brokerInvoiceEntryId)
    }
  }, [brokerInvoiceEntryId, dispatch])

  return (
    <Stack
      flexGrow={1}
      paddingInline={{ xs: 2, sm: 2, md: 5 }}
      paddingTop={5}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <Grid
        sx={{ height: '100%' }}
        container
        alignItems="center"
        width="100%"
        justifyContent="center"
      >
        <Grid
          sx={{
            height: '100%',
          }}
          size={{ xs: 12, sm: 12, md: 8, lg: 8, xl: 8 }}
        >
          <Stack
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <Stack
              direction="row"
              spacing={4}
              sx={{
                justifyContent: 'space-between',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                mb: 2,
                px: 4,
              }}
            >
              <Button
                variant="text"
                type="submit"
                startIcon={<ArrowBackIos />}
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'transparent',
                  },
                }}
                onClick={() => handleNavigateToCatalogue()}
              >
                Back to Catalogue
              </Button>
              <Button
                variant="text"
                type="button"
                onClick={() => {
                  if (brokerInvoiceEntryId) {
                    triggerDownloadTRD(brokerInvoiceEntryId)
                  }
                }}
                endIcon={<FolderDownloadIcon />}
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'transparent',
                  },
                }}
              >
                Download TRD
              </Button>
            </Stack>
            <Stack
              sx={{
                height: '100%',
                overflow: 'auto',
                paddingBottom: 5,
                px: 8,
              }}
            >
              <TrdInfoCard brokerInvoiceEntryId={brokerInvoiceEntryId} />
            </Stack>
          </Stack>
        </Grid>
      </Grid>
    </Stack>
  )
}

export default TRDPage
