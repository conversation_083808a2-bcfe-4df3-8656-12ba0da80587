/**
 * <AUTHOR> on 28/02/2025
 */

/**
 * Convert an object to a query string
 * @param obj
 */
export function mapObjectToUrlParams<T extends Record<string, any>>(
  obj: T
): string {
  const searchParams = new URLSearchParams()
  Object.keys(obj).forEach((key) => {
    const value = obj[key as keyof T]
    if (typeof value === 'boolean') {
      searchParams.append(key, value)
      return
    }
    if (value) {
      searchParams.append(key, value.toString())
    }
  })

  return searchParams.toString()
}

/**
 * Remove falsy values from an object, this is useful when you want to remove undefined, null, empty strings from an object
 * Boolean values are exempted
 * @param obj
 */
export function removeFalsyValues<T extends object>(obj: T): Partial<T> {
  return Object.entries(obj).reduce<Partial<T>>((acc, [key, value]) => {
    if (value || typeof value === 'boolean') {
      acc[key as keyof T] = value
    }
    return acc
  }, {} as Partial<T>)
}

/**
 * Safely parses a JSON string.
 * @param jsonString The JSON string to parse.
 * @param defaultValue The value to return if parsing fails. Defaults to null.
 * @returns The parsed object, or the defaultValue if parsing fails.
 */
export function safeJsonParse<T = any>(
  jsonString: string | null | undefined,
  defaultValue: T | null = null
): T | null {
  if (jsonString === null || jsonString === undefined) {
    return defaultValue
  }
  try {
    return JSON.parse(jsonString) as T
  } catch (error) {
    console.error('Failed to parse JSON string:', error)
    return defaultValue
  }
}
