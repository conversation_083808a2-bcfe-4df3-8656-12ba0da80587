/**
 * <AUTHOR> on 14/05/2025
 */
import * as openpgp from 'openpgp'
import { openapi2 } from '@dtbx/store/utils'

/**
 * Encrypts and signs a binary input using the provided private and public keys.
 *
 * @param {File} file - The file to be encrypted and signed.
 * @param {string} privateKeyArmored - The armored private key used for signing the binary data.
 * @param {string} publicKeyArmored - The armored public key used for encrypting the binary data.
 * @param passphrase - The passphrase used to decrypt the private key.
 * @return {Promise<openpgp.WebStream<string>>} A promise that resolves to a WebStream containing the encrypted and signed output.
 */
export async function encryptAndSignFile(
  file: File,
  privateKeyArmored: string,
  publicKeyArmored: string,
  passphrase: string
): Promise<openpgp.WebStream<string>> {
  const publicKey = await openpgp.readKey({ armoredKey: publicKeyArmored })
  const privateKey = await openpgp.decryptKey({
    privateKey: await openpgp.readPrivateKey({ armoredKey: privateKeyArmored }),
    passphrase: passphrase,
  })
  const uint8Array = await file.bytes()
  const message = await openpgp.createMessage({
    binary: uint8Array,
  })
  return openpgp.encrypt({
    message,
    encryptionKeys: publicKey,
    signingKeys: privateKey,
  })
}

interface EncryptionResponse {
  encryptedFile: Blob
  error?: string
}

export const encryptFile = async (
  formData: FormData
): Promise<EncryptionResponse> => {
  try {
    const response = await openapi2.post<Blob>(
      '/v1/eatta-service/onboarding/encrypt',
      formData,
      {
        responseType: 'blob',
      }
    )

    return {
      encryptedFile: response.data,
    }
  } catch (error) {
    return {
      encryptedFile: new Blob(),
      error: error instanceof Error ? error.message : 'Failed to encrypt file',
    }
  }
}
