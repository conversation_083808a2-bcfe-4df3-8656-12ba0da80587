import { WorkBook } from 'xlsx'

/**
 * Checks if data exists in specified columns of an Excel workbook sheet starting from row 4
 * @param workbook - The Excel workbook to check
 * @param sheetName - Name of the sheet to check
 * @param columnRange - Excel column range (e.g., "A:C")
 * @returns boolean indicating if data exists
 * @throws Error if workbook or column range parameters are invalid
 */

export function hasDataInColumns(
  workbook: WorkBook,
  sheetName: string,
  columnRange: string,
  fromRow: number = 5
): boolean {
  if (!columnRange?.match(/^[A-Z]:[A-Z]$/)) {
    throw new Error('Column range must be in format "A:Z"')
  }

  try {
    // Check if the Maincatalogue sheet exists
    const sheet = workbook.Sheets[sheetName]
    if (!sheet) {
      return false
    }

    // Get the worksheet
    const [startCol, endCol] = columnRange.split(':')

    // Check if there's data in the columns, starting from row fromRow
    for (const cell in sheet) {
      // Check if the cell is in our column range
      if (cell[0] >= startCol && cell[0] <= endCol) {
        const rowMatch = cell.match(/[A-Z]+(\d+)/)
        if (rowMatch) {
          const rowNum = Number.parseInt(rowMatch[1])
          // Only consider rows fromRow and beyond
          if (
            rowNum >= fromRow &&
            sheet[cell].v !== undefined &&
            sheet[cell].v !== null
          ) {
            return true
          }
        }
      }
    }
    return false
  } catch (e) {
    const message = (e as Error).message
    console.error(`Error checking Excel columns: ${message}`)
    throw message
  }
}
