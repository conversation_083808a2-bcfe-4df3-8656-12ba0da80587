/**
 * <AUTHOR> on 27/02/2025
 */

export const ORG_NAME_REGEX = /^(?! )[a-zA-Z0-9\s\[\]()]*(?<! )$/

export const ALPHA_NUMERIC_REGEX = /^[a-zA-Z0-9]+$/
export const ALPHA_REGEX = /^[a-zA-Z]+$/
export const NUMERIC_REGEX = /^\d+$/
export const DECIMAL_NUMERIC_REGEX = /^(?:\d+(?:\.\d*)?|\.\d+)$/
export const ID_REGEX = /^[0-9]{1,12}$/
export const REGISTRATION_NO_REGEX = /^[a-zA-Z0-9\s-]+$/
export const COMMENTS_REGEX =/^[A-Za-z0-9 ]*[^ ]$/
