/**
 * <AUTHOR> on 23/12/2024
 */

import forge from 'node-forge'
import { fetchPublicKey } from '@/store/actions/eattaAuth'

/**
 * Converts a Base64 string to a PEM format.
 * @param key Base64 string to convert
 */
export function base64ToPem(key: string) {
  const pemHeader = '-----BEGIN PUBLIC KEY-----\n'
  const pemFooter = '\n-----END PUBLIC KEY-----'
  // Split into lines of 64 characters
  const pemContents = key.match(/.{1,64}/g)?.join('\n')
  return pemHeader + pemContents + pemFooter
}

/**
 * Converts a Base64 public key to a forge public key.
 * @param key Base64 key
 */
export function getPublicKeyFromBase64(key: string) {
  const pemKey = base64ToPem(key)
  return forge.pki.publicKeyFromPem(pemKey)
}

/**
 * Encrypts the given data using RSAES-PKCS1-V1_5 encryption with the provided public key.
 * @param data Data to encrypt
 */
export async function encryptData(data: string) {
  const key = await fetchPublicKey()
  const publicKey = getPublicKeyFromBase64(key)
  const encrypted = publicKey.encrypt(data, 'RSAES-PKCS1-V1_5')
  return forge.util.encode64(encrypted)
}
