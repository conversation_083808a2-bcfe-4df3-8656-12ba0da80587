/**
 * <AUTHOR> on 30/04/2025
 */

export const PRE_AUCTION_GROUP_BY = ['producer', 'factory'] as const

export const POST_AUCTION_GROUP_BY = ['buyer', 'warehouse', 'factory'] as const

export const COUNTRY_CODES: Record<string, string> = {
  KE: '+254',
  TZ: '+255',
  UG: '+256',
  BI: '+257',
}

export const EATTA_MODULES = {
  users: 'Eatta Users',
  commissions: 'Eatta Commissions',
  organizations: 'Eatta Organizations',
  defaults: 'Eatta Defaults',
  allocations: 'Eatta Invoice Payment Allocation',
} as const

export const ACCESS_CONTROLS = {
  //organization
  CREATE_ORGANIZATION: [
    'EATTA_SUPER_CREATE_ORGANIZATION',
    'EATTA_MAKE_CREATE_ORGANIZATION',
  ],

  SUPER_CREATE_ORGANIZATION: ['EATTA_SUPER_CREATE_ORGANIZATION'],

  SUPER_UPDATE_ORGANIZATION: ['EATTA_SUPER_UPDATE_ORGANIZATION'],

  UPDATE_ORGANIZATION: [
    'EATTA_SUPER_UPDATE_ORGANIZATION',
    'EATTA_MAKE_UPDATE_ORGANIZATION',
  ],

  ACCEPT_CREATE_ORGANIZATION: ['EATTA_ACCEPT_CREATE_ORGANIZATION'],
  REJECT_CREATE_ORGANIZATION: ['EATTA_REJECT_CREATE_ORGANIZATION'],

  //Users
  CREATE_USER: ['EATTA_SUPER_CREATE_USER', 'EATTA_MAKE_CREATE_USER'],

  SUPER_CREATE_USER: ['EATTA_SUPER_CREATE_USER'],

  SUPER_UPDATE_USER: ['EATTA_SUPER_UPDATE_USER'],

  UPDATE_USER: ['EATTA_MAKE_UPDATE_USER', 'EATTA_SUPER_UPDATE_USER'],

  ACCEPT_CREATE_USER: ['EATTA_ACCEPT_CREATE_USER'],
  REJECT_CREATE_USER: ['EATTA_REJECT_CREATE_USER'],

  //Commission
  CREATE_COMMISSION: [
    'EATTA_SUPER_CREATE_COMMISSION',
    'EATTA_MAKE_CREATE_COMMISSION',
  ],
  SUPER_CREATE_COMMISSION: ['EATTA_SUPER_CREATE_COMMISSION'],
  UPDATE_COMMISSION: ['EATTA_UPDATE_COMMISSION'],
  ACCEPT_COMMISSION: ['EATTA_ACCEPT_CREATE_COMMISSION'],
  REJECT_COMMISSION: ['EATTA_REJECT_CREATE_COMMISSION'],
  VIEW_COMMISSION: ['EATTA_VIEW_COMMISSION'],

  //Payments
  ALLOCATE_PAYMENT: [
    'EATTA_MAKE_ALLOCATE_INVOICE_PAYMENT',
    'EATTA_SUPER_ALLOCATE_INVOICE_PAYMENT',
  ],
  VIEW_PAYMENT: ['EATTA_VIEW_INVOICE_PAYMENT'],
  SUPER_ALLOCATE_PAYMENT: ['EATTA_SUPER_ALLOCATE_INVOICE_PAYMENT'],
  ACCEPT_ALLOCATE_PAYMENT: ['EATTA_ACCEPT_ALLOCATE_INVOICE_PAYMENT'],
  REJECT_ALLOCATE_PAYMENT: ['EATTA_REJECT_ALLOCATE_INVOICE_PAYMENT'],
  RETRY_TRANSACTION: ['EATTA_RETRY_TRANSACTION'],

  SETUP_WAREHOUSE_FEE: ['SETUP_WAREHOUSE_FEE'],
}
