{
  "extends": "@dtbx/typescript-config/nextjs.json",
  "compilerOptions": {
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"],
      "@public/*": ["./public/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "next.config.ts",
    "vitest.setup.ts",
    "next.config",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    ".next-client/types/**/*.ts"
  ],
  "exclude": [
    "node_modules"
  ],
}
