/**
 * <AUTHOR> on 19/03/2025
 */

import { describe, expect, it, vi } from 'vitest'
import forge from 'node-forge'
import {
  base64ToPem,
  encryptData,
  getPublicKeyFromBase64,
} from '@/utils/passEncryption'

const base64Key =
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArzK9+RN/0ciqcmbiDUvbaX4b9VvlIc1m334coUVv5fOBoN8Phg//YcUfvK0+fL+tXt5HzZ82BDZEEYvrAtGqB4E4uOovEZmM7OAttn/JdCq3Qhs3d4awLcmpC+FbATyJWIK0gPUHM2+oHUezqEOl07HrrnqaTBki0U/XkQ6RcZMbEer5PN97Id4vY81Y+gbXjZ1XaqT//VddUlw/jgyL0EV25jf2nIIuvW90<PERSON>+k/fCAKDhYkq+fBfgAHQMLMo8QdSBY7gIYtfh+/FaIXsZzA6Y/ctFY9xG67m5lce8ChelCjp5DCYHMyvsvmCe/QGdep9bGqtn3CYUsH7SgUSW4DwIDAQAB'

vi.mock('@/store/actions/eattaAuth', () => {
  return {
    fetchPublicKey: async () => base64Key,
  }
})

describe('Pass Encryption', () => {
  it('should convert base64 key to pem key', () => {
    const result = base64ToPem(base64Key)
    expect(result).toEqual(
      '-----BEGIN PUBLIC KEY-----\n' +
        'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArzK9+RN/0ciqcmbiDUvb\n' +
        'aX4b9VvlIc1m334coUVv5fOBoN8Phg//YcUfvK0+fL+tXt5HzZ82BDZEEYvrAtGq\n' +
        'B4E4uOovEZmM7OAttn/JdCq3Qhs3d4awLcmpC+FbATyJWIK0gPUHM2+oHUezqEOl\n' +
        '07HrrnqaTBki0U/XkQ6RcZMbEer5PN97Id4vY81Y+gbXjZ1XaqT//VddUlw/jgyL\n' +
        '0EV25jf2nIIuvW90IL+k/fCAKDhYkq+fBfgAHQMLMo8QdSBY7gIYtfh+/FaIXsZz\n' +
        'A6Y/ctFY9xG67m5lce8ChelCjp5DCYHMyvsvmCe/QGdep9bGqtn3CYUsH7SgUSW4\n' +
        'DwIDAQAB\n' +
        '-----END PUBLIC KEY-----'
    )
  })

  it('should get public key from base64', () => {
    const result = getPublicKeyFromBase64(base64Key)
    expect(result.n.data.length).toEqual(74)
  })

  it('should encrypt data', async () => {
    const spy = vi.spyOn(forge.util, 'encode64')
    await encryptData('Test@12345')

    expect(spy).toHaveBeenCalled()
  })
})
