/**
 * <AUTHOR> on 19/03/2025
 */
import { describe, expect, it, vi } from 'vitest'
import { getRoutePath } from '@/utils/routePath'

describe('Route Path', () => {
  it('should append /backoffice on route when it is backoffice', () => {
    vi.stubEnv('NEXT_PUBLIC_EATTA_BUILD', 'backoffice')

    const result = getRoutePath('/dashboard')
    expect(result).toEqual('/backoffice/dashboard')
  })

  it('should not append /backoffice on route when it is client', () => {
    vi.stubEnv('NEXT_PUBLIC_EATTA_BUILD', 'client')

    const result = getRoutePath('/dashboard')
    expect(result).toEqual('/dashboard')
  })
})
