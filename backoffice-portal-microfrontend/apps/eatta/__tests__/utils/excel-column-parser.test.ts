/**
 * <AUTHOR> on 24/03/2025
 */

import { describe, expect, it, vi } from 'vitest'
import { hasDataInColumns } from '@/utils/excelColumnParser'
import { WorkBook } from 'xlsx'

describe('Excel Column Parser', () => {
  const workbook: WorkBook = {
    SheetNames: ['Sheet1'],
    Sheets: {
      Sheet1: {
        '!ref': 'A1:B3',
        A3: { v: 'Name' },
        B3: { v: 'Age' },
        A5: { v: 'Jane' },
        B5: { v: 30 },
      },
    },
  }

  it('should throw an error if column range is invalid', () => {
    const workbook: WorkBook = {
      SheetNames: ['Sheet1'],
      Sheets: {
        Sheet1: {
          '!ref': 'A1:B2',
        },
      },
    }

    expect(() => hasDataInColumns(workbook, 'Sheet1', '1:2')).toThrow(
      'Column range must be in format "A:Z"'
    )
  })

  it('should return false if sheet name is not found in workbook', () => {
    const book: WorkBook = {
      SheetNames: ['Sheet2'],
      Sheets: {
        Sheet2: {},
      },
    }
    const hasData = hasDataInColumns(book, 'Sheet1', 'A:B')
    expect(hasData).toBe(false)
  })

  it('should return true if data exists in specified columns of an Excel workbook sheet', () => {
    const hasData = hasDataInColumns(workbook, 'Sheet1', 'A:B')

    expect(hasData).toBe(true)
  })

  it('should return false if there is no data in the specified columns', () => {
    const book: WorkBook = {
      SheetNames: ['Sheet1'],
      Sheets: {
        Sheet1: {},
      },
    }

    const hasData = hasDataInColumns(book, 'Sheet1', 'A:B')

    expect(hasData).toBe(false)
  })

  it('should throw an error if an exception occurs', () => {
    const spy = vi.spyOn(workbook.Sheets, 'Sheet1', 'get')
    spy.mockImplementation(() => {
      throw new Error('Error parsing Excel sheet')
    })

    expect(() => hasDataInColumns(workbook, 'Sheet1', 'A:B')).toThrow(
      'Error parsing Excel sheet'
    )
  })
})
