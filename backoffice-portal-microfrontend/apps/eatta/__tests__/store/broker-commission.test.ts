/**
 * <AUTHOR> on 27/03/2025
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { apiclient } from '@/utils/apiclient'
import {
  activateCommissions,
  createBrokerCommissions,
  getBrokerCommissions,
} from '@/store/actions'
import {
  setCommissionsResponse,
  setIsActivatingCommissions,
  setIsLoadingCommissions,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { MockBrokerCommission } from '../stubs/commission'

const mockFilters = {
  page: 1,
  size: 10,
}

const mockBrokerRequest = {
  buyerCommission: 5,
  commissionType: 'PERCENTAGE',
  producerCommission: 4,
}

describe('Broker commission Actions', () => {

  describe('getBrokerCommissions', () => {
    const mockDispatch = vi.fn()

    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle get broker commission success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockBrokerCommission })

      await getBrokerCommissions(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingCommissions(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCommissionsResponse(MockBrokerCommission)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCommissions(false)
      )
    })

    it('should handle get broker commission error', async () => {
      const mockError = new Error('Error getting commissions')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getBrokerCommissions(mockDispatch, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting commissions',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCommissions(false)
      )
    })
  })

  describe('createBrokerCommissions', () => {
    const mockDispatch = vi.fn()

    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle create broker commission success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'post')
        .mockResolvedValue({ data: {status: 'success'} })

      vi.spyOn(apiclient, 'get').mockResolvedValue({
        data: MockBrokerCommission,
      })

      await createBrokerCommissions(mockDispatch, mockBrokerRequest)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingCommissions(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: `Broker configuration created successfully`,
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCommissions(false)
      )
    })

    it('should handle create broker commission error', async () => {
      const mockError = new Error('Error creating commissions')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'post')
        .mockRejectedValueOnce(mockError)

      await createBrokerCommissions(mockDispatch, mockBrokerRequest)

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error creating commissions',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCommissions(false)
      )
    })
  })

  describe('activateCommissions', () => {
    const mockDispatch = vi.fn()

    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle activate broker commission success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'patch')
        .mockResolvedValue({ data: { status: 'success' } })
      vi.spyOn(apiclient, 'get').mockResolvedValue({
        data: MockBrokerCommission,
      })

      const mockOnSuccess = vi.fn()

      await activateCommissions(mockDispatch, 'commissionId', mockOnSuccess)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsActivatingCommissions(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockOnSuccess).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsActivatingCommissions(false)
      )
    })

    it('should handle activate broker commission error', async () => {
      const mockError = new Error('Error activating commission')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'patch')
        .mockRejectedValueOnce(mockError)

      await activateCommissions(mockDispatch, 'commissionId', () => {})

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error activating commission',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsActivatingCommissions(false)
      )
    })
  })
})