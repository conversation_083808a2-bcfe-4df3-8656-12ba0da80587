/**
 * <AUTHOR> on 27/03/2025
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  createCompany,
  createCompanyUser,
  editUserByAuthUserId,
  fetchBanks,
  getCompanies,
  getUsersByOrganizationCode,
  getOrganizationById,
  setSelectedCompanyRecord,
  updateCompanyPaymentDetails,
} from '@/store/actions'
import {
  setBankBranches,
  setBanks,
  setCompaniesResponse,
  setCompanyUserResponse,
  setIsCreatingUser,
  setIsEditingUser,
  setIsLoadingCompanies,
  setIsLoadingCompanyUser,
  setIsLoadingOrganization,
  setIsOnboardingCompany,
  setOnboardingCompany,
  setSelectedCompanies,
  setSelectedCompany,
} from '@/store/reducers'
import {
  CompanyUsersResponse,
  MockBankBranchesResponse,
  MockBankResponse,
  MockCompaniesResponse,
  MockCompanyUserRequest,
  MockEditUser,
  MockPaymentDetails,
} from '../stubs/company'
import { apiclient } from '@/utils/apiclient'
import { setNotification } from '@dtbx/store/reducers'
import { CompanyProfile } from '@/store/interfaces'

describe('Companies Actions', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })
  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('setSelectedCompanyRecord', () => {
    it('should dispatch setSelectedCompanies with the provided companies', () => {
      setSelectedCompanyRecord(mockDispatch, MockCompaniesResponse.data)

      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCompanies(MockCompaniesResponse.data)
      )
    })
  })

  describe('getCompanies', () => {
    it('should handle get companies success', async () => {
      const mockParams = { name: 'Test Company' }
      const mockResponse = {
        data: MockCompaniesResponse,
      }

      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValueOnce(mockResponse)

      await getCompanies(mockDispatch, mockParams)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingCompanies(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/onboarding/organizations?name=Test+Company&ascending=false'
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCompaniesResponse(mockResponse.data)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setIsLoadingCompanies(false)
      )
    })

    it('should handle get companies error', async () => {
      const mockParams = { name: 'Invalid Company' }
      const mockError = new Error('Failed to fetch companies')

      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getCompanies(mockDispatch, mockParams)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingCompanies(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/onboarding/organizations?name=Invalid+Company&ascending=false'
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setIsLoadingCompanies(false)
      )
    })
  })

  describe('createCompany', () => {
    const mockData: CompanyProfile = {
      type: 'Broker',
      code: 'COMP123',
      name: 'Test Company',
      phoneNumber: '1234567890',
      emailAddress: '<EMAIL>',
      locationAddress: '123 Test Street',
    }

    it('should handle create company success', async () => {
      const mockResponse = {
        data: {
          data: MockCompaniesResponse.data[0],
        },
      }

      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockResolvedValueOnce(mockResponse)

      await createCompany(mockDispatch, mockData)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsOnboardingCompany(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/onboarding/register-organization',
        expect.anything()
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setOnboardingCompany(mockResponse.data.data)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message: 'Company profile saved successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsOnboardingCompany(false)
      )
    })

    it('should handle create company error', async () => {
      const mockError = new Error('Error Creating Company')

      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockRejectedValueOnce(mockError)

      await createCompany(mockDispatch, mockData)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsOnboardingCompany(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/onboarding/register-organization',
        expect.anything()
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsOnboardingCompany(false)
      )
    })
  })

  describe('updateCompanyPaymentDetails', () => {
    const organizationId = 'ORG789'

    it('should handle update company success', async () => {
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockResolvedValueOnce({
          data: { data: MockCompaniesResponse.data[0] },
        })

      await updateCompanyPaymentDetails(
        mockDispatch,
        organizationId,
        MockPaymentDetails
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsOnboardingCompany(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        `/backoffice-bff/eatta-service/onboarding/organization/${organizationId}`,
        MockPaymentDetails
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setOnboardingCompany(MockCompaniesResponse.data[0])
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Payment details saved successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).lastCalledWith(setIsOnboardingCompany(false))
    })

    it('should handle update company error', async () => {
      const mockError = new Error('Error Creating Company')
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockRejectedValueOnce(mockError)

      await updateCompanyPaymentDetails(
        mockDispatch,
        organizationId,
        MockPaymentDetails
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsOnboardingCompany(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        `/backoffice-bff/eatta-service/onboarding/organization/${organizationId}`,
        MockPaymentDetails
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Error Creating Company',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setIsOnboardingCompany(false)
      )
    })
  })

  describe('createCompanyUser', () => {
    it('should handle create user success', async () => {
      const mockOnCreateUserSuccess = vi.fn()
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockResolvedValueOnce({})

      await createCompanyUser(
        mockDispatch,
        MockCompanyUserRequest,
        mockOnCreateUserSuccess
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsCreatingUser(true))
      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/onboarding/register-user',
        MockCompanyUserRequest
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: `An email with a link to activate this account is on its way to ${MockCompanyUserRequest.email}. `,
          type: 'success',
        })
      )
      expect(mockOnCreateUserSuccess).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setIsCreatingUser(false))
    })

    it('should handle create user error', async () => {
      const mockOnCreateUserSuccess = vi.fn()

      const mockError = new Error('Error creating user')
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockRejectedValueOnce(mockError)

      await createCompanyUser(
        mockDispatch,
        MockCompanyUserRequest,
        mockOnCreateUserSuccess
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsCreatingUser(true))
      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/onboarding/register-user',
        MockCompanyUserRequest
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockOnCreateUserSuccess).not.toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setIsCreatingUser(false))
    })
  })

  describe('getOrganizationByOrganizationId', () => {
    const mockOrganizationId = 'ORG123'

    it('should handle get organization success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValueOnce({
          data: { status: 'success', data: MockCompaniesResponse.data[0] },
        })

      await getOrganizationById(mockDispatch, mockOrganizationId)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingOrganization(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        `/backoffice-bff/eatta-service/onboarding/${mockOrganizationId}`
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setSelectedCompany(MockCompaniesResponse.data[0])
      )
      expect(mockDispatch).lastCalledWith(setIsLoadingOrganization(false))
    })

    it('should handle get organization error', async () => {
      const mockError = new Error('Failed to fetch organization')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getOrganizationById(mockDispatch, mockOrganizationId)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingOrganization(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        `/backoffice-bff/eatta-service/onboarding/${mockOrganizationId}`
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setIsLoadingOrganization(false)
      )
    })
  })

  describe('getCompanyUsersByOrganizationCode', () => {
    const mockOrganizationCode = 'ORG456'
    const mockParams = { name: 'Jane Doe' }

    it('should handle get company users success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValueOnce({ data: CompanyUsersResponse })

      await getUsersByOrganizationCode(
        mockDispatch,
        mockOrganizationCode,
        mockParams
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingCompanyUser(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/users/ORG456?name=Jane+Doe'
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCompanyUserResponse(CompanyUsersResponse)
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCompanyUser(false))
    })

    it('should handle get company users error', async () => {
      const mockError = new Error('Failed to fetch users')

      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getUsersByOrganizationCode(
        mockDispatch,
        mockOrganizationCode,
        mockParams
      )

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCompanyUser(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        '/backoffice-bff/eatta-service/users/ORG456?name=Jane+Doe'
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCompanyUser(false))
    })
  })

  describe('editUserByAuthUserId', () => {
    const authUserId = 'user-123'

    it('should handle edit user success', async () => {
      const mockAxiosPatchHandler = vi
        .spyOn(apiclient, 'patch')
        .mockResolvedValueOnce({})

      await editUserByAuthUserId(mockDispatch, authUserId, MockEditUser)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsEditingUser(true))
      expect(mockAxiosPatchHandler).toHaveBeenCalledWith(
        `/backoffice-bff/eatta-service/users/update-user/${authUserId}`,
        MockEditUser
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: `User profile updated successfully`,
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setIsEditingUser(false))
    })

    it('should handle edit user error', async () => {
      const authUserId = 'user-456'
      const mockError = new Error('Error updating user')
      const mockAxiosPatchHandler = vi
        .spyOn(apiclient, 'patch')
        .mockRejectedValueOnce(mockError)

      await editUserByAuthUserId(mockDispatch, authUserId, MockEditUser)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsEditingUser(true))
      expect(mockAxiosPatchHandler).toHaveBeenCalledWith(
        `/backoffice-bff/eatta-service/users/update-user/${authUserId}`,
        { ...MockEditUser }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setIsEditingUser(false))
    })
  })

  describe('fetchBanks', () => {
    it('should handle get banks success', async () => {
      const mockBanksResponse = { data: MockBankResponse }
      const mockBranchesResponse = { data: MockBankBranchesResponse }

      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValueOnce(mockBanksResponse)
        .mockResolvedValueOnce(mockBranchesResponse)

      await fetchBanks(mockDispatch)

      expect(mockAxiosGetHandler).toHaveBeenCalledTimes(2)
      expect(mockAxiosGetHandler).toHaveBeenNthCalledWith(
        1,
        '/backoffice-bff/eatta-service/banks'
      )
      expect(mockAxiosGetHandler).toHaveBeenNthCalledWith(
        2,
        '/backoffice-bff/eatta-service/banks/branches'
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setBanks(mockBanksResponse.data)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setBankBranches(mockBranchesResponse.data)
      )
    })

    it('should handle get banks error', async () => {
      const mockError = new Error('Error fetching banks')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)
        .mockRejectedValueOnce(mockError)

      await fetchBanks(mockDispatch)

      expect(mockAxiosGetHandler).toHaveBeenCalledTimes(2)
      expect(mockAxiosGetHandler).toHaveBeenNthCalledWith(
        1,
        '/backoffice-bff/eatta-service/banks'
      )
      expect(mockAxiosGetHandler).toHaveBeenNthCalledWith(
        2,
        '/backoffice-bff/eatta-service/banks/branches'
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
    })
  })
})
