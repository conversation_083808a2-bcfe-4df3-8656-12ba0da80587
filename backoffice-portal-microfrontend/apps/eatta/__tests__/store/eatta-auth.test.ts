/**
 * <AUTHOR> on 27/03/2025
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  fetchPublicKey,
  generateOTP,
  handleChangePassword,
  handleClientLogin,
  handleExpiredResetLink,
  handleForgotPassword,
  handleLogout,
  handleResetPassword,
  handleValidateToken,
  refreshUserToken,
  verifyOTP,
} from '@/store/actions'
import * as eattaAuth from '@/store/actions/eattaAuth'
import { openapi2, secureapi } from '@dtbx/store/utils'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import {
  clearNotification,
  setCredentials,
  setDecodedToken,
  setIsLoadingLogin,
  setNotification,
} from '@dtbx/store/reducers'
import { encryptData } from '@/utils/passEncryption'
import { IDecodeToken } from '@dtbx/store/interfaces'
import { jwtDecode } from 'jwt-decode'
import { scheduleTokenRefresh } from '@/utils/refreshSchedule'

vi.mock('@/utils/encrypt', () => ({
  encrypt: vi.fn(),
}))
vi.mock('jwt-decode', () => ({
  jwtDecode: vi.fn(),
}))
vi.mock('@/utils/refreshSchedule', () => ({
  scheduleTokenRefresh: vi.fn(),
}))

const mockToken =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30'
const mockDecodedToken: IDecodeToken = {
  aud: '1a2b3c4d-5e6f-7g8h-9i10-j11k12l13m14',
  last_name: 'Doe',
  first_name: 'John',
  user_id: 'usr123',
  authorities: ['CAN_VIEW', 'CAN_CREATE', 'CAN_UPDATE', 'CAN_DELETE'],
  sub: 'client',
  iat: 1516239022,
  exp: 1516242622,
}

describe('Eatta Auth Actions', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })
  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('handleLogout', () => {
    const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance
    const mockClearStorage = vi.spyOn(Storage.prototype, 'clear')

    it('should handle logout', async () => {
      await handleLogout(mockDispatch, mockRouter)

      expect(mockDispatch).toHaveBeenCalledWith(clearNotification())
      expect(mockClearStorage).toHaveBeenCalledTimes(2)
      expect(mockRouter.replace).toHaveBeenCalledWith('/auth')
    })
  })

  describe('refreshUserToken', () => {
    const mockTokenResponse = {
      data: {
        access_token: 'newAccessToken',
        refresh_token: 'newRefreshToken',
      },
    }

    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should log an error if no refresh token exists', async () => {
      vi.spyOn(Storage.prototype, 'getItem').mockReturnValueOnce(null)
      const mockConsoleError = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      await refreshUserToken()

      expect(console.error).toHaveBeenCalledWith('ERROR NO REFRESH TOKEN FOUND')
      expect(localStorage.getItem).toHaveBeenCalledWith('refreshToken')

      mockConsoleError.mockRestore()
    })

    it('should refresh tokens and update localStorage on success', async () => {
      vi.spyOn(Storage.prototype, 'getItem')
        .mockReturnValueOnce(mockToken)
        .mockReturnValueOnce(mockToken)
      const mockSetSpy = vi.spyOn(Storage.prototype, 'setItem')

      const mockedJwtDecode = vi
        .mocked(jwtDecode)
        .mockReturnValue(mockDecodedToken)

      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'postForm')
        .mockResolvedValueOnce(mockTokenResponse)

      await refreshUserToken()

      expect(mockedJwtDecode).toHaveBeenCalledWith(mockToken)
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(mockSetSpy).toHaveBeenCalledWith('accessToken', 'newAccessToken')
      expect(mockSetSpy).toHaveBeenCalledWith('refreshToken', 'newRefreshToken')
      expect(scheduleTokenRefresh).toHaveBeenCalled()
    })

    it('should log an error if refreshing the token fails', async () => {
      vi.spyOn(Storage.prototype, 'getItem')
        .mockReturnValueOnce(mockToken)
        .mockReturnValueOnce(mockToken)
      const mockError = new Error('Failed to refresh token')
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'postForm')
        .mockRejectedValueOnce(mockError)

      const mockConsoleError = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      const mockedJwtDecode = vi
        .mocked(jwtDecode)
        .mockReturnValue(mockDecodedToken)

      await refreshUserToken()

      expect(mockedJwtDecode).toHaveBeenCalledWith(mockToken)
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(console.error).toHaveBeenCalledWith(
        'ERROR ON REFRESH TOKEN',
        mockError
      )

      mockConsoleError.mockRestore()
    })
  })

  describe('verifyOTP', () => {
    const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance

    const mockRequestOTP = {
      username: 'testUser',
      otp: '123456',
    }

    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle OTP verification success', async () => {
      const mockSetSpy = vi.spyOn(Storage.prototype, 'setItem')

      const mockResponse = {
        data: {
          status: 'SUCCESS',
          data: {
            access_token: 'testAccessToken',
            refresh_token: 'testRefreshToken',
          },
        },
      }

      const mockJwtDecode = vi
        .mocked(jwtDecode)
        .mockReturnValue(mockDecodedToken)
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockResolvedValueOnce(mockResponse)

      const result = await verifyOTP(mockRequestOTP, mockDispatch, mockRouter)

      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/v1/eatta-service/otp/verify',
        mockRequestOTP
      )
      expect(mockSetSpy).toHaveBeenCalledWith('accessToken', 'testAccessToken')
      expect(mockSetSpy).toHaveBeenCalledWith(
        'refreshToken',
        'testRefreshToken'
      )
      expect(mockJwtDecode).toHaveBeenCalledWith('testAccessToken')
      expect(mockDispatch).toHaveBeenCalledWith(
        setDecodedToken(mockDecodedToken)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'OTP verified. Login successful!',
          type: 'success',
        })
      )
      expect(mockRouter.replace).toHaveBeenCalledWith('/')
      expect(result).toBe(true)
    })

    it('should handle OTP verification error', async () => {
      const mockError = new Error('Verification Error')
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockRejectedValueOnce(mockError)

      const result = await verifyOTP(mockRequestOTP, mockDispatch, mockRouter)

      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/v1/eatta-service/otp/verify',
        mockRequestOTP
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'You entered an invalid or expired code.',
          type: 'error',
        })
      )
      expect(result).toBe(false)
    })
  })

  describe('generateOTP', () => {
    it('should handle generate OTP success', async () => {
      const mockDispatch = vi.fn()
      const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance
      const mockResponse = {
        data: {
          status: 'SUCCESS',
          data: { code: '123456' },
        },
      }

      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockResolvedValueOnce(mockResponse)

      const result = await generateOTP(
        'testUser',
        'EMAIL',
        mockDispatch,
        mockRouter
      )

      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/v1/eatta-service/otp/generate',
        { username: 'testUser', deliveryMode: 'EMAIL' }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Verification code sent successfully',
          type: 'success',
        })
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle generate OTP error', async () => {
      const mockDispatch = vi.fn()
      const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance
      const mockError = new Error('Failed to send verification code')

      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockRejectedValueOnce(mockError)

      const result = await generateOTP(
        'testUser',
        'SMS',
        mockDispatch,
        mockRouter
      )

      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/v1/eatta-service/otp/generate',
        { username: 'testUser', deliveryMode: 'SMS' }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message:
            'Failed to send verification code. Please try to login again.',
          type: 'error',
        })
      )
      expect(mockRouter.replace).toHaveBeenCalledWith('/auth')
      expect(result).toEqual(false)
    })
  })

  describe('handleClientLogin', () => {
    const mockDispatch = vi.fn()
    const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance
    const mockAuthRequest = {
      username: 'testUser',
      password: 'wrongPass',
      organizationCode: 'testOrg',
    }

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should handle client login success', async () => {
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockResolvedValueOnce({
          data: {
            status: 'SUCCESS',
            data: {
              email: '<EMAIL>',
              phoneNumber: '123456789',
            },
          },
        })

      await handleClientLogin(mockAuthRequest, mockDispatch, mockRouter)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoadingLogin(true))
      expect(encryptData).toHaveBeenCalledOnce()
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCredentials({
          username: 'testUser',
          email: '<EMAIL>',
          phoneNumber: '123456789',
        })
      )
      expect(mockRouter.replace).toHaveBeenCalledWith('/auth/verify')
    })

    it('should handle client login error', async () => {
      const mockError = new Error('Login failed')
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockRejectedValueOnce(mockError)

      await handleClientLogin(mockAuthRequest, mockDispatch, mockRouter)

      expect(encryptData).toHaveBeenCalledOnce()
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Login failed',
          type: 'error',
        })
      )
      expect(mockRouter.replace).not.toHaveBeenCalled()
    })
  })

  describe('handleResetPassword', () => {
    const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance
    it('should handle reset password success', async () => {
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockResolvedValueOnce({ data: { status: 'success' } })

      await handleResetPassword(
        'newPassword',
        'resetToken',
        mockDispatch,
        mockRouter
      )

      expect(encryptData).toHaveBeenCalledTimes(1)
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Password reset successfully. You can now log in.',
          type: 'success',
        })
      )
      expect(mockRouter.replace).toHaveBeenCalledWith('/auth')
    })

    it('should handle reset password error', async () => {
      const mockError = new Error('Failed to reset password')
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockRejectedValueOnce(mockError)

      await handleResetPassword(
        'newPassword',
        'resetToken',
        mockDispatch,
        mockRouter
      )

      expect(encryptData).toHaveBeenCalledTimes(1)
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Failed to reset password. Please try again.',
          type: 'error',
        })
      )
      expect(mockRouter.replace).not.toHaveBeenCalled()
    })
  })

  describe('handleExpiredResetLink', () => {
    const mockOnSuccess = vi.fn()

    it('should handle expired reset link success', async () => {
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockResolvedValueOnce({ data: { status: 'success' } })

      await handleExpiredResetLink(
        '<EMAIL>',
        'resetToken',
        mockDispatch,
        mockOnSuccess
      )

      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/v1/eatta-service/password/resend-reset-link',
        { email: '<EMAIL>', resetToken: 'resetToken' }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Reset link sent successfully to your email',
          type: 'success',
        })
      )
      expect(mockOnSuccess).toHaveBeenCalled()
    })

    it('should handle expired reset link error', async () => {
      const mockDispatch = vi.fn()
      const mockOnSuccess = vi.fn()
      const mockError = new Error('Failed to resend reset link')

      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockRejectedValueOnce(mockError)

      await handleExpiredResetLink(
        '<EMAIL>',
        'resetToken',
        mockDispatch,
        mockOnSuccess
      )

      expect(mockAxiosPostHandler).toHaveBeenCalledWith(
        '/v1/eatta-service/password/resend-reset-link',
        { email: '<EMAIL>', resetToken: 'resetToken' }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockOnSuccess).not.toHaveBeenCalled()
    })
  })

  describe('handleValidateToken', () => {
    it('should handle validate token success', async () => {
      const expected = {
        data: { status: 'success', data: { status: 'VALID' } },
      }
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'get')
        .mockResolvedValueOnce(expected)

      const res = await handleValidateToken('token', mockDispatch)

      expect(mockAxiosPostHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/password/validate-token/token'
      )
      expect(res).toEqual(expected.data.data)
    })
    it('should handle validate token error', async () => {
      const mockError = new Error('Failed to validate token')
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'get')
        .mockRejectedValueOnce(mockError)

      const res = await handleValidateToken('token', mockDispatch)

      expect(mockAxiosPostHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/password/validate-token/token'
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setNotification({ message: mockError.message, type: 'error' })
      )
      expect(res).toEqual({ status: 'ERROR', message: mockError.message })
    })
  })

  describe('handleForgotPassword', () => {
    const mockOnSuccess = vi.fn()

    it('should handle forgot password success', async () => {
      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockResolvedValueOnce({ data: { status: 'success' } })

      await handleForgotPassword('<EMAIL>', mockDispatch, mockOnSuccess)
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Password reset link sent successfully to your email',
          type: 'success',
        })
      )
    })
    it('should handle forgot password error', async () => {
      const mockError = new Error('Failed to request forgot password')

      const mockAxiosPostHandler = vi
        .spyOn(openapi2, 'post')
        .mockRejectedValueOnce(mockError)

      await handleForgotPassword('<EMAIL>', mockDispatch, mockOnSuccess)

      expect(mockAxiosPostHandler).toHaveBeenCalled

      expect(mockDispatch).toHaveBeenLastCalledWith(
        setNotification({ message: mockError.message, type: 'error' })
      )
    })
  })

  describe('handleChangePassword', () => {
    const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance

    it('should handle change password success', async () => {
      const mockAxiosPostHandler = vi
        .spyOn(secureapi, 'post')
        .mockResolvedValueOnce({ data: { status: 'success' } })

      await handleChangePassword(
        'oldPassword',
        'newPassword',
        mockDispatch,
        mockRouter
      )
      expect(encryptData).toHaveBeenCalledTimes(2)
      expect(mockAxiosPostHandler).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Password updated successfully. Login again to continue.',
          type: 'success',
        })
      )
    })
    it('should handle change password error', async () => {
      const mockError = new Error('Failed to change password')
      const mockRouter = { replace: vi.fn() } as unknown as AppRouterInstance
      const mockAxiosPostHandler = vi
        .spyOn(secureapi, 'post')
        .mockRejectedValueOnce(mockError)

      await handleChangePassword(
        'oldPassword',
        'newPassword',
        mockDispatch,
        mockRouter
      )

      expect(mockAxiosPostHandler).toHaveBeenCalled

      expect(mockDispatch).toHaveBeenLastCalledWith(
        setNotification({ message: mockError.message, type: 'error' })
      )
    })
  })

  describe('fetchPublicKey', () => {
    it('should handle fetch public key success', async () => {
      const expected = {
        data: { success: 'success', data: { key: 'sample api key' } },
      }
      const mockAxiosGetHandler = vi
        .spyOn(openapi2, 'get')
        .mockResolvedValueOnce(expected)

      const publicKey = await fetchPublicKey()

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()

      expect(publicKey).toEqual(expected.data.data.key)
    })
  })
})
