/**
 * <AUTHOR> on 27/03/2025
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  getPostAuctionCatalogues,
  getPreAuctionCatalogues,
  setSelectedCatalogue,
  setSelectedCatalogues,
  uploadCatalogue,
} from '@/store/actions'
import {
  MockPostCataloguesResponse,
  MockPreAuctionCatalogueResponse,
} from '../stubs/catalogues'
import {
  setIsLoadingCatalogues,
  setIsUploadingCatalogues,
  setPostAuctionCataloguesResponse,
  setPreAuctionCataloguesResponse,
  setSelectedRecord,
  setSelectedRecords,
} from '@/store/reducers'
import { apiclient } from '@/utils/apiclient'
import { setNotification } from '@dtbx/store/reducers'

const mockFilters = {
  saleDate: '2025-10-10',
  page: 1,
  size: 10,
}

const mockFile: File = new File([''], 'test.xls', {
  type: 'application/vnd.ms-excel',
})

const mockUploadResponse = { data: { status: 'success', message: 'Success' } }

describe('Catalogue Actions', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })
  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('setSelectedCatalogues', () => {
    it('should dispatch setSelectedRecords reducer action', () => {
      const expected = MockPostCataloguesResponse.data

      setSelectedCatalogues(mockDispatch, expected)

      expect(mockDispatch).toHaveBeenCalledExactlyOnceWith(
        setSelectedRecords([
          {
            catalogueId: expected[0].id,
            lotNumber: expected[0].lotNo,
          },
        ])
      )
    })
  })

  describe('setSelectedCatalogue', () => {
    it('should dispatch setSelectedRecord reducer action', () => {
      const expected = MockPostCataloguesResponse.data[0]
      setSelectedCatalogue(mockDispatch, MockPostCataloguesResponse.data[0])

      expect(mockDispatch).toHaveBeenCalledExactlyOnceWith(
        setSelectedRecord({
          catalogueId: expected.id,
          lotNumber: expected.lotNo,
        })
      )
    })
  })

  describe('getPostAuctionCatalogues', () => {
    it('should handle get post auction catalogues for client success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockPostCataloguesResponse })

      await getPostAuctionCatalogues(mockDispatch, false, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingCatalogues(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/catalogue?saleDate=2025-10-10&page=1&size=10'
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setPostAuctionCataloguesResponse(MockPostCataloguesResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCatalogues(false)
      )
    })

    it('should handle get post auction catalogues error', async () => {
      const mockError = new Error('Error getting post auction catalogues')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getPostAuctionCatalogues(mockDispatch, true, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting post auction catalogues',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCatalogues(false)
      )
    })
  })

  describe('uploadCatalogue', () => {
    it('should handle upload post auction catalogues for client success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'postForm')
        .mockResolvedValue(mockUploadResponse)

      await uploadCatalogue(
        mockDispatch,
        mockFile,
        '2025-10-10',
        'POST_AUCTION',
        {},
        () => {}
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsUploadingCatalogues(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/catalogue?saleDate=2025-10-10&auctionType=POST_AUCTION',
        {
          file: mockFile,
        },
        {}
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: mockUploadResponse.data.message,
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsUploadingCatalogues(false)
      )
    })

    it('should handle upload post auction catalogues error', async () => {
      const mockError = new Error('Error uploading post auction catalogues')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'postForm')
        .mockRejectedValueOnce(mockError)

      await uploadCatalogue(
        mockDispatch,
        mockFile,
        '2025-10-10',
        'POST_AUCTION',
        {},
        () => {}
      )

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error uploading post auction catalogues',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsUploadingCatalogues(false)
      )
    })
  })

  describe('uploadPreAuctionCatalogue', () => {
    it('should handle upload pre auction catalogues for client success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'postForm')
        .mockResolvedValue(mockUploadResponse)

      await uploadCatalogue(
        mockDispatch,
        mockFile,
        '2025-10-10',
        'PRE_AUCTION',
        {},
        () => {}
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsUploadingCatalogues(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/catalogue?saleDate=2025-10-10&auctionType=PRE_AUCTION',
        { file: mockFile },
        {}
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: mockUploadResponse.data.message,
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsUploadingCatalogues(false)
      )
    })

    it('should handle upload pre auction catalogues error', async () => {
      const mockError = new Error('Error uploading post auction catalogues')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'postForm')
        .mockRejectedValueOnce(mockError)

      await uploadCatalogue(
        mockDispatch,
        mockFile,
        '2025-10-10',
        'PRE_AUCTION',
        {},
        () => {}
      )

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error uploading post auction catalogues',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsUploadingCatalogues(false)
      )
    })
  })

  describe('getPreAuctionCatalogues', () => {
    it('should handle get pre auction catalogues, for client success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockPreAuctionCatalogueResponse })

      await getPreAuctionCatalogues(mockDispatch, false, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingCatalogues(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/catalogue/pre?saleDate=2025-10-10&page=1&size=10'
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setPreAuctionCataloguesResponse(MockPreAuctionCatalogueResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCatalogues(false)
      )
    })

    it('should handle get pre auction catalogues error', async () => {
      const mockError = new Error('Error getting post auction catalogues')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getPreAuctionCatalogues(mockDispatch, true, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting post auction catalogues',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsLoadingCatalogues(false)
      )
    })
  })
})
