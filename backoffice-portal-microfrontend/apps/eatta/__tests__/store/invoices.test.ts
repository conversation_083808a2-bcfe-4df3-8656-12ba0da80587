/**
 * <AUTHOR> on 27/03/2025
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { apiclient } from '@/utils/apiclient'
import {
  createInvoiceEslip,
  getBrokerInvoiceEntries,
  getBrokerInvoices,
  getInvoiceById,
  getInvoiceEslips,
  getTransactionHistory,
  uploadBrokerInvoice,
} from '@/store/actions'
import { setNotification } from '@dtbx/store/reducers'
import {
  setBrokerInvoiceEntryResponse,
  setBrokerInvoiceResponse,
  setBrokerInvoicesResponse,
  setInvoiceEslipsResponse,
  setIsLoading,
  setIsUploadingBrokerInvoices,
  setTransactionHistoryResponse,
} from '@/store/reducers'
import {
  MockBrokerInvoiceResponse,
  MockBrokerInvoicesEntryResponse,
  MockBrokerInvoicesResponse,
  MockInvoiceEslipResponse,
  Mo<PERSON><PERSON><PERSON>sactionHistoryResponse,
} from '../stubs/invoices'
import { BrokerInvoicePayload } from '@/store/interfaces'

const MockCreatePaymentInvoice = {
  catalogues: [
    {
      catalogueId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
      lotNumber: 124341412,
    },
  ],
}

const MockUpdatePaymentInvoice = {
  invoiceNumber: '124341412',
  catalogues: [
    {
      catalogueId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
      lotNumber: 124341412,
    },
  ],
}

const mockFilters = {
  page: 1,
  size: 10,
  ascending: true,
}

describe('invoices Actions', () => {
  describe('getInvoiceEslips', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should map get invoice url for backoffice correctly', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getInvoiceEslips(mockDispatch, true, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenNthCalledWith(
        1,
        '/backoffice-bff/eatta-service/invoice?'
      )
    })

    it('should map get invoice url for client correctly', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getInvoiceEslips(mockDispatch, false, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenNthCalledWith(
        1,
        '/v1/eatta-service/invoice?'
      )
    })

    it('should handle get payment invoice success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockInvoiceEslipResponse })

      await getInvoiceEslips(mockDispatch, false, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setInvoiceEslipsResponse(MockInvoiceEslipResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get payment invoice error', async () => {
      const mockError = new Error('Error fetching invoices')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getInvoiceEslips(mockDispatch, false, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error fetching e-slip invoices',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })
  })

  describe('createInvoiceEslip', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle create payment invoice success', async () => {
      const expected = MockPaymentInvoicesResponse.data[0]
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockResolvedValue({ data: { status: 'success', data: expected } })
      const mockShowCheckout = vi.fn()

      await createInvoiceEslip(
        mockDispatch,
        MockCreatePaymentInvoice,
        mockShowCheckout
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsSavingPaymentInvoice(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setPaymentInvoice(expected)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(3, resetSelectedCatalogues())

      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsSavingPaymentInvoice(false)
      )
      expect(mockShowCheckout).toHaveBeenCalledOnce()
    })

    it('should handle create payment invoice error', async () => {
      const mockError = new Error('Error creating invoice')
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockRejectedValueOnce(mockError)

      await createInvoiceEslip(mockDispatch, MockCreatePaymentInvoice, () => {})

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsSavingPaymentInvoice(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({ message: 'Error creating invoice', type: 'error' })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsSavingPaymentInvoice(false)
      )
    })
  })

  describe('getInvoiceEslipsById', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle create payment invoice success', async () => {
      const expected = MockPaymentInvoicesResponse.data[0]
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockResolvedValue({ data: { status: 'success', data: expected } })
      const mockShowCheckout = vi.fn()

      await createInvoiceEslip(
        mockDispatch,
        MockCreatePaymentInvoice,
        mockShowCheckout
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsSavingPaymentInvoice(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setPaymentInvoice(expected)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(3, resetSelectedCatalogues())

      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsSavingPaymentInvoice(false)
      )
      expect(mockShowCheckout).toHaveBeenCalledOnce()
    })

    it('should handle create payment invoice error', async () => {
      const mockError = new Error('Error creating invoice')
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'post')
        .mockRejectedValueOnce(mockError)

      await createInvoiceEslip(mockDispatch, MockCreatePaymentInvoice, () => {})

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsSavingPaymentInvoice(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({ message: 'Error creating invoice', type: 'error' })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsSavingPaymentInvoice(false)
      )
    })
  })

  describe('removeInvoiceEslipEntry', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle delete payment invoice success', async () => {
      const expected = MockPaymentInvoicesResponse.data[0]
      const mockAxiosDeleteHandler = vi
        .spyOn(apiclient, 'delete')
        .mockResolvedValue({ data: { status: 'success', data: expected } })

      await deletingInvoiceCatalogue(mockDispatch, MockUpdatePaymentInvoice)

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setDeletedInvoiceCatalogue(expected.catalogues[0].id)
      )
      expect(mockAxiosDeleteHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/invoice',
        {
          data: MockUpdatePaymentInvoice,
        }
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setPaymentInvoice(expected)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message: 'Successfully Updated',
          type: 'success',
        })
      )
    })

    it('should handle delete payment invoice error', async () => {
      const mockError = new Error('Error deleting invoice')
      const mockAxiosDeleteHandler = vi
        .spyOn(apiclient, 'delete')
        .mockRejectedValueOnce(mockError)

      await deletingInvoiceCatalogue(mockDispatch, MockUpdatePaymentInvoice)

      expect(mockAxiosDeleteHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({ message: 'Error deleting invoice', type: 'error' })
      )
    })
  })

  describe('uploadBrokerInvoice', () => {
    const mockDispatch = vi.fn()
    const mockFile: File = new File([''], 'test.xls', {
      type: 'application/vnd.ms-excel',
    })
    const mockBrokerInvoicePayload: BrokerInvoicePayload = {
      saleNumber: '1234',
      auctionDate: '2023-10-10',
      promptDate: '2023-10-10',
      file: mockFile,
    }
    const mockOnSuccess = vi.fn()

    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle upload broker invoice success', async () => {
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'postForm')
        .mockResolvedValue({
          data: { status: 'success', message: 'Success' },
        })

      await uploadBrokerInvoice(
        mockDispatch,
        mockBrokerInvoicePayload,
        {},
        mockOnSuccess
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsUploadingBrokerInvoices(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Success',
          type: 'success',
        })
      )
      expect(mockOnSuccess).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsUploadingBrokerInvoices(false)
      )
    })

    it('should handle upload broker invoice error', async () => {
      const mockError = new Error('Error uploading invoice')
      const mockAxiosPostHandler = vi
        .spyOn(apiclient, 'postForm')
        .mockRejectedValueOnce(mockError)

      await uploadBrokerInvoice(
        mockDispatch,
        mockBrokerInvoicePayload,
        {},
        mockOnSuccess
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsUploadingBrokerInvoices(true)
      )
      expect(mockAxiosPostHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error uploading invoice',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(
        setIsUploadingBrokerInvoices(false)
      )
    })
  })

  describe('getBrokerInvoices', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle get broker invoice success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockBrokerInvoicesResponse })

      await getBrokerInvoices(mockDispatch, mockFilters, true)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setBrokerInvoicesResponse(MockBrokerInvoicesResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get broker invoice error', async () => {
      const mockError = new Error('Error getting broker invoices')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getBrokerInvoices(mockDispatch, mockFilters, false)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting broker invoices',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })
  })

  describe('getBrokerInvoiceEntries', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle get broker invoice entries success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockBrokerInvoicesEntryResponse })

      await getBrokerInvoiceEntries(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setBrokerInvoiceEntryResponse(MockBrokerInvoicesEntryResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get broker invoice entries error', async () => {
      const mockError = new Error('Error getting broker invoice entries')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getBrokerInvoiceEntries(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting broker invoice entries',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })
  })

  describe('getInvoiceById', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle get invoice by id success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockBrokerInvoiceResponse })

      await getInvoiceById(mockDispatch, '123')

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setBrokerInvoiceResponse(MockBrokerInvoiceResponse.data)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get invoice by id error', async () => {
      const mockError = new Error('Error getting invoice by id')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getInvoiceById(mockDispatch, '123')

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting invoice by id',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })
  })

  describe('getTransactionHistory', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should map get invoice url correctly when no brokerId', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/wallet/transaction/?page=1&size=10&ascending=true'
      )
    })

    it('should map get invoice url correctly when brokerId is passed', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getTransactionHistory(mockDispatch, mockFilters, 'brokerId')

      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/wallet/transaction/brokerId/?page=1&size=10&ascending=true'
      )
    })

    it('should handle get transaction history by broker Id success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockTransactionHistoryResponse })

      await getTransactionHistory(mockDispatch, mockFilters, 'brokerId')

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTransactionHistoryResponse(MockTransactionHistoryResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get transaction history success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockTransactionHistoryResponse })

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTransactionHistoryResponse(MockTransactionHistoryResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get transaction history error', async () => {
      const mockError = new Error('Error getting transaction history')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting transaction history',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })
  })

  describe('dispatchInvoices', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should map get invoice url correctly when no brokerId', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/wallet/transaction/?page=1&size=10&ascending=true'
      )
    })

    it('should map get invoice url correctly when brokerId is passed', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getTransactionHistory(mockDispatch, mockFilters, 'brokerId')

      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/wallet/transaction/brokerId/?page=1&size=10&ascending=true'
      )
    })

    it('should handle get transaction history by broker Id success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockTransactionHistoryResponse })

      await getTransactionHistory(mockDispatch, mockFilters, 'brokerId')

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTransactionHistoryResponse(MockTransactionHistoryResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get transaction history success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockTransactionHistoryResponse })

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTransactionHistoryResponse(MockTransactionHistoryResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get transaction history error', async () => {
      const mockError = new Error('Error getting transaction history')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting transaction history',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })
  })

  describe('getProducerInvoiceEntries', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should map get invoice url correctly when no brokerId', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/wallet/transaction/?page=1&size=10&ascending=true'
      )
    })

    it('should map get invoice url correctly when brokerId is passed', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({})

      await getTransactionHistory(mockDispatch, mockFilters, 'brokerId')

      expect(mockAxiosGetHandler).toHaveBeenCalledExactlyOnceWith(
        '/v1/eatta-service/wallet/transaction/brokerId/?page=1&size=10&ascending=true'
      )
    })

    it('should handle get transaction history by broker Id success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockTransactionHistoryResponse })

      await getTransactionHistory(mockDispatch, mockFilters, 'brokerId')

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTransactionHistoryResponse(MockTransactionHistoryResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get transaction history success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: MockTransactionHistoryResponse })

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))

      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTransactionHistoryResponse(MockTransactionHistoryResponse)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })

    it('should handle get transaction history error', async () => {
      const mockError = new Error('Error getting transaction history')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getTransactionHistory(mockDispatch, mockFilters)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setIsLoading(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Error getting transaction history',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoading(false))
    })
  })
})
