/**
 * <AUTHOR> on 27/03/2025
 */

import { getInsights, getTopMetrics } from '@/store/actions/insights'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { apiclient } from '@/utils/apiclient'
import { mockInsightsResponse, mockTopMetricResponse } from '../stubs/insights'
import {
  setInsights,
  setIsLoadingInsights,
  setIsLoadingTopMetrics,
  setTopMetrics,
} from '@/store/reducers/insightsReducer'
import { setNotification } from '@dtbx/store/reducers'

vi.mock('@/utils/apiclient', () => ({
  apiclient: {
    get: vi.fn(),
  },
}))

describe('Insights actions', () => {
  describe('getInsights', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle get insights success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: mockInsightsResponse })

      await getInsights(mockDispatch, {})

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingInsights(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setInsights(mockInsightsResponse.data)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoadingInsights(false))
    })

    it('should handle get insights error', async () => {
      const mockError = new Error('Error fetching insights')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getInsights(mockDispatch, {})

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingInsights(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({message: 'Error fetching insights', type: 'error'})
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoadingInsights(false))
    })
  })

  describe('getTopMetrics', () => {
    const mockDispatch = vi.fn()
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should handle get top metrics success', async () => {
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockResolvedValue({ data: mockTopMetricResponse })

      await getTopMetrics(mockDispatch, {})

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingTopMetrics(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTopMetrics(mockTopMetricResponse.data)
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoadingTopMetrics(false))
    })

    it('should handle get top metrics api error', async () => {
      const mockError = new Error('Error fetching top metrics')
      const mockAxiosGetHandler = vi
        .spyOn(apiclient, 'get')
        .mockRejectedValueOnce(mockError)

      await getTopMetrics(mockDispatch, {})

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingTopMetrics(true)
      )
      expect(mockAxiosGetHandler).toHaveBeenCalledOnce()
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({message: 'Error fetching top metrics', type: 'error'})
      )
      expect(mockDispatch).toHaveBeenLastCalledWith(setIsLoadingTopMetrics(false))
    })
  })
})
