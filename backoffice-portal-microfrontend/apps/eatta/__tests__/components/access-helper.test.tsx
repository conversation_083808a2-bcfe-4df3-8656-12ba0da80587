/**
 * <AUTHOR> on 07/04/2025
 */

import { describe, expect, it, vi } from 'vitest'
import { render } from '../test-utils'
import { AccessWrapper } from '@/components/AccessHelper'
import { IAuth } from '@dtbx/store/reducers'

describe('AccessWrapper', () => {
  const mockAuthState = {
    decodedToken: { clientType: 'Buyer' },
  } as IAuth

  it('should renders children when user has access', () => {
    mockAuthState.decodedToken.clientType = 'Buyer'
    const { queryByText } = render(
      <AccessWrapper clientTypes={['Buyer']} backofficeAccess={false}>
        <div>Access Granted</div>
      </AccessWrapper>,
      {
        preloadedState: {
          auth: mockAuthState,
        },
      }
    )

    expect(queryByText('Access Granted')).toBeInTheDocument()
  })

  it('should not render children when user does not have access', () => {
    mockAuthState.decodedToken.clientType = 'Broker'
    const { queryByText } = render(
      <AccessWrapper clientTypes={['Buyer']} backofficeAccess={false}>
        <div>Access Granted</div>
      </AccessWrapper>,
      {
        preloadedState: {
          auth: mockAuthState,
        },
      }
    )

    expect(queryByText('Access Granted')).not.toBeInTheDocument()
  })

  it('should renders children when backoffice access is true', () => {
    vi.stubEnv('NEXT_PUBLIC_EATTA_BUILD', 'backoffice')
    const { queryByText } = render(
      <AccessWrapper backofficeAccess={true}>
        <div>Access Granted</div>
      </AccessWrapper>,
      {
        preloadedState: {
          auth: mockAuthState,
        },
      }
    )

    expect(queryByText('Access Granted')).toBeInTheDocument()
  })

  it('should not renders children when backoffice access is true and build is client', () => {
    vi.stubEnv('NEXT_PUBLIC_EATTA_BUILD', 'client')
    mockAuthState.decodedToken.clientType = 'Broker'
    const { queryByText } = render(
      <AccessWrapper backofficeAccess={true}>
        <div>Access Granted</div>
      </AccessWrapper>,
      {
        preloadedState: {
          auth: mockAuthState,
        },
      }
    )

    expect(queryByText('Access Granted')).not.toBeInTheDocument()
  })
})
