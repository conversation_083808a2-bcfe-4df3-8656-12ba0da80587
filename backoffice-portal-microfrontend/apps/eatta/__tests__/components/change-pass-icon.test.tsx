/**
 * <AUTHOR> on 04/04/2025
 */

import { describe, expect, it } from 'vitest'
import { ChangePasswordIcon } from '@/components/SvgIcons/ChangePassword'
import { render } from '../test-utils'

describe('ChangePassIcon', () => {
  it('should render change password icon', () => {
    const { getByTestId } = render(<ChangePasswordIcon />)

    expect(getByTestId('change-pass-icon')).toBeVisible()
  })
})
