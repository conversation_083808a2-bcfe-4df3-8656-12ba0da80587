/**
 * <AUTHOR> on 27/03/2025
 */
import { PaginatedResponse } from '@dtbx/store/interfaces'
import {
  BrokerInvoiceResponse,
  BrokerInvoicesEntryResponse,
  BrokerInvoicesResponse,
  InvoiceEslip,
  TransactionHistory,
} from '@/store/interfaces'

export const MockInvoiceEslipResponse: PaginatedResponse<InvoiceEslip> = {
  totalElements: 0,
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  data: [
    {
      id: '012744a8-fb5f-41cd-b4fd-7088955e3779',
      invoiceNumber: 'DTBBuyer1002OJ4583G4KM',
      invoiceEntries: [],
      status: 'NEW',
      paidAmount: 0,
      balance: 3409.67,
      lotCount: 1,
      penalties: 0,
      totalTeaValue: 3426.8,
      totalBrokerCommission: 17.13,
      totalWithholdingTax: 0.87,
      totalAmountToPay: 3409.67,
      totalSoldWeight: 2636.0,
    },
  ],
}

export const Mo<PERSON>TransactionHistoryResponse: PaginatedResponse<TransactionHistory> =
  {
    totalElements: 71,
    size: 10,
    page: 0,
    totalNumberOfPages: 8,
    data: [
      {
        id: '4fa85f64-5717-4562-b3fc-2c963f66afa7',
        transactionReference: 'TRX123456790',
        ledgerEntryType: 'DEBIT',
        dateCreated: '2024-03-26T15:45:00Z',
        channel: 'WEB',
        source: 'EATTA_WALLET',
        destination: 'BANK_TRANSFER',
        amount: 25000.0,
        balance: '100000.00',
        currency: 'KES',
        description: 'Withdrawal to Bank Account',
        walletName: 'EATTA Trading Wallet',
      },
    ],
  }

export const MockBrokerInvoiceResponse: BrokerInvoiceResponse = {
  status: '',
  message: '',
  errors: [],
  data: {
    id: 'c58b300b-b627-4400-8d9d-955d2f940e8b',
    invoiceNumber: 'INVTEST/25',
    buyerCode: 'Buyer1002',
    brokerCode: 'BROKER104',
    saleNumber: '20',
    auctionDate: '2025-05-20',
    promptDate: '2025-06-03',
    fileHash:
      '7a8b3e18d4c248eba0cbd2cbb9e8e9a12ff777f8015be54af6f59feb4ea73a90',
    status: 'UNPAID',
    soldWeight: 2636.0,
    totalInvoiceAmount: 3426.8,
    amountReceived: 0,
    remainingBalance: 3426.8,
    lotCount: 1,
  },
}

export const MockBrokerInvoicesEntryResponse: BrokerInvoicesEntryResponse = {
  totalElements: 71,
  size: 10,
  page: 0,
  totalNumberOfPages: 8,
  data: [
    {
      id: '659313fc-5fe3-4da2-9a24-409f73d571ea',
      invoiceNumber: 'INVTEST/25',
      gardenInvoice: '12101/25',
      lotNumber: 12101,
      auctionDate: '2025-05-20',
      buyerCode: 'Buyer1002',
      broker: 'BROKER104',
      mark: 'ATEFACTORYA',
      grade: 'PF1',
      totalWeight: 2636.0,
      saleDate: '20',
      category: null,
      pricePerKg: 1.25,
      netWeight: 2636.0,
      unitWeight: 66.0,
      totalValue: 3426.8,
      brokerShare: 17.13,
      withholdingTax: 0.87,
      netAmount: 3409.67,
      netPayable: 3409.67,
      warehouse: 'WarehouseABC',
      bags: '40.0',
      datePaid: null,
      teaReleaseDocumentDate: null,
      status: 'UNPAID',
      brokerCommissionFromProducer: 25.7,
      producerShare: 3401.09,
      producerExpectedAmount: 3401.09,
      errors: null,
    },
  ],
}

export const MockBrokerInvoicesResponse: BrokerInvoicesResponse = {
  totalElements: 71,
  size: 10,
  page: 0,
  totalNumberOfPages: 8,
  data: [
    {
      id: 'c58b300b-b627-4400-8d9d-955d2f940e8b',
      invoiceNumber: 'INVTEST/25',
      buyerCode: 'Buyer1002',
      brokerCode: 'BROKER104',
      saleNumber: '20',
      auctionDate: '2025-05-20',
      promptDate: '2025-06-03',
      fileHash:
        '7a8b3e18d4c248eba0cbd2cbb9e8e9a12ff777f8015be54af6f59feb4ea73a90',
      status: 'UNPAID',
      soldWeight: 2636.0,
      totalInvoiceAmount: 3426.8,
      amountReceived: 0,
      remainingBalance: 3426.8,
      lotCount: 1,
    },
  ],
}
