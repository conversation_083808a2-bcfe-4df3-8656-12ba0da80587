/**
 * <AUTHOR> on 11/04/2025
 */
import {
  Bank,
  BankBranch,
  Company,
  CompanyUser,
  CompanyUserRequest,
  EditCompanyUser,
  CompanyPayment,
} from '@/store/interfaces'
import { PaginatedResponse } from '@dtbx/store/interfaces'

export const MockBankResponse: Bank[] = [
  {
    bankCode: '063',
    bankName: 'DIAMOND TRUST BANK',
    bic: 'DTKEKENA',
    bicCode: 'DTKEKENA',
    ccCode: '63',
    shortName: 'DTB',
  },
]

export const MockBankBranchesResponse: BankBranch[] = [
  {
    code: '040',
    name: 'Madina Mall Branch',
    city: 'Nairobi',
    address:
      '1st Avenue and Starehe/Pumwani Road Basement, next to Tuskys Supermarket Eastleigh',
    phone: '+*********** 450',
  },
  {
    code: '024',
    name: 'Buruburu Branch',
    city: 'Nairobi',
    address: '1st Floor, Fairlane Centre, Off Mumias South Road',
    phone: '+*********** 130',
  },
]

export const MockEditUser: EditCompanyUser = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '**********',
  phoneNumberCountryId: '1',
  username: 'johndoe',
  status: 'ACTIVE',
}

export const MockCompanyUserRequest: CompanyUserRequest = {
  name: 'John Doe',
  email: '<EMAIL>',
  phoneNumber: '+*********0',
  phoneNumberCountryId: 'US',
  username: 'john_doe',
  organizationCode: 'ORG001',
  isCompanyAdmin: true,
  nationalId: '*********',
}

export const MockPaymentDetails: CompanyPayment = {
  bankName: 'Test Bank',
  bankCode: '123',
  bankBranchName: 'Central Branch',
  bankBranchCode: '12345',
  accountNumber: '*********',
  accountCurrency: 'USD',
  channel: 'IFT',
  swiftCode: 'SWFT123',
  countryCode: 'US',
}

export const CompanyUsersResponse: PaginatedResponse<CompanyUser> = {
  totalElements: 1,
  size: 10,
  page: 0,
  totalNumberOfPages: 1,
  data: [
    {
      id: '184aefae-11c4-430e-a896-f52f29ca02b7',
      name: 'MyTest MyTest',
      email: '<EMAIL>',
      phoneNumber: '*********',
      phoneNumberCountryId: 'KE',
      username: '<EMAIL>',
      status: 'ACTIVE',
      companyCode: '',
      password: '',
      lastLogin: '',
      resetToken: '',
    },
  ],
}

export const MockCompaniesResponse: PaginatedResponse<Company> = {
  totalElements: 60,
  size: 10,
  page: 0,
  totalNumberOfPages: 6,
  data: [
    {
      id: 'b3336dfa-80fd-4df4-993f-b95f2be37b78',
      name: 'ANMVS',
      type: 'Warehouse',
      code: '0001',
      accountNumber: '',
      phoneNumber: '+************',
      emailAddress: 'acnncsks@gjssksks',
      dateCreated: '',
      dateModified: '',
      createdBy: '',
      modifiedBy: '',
      teamMembers: 0,
      organizationSetUpStage: 'KYC',
      accountCurrency: '',
      bankCode: '',
      swiftCode: '',
      bankName: '',
      bankBranchCode: '',
      locationAddress: '',
      clientId: '',
    },
  ],
}
