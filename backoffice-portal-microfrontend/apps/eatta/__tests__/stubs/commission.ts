/**
 * <AUTHOR> on 04/04/2025
 */
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { BrokerCommissionResponse } from '@/store/interfaces'

export const MockBrokerCommission: PaginatedResponse<BrokerCommissionResponse> = {
  totalElements: 10,
  size: 10,
  page: 0,
  totalNumberOfPages: 1,
  data: [
    {
      id: '4fa85f64-5717-4562-b3fc-2c963f66afa7',
      brokerBuyerCommissionPercentage: 2.0,
      brokerProducerCommissionPercentage: 1.25,
      status: 'INACTIVE',
      measure: 'PERCENTAGE',
      createdBy: '<EMAIL>',
      dateCreated: '2024-04-03T15:30:00Z'
    }
  ]
}