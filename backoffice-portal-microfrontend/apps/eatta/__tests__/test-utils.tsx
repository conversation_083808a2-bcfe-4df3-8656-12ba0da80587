/**
 * <AUTHOR> on 07/04/2025
 */
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import React, { ReactNode } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { AppStore, RootState, setupStore } from '@/store'
import { Provider } from 'react-redux'

interface ExtendedRenderOptions
  extends Omit<RenderOptions, 'queries' | 'wrapper'> {
  preloadedState?: Partial<RootState>
  store?: AppStore
}

const renderWithProviders = (
  ui: ReactNode,
  {
    preloadedState = {},
    store = setupStore(preloadedState),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) => {
  const Wrapper = ({ children }: { children: ReactNode }) => {
    return (
      <Provider store={store}>
        <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
          <ThemeConfig themeType={'eatta'}>{children}</ThemeConfig>
        </NextAppDirEmotionCacheProvider>
      </Provider>
    )
  }
  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

export * from '@testing-library/react'
export { renderWithProviders as render }
