<svg width="481" height="481" viewBox="0 0 481 481" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_137_34186" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="481" height="481">
<rect width="480" height="480" transform="translate(0.5 0.929688)" fill="url(#paint0_radial_137_34186)"/>
</mask>
<g mask="url(#mask0_137_34186)">
<circle cx="240.5" cy="240.93" r="47.5" stroke="#EAECF0"/>
<circle cx="240.5" cy="240.93" r="79.5" stroke="#EAECF0"/>
<circle cx="240.5" cy="240.93" r="111.5" stroke="#EAECF0"/>
<circle cx="240.5" cy="240.93" r="143.5" stroke="#EAECF0"/>
<circle cx="240.5" cy="240.93" r="143.5" stroke="#EAECF0"/>
<circle cx="240.5" cy="240.93" r="175.5" stroke="#EAECF0"/>
<circle cx="240.5" cy="240.93" r="207.5" stroke="#EAECF0"/>
<circle cx="240.5" cy="240.93" r="239.5" stroke="#EAECF0"/>
</g>
<defs>
<radialGradient id="paint0_radial_137_34186" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(240 240) rotate(90) scale(240 240)">
<stop/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
