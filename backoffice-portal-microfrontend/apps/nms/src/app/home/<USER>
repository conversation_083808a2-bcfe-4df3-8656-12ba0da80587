'use client'
import { <PERSON>, Divider, Stack, Typography } from '@mui/material'
import { NotificationIcon } from '@dtbx/ui/icons'
import React, { useEffect, useState } from 'react'
import NoTemplates from './NoTemplates'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import { TemplateLists } from '@/app/tables'
import { getAllTemplates } from '@/store/actions/templates'
import { useAppDispatch, useAppSelector } from '@/store'
import { LoadingListsSkeleton } from '@dtbx/ui/components'

export default function NotificationsHomePage() {
  //store items
  const { notificationTemplates, isLoadingTemplates, templatesPagination } =
    useAppSelector((state) => state.templates)
  const dispatch = useAppDispatch()

  const [value, setValue] = useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  useEffect(() => {
    getAllTemplates(dispatch, 'page=1&size=10')
  }, [])
  return (
    <Stack>
      {/* Title section */}
      <Stack
        direction="row"
        alignItems="center"
        padding="1vh 2vw"
        gap="0.5vw"
        sx={{ backgroundColor: 'neutral.neutral5' }}
      >
        <NotificationIcon />
        <Typography variant="h5">All Notification Templates</Typography>
      </Stack>
      <AntTabs
        value={value}
        onChange={handleChange}
        aria-label="ant example"
        sx={{
          marginLeft: '2%',
        }}
      >
        <AntTab
          label={'Active'}
          sx={{
            marginBottom: '-10px',
          }}
          icon={
            <Chip
              label={`${templatesPagination.totalElements || '0'}`}
              size="small"
            />
          }
          iconPosition="end"
        />
        <AntTab
          label={'Pending'}
          sx={{
            marginBottom: '-10px',
          }}
          icon={<Chip label={'0'} size="small" />}
          iconPosition="end"
        />
        <AntTab
          label={'Inactive'}
          sx={{
            marginBottom: '-10px',
          }}
          icon={<Chip label={'0'} size="small" />}
          iconPosition="end"
        />
      </AntTabs>
      <Divider />
      <Stack
        sx={{
          px: '2%',
        }}
      >
        {isLoadingTemplates ? (
          <LoadingListsSkeleton />
        ) : notificationTemplates.length < 1 ? (
          <Stack
            minHeight="81.7vh"
            padding="2vh 2.2vw"
            justifyContent="center"
            alignItems="center"
            sx={{ backgroundColor: 'neutral.neutral4' }}
          >
            <Stack width="30%">
              <NoTemplates />
            </Stack>
          </Stack>
        ) : (
          <>
            <TabPanel value={value} index={0}>
              <TemplateLists type={'Active'} data={notificationTemplates} />
            </TabPanel>
            <TabPanel value={value} index={1}>
              <TemplateLists type={'Pending'} data={notificationTemplates} />
            </TabPanel>
            <TabPanel value={value} index={2}>
              <TemplateLists type={'Inactive'} data={notificationTemplates} />
            </TabPanel>
          </>
        )}
      </Stack>
    </Stack>
  )
}
