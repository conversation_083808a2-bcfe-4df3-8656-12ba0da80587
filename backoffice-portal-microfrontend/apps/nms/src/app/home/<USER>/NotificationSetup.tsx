import {
  Autocomplete,
  Button,
  FormControl,
  FormHelperText,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { FormikProps } from 'formik'
import { ICreateNotificationTemplate } from '@/store/interfaces'
import React, { useEffect } from 'react'
import { ArrowRightIcon } from '@dtbx/ui/icons'
import { useAppSelector } from '@/store'
export type ITemplateCreateProps = {
  formik: FormikProps<ICreateNotificationTemplate>
  setCurrentStep: (step: number) => void
}

export const NotificationSetupStep = ({
  formik,
  setCurrentStep,
}: ITemplateCreateProps) => {
  const { notificationEvents } = useAppSelector((state) => state.templates)

  useEffect(() => {
    const desc = notificationEvents
      .find((event) => event.id === formik.values.eventId)
      ?.eventName.split(' ')
      .join('_')
    formik.setFieldValue('templateName', desc)
    formik.setFieldValue(
      'placeholders',
      notificationEvents.find((event) => event.id === formik.values.eventId)
        ?.placeHolders
    )
  }, [formik.values.eventId])

  return (
    <Stack justifyContent="center" alignItems="center" gap="3vh">
      <Stack gap="1.2vh" alignItems="center">
        <Typography variant="subtitle2" color="#555C61">
          STEP 1 of 2
        </Typography>
        <Typography variant="h6">Notification Setup</Typography>
        <Typography variant="subtitle2" color="#555C61">
          Configure your notification preferences
        </Typography>
      </Stack>
      <Stack
        width="60%"
        padding="2.5vh 1.3vw"
        gap="2.5vh"
        sx={{
          backgroundColor: '#FFFFFF',
          borderRadius: '6px',
          border: '1px solid #D0D5DD',
        }}
      >
        <FormControl fullWidth >
          <FormHelperText
            error={formik.touched.eventId && Boolean(formik.errors.eventId)}
          >
            {formik.touched.eventId && formik.errors.eventId}
          </FormHelperText>
          <Autocomplete
            options={notificationEvents}
            getOptionLabel={(option) => option.eventName}
            value={
              notificationEvents.find(
                (category) => category.id === formik.values.eventId
              ) || null
            }
            onChange={(event, newValue) =>
              formik.setFieldValue('eventId', newValue?.id || '')
            }
            isOptionEqualToValue={(option, value) => option.id === value.id}
            renderInput={(params) => (
              <TextField
                {...params}
                label="What event triggers this notification?"
                variant="outlined"
              />
            )}
          />
        </FormControl>

        <TextField
          size="medium"
          label="Descriptive name for this notification template*"
          InputLabelProps={{ shrink: true }}
          slotProps={{ htmlInput: { readOnly: true } }}
          placeholder="Will be generated according to the trigger event chosen above "
          {...formik.getFieldProps('templateName')}
          error={
            formik.touched.templateName && Boolean(formik.errors.templateName)
          }
          helperText={
            (formik.touched.templateName && formik.errors.templateName) ||
            'This name is generated according to the event chosen, but you can append your own addition if need be'
          }
        />

        <TextField
          size="medium"
          label=" Template description"
          {...formik.getFieldProps('templateDescription')}
          error={
            formik.touched.templateDescription &&
            Boolean(formik.errors.templateDescription)
          }
          helperText={
            formik.touched.templateDescription &&
            formik.errors.templateDescription
          }
        />

        <Button
          variant="contained"
          endIcon={
            !!(
              formik.errors.templateName ||
              formik.errors.templateDescription ||
              formik.errors.eventId
            ) ? (
              <ArrowRightIcon stroke="rgba(0,0,0,0.26)" />
            ) : (
              <ArrowRightIcon />
            )
          }
          disabled={
            !!(
              formik.errors.templateName ||
              formik.errors.templateDescription ||
              formik.errors.eventId
            )
          }
          onClick={() => setCurrentStep(1)}
        >
          Next
        </Button>
      </Stack>
    </Stack>
  )
}
