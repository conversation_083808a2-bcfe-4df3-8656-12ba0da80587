import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  StepConnector,
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import React, { useEffect, useState } from 'react'
import { Form, FormikProvider, useFormik } from 'formik'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import CloseIcon from '@mui/icons-material/Close'
import { NotificationIcon, OutlinedSettingsIcon } from '@dtbx/ui/icons'
import { NotificationSetupStep } from '@/app/home/<USER>/NotificationSetup'
import { ContentConfigStep } from '@/app/home/<USER>/ContentConfig'
import * as Yup from 'yup'
import {
  createNotificationTemplate,
  getNotificationEvents,
} from '@/store/actions/templates'
import { useAppDispatch } from '@/store'

interface INewTemplateDrawerProps {
  origin?: string
}
const steps = [
  {
    icon: <NotificationIcon />,
    title: 'Notification Setup',
    description: 'Configure your notification preferences',
  },
  {
    icon: <OutlinedSettingsIcon />,
    title: 'Content Configuration',
    description: 'Customize your notification message',
  },
]
export const CreateNotificationTemplate = ({
  origin,
}: INewTemplateDrawerProps) => {
  const [open, setOpen] = useState<boolean>(false)
  const [currentStep, setCurrentStep] = useState<number>(0)
  const dispatch = useAppDispatch()
  const validationSchema = Yup.object({
    templateName: Yup.string().required('Template name is required'),
    smsTemplateSubject: Yup.string().when(
      'htmlContent',
      ([htmlContent], schema) =>
        htmlContent === 'sms'
          ? schema.required('Template Subject is required')
          : schema
    ),
    emailTemplateSubject: Yup.string().when(
      'htmlContent',
      ([htmlContent], schema) =>
        htmlContent === 'email'
          ? schema.required('Template Subject is required')
          : schema
    ),
    smsTemplateContent: Yup.string().when(
      'htmlContent',
      ([htmlContent], schema) =>
        htmlContent === 'sms'
          ? schema
              .required('Template Content is required')
              .min(1, 'Minimum of 1 Character is required')
              .max(160, 'Maximum of 160 characters allowed')
          : schema
    ),
    emailTemplateContent: Yup.string().when(
      'htmlContent',
      ([htmlContent], schema) =>
        htmlContent === 'email'
          ? schema.required('Template Content is required')
          : schema
    ),
    templateDescription: Yup.string().required(
      'Template Description is required'
    ),
    eventId: Yup.string().required('Event is required'),
  })
  const formik = useFormik({
    initialValues: {
      templateName: '',
      smsTemplateSubject: '',
      emailTemplateSubject: '',
      smsTemplateContent: '',
      emailTemplateContent: '',
      templateDescription: '',
      eventId: '',
      htmlContent: '',
      placeholders: [] as string[],
    },
    validationSchema,
    onSubmit: async (values) => {
      await createNotificationTemplate(values, dispatch)
      formik.resetForm()
      setOpen(false)
    },
  })

  const handleClose = (e: React.SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
    setCurrentStep(0)
    formik.resetForm()
  }
  useEffect(() => {
    getNotificationEvents(dispatch)
  }, [])
  return (
    <>
      {origin === 'list' ? (
        <Button
          startIcon={<AddIcon />}
          variant="contained"
          onClick={() => setOpen(!open)}
        >
          Create new notification template
        </Button>
      ) : (
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={() => setOpen(!open)}
          sx={{
            border: '1px solid #D0D5DD',
            textWrap: 'nowrap',
          }}
        >
          Create New Template
        </Button>
      )}
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '95%',
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '95%',
          },
        }}
      >
        <Stack
          sx={{
            background: 'neutral.neutral5',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            alignContent: 'center',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: 4,
                alignItems: 'center',
                alignContent: 'center',
                py: '0.8rem',
              }}
            >
              <IconButton
                onClick={(e) => handleClose(e, 'close')}
                sx={{ py: '0.2rem' }}
              >
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="subtitle2" color={'primary.main'}>
                Create New Template
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
              border: '1px solid #CBD5E1',
              borderRadius: '50%',
              mx: '1rem',
              py: '0.5rem',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack
          sx={{
            flexDirection: 'row',
            backgroundColor: '#F7F7F7',
            flexBasis: '92.8vh',
          }}
        >
          <Stack
            sx={{
              width: '25%',
              px: '4%',
              alignItems: 'flex-start',
              flexDirection: 'column',
              py: '2%',
              borderRight: '1px solid #E3E4E4',
            }}
          >
            <Stepper
              activeStep={currentStep}
              orientation="vertical"
              connector={
                <StepConnector
                  sx={{
                    '&.MuiStepConnector-vertical': {
                      height: '2vh',
                      borderColor: '#EAECF0',
                      borderWidth: 2,
                      marginLeft: '1.5vw',
                    },
                  }}
                />
              }
            >
              {steps.map(({ icon, title, description }, index) => (
                <Step
                  key={title}
                  sx={{
                    flexDirection: 'column',
                    cursor: 'pointer',
                  }}
                  onClick={() => setCurrentStep(index)}
                >
                  <StepLabel
                    icon={
                      <Stack
                        sx={{
                          border: '1px solid #E3E4E4',
                          borderRadius: '10px',
                          alignItems: 'center',
                          alignContent: 'center',
                          justifyContent: 'center',
                          height: 48,
                          width: 48,
                          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                          marginRight: '0.4vw',
                        }}
                      >
                        {icon}
                      </Stack>
                    }
                  >
                    <Typography variant="body1" color="primary.main">
                      {title}
                    </Typography>
                    <Typography>{description}</Typography>
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </Stack>
          
          <Stack
            sx={{
              width: '75%',
              py: '2%',
              px: '4%',
            }}
          >
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                {(() => {
                  switch (currentStep) {
                    case 0:
                      return (
                        <NotificationSetupStep
                          formik={formik}
                          setCurrentStep={setCurrentStep}
                        />
                      )
                    case 1:
                      return (
                        <ContentConfigStep
                          formik={formik}
                          setCurrentStep={setCurrentStep}
                        />
                      )
                    default:
                      return (
                        <NotificationSetupStep
                          formik={formik}
                          setCurrentStep={setCurrentStep}
                        />
                      )
                  }
                })()}
              </Form>
            </FormikProvider>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
