import {
  Button,
  Checkbox,
  Chip,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  IconButton,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material'
import { ITemplateCreateProps } from '@/app/home/<USER>/NotificationSetup'
import React, { useCallback, useEffect, useState } from 'react'
import { ArrowForward } from '@mui/icons-material'
import { FormikProps } from 'formik'
import { ICreateNotificationTemplate } from '@/store/interfaces'
import { Editor, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import { useAppDispatch, useAppSelector } from '@/store'
import { setFocusedField } from '@/store/reducers'
import { RichTextEditorProvider, RichTextField } from 'mui-tiptap'
import styles from '@/app/home/<USER>/styles/RichTextEditor.module.css'
import {
  BoldIcon,
  BulletsIcon,
  InsertImageIcon,
  ItalicsIcon,
  LargeHeadingIcon,
  LinkIcon,
  NumberingIcon,
  QuoteIcon,
  SmallHeadingIcon,
} from '@dtbx/ui/components/SvgIcons'
import { LoadingButton } from '@dtbx/ui/components'

export const ContentConfigStep = ({
  formik,
  setCurrentStep,
}: ITemplateCreateProps) => {
  const [openBody, setOpenBody] = useState('')
  const isLoading = useAppSelector(
    (state) => state.templates.isLoadingNotificationEvents
  )
  const handleCheckBox = (type: 'email' | 'sms') => {
    setOpenBody(type)
    formik.setFieldValue('htmlContent', type)
  }

  return (
    <Stack justifyContent="center" alignItems="center">
      <Stack gap="1.2vh" alignItems="center">
        <Typography variant="subtitle2" color="#555C61">
          STEP 2 of 2
        </Typography>
        <Typography variant="h6">Content Configuration</Typography>
        <Typography variant="subtitle2" color="#555C61">
          Customize your notification message
        </Typography>
      </Stack>
      <Stack
        width="60%"
        padding="2.5vh 1.3vw"
        sx={{
          borderRadius: '6px',
        }}
      >
        <FormControl>
          <FormLabel sx={{ fontWeight: '600' }}>Select mode of delivery*</FormLabel>
          <FormGroup
            sx={{
              gap: '1vh',
            }}
          >
            <FormLabel sx={{ color: '#475467' }}>Select all that apply</FormLabel>
            <Stack
              sx={{
                borderRadius: '8px',
                background: '#FFFFFF',
                py: '2%',
                px: '1.3vw',
              }}
            >
              <Stack direction="row" justifyContent="space-between">
                <Stack>
                  <Typography>SMS</Typography>
                  <Typography>
                    Sent as an SMS to the user’s mobile number.
                  </Typography>
                </Stack>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formik.values.htmlContent === 'sms'}
                      onChange={() => handleCheckBox('sms')}
                    />
                  }
                  label=""
                />
              </Stack>
              <SMSBody openBody={openBody} formik={formik} />
            </Stack>
            <Stack
              sx={{
                borderRadius: '8px',
                background: '#FFFFFF',
                py: '2%',
                px: '1.5vw',
              }}
            >
              <Stack justifyContent="space-between" direction="row">
                <Stack>
                  <Typography>Email</Typography>
                  <Typography>
                    Sent as a message to the user’s email.
                  </Typography>
                </Stack>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formik.values.htmlContent === 'email'}
                      onChange={() => handleCheckBox('email')}
                    />
                  }
                  label=""
                />
              </Stack>
              <EmailBody openBody={openBody} formik={formik} />
            </Stack>
          </FormGroup>
          {isLoading ? (
            <Stack
              sx={{
                marginTop: '2vh',
              }}
            >
              <LoadingButton />
            </Stack>
          ) : (
            <Button
              variant="contained"
              sx={{
                marginTop: '2vh',
              }}
              disabled={!formik.isValid || formik.isSubmitting}
              type="submit"
              endIcon={<ArrowForward />}
            >
              Create Template
            </Button>
          )}
        </FormControl>
      </Stack>
    </Stack>
  )
}

const SMSBody = ({
  openBody,
  formik,
}: {
  openBody: string
  formik: FormikProps<ICreateNotificationTemplate>
}) => {
  const handleChipClick = (placeholder: string) => {
    const currentContent = formik.values.smsTemplateContent
    const updatedContent = currentContent + `${placeholder}`
    formik.setFieldValue('smsTemplateContent', updatedContent)
  }
  return (
    <Stack sx={{ display: openBody === 'sms' ? 'flex' : 'none' }}>
      <TextField
        margin="normal"
        placeholder="SMS subject"
        label="Subject"
        error={Boolean(
          formik.touched.smsTemplateSubject && formik.errors.smsTemplateSubject
        )}
        helperText={
          formik.touched.smsTemplateSubject && formik.errors.smsTemplateSubject
        }
        {...formik.getFieldProps('smsTemplateSubject')}
        sx={{
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#F9FAFB',
          },
        }}
      />

      <TextField
        margin="normal"
        placeholder="Body message..."
        multiline
        label="Body"
        error={Boolean(
          formik.touched.smsTemplateContent && formik.errors.smsTemplateContent
        )}
        helperText={
          formik.touched.smsTemplateContent && formik.errors.smsTemplateContent
        }
        rows={5}
        {...formik.getFieldProps('smsTemplateContent')}
        sx={{
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#F9FAFB',
          },
        }}
      />
      <Stack gap="1vh">
        <Stack>
          <Typography variant="subtitle2">Available Placeholders</Typography>
          <Typography variant="body2">
            Click on a placeholder below to insert it into the body message
          </Typography>
        </Stack>
        <Stack
          direction="row"
          rowGap="1vh"
          columnGap="0.5vw"
          sx={{ width: '100%', flexWrap: 'wrap' }}
        >
          {formik.values.placeholders?.map((holder) => (
            <Chip
              key={holder}
              label={holder.replace(/\$\{(.*?)\}/g, '$1')}
              variant="outlined"
              onClick={() => handleChipClick(holder)}
              sx={{
                background: '#F3F5F5',
                border: 'none',
                cursor: 'pointer',
              }}
            />
          ))}
        </Stack>
      </Stack>
    </Stack>
  )
}
const EmailBody = ({
  openBody,
  formik,
}: {
  openBody: string
  formik: FormikProps<ICreateNotificationTemplate>
}) => {
  const dispatch = useAppDispatch()
  const focusedField = useAppSelector((state) => state.templates.focusedField)
  const handleChipClick = (placeholder: string) => {
    if (focusedField === 'emailSubject') {
      const currentContent = formik.values.emailTemplateSubject
      const updatedContent = currentContent + `${placeholder}`
      formik.setFieldValue('emailTemplateSubject', updatedContent)
    }

    if (focusedField === 'emailBody') {
      const currentContent = formik.values.emailTemplateContent
      const updatedContent = currentContent + `${placeholder}`
      formik.setFieldValue('emailTemplateContent', updatedContent)
    }
  }

  // Tiptap editor instance
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'Body message...',
      }),
      Link,
      Image,
    ],
    content: formik.values.emailTemplateContent,
    onUpdate: ({ editor }) => {
      formik.setFieldValue('emailTemplateContent', editor.getHTML())
    },
    onFocus: () => {
      dispatch(setFocusedField('emailBody'))
    },
    editorProps: {
      attributes: {
        style:
          'width: 100%; max-width: 464px; height: 150px; overflow-y: scroll;',
      },
    },
  })

  // Rich Text Styling buttons
  const textEditorButtons = [
    {
      name: 'bold',
      icon: <BoldIcon fill="#98A2B3" />,
      tooltip: 'Bold',
      onClick: (editor: Editor | null) =>
        editor?.chain().focus().toggleBold().run(),
    },
    {
      name: 'italics',
      icon: <ItalicsIcon fill="#98A2B3" />,
      tooltip: 'Italicize',
      onClick: (editor: Editor | null) =>
        editor?.chain().focus().toggleItalic().run(),
    },
    {
      name: 'large heading',
      icon: <LargeHeadingIcon fill="#98A2B3" />,
      tooltip: 'Large heading',
      onClick: (editor: Editor | null) =>
        editor?.chain().focus().toggleHeading({ level: 2 }).run(),
    },
    {
      name: 'small heading',
      icon: <SmallHeadingIcon fill="#98A2B3" />,
      tooltip: 'Small heading',
      onClick: (editor: Editor | null) =>
        editor?.chain().focus().toggleHeading({ level: 3 }).run(),
    },
    {
      name: 'quote',
      icon: <QuoteIcon fill="#98A2B3" />,
      tooltip: 'Quote',
      onClick: (editor: Editor | null) =>
        editor?.chain().focus().toggleBlockquote().run(),
    },
    {
      name: 'link',
      icon: <LinkIcon fill="#98A2B3" />,
      tooltip: 'Link',
      onClick: useCallback(() => {
        const previousUrl = editor?.getAttributes('link').href
        const url = window.prompt('URL', previousUrl)

        // cancelled
        if (url === null) {
          return
        }

        // empty
        if (url === '') {
          editor?.chain().focus().extendMarkRange('link').unsetLink().run()

          return
        }

        // update link
        try {
          editor
            ?.chain()
            .focus()
            .extendMarkRange('link')
            .setLink({ href: url })
            .run()
        } catch (e) {
          alert((e as Error).message)
        }
      }, [editor]),
    },
    {
      name: 'image',
      icon: <InsertImageIcon fill="#98A2B3" />,
      tooltip: 'Image',
      onClick: useCallback(() => {
        const url = window.prompt('URL')

        if (url) {
          editor?.chain().focus().setImage({ src: url }).run()
        }
      }, [editor]),
    },
    {
      name: 'bullets',
      icon: <BulletsIcon fill="#98A2B3" />,
      tooltip: 'Bullets',
      onClick: (editor: Editor | null) =>
        editor?.chain().focus().toggleBulletList().run(),
    },
    {
      name: 'numbered list',
      icon: <NumberingIcon fill="#98A2B3" />,
      tooltip: 'Numbered list',
      onClick: (editor: Editor | null) =>
        editor?.chain().focus().toggleOrderedList().run(),
    },
  ]

  // Update the editor when placeholders are added
  useEffect(() => {
    if (editor && formik.values.emailTemplateContent !== editor.getHTML()) {
      editor.commands.setContent(formik.values.emailTemplateContent)
    }
  }, [formik.values.emailTemplateContent, editor])

  return (
    <Stack gap="1vh" sx={{ display: openBody === 'email' ? 'flex' : 'none' }}>
      <TextField
        margin="normal"
        placeholder="Email subject"
        label="Subject"
        onFocus={() => dispatch(setFocusedField('emailSubject'))}
        error={Boolean(
          formik.touched.emailTemplateSubject &&
            formik.errors.emailTemplateSubject
        )}
        helperText={
          formik.touched.emailTemplateSubject &&
          formik.errors.emailTemplateSubject
        }
        {...formik.getFieldProps('emailTemplateSubject')}
        sx={{
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#F9FAFB',
          },
        }}
      />

      {/* Styling Buttons */}
      <Stack direction="row" columnGap="0.3vw" justifyContent="flex-start">
        {textEditorButtons.map((btn) => {
          return (
            <Tooltip key={btn.name} title={btn.tooltip} placement="bottom-end">
              <IconButton onClick={() => btn.onClick(editor)}>
                {btn.icon}
              </IconButton>
            </Tooltip>
          )
        })}
      </Stack>

      {/* Rich text section here*/}
      <RichTextEditorProvider editor={editor}>
        <RichTextField className={styles.emailSubject} />
      </RichTextEditorProvider>

      <Typography variant="subtitle2">Available Placeholders</Typography>
      <Typography variant="body2">
        Click on a placeholder below to insert it into the body message
      </Typography>
      <Stack
        direction="row"
        rowGap="1vh"
        columnGap="0.5vw"
        sx={{ width: '100%', flexWrap: 'wrap' }}
      >
        {formik.values.placeholders?.map((holder, index) => (
          <Chip
            key={index}
            label={holder.replace(/\$\{(.*?)\}/g, '$1')}
            variant="outlined"
            onClick={() => handleChipClick(holder)}
            sx={{
              background: '#F3F5F5',
              border: 'none',
              cursor: 'pointer',
            }}
          />
        ))}
      </Stack>
    </Stack>
  )
}
