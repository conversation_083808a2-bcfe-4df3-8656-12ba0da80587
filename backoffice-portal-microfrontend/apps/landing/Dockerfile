FROM node:23-alpine AS base
LABEL authors="<PERSON><PERSON>"

FROM base AS builder
RUN apk update
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
#Set up pnpm
RUN npm install -g pnpm turbo
ENV PNPM_HOME=/app/.pnpm
ENV PATH=$PNPM_HOME:$PATH

# Set working directory
WORKDIR /app

COPY . .

RUN turbo prune landing --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer

ARG BUILD_ENV
RUN echo $BUILD_ENV

RUN apk update
RUN apk add --no-cache libc6-compat
#Set up pnpm
RUN npm install -g pnpm turbo

WORKDIR /app

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN pnpm install

# Build the project
COPY --from=builder /app/out/full/ .

COPY /apps/landing/.env.$BUILD_ENV /app/apps/landing/.env.production
RUN ls -la
RUN pnpm turbo build --filter=landing...

FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/apps/landing/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/landing/.next/static ./apps/landing/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/landing/public ./apps/landing/public

EXPOSE 3000
ENV PORT=3000

CMD [ "node", "apps/landing/server.js" ]