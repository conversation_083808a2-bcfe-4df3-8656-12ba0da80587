'use client'

import { useSearchParams } from 'next/navigation'
import { Suspense, useEffect } from 'react'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { LoadingFullScreen } from '@dtbx/ui/components/Loading'
import { useAppDispatch } from '@dtbx/store'
import { handleLogin } from '@dtbx/store/actions'
import { setIsLoginError, setNotification } from '@dtbx/store/reducers'

export default function AuthenticatePage() {
  const searchParams = useSearchParams()
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const accessTokenObject = searchParams.get('accessToken') || ''
  useEffect(() => {
    const tokenObject = JSON.parse(accessTokenObject)
    if (tokenObject.success === true) {
      handleLogin(tokenObject, dispatch, router)
    } else if (tokenObject.success === false) {
      if (tokenObject.statusMessage.includes('disabled'))
        dispatch(setIsLoginError(true))
      router.push('/')
    }
  }, [accessTokenObject])
  return (
    <Suspense>
      <LoadingFullScreen />
    </Suspense>
  )
}
