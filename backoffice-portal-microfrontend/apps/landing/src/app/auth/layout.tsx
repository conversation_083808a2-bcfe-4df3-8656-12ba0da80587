'use client'

import { ReactNode } from 'react'
import { AuthWrapper, LocalNotification } from '@dtbx/ui/components'
import { ReduxProvider, useAppDispatch, useAppSelector } from '@dtbx/store'
import { isLoggedIn } from '@dtbx/store/utils'
import { clearNotification } from '@dtbx/store/reducers'

export default function AuthLayout(props: { children: ReactNode }) {
  const dispatch = useAppDispatch()
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'
  return (
    <AuthWrapper requiresAuth={false} isLoggedIn={isLoggedIn}>
      <ReduxProvider>
        <LocalNotification
          clearNotification={() => dispatch(clearNotification())}
          notification={notification}
          notificationType={notificationType}
        />
        {props.children}
      </ReduxProvider>
    </AuthWrapper>
  )
}
