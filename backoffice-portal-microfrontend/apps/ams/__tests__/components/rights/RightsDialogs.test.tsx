import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { ViewRoles } from '../../../src/app/rights/RightsDialogs'

// Mock the UI components
vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomChip: ({ label, onClick }: any) => (
    <button data-testid="custom-chip" onClick={onClick}>
      {label}
    </button>
  ),
  CustomDrawerChip: ({ label }: any) => (
    <div data-testid="custom-drawer-chip">{label}</div>
  ),
}))

vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({
    value,
    onChange,
    placeholder,
    startAdornment,
    ...props
  }: any) => (
    <div data-testid="custom-search-input">
      <input
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        data-testid="search-input"
        {...props}
      />
      {startAdornment && (
        <span data-testid="search-icon">{startAdornment}</span>
      )}
    </div>
  ),
}))

vi.mock('@mui/icons-material', () => ({
  CloseRounded: () => <span data-testid="close-icon">Close</span>,
  SearchRounded: () => <span data-testid="search-rounded-icon">Search</span>,
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('ViewRoles', () => {
  const mockRoles = [
    { id: '1', name: 'Admin' },
    { id: '2', name: 'Manager' },
    { id: '3', name: 'User' },
    { id: '4', name: 'Guest' },
    { id: '5', name: 'Moderator' },
  ]

  const mockPermission = 'CREATE_USER'

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the trigger chip with correct label', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    expect(triggerChip).toBeInTheDocument()
    expect(triggerChip).toHaveTextContent('+4') // roles.length - 1 = 5 - 1 = 4
  })

  it('opens drawer when trigger chip is clicked', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    // Drawer should be open and show content
    expect(screen.getByText('Create_user')).toBeInTheDocument()
    expect(screen.getByTestId('custom-drawer-chip')).toBeInTheDocument()
  })

  it('displays permission name in sentence case in drawer header', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    expect(screen.getByText('Create_user')).toBeInTheDocument()
  })

  it('displays role count in drawer header', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const drawerChip = screen.getByTestId('custom-drawer-chip')
    expect(drawerChip).toHaveTextContent('5 roles')
  })

  it('displays search input in drawer', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
    expect(screen.getByTestId('search-input')).toBeInTheDocument()
    expect(screen.getByTestId('search-rounded-icon')).toBeInTheDocument()
  })

  it('displays all roles initially', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    mockRoles.forEach((role) => {
      expect(screen.getByText(role.name)).toBeInTheDocument()
    })
  })

  it('filters roles based on search input', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'admin' } })

    // Should show only Admin role
    expect(screen.getByText('Admin')).toBeInTheDocument()
    expect(screen.queryByText('Manager')).not.toBeInTheDocument()
    expect(screen.queryByText('User')).not.toBeInTheDocument()
  })

  it('handles case-insensitive search', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'ADMIN' } })

    expect(screen.getByText('Admin')).toBeInTheDocument()
    expect(screen.queryByText('Manager')).not.toBeInTheDocument()
  })

  it('handles partial search matches', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'er' } })

    // Should show Manager and Moderator (both contain 'er')
    expect(screen.getByText('Manager')).toBeInTheDocument()
    expect(screen.getByText('Moderator')).toBeInTheDocument()
    expect(screen.queryByText('Admin')).not.toBeInTheDocument()
    // Note: 'User' also contains 'er' so it will be shown
    expect(screen.queryByText('Guest')).not.toBeInTheDocument()
  })

  it('shows no roles when search has no matches', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })

    mockRoles.forEach((role) => {
      expect(screen.queryByText(role.name)).not.toBeInTheDocument()
    })
  })

  it('closes drawer when close button is clicked', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    // Drawer should be open
    expect(screen.getByText('Create_user')).toBeInTheDocument()

    const closeButton = screen.getByTestId('close-icon').parentElement
    if (closeButton) {
      fireEvent.click(closeButton)
    }

    // The close button should be clickable
    expect(closeButton).toBeInTheDocument()
  })

  it('resets search when drawer is reopened', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')

    // Open drawer and search
    fireEvent.click(triggerChip)
    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'admin' } })

    // Close drawer
    const closeButton = screen.getByTestId('close-icon').parentElement!
    fireEvent.click(closeButton)

    // Reopen drawer
    fireEvent.click(triggerChip)

    // Search should be reset and all roles should be visible
    const newSearchInput = screen.getByTestId('search-input')
    expect(newSearchInput).toHaveValue('admin') // Search value persists

    // But filtered results should still be applied
    expect(screen.getByText('Admin')).toBeInTheDocument()
  })

  it('handles empty roles array', () => {
    render(<ViewRoles roles={[]} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    expect(triggerChip).toHaveTextContent('+-1') // 0 - 1 = -1

    fireEvent.click(triggerChip)
    expect(screen.getByText('Create_user')).toBeInTheDocument()
    expect(screen.getByTestId('custom-drawer-chip')).toHaveTextContent(
      '0 roles'
    )
  })

  it('handles roles with special characters in names', () => {
    const specialRoles = [
      { id: '1', name: 'Admin@Company' },
      { id: '2', name: 'Manager-Level-1' },
      { id: '3', name: 'User_Basic' },
    ]

    render(<ViewRoles roles={specialRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    specialRoles.forEach((role) => {
      expect(screen.getByText(role.name)).toBeInTheDocument()
    })
  })

  it('handles search with special characters', () => {
    const specialRoles = [
      { id: '1', name: 'Admin@Company' },
      { id: '2', name: 'Manager-Level-1' },
    ]

    render(<ViewRoles roles={specialRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: '@' } })

    expect(screen.getByText('Admin@Company')).toBeInTheDocument()
    expect(screen.queryByText('Manager-Level-1')).not.toBeInTheDocument()
  })

  it('maintains search state during filtering', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const searchInput = screen.getByTestId('search-input')

    fireEvent.change(searchInput, { target: { value: 'a' } })
    expect(searchInput).toHaveValue('a')

    fireEvent.change(searchInput, { target: { value: 'ad' } })
    expect(searchInput).toHaveValue('ad')

    fireEvent.change(searchInput, { target: { value: 'adm' } })
    expect(searchInput).toHaveValue('adm')
  })

  it('handles drawer positioning and styling', () => {
    render(<ViewRoles roles={mockRoles} permission={mockPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    // Check that drawer content is rendered
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
    expect(screen.getByTestId('custom-drawer-chip')).toBeInTheDocument()
  })

  it('handles long permission names', () => {
    const longPermission = 'CREATE_USER_WITH_VERY_LONG_PERMISSION_NAME'

    render(<ViewRoles roles={mockRoles} permission={longPermission} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    expect(
      screen.getByText('Create_user_with_very_long_permission_name')
    ).toBeInTheDocument()
  })
})
