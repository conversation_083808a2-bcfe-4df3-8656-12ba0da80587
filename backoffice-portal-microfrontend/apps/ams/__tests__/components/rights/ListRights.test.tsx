import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { ListRights } from '../../../src/app/rights/ListRights'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions/roles'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions/roles', () => ({
  getPermissionsFilter: vi.fn(),
}))

// Mock the components
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({ options, handlePagination }: any) => (
    <div data-testid="custom-pagination">
      <button
        onClick={() => handlePagination({ page: 2, size: 10, totalPages: 5 })}
      >
        Next Page
      </button>
      <span>
        Page {options.page} of {options.totalPages}
      </span>
    </div>
  ),
  CustomTableHeader: ({
    headLabel,
    onSelectAllClick,
    onRequestSort,
    showCheckbox,
  }: any) => (
    <thead data-testid="custom-table-header">
      <tr>
        {showCheckbox && (
          <th>
            <input
              type="checkbox"
              data-testid="select-all-checkbox"
              onChange={onSelectAllClick}
            />
          </th>
        )}
        {headLabel.map((header: any) => (
          <th key={header.id}>
            <button onClick={(e) => onRequestSort(e, header.id)}>
              {header.label}
            </button>
          </th>
        ))}
      </tr>
    </thead>
  ),
}))

vi.mock('@dtbx/ui/components/CheckBox', () => ({
  CustomCheckBox: ({ checked, slotProps }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={() => {}}
      data-testid="row-checkbox"
      {...slotProps?.input}
    />
  ),
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomActiveChip: ({ label }: any) => (
    <div data-testid="active-chip">{label}</div>
  ),
  CustomErrorChip: ({ label }: any) => (
    <div data-testid="error-chip">{label}</div>
  ),
  CustomChip: ({ label }: any) => <div data-testid="custom-chip">{label}</div>,
}))

vi.mock('../../../src/app/rights/RightsDialogs', () => ({
  ViewRoles: ({ roles, permission }: any) => (
    <div data-testid="view-roles">
      <span>
        +{roles.length - 1} more roles for {permission}
      </span>
    </div>
  ),
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('ListRights', () => {
  const mockDispatch = vi.fn()
  const mockGetPermissionsFilter = vi.fn()
  const mockSetPage = vi.fn()

  const mockPermissions = [
    {
      id: '1',
      name: 'CREATE_USER',
      visible: true,
      groupName: 'users',
    },
    {
      id: '2',
      name: 'UPDATE_USER',
      visible: false,
      groupName: 'users',
    },
    {
      id: '3',
      name: 'DELETE_USER',
      visible: true,
      groupName: 'users',
    },
  ]

  const mockRoles = [
    { id: '1', name: 'Admin' },
    { id: '2', name: 'Manager' },
    { id: '3', name: 'User' },
  ]

  const mockState = {
    permissions: mockPermissions,
    rolesList: mockRoles,
    permissionPageCount: 3,
    permissionsCount: 25,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue(mockState)
    vi.mocked(rolesActions.getPermissionsFilter).mockImplementation(
      mockGetPermissionsFilter
    )
  })

  it('renders the rights table with permissions', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.getByText('Create_user')).toBeInTheDocument()
    expect(screen.getByText('Update_user')).toBeInTheDocument()
    expect(screen.getByText('Delete_user')).toBeInTheDocument()
  })

  it('displays correct table headers', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    expect(screen.getByText('Right')).toBeInTheDocument()
    expect(screen.getByText('Is Visible')).toBeInTheDocument()
    expect(screen.getByText('Module')).toBeInTheDocument()
    expect(screen.getByText('Roles Assigned')).toBeInTheDocument()
  })

  it('displays visibility chips correctly', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const activeChips = screen.getAllByTestId('active-chip')
    const errorChips = screen.getAllByTestId('error-chip')

    expect(activeChips).toHaveLength(2) // CREATE_USER and DELETE_USER are visible
    expect(errorChips).toHaveLength(1) // UPDATE_USER is not visible
    expect(activeChips[0]).toHaveTextContent('Yes')
    expect(errorChips[0]).toHaveTextContent('No')
  })

  it('displays module names correctly', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const moduleElements = screen.getAllByText('users')
    expect(moduleElements).toHaveLength(3) // All permissions are in 'users' module
  })

  it('displays role chips correctly', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const roleChips = screen.getAllByTestId('custom-chip')
    expect(roleChips).toHaveLength(6) // 2 roles per permission (3 permissions)
    expect(roleChips[0]).toHaveTextContent('Admin')
    expect(roleChips[1]).toHaveTextContent('Manager')
  })

  it('shows ViewRoles component when there are more than 2 roles', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const viewRolesComponents = screen.getAllByTestId('view-roles')
    expect(viewRolesComponents).toHaveLength(3) // One for each permission
    expect(viewRolesComponents[0]).toHaveTextContent(
      '+2 more roles for CREATE_USER'
    )
  })

  it('handles row selection', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const firstRowCheckbox = screen.getAllByTestId('row-checkbox')[0]
    fireEvent.click(firstRowCheckbox.parentElement!)

    expect(firstRowCheckbox).toBeChecked()
  })

  it('handles select all functionality', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const selectAllCheckbox = screen.getByTestId('select-all-checkbox')
    fireEvent.change(selectAllCheckbox, { target: { checked: true } })

    // The select all functionality should trigger the handler
    expect(selectAllCheckbox).toBeInTheDocument()
  })

  it('handles deselect all functionality', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const selectAllCheckbox = screen.getByTestId('select-all-checkbox')

    // First select all
    fireEvent.change(selectAllCheckbox, { target: { checked: true } })

    // Then deselect all
    fireEvent.change(selectAllCheckbox, { target: { checked: false } })

    const rowCheckboxes = screen.getAllByTestId('row-checkbox')
    rowCheckboxes.forEach((checkbox) => {
      expect(checkbox).not.toBeChecked()
    })
  })

  it('handles table sorting', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const rightHeaderButton = screen.getByText('Right')
    fireEvent.click(rightHeaderButton)

    // Should trigger sort functionality
    expect(rightHeaderButton).toBeInTheDocument()
  })

  it('handles pagination', async () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const nextPageButton = screen.getByText('Next Page')
    fireEvent.click(nextPageButton)

    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 2,
        size: 10,
        totalPages: 5,
      })
      expect(mockSetPage).toHaveBeenCalledWith(2)
    })
  })

  it('renders pagination when permissionPageCount > 0', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
    expect(screen.getByText('Page 1 of 3')).toBeInTheDocument()
  })

  it('does not render pagination when permissionPageCount is 0', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      permissionPageCount: 0,
    })

    render(<ListRights page={1} setPage={mockSetPage} />)

    expect(screen.queryByTestId('custom-pagination')).not.toBeInTheDocument()
  })

  it('handles individual row selection correctly', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    // Click on the first row
    const firstRow = screen.getAllByRole('checkbox')[0].closest('tr')
    if (firstRow) {
      fireEvent.click(firstRow)
    }

    const firstRowCheckbox = screen.getAllByTestId('row-checkbox')[0]
    expect(firstRowCheckbox).toBeInTheDocument()
  })

  it('handles multiple row selections', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    // Select first row
    const firstRow = screen.getAllByRole('checkbox')[0].closest('tr')
    if (firstRow) {
      fireEvent.click(firstRow)
    }

    // Select second row
    const secondRow = screen.getAllByRole('checkbox')[1].closest('tr')
    if (secondRow) {
      fireEvent.click(secondRow)
    }

    const rowCheckboxes = screen.getAllByTestId('row-checkbox')
    expect(rowCheckboxes).toHaveLength(3)
  })

  it('handles deselecting individual rows', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    // Select first row
    const firstRow = screen.getAllByRole('checkbox')[0].closest('tr')!
    fireEvent.click(firstRow)

    // Deselect first row
    fireEvent.click(firstRow)

    const firstRowCheckbox = screen.getAllByTestId('row-checkbox')[0]
    expect(firstRowCheckbox).not.toBeChecked()
  })

  it('handles empty permissions array', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      permissions: [],
    })

    render(<ListRights page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.queryByText('Create_user')).not.toBeInTheDocument()
  })

  it('handles permissions without roles', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      rolesList: [],
    })

    render(<ListRights page={1} setPage={mockSetPage} />)

    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.queryByTestId('custom-chip')).not.toBeInTheDocument()
    expect(screen.queryByTestId('view-roles')).not.toBeInTheDocument()
  })

  it('handles permissions with only one role', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      rolesList: [{ id: '1', name: 'Admin' }],
    })

    render(<ListRights page={1} setPage={mockSetPage} />)

    const roleChips = screen.getAllByTestId('custom-chip')
    expect(roleChips).toHaveLength(3) // One role per permission
    expect(screen.queryByTestId('view-roles')).not.toBeInTheDocument()
  })

  it('handles permissions with exactly two roles', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState,
      rolesList: [
        { id: '1', name: 'Admin' },
        { id: '2', name: 'Manager' },
      ],
    })

    render(<ListRights page={1} setPage={mockSetPage} />)

    const roleChips = screen.getAllByTestId('custom-chip')
    expect(roleChips).toHaveLength(6) // Two roles per permission
    expect(screen.queryByTestId('view-roles')).not.toBeInTheDocument()
  })

  it('applies correct styling to the table container', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'rights table')
  })

  it('handles sorting by different columns', () => {
    render(<ListRights page={1} setPage={mockSetPage} />)

    // Test sorting by different columns
    const visibleHeader = screen.getByText('Is Visible')
    fireEvent.click(visibleHeader)

    const moduleHeader = screen.getByText('Module')
    fireEvent.click(moduleHeader)

    const rolesHeader = screen.getByText('Roles Assigned')
    fireEvent.click(rolesHeader)

    // All headers should be clickable
    expect(visibleHeader).toBeInTheDocument()
    expect(moduleHeader).toBeInTheDocument()
    expect(rolesHeader).toBeInTheDocument()
  })
})
