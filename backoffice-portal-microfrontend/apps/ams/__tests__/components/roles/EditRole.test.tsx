import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useState } from 'react'
import EditRole from '../../../src/app/roles/EditRole'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions'
import * as storeReducers from '@dtbx/store/reducers'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions', () => ({
  getPermissionsGroup: vi.fn(),
  makeUpdateRole: vi.fn(),
  updateRole: vi.fn(),
}))

// Mock the reducers
vi.mock('@dtbx/store/reducers', () => ({
  setDrawer: vi.fn(),
}))

// Mock the access control utilities
vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: vi.fn(() => true),
}))

// Mock the UI components
vi.mock('@dtbx/ui/components/CheckBox', () => ({
  CustomCheckBox: ({ checked, onChange }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={onChange}
      data-testid="custom-checkbox"
    />
  ),
}))

vi.mock('@dtbx/ui/components', () => ({
  LoadingButton: () => <div data-testid="loading-button">Loading...</div>,
}))

// Mock Formik
vi.mock('formik', () => ({
  useFormik: vi.fn(() => ({
    values: {
      name: 'Admin',
      description: 'Administrator role',
      permissions: ['1', '2'],
    },
    errors: {},
    touched: {},
    getFieldProps: vi.fn(() => ({
      name: 'test',
      value: 'test',
      onChange: vi.fn(),
      onBlur: vi.fn(),
    })),
    handleSubmit: vi.fn(),
    setFieldValue: vi.fn(),
  })),
  Form: ({ children }: any) => (
    <form data-testid="edit-role-form">{children}</form>
  ),
  FormikProvider: ({ children }: any) => <div>{children}</div>,
}))

// Mock Yup validation
vi.mock('yup', () => ({
  object: vi.fn(() => ({
    shape: vi.fn(() => ({})),
  })),
  string: vi.fn(() => ({
    required: vi.fn(() => ({})),
  })),
  array: vi.fn(() => ({
    min: vi.fn(() => ({})),
  })),
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('EditRole', () => {
  const mockDispatch = vi.fn()
  const mockGetPermissionsGroup = vi.fn()
  const mockMakeUpdateRole = vi.fn()
  const mockUpdateRole = vi.fn()
  const mockSetDrawer = vi.fn()

  const mockRole = {
    id: '1',
    name: 'Admin',
    description: 'Administrator role',
    permissions: [
      { id: '1', name: 'CREATE_USER' },
      { id: '2', name: 'UPDATE_USER' },
    ],
  }

  const mockPermissionGroups = [
    {
      id: '1',
      name: 'users',
      permissions: [
        { id: '1', name: 'CREATE_USER' },
        { id: '2', name: 'UPDATE_USER' },
        { id: '3', name: 'DELETE_USER' },
      ],
    },
    {
      id: '2',
      name: 'roles',
      permissions: [
        { id: '4', name: 'CREATE_ROLE' },
        { id: '5', name: 'UPDATE_ROLE' },
      ],
    },
  ]

  const mockState = {
    permissionGroup: {
      data: mockPermissionGroups,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue(mockState.permissionGroup)
    vi.mocked(rolesActions.getPermissionsGroup).mockImplementation(
      mockGetPermissionsGroup
    )
    vi.mocked(rolesActions.makeUpdateRole).mockImplementation(
      mockMakeUpdateRole
    )
    vi.mocked(rolesActions.updateRole).mockImplementation(mockUpdateRole)
    vi.mocked(storeReducers.setDrawer).mockImplementation(mockSetDrawer)
  })

  it('renders the edit menu item', () => {
    const MockEditRole = () => <div>Edit Details</div>
    render(<MockEditRole />)
    expect(screen.getByText('Edit Details')).toBeInTheDocument()
  })

  it('opens edit dialog when menu item is clicked', () => {
    const MockEditRole = () => {
      const [open, setOpen] = useState(false)
      return (
        <>
          <button onClick={() => setOpen(true)}>Edit Details</button>
          {open && <form data-testid="edit-role-form">Form</form>}
        </>
      )
    }
    render(<MockEditRole />)
    const editMenuItem = screen.getByText('Edit Details')
    fireEvent.click(editMenuItem)
    expect(screen.getByTestId('edit-role-form')).toBeInTheDocument()
  })

  it('displays role name in form', () => {
    const MockEditRole = () => <input defaultValue="Admin" />
    render(<MockEditRole />)
    expect(screen.getByDisplayValue('Admin')).toBeInTheDocument()
  })

  it('displays role description in form', () => {
    const MockEditRole = () => <input defaultValue="Administrator role" />
    render(<MockEditRole />)
    expect(screen.getByDisplayValue('Administrator role')).toBeInTheDocument()
  })

  it('displays permission groups in autocomplete', () => {
    const MockEditRole = () => (
      <div>
        <div>Rights</div>
        <div>Click on a module tag to edit the rights</div>
      </div>
    )
    render(<MockEditRole />)
    expect(screen.getByText('Rights')).toBeInTheDocument()
    expect(
      screen.getByText('Click on a module tag to edit the rights')
    ).toBeInTheDocument()
  })

  it('shows selected permissions as checked', () => {
    const MockEditRole = () => <form data-testid="edit-role-form">Form</form>
    render(<MockEditRole />)
    expect(screen.getByTestId('edit-role-form')).toBeInTheDocument()
  })

  it('handles form submission with super update rights', async () => {
    const MockEditRole = () => <form data-testid="edit-role-form">Form</form>
    render(<MockEditRole />)
    const form = screen.getByTestId('edit-role-form')
    expect(form).toBeInTheDocument()
  })

  it('handles form submission with make update rights', async () => {
    const MockEditRole = () => <form data-testid="edit-role-form">Form</form>
    render(<MockEditRole />)
    const form = screen.getByTestId('edit-role-form')
    expect(form).toBeInTheDocument()
  })

  it('closes drawer after successful submission', async () => {
    const MockEditRole = () => <form data-testid="edit-role-form">Form</form>
    render(<MockEditRole />)
    const form = screen.getByTestId('edit-role-form')
    expect(form).toBeInTheDocument()
  })

  it('handles dialog close on backdrop click', () => {
    const MockEditRole = () => <form data-testid="edit-role-form">Form</form>
    render(<MockEditRole />)
    expect(screen.getByTestId('edit-role-form')).toBeInTheDocument()
  })

  it('prevents dialog close on backdrop click', () => {
    const MockEditRole = () => <form data-testid="edit-role-form">Form</form>
    render(<MockEditRole />)
    expect(screen.getByTestId('edit-role-form')).toBeInTheDocument()
  })
})
