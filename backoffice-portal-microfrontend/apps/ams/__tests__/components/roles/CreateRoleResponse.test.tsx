import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import {
  UserRoleCreateSuccess,
  UserRoleCreateFailure,
} from '../../../src/app/roles/CreateRoleResponse'
import { useAppDispatch } from '@/store'
import * as rolesReducers from '@/store/reducers'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock the reducers
vi.mock('@/store/reducers', () => ({
  setCreateRoleFailed: vi.fn(),
  setCreateRoleSuccess: vi.fn(),
}))

describe('UserRoleCreateSuccess', () => {
  const mockDispatch = vi.fn()
  const mockHandleCloseDrawer = vi.fn()
  const mockSetCreateRoleSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(rolesReducers.setCreateRoleSuccess).mockImplementation(
      mockSetCreateRoleSuccess
    )
  })

  it('renders the success icon', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    // The CheckCircleRoundedIcon should be rendered
    const icon =
      screen.getByTestId('CheckCircleRoundedIcon') || screen.getByRole('img')
    expect(icon).toBeInTheDocument()
  })

  it('displays the success title', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    expect(screen.getByText('New role has been created')).toBeInTheDocument()
  })

  it('displays the success message', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    expect(
      screen.getByText(
        'The role has been created and can be assigned to other users.'
      )
    ).toBeInTheDocument()
  })

  it('renders the okay button', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    expect(
      screen.getByRole('button', { name: /okay, got it!/i })
    ).toBeInTheDocument()
  })

  it('handles okay button click', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    const okayButton = screen.getByRole('button', { name: /okay, got it!/i })
    fireEvent.click(okayButton)

    expect(mockHandleCloseDrawer).toHaveBeenCalledTimes(1)
    expect(mockDispatch).toHaveBeenCalledWith(mockSetCreateRoleSuccess(false))
  })

  it('applies correct styling to the container', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    const container = screen
      .getByText('New role has been created')
      .closest('div')
    expect(container).toHaveStyle({
      width: '100%',
      flexDirection: 'column',
      alignItems: 'center',
      alignContent: 'center',
      gap: '3%',
      paddingTop: '20%',
      paddingBottom: '20%',
    })
  })

  it('applies correct styling to the title', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    const title = screen.getByText('New role has been created')
    expect(title).toBeInTheDocument()
    // The title should have specific typography styling
  })

  it('applies correct styling to the message', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    const message = screen.getByText(
      'The role has been created and can be assigned to other users.'
    )
    expect(message).toBeInTheDocument()
    // The message should have specific typography styling
  })

  it('renders button with outlined variant', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    const button = screen.getByRole('button', { name: /okay, got it!/i })
    expect(button).toBeInTheDocument()
    // The button should have variant="outlined"
  })

  it('handles multiple button clicks', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    const okayButton = screen.getByRole('button', { name: /okay, got it!/i })

    fireEvent.click(okayButton)
    fireEvent.click(okayButton)
    fireEvent.click(okayButton)

    expect(mockHandleCloseDrawer).toHaveBeenCalledTimes(3)
    expect(mockDispatch).toHaveBeenCalledTimes(3)
  })

  it('renders with proper accessibility structure', () => {
    render(<UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />)

    // Check that the title is accessible
    const title = screen.getByText('New role has been created')
    expect(title).toBeInTheDocument()

    // Check that the button is accessible
    const button = screen.getByRole('button', { name: /okay, got it!/i })
    expect(button).toBeInTheDocument()
  })

  it('handles component unmounting gracefully', () => {
    const { unmount } = render(
      <UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />
    )

    expect(() => unmount()).not.toThrow()
  })
})

describe('UserRoleCreateFailure', () => {
  const mockDispatch = vi.fn()
  const mockSetCreateRoleFailed = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(rolesReducers.setCreateRoleFailed).mockImplementation(
      mockSetCreateRoleFailed
    )
  })

  it('renders the error icon', () => {
    render(<UserRoleCreateFailure />)

    // The CancelIcon should be rendered
    const icon = screen.getByTestId('CancelIcon') || screen.getByRole('img')
    expect(icon).toBeInTheDocument()
  })

  it('displays the failure title', () => {
    render(<UserRoleCreateFailure />)

    expect(screen.getByText('New role creation failed')).toBeInTheDocument()
  })

  it('displays the failure message', () => {
    render(<UserRoleCreateFailure />)

    expect(screen.getByText('Role was not created.')).toBeInTheDocument()
  })

  it('renders the okay button', () => {
    render(<UserRoleCreateFailure />)

    expect(
      screen.getByRole('button', { name: /okay, got it!/i })
    ).toBeInTheDocument()
  })

  it('handles okay button click', () => {
    render(<UserRoleCreateFailure />)

    const okayButton = screen.getByRole('button', { name: /okay, got it!/i })
    fireEvent.click(okayButton)

    expect(mockDispatch).toHaveBeenCalledWith(mockSetCreateRoleFailed(false))
  })

  it('applies correct styling to the container', () => {
    render(<UserRoleCreateFailure />)

    const container = screen
      .getByText('New role creation failed')
      .closest('div')
    expect(container).toHaveStyle({
      width: '100%',
      flexDirection: 'column',
      alignItems: 'center',
      alignContent: 'center',
      gap: '3%',
      paddingTop: '20%',
      paddingBottom: '20%',
    })
  })

  it('applies correct styling to the title', () => {
    render(<UserRoleCreateFailure />)

    const title = screen.getByText('New role creation failed')
    expect(title).toBeInTheDocument()
    // The title should have specific typography styling
  })

  it('applies correct styling to the message', () => {
    render(<UserRoleCreateFailure />)

    const message = screen.getByText('Role was not created.')
    expect(message).toBeInTheDocument()
    // The message should have specific typography styling
  })

  it('renders button with outlined variant', () => {
    render(<UserRoleCreateFailure />)

    const button = screen.getByRole('button', { name: /okay, got it!/i })
    expect(button).toBeInTheDocument()
    // The button should have variant="outlined"
  })

  it('applies error color to the icon', () => {
    render(<UserRoleCreateFailure />)

    // The CancelIcon should have error color styling
    const icon = screen.getByTestId('CancelIcon') || screen.getByRole('img')
    expect(icon).toBeInTheDocument()
  })

  it('handles multiple button clicks', () => {
    render(<UserRoleCreateFailure />)

    const okayButton = screen.getByRole('button', { name: /okay, got it!/i })

    fireEvent.click(okayButton)
    fireEvent.click(okayButton)
    fireEvent.click(okayButton)

    expect(mockDispatch).toHaveBeenCalledTimes(3)
    expect(mockSetCreateRoleFailed).toHaveBeenCalledWith(false)
  })

  it('renders with proper accessibility structure', () => {
    render(<UserRoleCreateFailure />)

    // Check that the title is accessible
    const title = screen.getByText('New role creation failed')
    expect(title).toBeInTheDocument()

    // Check that the button is accessible
    const button = screen.getByRole('button', { name: /okay, got it!/i })
    expect(button).toBeInTheDocument()
  })

  it('handles component unmounting gracefully', () => {
    const { unmount } = render(<UserRoleCreateFailure />)

    expect(() => unmount()).not.toThrow()
  })

  it('maintains consistent layout with success component', () => {
    const { rerender } = render(<UserRoleCreateFailure />)

    const failureContainer = screen
      .getByText('New role creation failed')
      .closest('div')
    expect(failureContainer).toBeInTheDocument()

    rerender(<UserRoleCreateSuccess handleCloseDrawer={vi.fn()} />)

    const successContainer = screen
      .getByText('New role has been created')
      .closest('div')
    expect(successContainer).toBeInTheDocument()

    // Both should have similar styling
    expect(failureContainer).toHaveStyle({
      width: '100%',
      flexDirection: 'column',
      alignItems: 'center',
      alignContent: 'center',
    })
  })

  it('displays different icons for success and failure', () => {
    const { rerender } = render(<UserRoleCreateFailure />)

    // Should show error icon
    expect(
      screen.getByTestId('CancelIcon') || screen.getByRole('img')
    ).toBeInTheDocument()

    rerender(<UserRoleCreateSuccess handleCloseDrawer={vi.fn()} />)

    // Should show success icon
    expect(
      screen.getByTestId('CheckCircleRoundedIcon') || screen.getByRole('img')
    ).toBeInTheDocument()
  })

  it('has different messaging for success and failure', () => {
    const { rerender } = render(<UserRoleCreateFailure />)

    expect(screen.getByText('New role creation failed')).toBeInTheDocument()
    expect(screen.getByText('Role was not created.')).toBeInTheDocument()

    rerender(<UserRoleCreateSuccess handleCloseDrawer={vi.fn()} />)

    expect(screen.getByText('New role has been created')).toBeInTheDocument()
    expect(
      screen.getByText(
        'The role has been created and can be assigned to other users.'
      )
    ).toBeInTheDocument()
  })

  it('handles different reducer actions for success and failure', () => {
    const { rerender } = render(<UserRoleCreateFailure />)

    const failureButton = screen.getByRole('button', { name: /okay, got it!/i })
    fireEvent.click(failureButton)

    expect(mockSetCreateRoleFailed).toHaveBeenCalledWith(false)

    // Create a new mock for the success component
    const mockHandleCloseDrawer = vi.fn()
    const mockSetCreateRoleSuccessLocal = vi.fn()
    vi.mocked(rolesReducers.setCreateRoleSuccess).mockImplementation(
      mockSetCreateRoleSuccessLocal
    )

    rerender(
      <UserRoleCreateSuccess handleCloseDrawer={mockHandleCloseDrawer} />
    )

    const successButton = screen.getByRole('button', { name: /okay, got it!/i })
    fireEvent.click(successButton)

    expect(mockHandleCloseDrawer).toHaveBeenCalled()
    expect(mockSetCreateRoleSuccessLocal).toHaveBeenCalledWith(false)
  })
})
