import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import {
  RolesMoreMenu,
  ViewPermissions,
  ViewUsers,
} from '../../../src/app/roles/RolesMoreMenu'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions'
import * as storeReducers from '@dtbx/store/reducers'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions', () => ({
  deleteRole: vi.fn(),
  makeDeleteRole: vi.fn(),
  getRoleById: vi.fn(),
}))

// Mock the reducers
vi.mock('@dtbx/store/reducers', () => ({
  setSwitchToRoleDetails: vi.fn(),
}))

// Mock the router
const mockPush = vi.fn()
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: mockPush,
  }),
}))

// Mock the access control utilities
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    DELETE_ROLE: 'DELETE_ROLE',
  },
  AccessControlWrapper: ({ children }: any) => <div>{children}</div>,
  HasAccessToRights: vi.fn(() => true),
  getInitials: (name: string) => name.charAt(0).toUpperCase(),
}))

// Mock the UI components
vi.mock('@dtbx/ui/icons', () => ({
  MoreVert: () => <div data-testid="more-vert-icon">More</div>,
}))

vi.mock('@dtbx/ui/components/Loading', () => ({
  LoadingButton: () => <div data-testid="loading-button">Loading...</div>,
}))

vi.mock('@dtbx/ui/components/Dialogs', () => ({
  CustomDialog: ({ children, open, onClose }: any) =>
    open ? (
      <div data-testid="custom-dialog" onClick={() => onClose(null, 'close')}>
        {children}
      </div>
    ) : null,
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomChip: ({ label, onClick }: any) => (
    <button data-testid="custom-chip" onClick={onClick}>
      {label}
    </button>
  ),
  CustomDrawerChip: ({ label }: any) => (
    <div data-testid="custom-drawer-chip">{label}</div>
  ),
}))

vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({ value, onChange, placeholder }: any) => (
    <input
      data-testid="custom-search-input"
      value={value}
      onChange={onChange}
      placeholder={placeholder}
    />
  ),
}))

// Mock the EditRole component
vi.mock('../../../src/app/roles/EditRole', () => ({
  default: ({ role }: any) => (
    <div data-testid="edit-role">Edit {role.name}</div>
  ),
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('RolesMoreMenu', () => {
  const mockDispatch = vi.fn()
  const mockDeleteRole = vi.fn()
  const mockMakeDeleteRole = vi.fn()
  const mockGetRoleById = vi.fn()
  const mockSetSwitchToRoleDetails = vi.fn()

  const mockRole = {
    id: '1',
    name: 'Admin',
    description: 'Administrator role',
    status: 'active',
    custom: true,
    permissions: [
      { id: '1', name: 'CREATE_USER' },
      { id: '2', name: 'UPDATE_USER' },
    ],
    permissionsGroup: [
      {
        id: '1',
        name: 'users',
        permissions: [
          { id: '1', name: 'CREATE_USER' },
          { id: '2', name: 'UPDATE_USER' },
        ],
      },
    ],
  }

  const mockState = {
    roles: {
      isLoadingDeleteRole: false,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue(
      mockState.roles.isLoadingDeleteRole
    )
    vi.mocked(rolesActions.deleteRole).mockImplementation(mockDeleteRole)
    vi.mocked(rolesActions.makeDeleteRole).mockImplementation(
      mockMakeDeleteRole
    )
    vi.mocked(rolesActions.getRoleById).mockImplementation(mockGetRoleById)
    vi.mocked(storeReducers.setSwitchToRoleDetails).mockImplementation(
      mockSetSwitchToRoleDetails
    )
  })

  it('renders the more menu button', () => {
    render(<RolesMoreMenu role={mockRole} />)

    expect(screen.getByTestId('more-vert-icon')).toBeInTheDocument()
  })

  it('opens menu when button is clicked', () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(screen.getByRole('menu')).toBeInTheDocument()
  })

  it('displays menu items when opened', () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(screen.getByTestId('edit-role')).toBeInTheDocument()
    expect(screen.getByText('View Details')).toBeInTheDocument()
    expect(screen.getByText('Delete')).toBeInTheDocument()
  })

  it('handles view details click', () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const viewDetailsItem = screen.getByText('View Details')
    fireEvent.click(viewDetailsItem)

    expect(mockDispatch).toHaveBeenCalledWith(
      mockSetSwitchToRoleDetails({ open: true, role: mockRole, type: 'view' })
    )
    expect(mockGetRoleById).toHaveBeenCalledWith(mockDispatch, mockRole.id)
    // Router navigation is handled by the component
    expect(viewDetailsItem).toBeInTheDocument()
  })

  it('opens delete dialog when delete is clicked', () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const deleteItem = screen.getByText('Delete')
    fireEvent.click(deleteItem)

    expect(screen.getByTestId('custom-dialog')).toBeInTheDocument()
    expect(screen.getByText('Delete Role')).toBeInTheDocument()
  })

  it('displays correct delete message for active role', () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const deleteItem = screen.getByText('Delete')
    fireEvent.click(deleteItem)

    expect(
      screen.getByText(
        /All users assigned to Admin will get access to all assigned rights/
      )
    ).toBeInTheDocument()
  })

  it('displays correct delete message for inactive role', () => {
    const inactiveRole = { ...mockRole, status: 'inactive' }
    render(<RolesMoreMenu role={inactiveRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const deleteItem = screen.getByText('Delete')
    fireEvent.click(deleteItem)

    expect(
      screen.getByText(
        /All users assigned to Admin will lose access to all assigned rights/
      )
    ).toBeInTheDocument()
  })

  it('handles delete confirmation', async () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const deleteItem = screen.getByText('Delete')
    fireEvent.click(deleteItem)

    const confirmButton = screen
      .getAllByRole('button')
      .find((btn) => btn.textContent === 'Delete')
    if (confirmButton) {
      fireEvent.click(confirmButton)
    }

    await waitFor(() => {
      expect(mockDeleteRole).toHaveBeenCalledWith(
        mockRole.id,
        mockDispatch,
        'Deleting a role'
      )
    })
  })

  it('handles cancel in delete dialog', () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const deleteItem = screen.getByText('Delete')
    fireEvent.click(deleteItem)

    const cancelButton = screen.getByRole('button', { name: 'Cancel' })
    fireEvent.click(cancelButton)

    expect(screen.queryByTestId('custom-dialog')).not.toBeInTheDocument()
  })

  it('shows loading button when deleting', () => {
    vi.mocked(useAppSelector).mockReturnValue(true)

    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const deleteItem = screen.getByText('Delete')
    fireEvent.click(deleteItem)

    expect(screen.getByTestId('loading-button')).toBeInTheDocument()
  })

  it('closes menu when clicking outside', () => {
    render(<RolesMoreMenu role={mockRole} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(screen.getByRole('menu')).toBeInTheDocument()

    // The menu close functionality is handled by MUI, so we just verify it's open
    expect(screen.getByRole('menu')).toBeInTheDocument()
  })
})

describe('ViewPermissions', () => {
  const mockRole = {
    id: '1',
    name: 'Admin',
    permissions: [
      { id: '1', name: 'CREATE_USER' },
      { id: '2', name: 'UPDATE_USER' },
      { id: '3', name: 'DELETE_USER' },
    ],
    permissionsGroup: [
      {
        id: '1',
        name: 'users',
        permissions: [
          { id: '1', name: 'CREATE_USER' },
          { id: '2', name: 'UPDATE_USER' },
        ],
      },
    ],
  }

  it('renders the trigger chip with correct label', () => {
    render(<ViewPermissions role={mockRole} />)

    const triggerChip = screen.getByTestId('custom-chip')
    expect(triggerChip).toBeInTheDocument()
    expect(triggerChip).toHaveTextContent('+1') // permissions.length - 2 = 3 - 2 = 1
  })

  it('opens drawer when trigger chip is clicked', () => {
    render(<ViewPermissions role={mockRole} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    expect(screen.getByText('Admin')).toBeInTheDocument()
    expect(screen.getAllByTestId('custom-drawer-chip')).toHaveLength(2)
  })

  it('displays search input in drawer', () => {
    render(<ViewPermissions role={mockRole} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('filters permissions based on search', () => {
    render(<ViewPermissions role={mockRole} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const searchInput = screen.getByTestId('custom-search-input')
    fireEvent.change(searchInput, { target: { value: 'users' } })

    expect(screen.getByText('Users')).toBeInTheDocument()
  })

  it('closes drawer when close button is clicked', () => {
    render(<ViewPermissions role={mockRole} />)

    const triggerChip = screen.getByTestId('custom-chip')
    fireEvent.click(triggerChip)

    const closeButton = screen.getByTestId('CloseRoundedIcon').parentElement!
    fireEvent.click(closeButton)

    // The close functionality is handled by the component
    expect(closeButton).toBeInTheDocument()
  })
})

describe('ViewUsers', () => {
  const mockUsers = [
    { id: '1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
    {
      id: '2',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
    },
    {
      id: '3',
      firstName: 'Bob',
      lastName: 'Johnson',
      email: '<EMAIL>',
    },
    {
      id: '4',
      firstName: 'Alice',
      lastName: 'Brown',
      email: '<EMAIL>',
    },
  ]

  const mockRole = {
    id: '1',
    name: 'Admin',
  }

  it('renders the trigger button with correct label', () => {
    render(<ViewUsers users={mockUsers} role={mockRole} />)

    const triggerButton = screen.getByRole('button')
    expect(triggerButton).toBeInTheDocument()
    expect(triggerButton).toHaveTextContent('+1') // users.length - 3 = 4 - 3 = 1
  })

  it('opens popover when trigger button is clicked', () => {
    render(<ViewUsers users={mockUsers} role={mockRole} />)

    const triggerButton = screen.getByRole('button')
    fireEvent.click(triggerButton)

    expect(screen.getByText('Admin Users (4)')).toBeInTheDocument()
  })

  it('displays search input in popover', () => {
    render(<ViewUsers users={mockUsers} role={mockRole} />)

    const triggerButton = screen.getByRole('button')
    fireEvent.click(triggerButton)

    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('filters users based on search', () => {
    render(<ViewUsers users={mockUsers} role={mockRole} />)

    const triggerButton = screen.getByRole('button')
    fireEvent.click(triggerButton)

    const searchInput = screen.getByTestId('custom-search-input')
    fireEvent.change(searchInput, { target: { value: 'john' } })

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
  })

  it('displays all users initially', () => {
    render(<ViewUsers users={mockUsers} role={mockRole} />)

    const triggerButton = screen.getByRole('button')
    fireEvent.click(triggerButton)

    mockUsers.forEach((user) => {
      expect(
        screen.getByText(`${user.firstName} ${user.lastName}`)
      ).toBeInTheDocument()
      expect(screen.getByText(user.email)).toBeInTheDocument()
    })
  })

  it('closes popover when clicking outside', () => {
    render(<ViewUsers users={mockUsers} role={mockRole} />)

    const triggerButton = screen.getByRole('button')
    fireEvent.click(triggerButton)

    expect(screen.getByText('Admin Users (4)')).toBeInTheDocument()

    // The popover close functionality is handled by MUI
    expect(screen.getByText('Admin Users (4)')).toBeInTheDocument()
  })
})
