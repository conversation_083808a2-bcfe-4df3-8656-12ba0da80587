import { render, screen, fireEvent, waitFor } from '../../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { AllApprovalRequestsMoreMenu } from '../../../../src/app/approval-requests/All/MoreMenu'
import { useAppDispatch } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  }),
}))

// Mock ResolvedRequestDetails component
vi.mock(
  '../../../../src/app/approval-requests/All/Dialog/ResolvedRequestDetails',
  () => ({
    default: ({ request }: { request: IApprovalRequest }) => (
      <div data-testid="resolved-request-details">
        <span>Request Details for {request.id}</span>
      </div>
    ),
  })
)

// Mock ApprovalRequestRouting
vi.mock('../../../../src/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn(),
}))

// Mock AccessControlWrapper
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    VIEW_APPROVALREQUEST_CUSTOMERS: ['VIEW_CUSTOMERS'],
    VIEW_APPROVALREQUEST_ROLES: ['VIEW_ROLES'],
    VIEW_APPROVALREQUEST_USERS: ['VIEW_USERS'],
    ACCEPT_APPROVALREQUEST_CUSTOMERS: ['ACCEPT_CUSTOMERS'],
    ACCEPT_APPROVALREQUEST_ROLES: ['ACCEPT_ROLES'],
    ACCEPT_APPROVALREQUEST_USERS: ['ACCEPT_USERS'],
    REJECT_APPROVALREQUEST_CUSTOMERS: ['REJECT_CUSTOMERS'],
    REJECT_APPROVALREQUEST_ROLES: ['REJECT_ROLES'],
    REJECT_APPROVALREQUEST_USERS: ['REJECT_USERS'],
  },
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="access-control-wrapper">{children}</div>
  ),
}))

describe('MoreMenu', () => {
  const mockDispatch = vi.fn()

  const mockRequest: IApprovalRequest = {
    id: '123',
    maker: 'John Doe',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-01T10:00:00Z',
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_USERS'],
      description: 'Create user request',
      makerPermissions: ['CREATE_USERS'],
      module: 'users',
      name: 'Create User',
      overridePermissions: [],
      type: 'CREATE_USER',
    },
    entityId: 'user-123',
    entity: 'User',
    diff: [
      {
        field: 'firstName',
        oldValue: '',
        newValue: 'John',
      },
    ],
    makerComments: 'Creating new user',
    status: 'APPROVED',
    checker: 'Jane Smith',
    checkerComments: 'Approved successfully',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the more menu button', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    expect(moreButton).toBeInTheDocument()
    expect(moreButton).toHaveAttribute('aria-haspopup', 'true')
  })

  it('opens menu when button is clicked', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByRole('menu')).toBeInTheDocument()
  })

  it('closes menu when clicking outside', async () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByRole('menu')).toBeInTheDocument()

    // Click outside - use the backdrop element
    const backdrop = document.querySelector('.MuiBackdrop-root')
    if (backdrop) {
      fireEvent.click(backdrop)
    }

    await waitFor(
      () => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      },
      { timeout: 2000 }
    )
  })

  it('renders ResolvedRequestDetails component in menu', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
    expect(screen.getByText('Request Details for 123')).toBeInTheDocument()
  })

  it('wraps content with AccessControlWrapper', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('access-control-wrapper')).toBeInTheDocument()
  })

  it('handles different request types', () => {
    const roleRequest: IApprovalRequest = {
      ...mockRequest,
      id: '456',
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        module: 'groups',
        type: 'CREATE_ROLE',
        name: 'Create Role',
      },
    }

    render(<AllApprovalRequestsMoreMenu request={roleRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByText('Request Details for 456')).toBeInTheDocument()
  })

  it('handles customer requests', () => {
    const customerRequest: IApprovalRequest = {
      ...mockRequest,
      id: '789',
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        module: 'Customers',
        type: 'CREATE_CUSTOMER',
        name: 'Create Customer',
      },
    }

    render(<AllApprovalRequestsMoreMenu request={customerRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByText('Request Details for 789')).toBeInTheDocument()
  })

  it('displays correct button icon', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    expect(moreButton).toBeInTheDocument()

    // Check if the KeyboardArrowDownIcon is rendered
    const icon = moreButton.querySelector('svg')
    expect(icon).toBeInTheDocument()
  })

  it('handles menu item interactions', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    const menuItems = screen.getAllByRole('menuitem')
    expect(menuItems.length).toBeGreaterThan(0)
  })

  it('maintains menu state correctly', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')

    // Open menu
    fireEvent.click(moreButton)
    expect(screen.getByRole('menu')).toBeInTheDocument()

    // Menu should be open and functional
    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
  })

  it('handles keyboard navigation', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')

    // Test clicking to open menu first
    fireEvent.click(moreButton)
    expect(screen.getByRole('menu')).toBeInTheDocument()

    // Test that menu items are accessible
    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
  })

  it('renders with different request statuses', () => {
    const rejectedRequest: IApprovalRequest = {
      ...mockRequest,
      status: 'REJECTED',
      checkerComments: 'Request rejected due to insufficient information',
    }

    render(<AllApprovalRequestsMoreMenu request={rejectedRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
  })

  it('handles requests without checker comments', () => {
    const requestWithoutComments: IApprovalRequest = {
      ...mockRequest,
      checkerComments: undefined,
    }

    render(<AllApprovalRequestsMoreMenu request={requestWithoutComments} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
  })

  it('handles requests without entity ID', () => {
    const requestWithoutEntityId: IApprovalRequest = {
      ...mockRequest,
      entityId: undefined,
    }

    render(<AllApprovalRequestsMoreMenu request={requestWithoutEntityId} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
  })

  it('handles empty diff array', () => {
    const requestWithEmptyDiff: IApprovalRequest = {
      ...mockRequest,
      diff: [],
    }

    render(<AllApprovalRequestsMoreMenu request={requestWithEmptyDiff} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
  })

  it('handles complex diff structures', () => {
    const requestWithComplexDiff: IApprovalRequest = {
      ...mockRequest,
      diff: [
        {
          field: 'permissions',
          oldValue: [{ field: 'read', oldValue: 'false', newValue: 'true' }],
          newValue: [
            { field: 'read', oldValue: 'false', newValue: 'true' },
            { field: 'write', oldValue: 'false', newValue: 'true' },
          ],
        },
      ],
    }

    render(<AllApprovalRequestsMoreMenu request={requestWithComplexDiff} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('resolved-request-details')).toBeInTheDocument()
  })

  it('handles menu positioning correctly', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    const menu = screen.getByRole('menu')
    expect(menu).toBeInTheDocument()

    // Menu should be positioned relative to the button
    expect(menu).toHaveAttribute('role', 'menu')
  })
})
