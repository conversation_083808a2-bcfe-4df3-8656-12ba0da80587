import { render, screen, fireEvent, waitFor } from '../../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { PendingRequestMoreMenu } from '../../../../src/app/approval-requests/Pending/MoreMenu'
import { useAppDispatch } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  }),
}))

// Mock ReviewRequest component
vi.mock(
  '../../../../src/app/approval-requests/Pending/Dialog/ReviewRequest',
  () => ({
    default: ({ request }: { request: IApprovalRequest }) => (
      <div data-testid="review-request">
        <span>Review Request for {request.id}</span>
      </div>
    ),
  })
)

// Mock ApprovalRequestRouting
vi.mock('../../../../src/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn(),
}))

// Mock AccessControlWrapper
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    VIEW_APPROVALREQUEST_CUSTOMERS: ['VIEW_CUSTOMERS'],
    VIEW_APPROVALREQUEST_ROLES: ['VIEW_ROLES'],
    VIEW_APPROVALREQUEST_USERS: ['VIEW_USERS'],
    ACCEPT_APPROVALREQUEST_CUSTOMERS: ['ACCEPT_CUSTOMERS'],
    ACCEPT_APPROVALREQUEST_ROLES: ['ACCEPT_ROLES'],
    ACCEPT_APPROVALREQUEST_USERS: ['ACCEPT_USERS'],
    REJECT_APPROVALREQUEST_CUSTOMERS: ['REJECT_CUSTOMERS'],
    REJECT_APPROVALREQUEST_ROLES: ['REJECT_ROLES'],
    REJECT_APPROVALREQUEST_USERS: ['REJECT_USERS'],
  },
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="access-control-wrapper">{children}</div>
  ),
}))

describe('MoreMenu (Pending)', () => {
  const mockDispatch = vi.fn()

  const mockRequest: IApprovalRequest = {
    id: '123',
    maker: 'John Doe',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-01T10:00:00Z',
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_USERS'],
      description: 'Create user request',
      makerPermissions: ['CREATE_USERS'],
      module: 'users',
      name: 'Create User',
      overridePermissions: [],
      type: 'CREATE_USER',
    },
    entityId: 'user-123',
    entity: 'User',
    diff: [
      {
        field: 'firstName',
        oldValue: '',
        newValue: 'John',
      },
    ],
    makerComments: 'Creating new user for the team',
    status: 'PENDING',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the more menu button', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    expect(moreButton).toBeInTheDocument()
    expect(moreButton).toHaveAttribute('aria-haspopup', 'true')
  })

  it('opens menu when button is clicked', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByRole('menu')).toBeInTheDocument()
  })

  it('closes menu when clicking outside', async () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByRole('menu')).toBeInTheDocument()

    // Click outside - use the backdrop element
    const backdrop = document.querySelector('.MuiBackdrop-root')
    if (backdrop) {
      fireEvent.click(backdrop)
    }

    await waitFor(
      () => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument()
      },
      { timeout: 2000 }
    )
  })

  it('renders ReviewRequest component in menu', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('review-request')).toBeInTheDocument()
    expect(screen.getByText('Review Request for 123')).toBeInTheDocument()
  })

  it('wraps content with AccessControlWrapper', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('access-control-wrapper')).toBeInTheDocument()
  })

  it('handles different request types', () => {
    const roleRequest: IApprovalRequest = {
      ...mockRequest,
      id: '456',
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        module: 'groups',
        type: 'CREATE_ROLE',
        name: 'Create Role',
      },
    }

    render(<PendingRequestMoreMenu request={roleRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByText('Review Request for 456')).toBeInTheDocument()
  })

  it('handles customer requests', () => {
    const customerRequest: IApprovalRequest = {
      ...mockRequest,
      id: '789',
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        module: 'Customers',
        type: 'CREATE_CUSTOMER',
        name: 'Create Customer',
      },
    }

    render(<PendingRequestMoreMenu request={customerRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByText('Review Request for 789')).toBeInTheDocument()
  })

  it('displays correct button icon', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    expect(moreButton).toBeInTheDocument()

    // Check if the KeyboardArrowDownIcon is rendered
    const icon = moreButton.querySelector('svg')
    expect(icon).toBeInTheDocument()
  })

  it('handles menu item interactions', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    const menuItems = screen.getAllByRole('menuitem')
    expect(menuItems.length).toBeGreaterThan(0)
  })

  it('maintains menu state correctly', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')

    // Open menu
    fireEvent.click(moreButton)
    expect(screen.getByRole('menu')).toBeInTheDocument()

    // Menu should be open and functional
    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles keyboard navigation', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')

    // Test clicking to open menu first
    fireEvent.click(moreButton)
    expect(screen.getByRole('menu')).toBeInTheDocument()

    // Test that menu items are accessible
    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles requests with different statuses', () => {
    const pendingRequest: IApprovalRequest = {
      ...mockRequest,
      status: 'PENDING',
    }

    render(<PendingRequestMoreMenu request={pendingRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles requests without entity ID', () => {
    const requestWithoutEntityId: IApprovalRequest = {
      ...mockRequest,
      entityId: undefined,
    }

    render(<PendingRequestMoreMenu request={requestWithoutEntityId} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles empty diff array', () => {
    const requestWithEmptyDiff: IApprovalRequest = {
      ...mockRequest,
      diff: [],
    }

    render(<PendingRequestMoreMenu request={requestWithEmptyDiff} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles complex diff structures', () => {
    const requestWithComplexDiff: IApprovalRequest = {
      ...mockRequest,
      diff: [
        {
          field: 'permissions',
          oldValue: [{ field: 'read', oldValue: 'false', newValue: 'true' }],
          newValue: [
            { field: 'read', oldValue: 'false', newValue: 'true' },
            { field: 'write', oldValue: 'false', newValue: 'true' },
          ],
        },
      ],
    }

    render(<PendingRequestMoreMenu request={requestWithComplexDiff} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles menu positioning correctly', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    const menu = screen.getByRole('menu')
    expect(menu).toBeInTheDocument()

    // Menu should be positioned relative to the button
    expect(menu).toHaveAttribute('role', 'menu')
  })

  it('handles requests with maker comments', () => {
    const requestWithComments: IApprovalRequest = {
      ...mockRequest,
      makerComments: 'This is a test request for creating a new user',
    }

    render(<PendingRequestMoreMenu request={requestWithComments} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles requests without maker comments', () => {
    const requestWithoutComments: IApprovalRequest = {
      ...mockRequest,
      makerComments: undefined,
    }

    render(<PendingRequestMoreMenu request={requestWithoutComments} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })

  it('handles requests with different modules', () => {
    const moduleRequests = [
      { module: 'users', type: 'UPDATE_USER' },
      { module: 'groups', type: 'DELETE_ROLE' },
      { module: 'Customers', type: 'UPDATE_CUSTOMER' },
    ]

    moduleRequests.forEach((moduleRequest, index) => {
      const request: IApprovalRequest = {
        ...mockRequest,
        id: `request-${index}`,
        makerCheckerType: {
          ...mockRequest.makerCheckerType,
          module: moduleRequest.module,
          type: moduleRequest.type,
        },
      }

      render(<PendingRequestMoreMenu request={request} />)

      const moreButton = screen.getByRole('button')
      fireEvent.click(moreButton)

      expect(
        screen.getByText(`Review Request for request-${index}`)
      ).toBeInTheDocument()
    })
  })

  it('handles menu close on item selection', () => {
    render(<PendingRequestMoreMenu request={mockRequest} />)

    const moreButton = screen.getByRole('button')
    fireEvent.click(moreButton)

    expect(screen.getByRole('menu')).toBeInTheDocument()

    // Simulate menu item click (this would typically close the menu)
    const menuItem = screen.getByRole('menuitem')
    fireEvent.click(menuItem)

    // The menu behavior depends on the implementation
    expect(screen.getByTestId('review-request')).toBeInTheDocument()
  })
})
