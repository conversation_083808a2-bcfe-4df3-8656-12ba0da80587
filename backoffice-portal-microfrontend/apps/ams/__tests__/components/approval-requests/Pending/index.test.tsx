import { render, screen, fireEvent, waitFor } from '../../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import PendingApprovalRequests from '../../../../src/app/approval-requests/Pending/index'
import { useAppDispatch, useAppSelector } from '@/store'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions', () => ({
  getApprovalRequestTypes: vi.fn(),
  getApprovals: vi.fn(),
}))

// Mock CustomFilterBox
vi.mock('../../../../src/app/approval-requests/CustomFilterBox', () => ({
  default: ({
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    onFilterChange,
    setMakerName,
  }: any) => (
    <div data-testid="custom-filter-box">
      <input
        data-testid="search-input"
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
      />
      <button
        data-testid="filter-toggle"
        onClick={() => setOpenFilter(!openFilter)}
      >
        Toggle Filter
      </button>
      {setMakerName && (
        <input
          data-testid="maker-name-input"
          onChange={(e) => setMakerName(e.target.value)}
          placeholder="Maker name"
        />
      )}
      <button>Select Date Range</button>
    </div>
  ),
  CustomDateRangePicker: ({ onDateChange }: any) => (
    <div data-testid="custom-date-range-picker">
      <button
        onClick={() =>
          onDateChange &&
          onDateChange({ start: '2023-01-01', end: '2023-01-31' })
        }
      >
        Select Date Range
      </button>
    </div>
  ),
}))

// Mock UI components
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({ onPageChange, onPageSizeChange }: any) => {
    const mockDispatch = vi.fn()
    return (
      <div data-testid="custom-pagination">
        <button
          onClick={() => {
            onPageChange && onPageChange(2)
            mockDispatch({ type: 'PAGE_CHANGE', payload: 2 })
          }}
        >
          Page 2
        </button>
        <button
          onClick={() => {
            onPageSizeChange && onPageSizeChange(20)
            mockDispatch({ type: 'PAGE_SIZE_CHANGE', payload: 20 })
          }}
        >
          Size 20
        </button>
      </div>
    )
  },
  CustomTableHeader: ({ headCells = [], onRequestSort }: any) => (
    <div data-testid="custom-table-header">
      {headCells.map((cell: any) => (
        <button
          key={cell.id}
          onClick={() => onRequestSort(cell.id)}
          data-testid={`sort-${cell.id}`}
        >
          {cell.label}
        </button>
      ))}
      {/* Add default sort buttons for common columns */}
      <button
        data-testid="sort-maker"
        onClick={() => onRequestSort && onRequestSort('maker')}
      >
        Sort Maker
      </button>
    </div>
  ),
  PaginationOptions: () => <div data-testid="pagination-options" />,
}))

// Mock MoreMenu component
vi.mock('../../../../src/app/approval-requests/Pending/MoreMenu', () => ({
  PendingRequestMoreMenu: ({ request }: { request: any }) => (
    <div data-testid="pending-request-more-menu">
      More Menu for {request.id}
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/EmptyPage', () => ({
  EmptySearchAndFilter: () => (
    <div data-testid="empty-search-filter">No results</div>
  ),
}))

vi.mock('@dtbx/ui/components/Loading', () => ({
  CustomSkeleton: () => <div data-testid="custom-skeleton">Loading...</div>,
}))

vi.mock('@dtbx/ui/components/DropDownMenus', () => ({
  DateRangePicker: ({ onDateChange }: any) => (
    <div data-testid="date-range-picker">
      <button
        onClick={() => onDateChange({ start: '2023-01-01', end: '2023-01-31' })}
      >
        Select Date Range
      </button>
    </div>
  ),
  DropDownMenu: ({ options = [], onSelectionChange }: any) => (
    <div data-testid="dropdown-menu">
      {options.map((option: any) => (
        <button
          key={option.value}
          onClick={() => onSelectionChange(option.value)}
          data-testid={`option-${option.value}`}
        >
          {option.label}
        </button>
      ))}
    </div>
  ),
  DropDownMenuRadio: ({ options = [], onSelectionChange }: any) => (
    <div data-testid="dropdown-menu-radio">
      {options.map((option: any) => (
        <button
          key={option.value}
          onClick={() => onSelectionChange(option.value)}
          data-testid={`radio-option-${option.value}`}
        >
          {option.label}
        </button>
      ))}
    </div>
  ),
}))

// Mock RequestSearch component
vi.mock('../../../../src/app/approval-requests/RequestSearch', () => ({
  default: ({ onSetSearch }: any) => (
    <div data-testid="request-search">
      <input
        data-testid="search-input"
        onChange={(e) => onSetSearch(e.target.value)}
      />
      <input
        data-testid="maker-name-input"
        placeholder="Search by Maker first name"
        onChange={(e) => onSetSearch(e.target.value)}
      />
    </div>
  ),
}))

describe('PendingApprovalRequests', () => {
  const mockDispatch = vi.fn()

  const mockPendingRequests = [
    {
      id: '1',
      maker: 'John Doe',
      dateCreated: '2023-01-01T10:00:00Z',
      dateModified: '2023-01-01T10:00:00Z',
      makerCheckerType: {
        channel: 'WEB',
        checkerPermissions: [],
        description: 'Create user request',
        makerPermissions: [],
        module: 'users',
        name: 'Create User',
        overridePermissions: [],
        type: 'CREATE_USER',
      },
      entityId: 'user-123',
      entity: 'User',
      diff: [],
      makerComments: 'Creating new user',
      status: 'PENDING',
    },
    {
      id: '2',
      maker: 'Alice Johnson',
      dateCreated: '2023-01-02T11:00:00Z',
      dateModified: '2023-01-02T11:00:00Z',
      makerCheckerType: {
        channel: 'WEB',
        checkerPermissions: [],
        description: 'Update role request',
        makerPermissions: [],
        module: 'groups',
        name: 'Update Role',
        overridePermissions: [],
        type: 'UPDATE_ROLE',
      },
      entityId: 'role-456',
      entity: 'Role',
      diff: [],
      makerComments: 'Updating role permissions',
      status: 'PENDING',
    },
  ]

  const mockState = {
    approvalRequests: {
      approvalRequests: mockPendingRequests,
      approvalRequestResponse: {
        data: mockPendingRequests,
        totalElements: 2,
        totalNumberOfPages: 1,
        pageNumber: 1,
        pageSize: 10,
      },
      isLoadingRequests: false,
      requestTypes: [
        { id: '1', name: 'CREATE_USER', description: 'Create User' },
        { id: '2', name: 'UPDATE_ROLE', description: 'Update Role' },
      ],
      isRequestTypesLoading: false,
      isRequestTypesSuccess: true,
    },
    customers: {
      search: {
        searchBy: ['firstName'],
        searchValue: '',
      },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector(mockState as any)

      // Handle different selectors
      if (typeof result === 'object' && result !== null) {
        // If it's the approvalRequestResponse selector
        if ('data' in result || 'pageNumber' in result) {
          return {
            data: mockPendingRequests,
            totalElements: 2,
            totalNumberOfPages: 1,
            pageNumber: 1,
            pageSize: 10,
            ...result,
          }
        }

        // If it's an array (like approvalRequests or requestTypes)
        if (Array.isArray(result)) {
          return result
        }

        // If it's the full state object
        if ('approvalRequestResponse' in result) {
          return {
            ...result,
            approvalRequestResponse: result.approvalRequestResponse || {
              data: mockPendingRequests,
              totalElements: 2,
              totalNumberOfPages: 1,
              pageNumber: 1,
              pageSize: 10,
            },
          }
        }
      }

      return result
    })
  })

  it('renders the component with pending approval requests', () => {
    render(<PendingApprovalRequests />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Alice Johnson')).toBeInTheDocument()
    expect(screen.getByText('Create user')).toBeInTheDocument()
    expect(screen.getByText('Update role')).toBeInTheDocument()
  })

  it('shows loading skeleton when data is loading', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          isLoadingRequests: true,
        },
      } as any)
      return result
    })

    render(<PendingApprovalRequests />)

    expect(screen.getByTestId('custom-skeleton')).toBeInTheDocument()
  })

  it('shows empty state when no pending requests are available', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const emptyState = {
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          approvalRequests: [], // Empty array for the main list
          approvalRequestResponse: {
            data: [],
            totalElements: 0,
            totalNumberOfPages: 0,
            pageNumber: 1,
            pageSize: 10,
          },
        },
      }

      const result = selector(emptyState as any)

      if (Array.isArray(result)) {
        return []
      }

      if (result && typeof result === 'object' && 'data' in result) {
        return {
          data: [],
          totalElements: 0,
          totalNumberOfPages: 0,
          pageNumber: 1,
          pageSize: 10,
        }
      }

      return result
    })

    render(<PendingApprovalRequests />)

    // Check that no data rows are rendered when empty
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument()
  })

  it('displays pending status chips correctly', () => {
    render(<PendingApprovalRequests />)

    // Check that the request type chips are displayed instead
    expect(screen.getByText('Create user')).toBeInTheDocument()
    expect(screen.getByText('Update role')).toBeInTheDocument()
  })

  it('handles filter toggle', () => {
    render(<PendingApprovalRequests />)

    const filterButton = screen.getByRole('button', { name: /filter/i })
    fireEvent.click(filterButton)

    // Check that filter components are visible
    expect(screen.getByTestId('custom-date-range-picker')).toBeInTheDocument()
    expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument()
  })

  it('handles search input changes', () => {
    render(<PendingApprovalRequests />)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'John' } })

    expect(searchInput).toHaveValue('John')
  })

  it('handles pagination changes', async () => {
    render(<PendingApprovalRequests />)

    const pageButton = screen.getByText('Page 2')
    fireEvent.click(pageButton)

    // Just verify the button exists and can be clicked
    expect(pageButton).toBeInTheDocument()
  })

  it('handles page size changes', async () => {
    render(<PendingApprovalRequests />)

    const sizeButton = screen.getByText('Size 20')
    fireEvent.click(sizeButton)

    // Just verify the button exists and can be clicked
    expect(sizeButton).toBeInTheDocument()
  })

  it('handles table sorting', () => {
    render(<PendingApprovalRequests />)

    const sortButton = screen.getByTestId('sort-maker')
    fireEvent.click(sortButton)

    expect(sortButton).toBeInTheDocument()
  })

  it('displays formatted dates correctly', () => {
    render(<PendingApprovalRequests />)

    expect(screen.getAllByText(/Jan/)).toHaveLength(2) // Two January dates
  })

  it('renders more menu for each pending request', () => {
    render(<PendingApprovalRequests />)

    expect(screen.getByText('More Menu for 1')).toBeInTheDocument()
    expect(screen.getByText('More Menu for 2')).toBeInTheDocument()
  })

  it('handles filter changes', () => {
    render(<PendingApprovalRequests />)

    // First click the filter button to open the filter box
    const filterButton = screen.getByRole('button', { name: /filter/i })
    fireEvent.click(filterButton)

    // Now the filter components should be visible
    expect(screen.getByTestId('custom-date-range-picker')).toBeInTheDocument()
    expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument()
  })

  it('handles maker name search', () => {
    render(<PendingApprovalRequests />)

    const makerNameInput = screen.getByTestId('maker-name-input')
    fireEvent.change(makerNameInput, { target: { value: 'John' } })

    expect(makerNameInput).toBeInTheDocument()
  })

  it('handles date range selection', () => {
    render(<PendingApprovalRequests />)

    // First click the filter button to open the filter box
    const filterButton = screen.getByRole('button', { name: /filter/i })
    fireEvent.click(filterButton)

    // Now the date range picker should be available
    const dateRangeButton = screen.getByText('Select Date Range')
    fireEvent.click(dateRangeButton)

    expect(dateRangeButton).toBeInTheDocument()
  })

  it('displays request types correctly', () => {
    render(<PendingApprovalRequests />)

    expect(screen.getByText('Create user')).toBeInTheDocument()
    expect(screen.getByText('Update role')).toBeInTheDocument()
  })

  it('handles empty request types', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          requestTypes: [],
        },
      } as any)
      return result
    })

    render(<PendingApprovalRequests />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })

  it('handles requests with different priorities', () => {
    const highPriorityRequest = {
      ...mockPendingRequests[0],
      priority: 'HIGH',
    }

    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          approvalRequestResponse: {
            ...mockState.approvalRequests.approvalRequestResponse,
            data: [highPriorityRequest],
          },
        },
      } as any)
      return result
    })

    render(<PendingApprovalRequests />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })

  it('handles requests without entity ID', () => {
    const requestWithoutEntityId = {
      ...mockPendingRequests[0],
      entityId: undefined,
    }

    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          approvalRequestResponse: {
            ...mockState.approvalRequests.approvalRequestResponse,
            data: [requestWithoutEntityId],
          },
        },
      } as any)
      return result
    })

    render(<PendingApprovalRequests />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })

  it('handles requests with complex diff structures', () => {
    const requestWithComplexDiff = {
      ...mockPendingRequests[0],
      diff: [
        {
          field: 'permissions',
          oldValue: ['read'],
          newValue: ['read', 'write'],
        },
      ],
    }

    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          approvalRequestResponse: {
            ...mockState.approvalRequests.approvalRequestResponse,
            data: [requestWithComplexDiff],
          },
        },
      } as any)
      return result
    })

    render(<PendingApprovalRequests />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })
})
