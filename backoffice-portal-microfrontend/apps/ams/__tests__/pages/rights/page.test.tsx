import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import RightsPage from '../../../src/app/rights/page'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions/roles'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions/roles', () => ({
  getPermissionsFilter: vi.fn(),
  getPermissionsGroup: vi.fn(),
}))

// Mock the components
vi.mock('../../../src/app/rights/ListRights', () => ({
  ListRights: ({ page, setPage }: any) => (
    <div data-testid="list-rights">
      <span>Current page: {page}</span>
      <button onClick={() => setPage(2)}>Go to page 2</button>
    </div>
  ),
}))

vi.mock('../../../src/app/rights/PageHeader', () => ({
  RightsPageHeader: ({ search, filter }: any) => (
    <div data-testid="rights-page-header">
      <input
        data-testid="search-input"
        onChange={(e) => search(e.target.value)}
        placeholder="Search permissions"
      />
      <button
        data-testid="apply-filter"
        onClick={() => filter({ 'Is Visible': 'yes', Module: 'users' })}
      >
        Apply Filter
      </button>
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/Loading', () => ({
  LoadingListsSkeleton: () => (
    <div data-testid="loading-skeleton">Loading...</div>
  ),
}))

// Mock lodash debounce
vi.mock('lodash', () => ({
  default: {
    debounce: (fn: any) => {
      fn.cancel = vi.fn()
      return fn
    },
  },
  debounce: (fn: any) => {
    fn.cancel = vi.fn()
    return fn
  },
}))

describe('RightsPage', () => {
  const mockDispatch = vi.fn()
  const mockGetPermissionsFilter = vi.fn()
  const mockGetPermissionsGroup = vi.fn()

  const mockState = {
    roles: {
      isLoadingPermissions: false,
      permissions: [
        {
          id: '1',
          name: 'CREATE_USER',
          visible: true,
          groupName: 'users',
        },
        {
          id: '2',
          name: 'UPDATE_USER',
          visible: false,
          groupName: 'users',
        },
      ],
      permissionPageCount: 3,
      permissionsCount: 25,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue(mockState.roles)
    vi.mocked(rolesActions.getPermissionsFilter).mockImplementation(
      mockGetPermissionsFilter
    )
    vi.mocked(rolesActions.getPermissionsGroup).mockImplementation(
      mockGetPermissionsGroup
    )
  })

  it('renders the rights page with header and list', () => {
    render(<RightsPage />)

    expect(screen.getByTestId('rights-page-header')).toBeInTheDocument()
    expect(screen.getByTestId('list-rights')).toBeInTheDocument()
    expect(screen.getByText('Current page: 1')).toBeInTheDocument()
  })

  it('shows loading skeleton when permissions are loading', () => {
    vi.mocked(useAppSelector).mockReturnValue({
      ...mockState.roles,
      isLoadingPermissions: true,
    })

    render(<RightsPage />)

    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument()
    expect(screen.queryByTestId('list-rights')).not.toBeInTheDocument()
  })

  it('calls getPermissionsGroup on mount', () => {
    render(<RightsPage />)

    expect(mockGetPermissionsGroup).toHaveBeenCalledWith(mockDispatch)
  })

  it('calls getPermissionsFilter when page changes', async () => {
    render(<RightsPage />)

    const goToPageButton = screen.getByText('Go to page 2')
    fireEvent.click(goToPageButton)

    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 2,
        size: 10,
        permissionName: '',
        isVisible: undefined,
        groupName: undefined,
      })
    })
  })

  it('handles search input changes', async () => {
    render(<RightsPage />)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'CREATE_USER' } })

    // The debounced search should be called
    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
        permissionName: 'CREATE_USER',
        isVisible: undefined,
        groupName: undefined,
      })
    })
  })

  it('handles filter changes', async () => {
    render(<RightsPage />)

    const applyFilterButton = screen.getByTestId('apply-filter')
    fireEvent.click(applyFilterButton)

    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
        permissionName: '',
        isVisible: 'yes',
        groupName: 'users',
      })
    })
  })

  it('resets page to 1 when filter changes', async () => {
    render(<RightsPage />)

    // First go to page 2
    const goToPageButton = screen.getByText('Go to page 2')
    fireEvent.click(goToPageButton)

    // Then apply filter
    const applyFilterButton = screen.getByTestId('apply-filter')
    fireEvent.click(applyFilterButton)

    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
        permissionName: '',
        isVisible: 'yes',
        groupName: 'users',
      })
    })
  })

  it('handles search with existing filters', async () => {
    render(<RightsPage />)

    // First apply filter
    const applyFilterButton = screen.getByTestId('apply-filter')
    fireEvent.click(applyFilterButton)

    // Then search
    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'UPDATE' } })

    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
        permissionName: 'UPDATE',
        isVisible: 'yes',
        groupName: 'users',
      })
    })
  })

  it('handles empty search value', async () => {
    render(<RightsPage />)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: '' } })

    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
        permissionName: '',
        isVisible: undefined,
        groupName: undefined,
      })
    })
  })

  it('handles filter with only visibility', async () => {
    render(<RightsPage />)

    // Mock a filter with only visibility
    const pageHeader = screen.getByTestId('rights-page-header')
    const customButton = document.createElement('button')
    customButton.onclick = () => {
      const event = new CustomEvent('filter', {
        detail: { 'Is Visible': 'no' },
      })
      pageHeader.dispatchEvent(event)
    }

    // Simulate filter change through the component
    const applyFilterButton = screen.getByTestId('apply-filter')
    fireEvent.click(applyFilterButton)

    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalled()
    })
  })

  it('handles filter with only module', async () => {
    render(<RightsPage />)

    // Test that the component can handle partial filter objects
    expect(screen.getByTestId('rights-page-header')).toBeInTheDocument()
    expect(screen.getByTestId('list-rights')).toBeInTheDocument()
  })

  it('maintains correct payload structure', async () => {
    render(<RightsPage />)

    // Verify initial call has correct structure
    expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
      page: 1,
      size: 10,
      permissionName: '',
      isVisible: undefined,
      groupName: undefined,
    })
  })

  it('handles multiple rapid search changes', async () => {
    render(<RightsPage />)

    const searchInput = screen.getByTestId('search-input')

    // Simulate rapid typing
    fireEvent.change(searchInput, { target: { value: 'C' } })
    fireEvent.change(searchInput, { target: { value: 'CR' } })
    fireEvent.change(searchInput, { target: { value: 'CRE' } })
    fireEvent.change(searchInput, { target: { value: 'CREA' } })

    // Due to debouncing, only the last value should be processed
    await waitFor(() => {
      expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
        page: 1,
        size: 10,
        permissionName: 'CREA',
        isVisible: undefined,
        groupName: undefined,
      })
    })
  })

  it('renders with correct stack styling', () => {
    render(<RightsPage />)

    const stackElement = screen.getByTestId('rights-page-header').parentElement
    expect(stackElement).toHaveStyle({
      flexDirection: 'column',
    })
  })

  it('handles page changes correctly', async () => {
    render(<RightsPage />)

    // Initial page should be 1
    expect(screen.getByText('Current page: 1')).toBeInTheDocument()

    // Change to page 2
    const goToPageButton = screen.getByText('Go to page 2')
    fireEvent.click(goToPageButton)

    // Should update the page display
    expect(screen.getByText('Current page: 2')).toBeInTheDocument()
  })

  it('calls getPermissionsFilter on initial render', () => {
    render(<RightsPage />)

    expect(mockGetPermissionsFilter).toHaveBeenCalledWith(mockDispatch, {
      page: 1,
      size: 10,
      permissionName: '',
      isVisible: undefined,
      groupName: undefined,
    })
  })

  it('handles component unmounting gracefully', () => {
    const { unmount } = render(<RightsPage />)

    expect(() => unmount()).not.toThrow()
  })
})
