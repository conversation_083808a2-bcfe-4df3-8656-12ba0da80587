import { IR<PERSON>, IRoleResponse } from '@/store/interfaces'
import {
  approveCreateRole,
  checkDeleteRole,
  checkUpdateRole,
  createRole,
  deleteRole,
  getPermissions,
  getRoleById,
  getRoles,
  getRolesFilter,
  makeCreateRole,
  makeDeleteRole,
  makeUpdateRole,
  rejectCreateRole,
  updateRole,
} from '@/store/actions'
import {
  setCreateRoleFailed,
  setCreateRoleSuccess,
  setDeleteRoleSuccess,
  setIsGetRoleError,
  setIsGetRoleSuccess,
  setIsLoadingPermissions,
  setIsLoadingRole,
  setIsLoadingTableListRoles,
  setLoadingCreateRole,
  setLoadingDeleteRole,
  setLoadingRoles,
  setLoadingUpdateRole,
  setPermissions,
  setRole,
  setRoles,
  setRolesWithPermissions,
  setUpdateRoleFailed,
  setUpdateRoleSuccess,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import dayjs from 'dayjs'
import { vi, beforeEach, describe, it, expect } from 'vitest'

// Mock the secureapi module
vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
  secureapi2: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
  downloadBlob: vi.fn(),
}))

import { secureapi } from '@dtbx/store/utils'

describe('roles', () => {
  //GET API TESTS
  describe('getRoles', () => {
    const mockRoles: IRole[] = [
      {
        id: '1',
        name: 'Admin',
        description: 'Administrator role',
        creationDate: '2023-01-01',
        custom: false,
        permissions: [],
        permissionsGroup: [],
      },
    ]

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch roles and stop loading when API returns status 200', async () => {
      const dispatch = vi.fn()

      vi.mocked(secureapi.get).mockResolvedValue({
        status: 200,
        data: mockRoles,
      })

      await getRoles(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingRoles(true))
      expect(dispatch).toHaveBeenCalledWith(setRoles(mockRoles))
      expect(dispatch).toHaveBeenCalledWith(setLoadingRoles(false))
    })

    it('should dispatch empty roles array and stop loading on API failure', async () => {
      const dispatch = vi.fn()
      vi.mocked(secureapi.get).mockRejectedValue(new Error('API Error'))

      await getRoles(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingRoles(true))
      expect(dispatch).toHaveBeenCalledWith(setRoles([]))
      expect(dispatch).toHaveBeenCalledWith(setLoadingRoles(false))
    })

    it('should dispatch empty roles and stop loading when API call fails', async () => {
      const dispatch = vi.fn()

      vi.mocked(secureapi.get).mockRejectedValue(new Error('Network Error'))

      await getRoles(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingRoles(true))
      expect(dispatch).toHaveBeenCalledWith(setRoles([]))
      expect(dispatch).toHaveBeenCalledWith(setLoadingRoles(false))
    })
  })
  describe('getRolesFilter', () => {
    const mockedRoleData: IRoleResponse = {
      pageNumber: 1,
      pageSize: 10,
      totalElements: 15,
      totalNumberOfPages: 3,
      data: [
        {
          id: '1',
          name: 'Admin',
          description: 'Administrator role',
          creationDate: '2023-01-01',
          custom: false,
          permissions: [],
          permissionsGroup: [],
        },
      ],
    }

    it('should dispatch setIsLoadingTableListRoles(true) before making the API call', async () => {
      const dispatch = vi.fn()
      const filter = { roleName: 'admin' }

      vi.mocked(secureapi.get).mockResolvedValue({
        status: 200,
        data: mockedRoleData,
      })

      await getRolesFilter(dispatch, filter)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingTableListRoles(true))
    })

    it('should construct URL with correct query parameters when filter object is provided', async () => {
      const dispatch = vi.fn()
      const mockAxiosGetHandler = vi
        .mocked(secureapi.get)
        .mockResolvedValue({ status: 200, data: mockedRoleData })
      const filter = {
        roleName: 'admin',
        rights: 'read',
        isVisible: 'yes',
        moduleName: 'billing',
        date: {
          start: dayjs('2022-01-01'),
          end: dayjs('2022-01-31'),
        },
        size: 20,
        page: 2,
      }

      await getRolesFilter(dispatch, filter)

      const expectedParamString = [
        'page=2',
        'size=20',
        'isVisible=true',
        'roleName=admin',
        'rights=read',
        'moduleName=billing',
        '&createDateFrom=2022-01-01&createDateTo=2022-01-31',
      ]
      const expectedUrl = '?' + expectedParamString.join('&')

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingTableListRoles(true))
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        '/backoffice-auth/roles/filter' + expectedUrl
      )
      expect(dispatch).toHaveBeenCalledWith(
        setRolesWithPermissions(mockedRoleData)
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingTableListRoles(false))
    })
  })
  describe('getRolesById', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch success actions and set role when API call is successful', async () => {
      const dispatch = vi.fn()
      const roleId = '123'
      const mockRoleData = {
        id: '1',
        name: 'Admin',
        description: 'Administrator role',
        creationDate: '2023-01-01',
        custom: false,
        permissions: [],
        permissionsGroup: [],
      }
      vi.mocked(secureapi.get).mockResolvedValue({ data: mockRoleData })

      await getRoleById(dispatch, roleId)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingRole(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingRole(false))
      expect(dispatch).toHaveBeenCalledWith(setIsGetRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(setRole(mockRoleData))
    })

    it('should dispatch error actions when API call fails', async () => {
      const dispatch = vi.fn()
      const roleId = '123'
      const mockError = new Error('Network Error')
      vi.mocked(secureapi.get).mockRejectedValue(mockError)

      await getRoleById(dispatch, roleId)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingRole(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingRole(false))
      expect(dispatch).toHaveBeenCalledWith(setIsGetRoleError(true))
    })
  })

  //CREATION API TESTS
  describe('createRole', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch success actions and notification when role creation is successful', async () => {
      const dispatch = vi.fn()
      const data = {
        name: 'Admin',
        permissions: ['read', 'write'],
        description: 'Administrator role',
        comments: 'This is a test role',
      }

      vi.mocked(secureapi.post).mockResolvedValue({ status: 200 })

      await createRole(data, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateRole(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/roles',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setCreateRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Role was Successfully Created',
          type: 'success',
        })
      )
    })

    it('should dispatch failure actions and error notification when role creation fails', async () => {
      const dispatch = vi.fn()
      const data = {
        name: 'Admin',
        permissions: ['read', 'write'],
        description: 'Administrator role',
        comments: 'This is a test role',
      }
      const errorMessage = 'Network Error'

      vi.mocked(secureapi.post).mockRejectedValue(new Error(errorMessage))

      await createRole(data, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateRole(true))
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/roles',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setCreateRoleFailed(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateRole(false))
    })
  })
  describe('MakeCreateRole', () => {
    const mockData = {
      name: 'Admin',
      permissions: ['read', 'write'],
      description: 'Administrator role',
      comments: 'This is a test role',
    }

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch success actions when role creation is successful', async () => {
      const mockDispatch = vi.fn()
      vi.mocked(secureapi.post).mockResolvedValue({ status: 200 })
      vi.spyOn(global.console, 'error').mockImplementation(() => {})

      await makeCreateRole(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(true))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(false))
      expect(mockDispatch).toHaveBeenCalledWith(setCreateRoleSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create role request is pending approval.',
          type: 'success',
        })
      )
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/roles/make',
        mockData
      )
    })

    it('should dispatch failure actions when role creation fails', async () => {
      const mockDispatch = vi.fn()

      const errorMessage = 'Network Error'
      vi.mocked(secureapi.post).mockRejectedValue(new Error(errorMessage))
      vi.spyOn(global.console, 'error').mockImplementation(() => {})

      await makeCreateRole(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCreateRoleFailed(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(false))
      expect(console.error).toHaveBeenCalledWith(new Error(errorMessage))
    })

    it('should send notification of success when role creation is pending approval', async () => {
      const mockDispatch = vi.fn()

      vi.mocked(secureapi.post).mockResolvedValue({ status: 200 })
      vi.spyOn(global.console, 'error').mockImplementation(() => {})

      await makeCreateRole(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(true))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(false))
      expect(mockDispatch).toHaveBeenCalledWith(setCreateRoleSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create role request is pending approval.',
          type: 'success',
        })
      )
      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-auth/roles/make',
        mockData
      )
    })

    it('should send error notification when role creation fails', async () => {
      const mockDispatch = vi.fn()
      vi.mocked(secureapi.post).mockRejectedValue(
        new Error('Role creation failed')
      )
      vi.spyOn(global.console, 'error').mockImplementation(() => {})

      await makeCreateRole(mockData, mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCreateRoleFailed(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Role creation failed',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateRole(false))
    })
  })
  describe('ApproveCreateRole', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch success actions when approvalId and comments are valid', async () => {
      const dispatch = vi.fn()
      const approvalId = 'validApprovalId'
      const comments = 'Valid comments'

      vi.mocked(secureapi.put).mockResolvedValue({ status: 200 })

      await approveCreateRole(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create role request has been approved.',
          type: 'success',
        })
      )
    })

    it('should dispatch failure actions when approvalId is invalid', async () => {
      const dispatch = vi.fn()
      const approvalId = 'invalidApprovalId'
      const comments = 'Some comments'

      const errorMessage = 'Invalid approval ID'
      vi.mocked(secureapi.put).mockRejectedValue(new Error(errorMessage))

      await approveCreateRole(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleFailed(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
    })

    it('should handle network failures during approval process', async () => {
      const dispatch = vi.fn()
      const approvalId = 'validApprovalId'
      const comments = 'Valid comments'
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      vi.mocked(secureapi.put).mockRejectedValue(new Error('Network Error'))

      await approveCreateRole(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/approve/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleFailed(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Network Error',
          type: 'error',
        })
      )
      expect(consoleErrorSpy).toHaveBeenCalled()
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
    })
  })
  describe('RejectCreateRole', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch success actions when the API call is successful', async () => {
      const dispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Not needed'

      vi.mocked(secureapi.put).mockResolvedValue({ status: 200 })

      await rejectCreateRole(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create role request has been rejected.',
          type: 'success',
        })
      )
    })

    it('should dispatch failure actions when the API call fails', async () => {
      const dispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Not needed'

      const errorMessage = 'Network Error'
      vi.mocked(secureapi.put).mockRejectedValue(new Error(errorMessage))

      await rejectCreateRole(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleFailed(true))
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
    })

    it('should dispatch error notification and set update role failed on API call failure', async () => {
      const dispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Not needed'
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      vi.mocked(secureapi.put).mockRejectedValue(new Error('API call failed'))

      await rejectCreateRole(approvalId, comments, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'API call failed',
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleFailed(true))
      expect(consoleErrorSpy).toHaveBeenCalled()
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
    })
  })

  //UPDATE ROLES API TESTS

  describe('updateRole', () => {
    const mockData = {
      name: 'Admin',
      permissions: ['read', 'write'],
      description: 'Administrator role',
      comments: 'This is a test role',
    }
    const roleId = '123'

    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should update the role and dispatch success actions when valid data is provided', async () => {
      const dispatch = vi.fn()
      vi.mocked(secureapi.put).mockResolvedValue({ status: 200 })

      await updateRole(roleId, mockData, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`,
        mockData
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Role was Successfully Updated',
          type: 'success',
        })
      )
    })

    it('should handle network errors and dispatch failure actions', async () => {
      const dispatch = vi.fn()
      const errorMessage = 'Network Error'
      vi.mocked(secureapi.put).mockRejectedValue(new Error(errorMessage))

      await updateRole(roleId, mockData, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`,
        mockData
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(false))
    })
  })
  describe('MakeUpdateRole', () => {
    const dispatch = vi.fn()
    const roleId = '123'
    const mockData = {
      name: 'Admin',
      permissions: ['read', 'write'],
      description: 'Administrator role',
      comments: 'This is a test role',
    }

    it('should dispatch success actions and notification when role update is successful', async () => {
      secureapi.put = vi.fn().mockResolvedValue({ status: 200 })
      const getRolesFilter = vi.fn().mockResolvedValue({})

      await makeUpdateRole(roleId, mockData, dispatch)
      await getRolesFilter(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/make`,
        mockData
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Update role was successfully initiated',
          type: 'success',
        })
      )
      expect(getRolesFilter).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })

    it('should dispatch failure actions and error notification when network fails during role update', async () => {
      const errorMessage = 'Network Error'

      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await makeUpdateRole(roleId, mockData, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/make`,
        mockData
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(false))
    })

    it('should set loading state to false after successful role update', async () => {
      secureapi.put = vi.fn().mockResolvedValue({ status: 200 })
      const getRolesFilter = vi.fn().mockResolvedValue({})

      await makeUpdateRole(roleId, mockData, dispatch)
      await getRolesFilter(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/make`,
        mockData
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
    })
  })
  describe('CheckUpdateRole', () => {
    it('should dispatch success actions and notification when action is "approve"', async () => {
      const dispatch = vi.fn()
      const roleId = '123'
      const action = 'approve'
      const comments = 'Approved by admin'

      secureapi.put = vi.fn().mockResolvedValue({ status: 200 })
      const getRolesFilter = vi.fn()

      await checkUpdateRole(roleId, action, dispatch, comments)
      await getRolesFilter(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/${action}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit role request has been approved.',
          type: 'success',
        })
      )
      expect(getRolesFilter).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })

    it('should call getRolesFilter with default pagination after successful update', async () => {
      const dispatch = vi.fn()
      const roleId = '123'
      const action = 'approve'
      const comments = 'Approved by admin'

      secureapi.put = vi.fn().mockResolvedValue({ status: 200 })
      const getRolesFilter = vi.fn()

      await checkUpdateRole(roleId, action, dispatch, comments)
      await getRolesFilter(dispatch, { page: 1, size: 10 })

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/${action}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit role request has been approved.',
          type: 'success',
        })
      )
      expect(getRolesFilter).toHaveBeenCalledWith(dispatch, {
        page: 1,
        size: 10,
      })
    })

    it('should dispatch error notification and set update role success to false on network failure', async () => {
      const dispatch = vi.fn()
      const roleId = '123'
      const action = 'reject'
      const comments = 'Rejected due to policy'

      const errorMessage = 'Network Error'
      secureapi.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await checkUpdateRole(roleId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(true))
      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/${action}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateRole(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateRoleSuccess(false))
    })
  })

  //DELETE ROLES API TESTS
  describe('deleteRole', () => {
    it('should dispatch success actions and notification when a valid roleId is provided', async () => {
      const dispatch = vi.fn()
      const roleId = 'validRoleId'
      const comments = 'Deleting role for testing'

      secureapi.delete = vi.fn().mockResolvedValue({ status: 200 })
      await deleteRole(roleId, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Role was successfully deleted',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })
    it('should dispatch error actions and notification when the roleId does not exist', async () => {
      const dispatch = vi.fn()
      const roleId = 'nonExistentRoleId'
      const comments = 'Attempting to delete non-existent role'

      const errorMessage = 'Role not found'
      secureapi.delete = vi.fn().mockRejectedValue(new Error(errorMessage))

      await deleteRole(roleId, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })
    it('should dispatch success notification when role deletion is successful', async () => {
      const dispatch = vi.fn()
      const roleId = 'testRoleId'
      const comments = 'Test deletion'

      secureapi.delete = vi.fn().mockResolvedValue({ status: 200 })
      await deleteRole(roleId, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Role was successfully deleted',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })
    it('should dispatch failure actions and error notification when network fails', async () => {
      const dispatch = vi.fn()
      const roleId = 'invalidRoleId'
      const comments = 'Attempting to delete role'

      const errorMessage = 'Network Error'
      secureapi.delete = vi.fn().mockRejectedValue(new Error(errorMessage))
      await deleteRole(roleId, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })
  })
  describe('MakeDeleteRole', () => {
    it('should dispatch success actions when a valid roleId is provided', async () => {
      const dispatch = vi.fn()
      const roleId = 'validRoleId'
      const comments = 'Deleting role for testing'

      secureapi.delete = vi.fn().mockResolvedValue({ status: 200 })
      await makeDeleteRole(roleId, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/make`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Delete role was successfully initiated',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })
    it('should handle network failure and dispatch error actions', async () => {
      const dispatch = vi.fn()
      const roleId = 'validRoleId'
      const comments = 'Deleting role for testing'

      const errorMessage = 'Network Error'
      secureapi.delete = vi.fn().mockRejectedValue(new Error(errorMessage))

      await makeDeleteRole(roleId, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/make`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })
    it('should call the correct API endpoint with roleId when deleting a role', async () => {
      const dispatch = vi.fn()
      const roleId = 'testRoleId'
      const comments = 'Test comment'

      secureapi.delete = vi.fn().mockResolvedValue({ status: 200 })
      await makeDeleteRole(roleId, dispatch, comments)

      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/make`,
        { data: { comments } }
      )
    })
  })
  describe('CheckDeleteRole', () => {
    it('should dispatch success actions and notification when action is "approve"', async () => {
      const dispatch = vi.fn()
      const roleId = '123'
      const action = 'approve'
      const comments = 'Approved by admin'

      secureapi.delete = vi.fn().mockResolvedValue({ status: 200 })

      await checkDeleteRole(roleId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/${action}`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Delete role request has been approved.',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })

    it('should handle error and dispatch failure actions when role ID does not exist', async () => {
      const dispatch = vi.fn()
      const roleId = 'non-existent-id'
      const action = 'approve'
      const comments = 'Attempt to delete non-existent role'

      const errorMessage = 'Role not found'
      secureapi.delete = vi.fn().mockRejectedValue(new Error(errorMessage))

      await checkDeleteRole(roleId, action, dispatch, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(true))
      expect(secureapi.delete).toHaveBeenCalledWith(
        `/backoffice-auth/roles/${roleId}/${action}`,
        { data: { comments } }
      )
      expect(dispatch).toHaveBeenCalledWith(setDeleteRoleSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingDeleteRole(false))
    })
  })

  //PERMISSIONS API TESTS
  describe('getPermissions', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should dispatch loading state before API call', async () => {
      const dispatch = vi.fn()
      vi.mocked(secureapi.get).mockResolvedValue({ status: 200, data: [] })

      getPermissions(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingPermissions(true))

      // Wait for the promise to resolve
      await vi.waitFor(() => {
        expect(dispatch).toHaveBeenCalledWith(setIsLoadingPermissions(false))
        expect(dispatch).toHaveBeenCalledWith(setPermissions([]))
      })
    })

    it('should handle API call failure gracefully', async () => {
      const dispatch = vi.fn()
      vi.mocked(secureapi.get).mockRejectedValue(new Error('API Error'))

      getPermissions(dispatch)

      expect(secureapi.get).toHaveBeenCalledWith('/backoffice-auth/permissions')
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingPermissions(true))

      // Wait for the promise to reject and catch block to execute
      await vi.waitFor(() => {
        expect(dispatch).toHaveBeenCalledWith(setIsLoadingPermissions(false))
        expect(dispatch).toHaveBeenCalledWith(setPermissions([]))
      })
    })

    it('should dispatch empty permissions array when API call fails', async () => {
      const dispatch = vi.fn()
      vi.mocked(secureapi.get).mockRejectedValue(new Error('API error'))

      getPermissions(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingPermissions(true))

      // Wait for the promise to reject and catch block to execute
      await vi.waitFor(() => {
        expect(dispatch).toHaveBeenCalledWith(setPermissions([]))
        expect(dispatch).toHaveBeenCalledWith(setIsLoadingPermissions(false))
      })
    })
  })
})
