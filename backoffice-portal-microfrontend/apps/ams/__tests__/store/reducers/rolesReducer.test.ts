import { describe, it, expect } from 'vitest'
import rolesReducer, {
  setRoles,
  setRolesWithPermissions,
  setIsLoadingTableListRoles,
  setUpdatingRole,
  setCreatingRole,
  setLoadingRoles,
  setLoadingCreateRole,
  setLoadingUpdateRole,
  setLoadingDeleteRole,
  setLoadingError,
  setCreateRoleSuccess,
  setUpdateRoleSuccess,
  setDeleteRoleSuccess,
  setPermissions,
  setPermissionsWithFilters,
  setIsLoadingPermissions,
  setIsPermissionGroupLoading,
  setIsPermissionGroupSuccess,
  setPermissionGroup,
  resetRolesStore,
  setRoleFilterValue,
  setRoleSearchValue,
  setPermissionSearchValue,
  setPermissionFilterValue,
  setRole,
  setIsLoadingRole,
  setIsGetRoleSuccess,
  setIsGetRoleError,
  setCreateRoleFailed,
  setUpdateRoleFailed,
  type RolesReducerProps,
} from '@/store/reducers/rolesReducer'
import {
  IRole,
  IPermission,
  IPermissionGroupResponse,
} from '@/store/interfaces'

describe('rolesReducer', () => {
  const initialState: RolesReducerProps = {
    rolesList: [],
    tableListRoles: [],
    isLoadingTableListRoles: false,
    role: {
      id: '',
      name: '',
      description: '',
      creationDate: '',
      custom: false,
      permissions: [],
      permissionsGroup: [],
    },
    isLoadingRole: false,
    isGetRoleSuccess: false,
    isGetRoleError: false,
    isLoadingRoles: false,
    isLoadingCreateRole: false,
    isLoadingUpdateRole: false,
    isLoadingDeleteRole: false,
    isLoadingError: false,
    isCreateRoleSuccess: false,
    isCreateRoleFailed: false,
    isUpdateRoleSuccess: false,
    isUpdateRoleFailed: false,
    isDeleteRoleSuccess: false,
    isCreateRoleOpen: false,
    isUpdateRoleOpen: false,
    pageCount: 0,
    rolesCount: 0,
    permissions: [],
    permissionGroup: {
      pageNumber: 0,
      pageSize: 0,
      totalNumberOfPages: 0,
      totalElements: 0,
      data: [],
    },
    isPermissionGroupLoading: false,
    isPermissionGroupsSuccess: false,
    permissionPageCount: 0,
    permissionsCount: 0,
    isLoadingPermissions: false,
    roleSearchValue: '',
    roleFilterValue: {},
    permissionSearchValue: '',
    permissionFilterValue: {},
    isGeneratedRoleReportLoading: false,
    isGeneratedRoleReportSuccess: false,
    isGeneratedRoleReportFailure: false,
  }

  it('should return the initial state', () => {
    expect(rolesReducer(undefined, { type: 'unknown' })).toEqual(initialState)
  })

  it('should handle setRoles', () => {
    const roles: IRole[] = [
      {
        id: '1',
        name: 'Admin',
        description: 'Administrator role',
        creationDate: '2023-01-01',
        custom: true,
        permissions: [],
        permissionsGroup: [],
      },
    ]
    const actual = rolesReducer(initialState, setRoles(roles))
    expect(actual.rolesList).toEqual(roles)
  })

  it('should handle setRolesWithPermissions', () => {
    const roleResponse = {
      data: [
        {
          id: '1',
          name: 'User',
          description: 'User role',
          creationDate: '2023-01-01',
          custom: false,
          permissions: [],
          permissionsGroup: [],
        },
      ],
      totalNumberOfPages: 1,
      totalElements: 1,
      pageNumber: 0,
      pageSize: 10,
    }
    const actual = rolesReducer(
      initialState,
      setRolesWithPermissions(roleResponse)
    )
    expect(actual.tableListRoles).toEqual(roleResponse.data)
    expect(actual.pageCount).toBe(roleResponse.totalNumberOfPages)
    expect(actual.rolesCount).toBe(roleResponse.totalElements)
  })

  it('should handle setIsLoadingTableListRoles', () => {
    const actual = rolesReducer(initialState, setIsLoadingTableListRoles(true))
    expect(actual.isLoadingTableListRoles).toBe(true)
  })

  it('should handle setUpdatingRole', () => {
    const actual = rolesReducer(initialState, setUpdatingRole(true))
    expect(actual.isUpdateRoleOpen).toBe(true)
  })

  it('should handle setCreatingRole', () => {
    const actual = rolesReducer(initialState, setCreatingRole(true))
    expect(actual.isCreateRoleOpen).toBe(true)
  })

  it('should handle loading states', () => {
    expect(
      rolesReducer(initialState, setLoadingRoles(true)).isLoadingRoles
    ).toBe(true)
    expect(
      rolesReducer(initialState, setLoadingCreateRole(true)).isLoadingCreateRole
    ).toBe(true)
    expect(
      rolesReducer(initialState, setLoadingUpdateRole(true)).isLoadingUpdateRole
    ).toBe(true)
    expect(
      rolesReducer(initialState, setLoadingDeleteRole(true)).isLoadingDeleteRole
    ).toBe(true)
    expect(
      rolesReducer(initialState, setLoadingError(true)).isLoadingError
    ).toBe(true)
  })

  it('should handle success states', () => {
    expect(
      rolesReducer(initialState, setCreateRoleSuccess(true)).isCreateRoleSuccess
    ).toBe(true)
    expect(
      rolesReducer(initialState, setUpdateRoleSuccess(true)).isUpdateRoleSuccess
    ).toBe(true)
    expect(
      rolesReducer(initialState, setDeleteRoleSuccess(true)).isDeleteRoleSuccess
    ).toBe(true)
  })

  it('should handle failure states', () => {
    expect(
      rolesReducer(initialState, setCreateRoleFailed(true)).isCreateRoleFailed
    ).toBe(true)
    expect(
      rolesReducer(initialState, setUpdateRoleFailed(true)).isUpdateRoleFailed
    ).toBe(true)
  })

  it('should handle setPermissions', () => {
    const permissions: IPermission[] = [
      {
        id: '1',
        name: 'READ_USERS',
        description: 'Read users permission',
        module: 'users',
      },
    ]
    const actual = rolesReducer(initialState, setPermissions(permissions))
    expect(actual.permissions).toEqual(permissions)
  })

  it('should handle setPermissionsWithFilters', () => {
    const permissionResponse = {
      data: [
        {
          id: '1',
          name: 'WRITE_USERS',
          description: 'Write users permission',
          module: 'users',
        },
      ],
      totalNumberOfPages: 5,
      totalElements: 50,
      pageNumber: 0,
      pageSize: 10,
    }
    const actual = rolesReducer(
      initialState,
      setPermissionsWithFilters(permissionResponse)
    )
    expect(actual.permissions).toEqual(permissionResponse.data)
    expect(actual.permissionPageCount).toBe(
      permissionResponse.totalNumberOfPages
    )
    expect(actual.permissionsCount).toBe(permissionResponse.totalElements)
  })

  it('should handle setPermissionGroup', () => {
    const permissionGroup: IPermissionGroupResponse = {
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 3,
      totalElements: 25,
      data: [
        {
          id: '1',
          name: 'User Management',
          description: 'User management permissions',
          permissions: [],
        },
      ],
    }
    const actual = rolesReducer(
      initialState,
      setPermissionGroup(permissionGroup)
    )
    expect(actual.permissionGroup).toEqual(permissionGroup)
  })

  it('should handle permission loading states', () => {
    expect(
      rolesReducer(initialState, setIsLoadingPermissions(true))
        .isLoadingPermissions
    ).toBe(true)
    expect(
      rolesReducer(initialState, setIsPermissionGroupLoading(true))
        .isPermissionGroupLoading
    ).toBe(true)
    expect(
      rolesReducer(initialState, setIsPermissionGroupSuccess(true))
        .isPermissionGroupsSuccess
    ).toBe(true)
  })

  it('should handle search and filter values', () => {
    const roleSearchValue = 'admin'
    const roleFilterValue = { module: 'users' }
    const permissionSearchValue = 'read'
    const permissionFilterValue = { type: 'read' }

    expect(
      rolesReducer(initialState, setRoleSearchValue(roleSearchValue))
        .roleSearchValue
    ).toBe(roleSearchValue)
    expect(
      rolesReducer(initialState, setRoleFilterValue(roleFilterValue))
        .roleFilterValue
    ).toEqual(roleFilterValue)
    expect(
      rolesReducer(
        initialState,
        setPermissionSearchValue(permissionSearchValue)
      ).permissionSearchValue
    ).toBe(permissionSearchValue)
    expect(
      rolesReducer(
        initialState,
        setPermissionFilterValue(permissionFilterValue)
      ).permissionFilterValue
    ).toEqual(permissionFilterValue)
  })

  it('should handle setRole', () => {
    const role: IRole = {
      id: '1',
      name: 'Test Role',
      description: 'Test description',
      creationDate: '2023-01-01',
      custom: true,
      permissions: [],
      permissionsGroup: [],
    }
    const actual = rolesReducer(initialState, setRole(role))
    expect(actual.role).toEqual(role)
  })

  it('should handle role loading states', () => {
    expect(
      rolesReducer(initialState, setIsLoadingRole(true)).isLoadingRole
    ).toBe(true)
    expect(
      rolesReducer(initialState, setIsGetRoleSuccess(true)).isGetRoleSuccess
    ).toBe(true)
    expect(
      rolesReducer(initialState, setIsGetRoleError(true)).isGetRoleError
    ).toBe(true)
  })

  it('should handle resetRolesStore', () => {
    const modifiedState = {
      ...initialState,
      rolesList: [{ id: '1', name: 'Test' } as IRole],
      isLoadingRoles: true,
      roleSearchValue: 'test',
    }
    const actual = rolesReducer(modifiedState, resetRolesStore())
    expect(actual).toEqual(initialState)
  })
})
