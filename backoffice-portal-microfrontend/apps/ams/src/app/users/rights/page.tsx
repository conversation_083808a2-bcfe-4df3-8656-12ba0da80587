'use client'
import React, { useCallback, useEffect, useState } from 'react'
import { Stack } from '@mui/system'
import _ from 'lodash'
import { useAppDispatch, useAppSelector } from '@/store'
import { getPermissionsFilter, getPermissionsGroup } from '@/store/actions'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

import { ListRights } from '@/app/rights/ListRights'
import { RightsPageHeader } from '@/app/rights/PageHeader'

const RightsPage = () => {
  const dispatch = useAppDispatch()
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [filterValue, setFilterValue] = useState<
    Record<string, string | string[]>
  >({})
  const [rowsPerPage] = useState(10)

  const isLoadingPermissions = useAppSelector(
    (state) => state.roles.isLoadingPermissions
  )
  const payload = {
    page,
    size: rowsPerPage,
    permissionName: search,
    isVisible: filterValue['Is Visible'],
    groupName: filterValue.Module,
  }
  useEffect(() => {
    getPermissionsFilter(dispatch, { page, size: rowsPerPage })
    getPermissionsGroup(dispatch, { page: 1, size: 100 })
  }, [])
  const debouncedSearch = useCallback(
    _.debounce(
      (value: string, filter: Record<string, string | string[]>) =>
        getPermissionsFilter(dispatch, {
          ...payload,
          page: 1,
          size: rowsPerPage,
          permissionName: value,
          isVisible: filter['Is Visible'],
          groupName: filter.Module,
        }),
      1000
    ),
    []
  )
  useEffect(() => {
    getPermissionsFilter(dispatch, payload)
  }, [page])

  const triggerSearch = (searchValue: string) => {
    setSearch(searchValue)
    debouncedSearch(searchValue, filterValue)
  }
  const triggerFilter = (filter: Record<string, string | string[]>) => {
    setFilterValue(filter)
    setPage(1)
    getPermissionsFilter(dispatch, {
      ...payload,
      page: 1,
      isVisible: filter['Is Visible'],
      groupName: filter.Module,
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        py: '0.5%',
        px: '1.5%',
      }}
    >
      <RightsPageHeader search={triggerSearch} filter={triggerFilter} />
      {isLoadingPermissions ? (
        <CustomSkeleton
          animation="pulse"
          variant="rectangular"
          width={'100%'}
          height={'60vh'}
        />
      ) : (
        <>
          <ListRights page={page} setPage={setPage} />
        </>
      )}
    </Stack>
  )
}

export default RightsPage
