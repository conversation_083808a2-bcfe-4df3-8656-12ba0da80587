import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  createFilterOptions,
  DialogContent,
  DialogTitle,
  Divider,
  Drawer,
  FormControl,
  FormControlLabel,
  FormControlLabelProps,
  FormGroup,
  IconButton,
  InputAdornment,
  InputBase,
  ListItem,
  Menu,
  MenuItem,
  Paper,
  Popover,
  Portal,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import React, { useEffect, useState } from 'react'
import {
  AddRounded,
  Close,
  CloseRounded,
  DoneRounded,
  EditRounded,
  KeyboardArrowDownRounded,
  NorthEast,
  SearchRounded,
} from '@mui/icons-material'
import { sentenceCase } from 'tiny-case'
import CloseIcon from '@mui/icons-material/Close'
import { Form, FormikProvider, useFormik } from 'formik'
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input'
import {
  IPermission,
  IPermissionGroup,
  IRole,
  IUser,
} from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { useAppSelector as globalAppSelector } from '@dtbx/store'
import { setDrawer, setSwitchToUserDetails } from '@dtbx/store/reducers'
import {
  setCreateRoleSuccess,
  setCreatingRole,
  setSelectedApprovalRequest,
  setSingleUserData,
} from '@/store/reducers'
import {
  activateUser,
  assignUserLoanProducts,
  changeUserStatus,
  createRole,
  deactivateUser,
  getLoanProducts,
  getRoles,
  makeCreateRole,
  makeUpdateUser,
  updateUser,
} from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  HasAccessToRights,
} from '@dtbx/store/utils'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { Dialog } from '@dtbx/ui/components/Overlay'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { Tooltips } from '@dtbx/ui/components/Tooltip'
import { MoreVert } from '@dtbx/ui/icons'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import * as Yup from 'yup'
import { CustomDrawerChip } from '@dtbx/ui/components/Chip'
import {
  UserRoleCreateSuccess,
  UserRoleCreateFailure,
} from '@/app/roles/CreateRoleResponse'
const CustomCreateRoleInput = styled(InputBase)(({ theme }) => ({
  '& .MuiLabel': {
    marginTop: 3,
  },
  borderRadius: '4px',
  backgroundColor: '#fffff',
  border: `1px solid ${theme.palette.neutral.main}`,
  fontSize: 16,
  width: '100%',
  padding: '10px 12px',
  transition: theme.transitions.create(['border-color', 'box-shadow']),
  height: '40px',
  '&:focus': {
    boxShadow: theme.shadows[6],
    borderColor: theme.palette.neutral.main,
  },
}))
interface ISeeRights {
  setOpen: React.Dispatch<React.SetStateAction<IRole | undefined>>
  role: IRole
  setDialogSize?: React.Dispatch<React.SetStateAction<string>>
}

interface ICreateRole {
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  setDialogSize?: React.Dispatch<React.SetStateAction<string>>
}

const CreateRoleComponent: React.FC<ICreateRole> = ({
  setOpen,
  setDialogSize,
}) => {
  const dispatch = useAppDispatch()
  const modules = useAppSelector((state) => state.roles.permissionGroup)
  const modulesLoading = useAppSelector(
    (state) => state.roles.isPermissionGroupLoading
  )

  const { isCreateRoleSuccess, isCreateRoleFailed, isLoadingCreateRole } =
    useAppSelector((state) => state.roles)

  const filterOptions = createFilterOptions({
    matchFrom: 'start',
    stringify: (option: IPermissionGroup) => option.name,
    ignoreCase: true,
  })

  const [selectedModules, setSelectedModules] = React.useState<
    IPermissionGroup[]
  >([])
  const [selectedModule, setSelectedModule] = React.useState<IPermissionGroup>(
    selectedModules[selectedModules.length - 1]
  )
  const [selectedPermByModule, setSelectedPermByModule] = React.useState<
    IPermissionGroup[]
  >([])
  const currentModule = selectedPermByModule.find(
    (module) => module.id === selectedModule?.id
  )
  const selectedPermissions = currentModule ? currentModule.permissions : []
  const [selectedPerm, setSelectedPerm] = React.useState<IPermission[]>([
    ...selectedPermissions,
  ])
  const [openDropDownAutoComplete, setOpenDropDownAutoComplete] =
    React.useState<boolean>(false)

  const [showAccordion, setShowAccordion] = React.useState<boolean>(false)

  const roleValidation = Yup.object().shape({
    name: Yup.string().required('Role Name is required'),
    description: Yup.string().required('Description is required'),
    permissions: Yup.array()
      .of(Yup.string())
      .min(1, 'Select at least one permission'),
  })
  const formik = useFormik({
    async onSubmit(values) {
      if (HasAccessToRights(['SUPER_CREATE_GROUPS'])) {
        await createRole(
          {
            name: values.name,
            description: values.description,
            permissions: [
              ...selectedPermByModule
                .map((pg) => pg.permissions.map((perms) => perms.id))
                .flat(),
            ],
            comments: 'Created a new role',
          },
          dispatch
        )
      } else {
        await makeCreateRole(
          {
            name: values.name,
            description: values.description,
            permissions: [
              ...selectedPermByModule
                .map((pg) => pg.permissions.map((perms) => perms.id))
                .flat(),
            ],
            comments: 'Created a new role',
          },
          dispatch
        )
      }
    },
    initialValues: {
      name: '',
      description: '',
      Permissions: [],
    },
    validationSchema: roleValidation,
  })
  /** Start of select Chip handlers */
  const handleEdit = (option: IPermissionGroup, index: number) => {
    const isEqual = option.id === selectedModules[index].id
    if (isEqual) {
      setSelectedModule(option)
    }
    setOpenDropDownAutoComplete(false)
  }

  const handleDelete = (option: IPermissionGroup) => {
    // remove the selected module from selectedModules
    setSelectedModules(
      selectedModules.filter((module) => module.id !== option.id)
    )

    // remove the selected module from selectedPermByModule
    setSelectedPermByModule(
      selectedPermByModule.filter((module) => module.id !== option.id)
    )
  }

  /** End of select chip button handleers */

  /**
   * start of accordion handlers
   */
  const handleSingleCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    const permId = e.target.value
    // first identify the with module with the permission,  which is selectedModule
    // then find the permission in the selectedPerm
    // if found, remove it
    const isPermInModule = selectedModule?.permissions.find(
      (perm) => perm.id === permId
    )
    const isPermInSelectedPerm = selectedPerm.find((perm) => perm.id === permId)

    if (e.target.checked) {
      if (!isPermInSelectedPerm && isPermInModule) {
        setSelectedPerm([...selectedPerm, isPermInModule])
      }
    }
    if (!e.target.checked) {
      // remove item with permId from selectedPerm
      if (isPermInSelectedPerm) {
        setSelectedPerm([...selectedPerm.filter((perm) => perm.id !== permId)])
      }
    }
  }
  const handleAllCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    const moduleID = e.target.value
    const currentModule = selectedModules.find(
      (module) => module.id === moduleID
    )

    if (e.target.checked) {
      if (currentModule) {
        //check if permission in
        if (selectedPerm.length !== currentModule.permissions.length) {
          setSelectedPerm([...currentModule.permissions])
          // check if module is in selectedPermByModule
          const moduleIndex = selectedPermByModule.findIndex(
            (mod) => mod.id === currentModule.id
          )
          if (moduleIndex > 0) {
            const updatedModule = selectedPermByModule[moduleIndex]
            updatedModule.permissions = currentModule.permissions
            setSelectedPermByModule([
              ...selectedPermByModule.slice(0, moduleIndex),
              updatedModule,
              ...selectedPermByModule.slice(moduleIndex + 1),
            ])
          } else {
            setSelectedPermByModule([
              ...selectedPermByModule,
              {
                ...currentModule,
                permissions: currentModule.permissions,
              },
            ])
          }
        }
      }
    }
    if (!e.target.checked) {
      // remove item with moduleID from selectedPermByModule
      if (selectedPerm.length === currentModule?.permissions.length) {
        setSelectedPerm([])
      }
      // find module in id in selectedByPermModule and remove it
      const moduleIndex = selectedPermByModule.findIndex(
        (mod) => mod.id === moduleID
      )
      // if deleted module is found, remove it
      if (moduleIndex >= 0) {
        setSelectedPermByModule(
          selectedPermByModule.filter((pg) => pg.id !== moduleID)
        )
      }
    }
  }

  const handleDoneButton = (module: IPermissionGroup) => {
    setOpenDropDownAutoComplete(true)

    setShowAccordion(false)
    // find module in id in selectedByPermModule
    // if found, update the permissions
    // if not found, add the module to selectedPermByModule with update selectedPerm as permissions
    const moduleIndex = selectedPermByModule.findIndex(
      (mod) => mod.id === module.id
    )
    if (moduleIndex > 0) {
      const updatedModule = selectedPermByModule[moduleIndex]
      updatedModule.permissions = selectedPerm
      setSelectedPermByModule([
        ...selectedPermByModule.slice(0, moduleIndex),
        updatedModule,
        ...selectedPermByModule.slice(moduleIndex + 1),
      ])
    } else {
      setSelectedPermByModule([
        ...selectedPermByModule,
        {
          ...module,
          permissions: selectedPerm,
        },
      ])
    }
  }
  /** End of Accordion Input handlers */

  const { errors, getFieldProps, touched, handleSubmit } = formik
  return (
    <>
      {!isCreateRoleSuccess && !isCreateRoleFailed ? (
        <Stack
          sx={{
            height: '100%',
          }}
        >
          {setOpen && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                px: '4%',
              }}
            >
              <Typography variant="subtitle2">Create a new role</Typography>

              <Button
                endIcon={<CloseRounded />}
                sx={{
                  height: '36px',
                  fontSize: '12px',
                  fontWeight: 400,
                  color: '#555C61',
                }}
                onClick={() => {
                  setOpen && setOpen(false)
                  setDialogSize && setDialogSize('30%')
                }}
              >
                Cancel{' '}
              </Button>
            </Box>
          )}
          <Stack
            sx={{
              px: '4%',
              height: '100%',
            }}
          >
            {/* textfield */}
            <FormikProvider value={formik}>
              <Form
                style={{
                  height: '100%',
                }}
                onSubmit={handleSubmit}
              >
                <Stack
                  sx={{
                    display: 'flex',
                    height: '100%',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                  }}
                >
                  <Box
                    sx={{
                      height: '100%',
                    }}
                  >
                    <Box>
                      <Typography variant="subtitle3" component={'label'}>
                        Name
                      </Typography>
                      <CustomCreateRoleInput
                        size="small"
                        fullWidth
                        autoComplete="name"
                        type="text"
                        {...getFieldProps('name')}
                        error={Boolean(touched.name && errors.name)}
                      />
                      {touched.name && errors.name && (
                        <Typography variant="label3" color="error">
                          {errors.name}
                        </Typography>
                      )}
                    </Box>
                    <Box>
                      <Typography variant="subtitle3" component={'label'}>
                        Description
                      </Typography>
                      <CustomCreateRoleInput
                        size="small"
                        multiline
                        fullWidth
                        autoComplete="description"
                        type="text"
                        {...getFieldProps('description')}
                        error={Boolean(touched.name && errors.name)}
                      />
                      {touched.description && errors.description && (
                        <Typography variant="label3" color="error">
                          {errors.description}
                        </Typography>
                      )}
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '5px',
                      }}
                    >
                      <Typography variant="subtitle3" component={'label'}>
                        Rights
                      </Typography>
                      <Typography variant="label3">
                        Start typing to search for a module and select rights
                      </Typography>
                      <Autocomplete
                        open={openDropDownAutoComplete}
                        onOpen={() => setOpenDropDownAutoComplete(true)}
                        onClose={() => setOpenDropDownAutoComplete(false)}
                        filterOptions={filterOptions}
                        multiple
                        loading={modulesLoading}
                        sx={{
                          height: 'auto !important',
                        }}
                        size="small"
                        onChange={(event, newValue) => {
                          setSelectedModule(newValue[newValue.length - 1])
                          setSelectedModules(newValue)

                          setShowAccordion(true)
                        }}
                        onFocus={() => {
                          setOpenDropDownAutoComplete(false)
                          setShowAccordion(true)
                        }}
                        onBlur={() => {}}
                        isOptionEqualToValue={(option, value) =>
                          option.id === value.id
                        }
                        renderTags={(value, getTagProps) =>
                          value.map((option, index) => (
                            <Box
                              key={index}
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                                height: '20px',
                                minWidth: '85px',
                                marginRight: '4px',
                                marginBottom: '4px',
                              }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '2px',
                                  borderRadius: '4px',
                                  border: '1px solid #D0D5DD',
                                  height: '20px',
                                  padding: '2px 11px 2px 7px',
                                }}
                              >
                                <IconButton
                                  size="small"
                                  {...getTagProps({ index })}
                                  onClick={() => {
                                    handleDelete(option)
                                  }}
                                >
                                  <CloseRounded />
                                </IconButton>
                                <Typography>
                                  {sentenceCase(option.name)}
                                </Typography>
                                {(!showAccordion ||
                                  index !== selectedModules.length - 1) && (
                                  <IconButton
                                    size="small"
                                    onClick={() => handleEdit(option, index)}
                                  >
                                    <EditRounded />
                                  </IconButton>
                                )}
                              </Box>
                            </Box>
                          ))
                        }
                        renderOption={(props, option) => {
                          return (
                            <li {...props} key={option.id}>
                              {sentenceCase(option.name)}
                            </li>
                          )
                        }}
                        value={selectedModules}
                        getOptionKey={(option) => option.id}
                        getOptionLabel={(option) => option.name}
                        renderInput={(params) => {
                          return (
                            <TextField
                              {...params}
                              inputProps={{
                                ...params.inputProps,
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <SearchRounded />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )
                        }}
                        options={[...(modules && modules.data)].filter(
                          (option) =>
                            !selectedModules.find(
                              (selected) => selected.id === option.id
                            )
                        )}
                      />
                    </Box>
                    {showAccordion && selectedModules.length > 0 && (
                      <Accordion
                        sx={{
                          border: '1px solid #D0D5DD',
                          marginTop: '10px',
                          borderRadius: '4px',
                          overflow: 'auto',
                          maxHeight: '500px',
                        }}
                        elevation={0}
                      >
                        <AccordionSummary>
                          {' '}
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'row',
                              justifyContent: 'flex-start',
                              alignItems: 'center',
                              gap: '4px',
                            }}
                          >
                            <CustomCheckBox
                              value={selectedModule?.id}
                              checked={
                                selectedPermByModule.find(
                                  (mod) => mod.id === selectedModule?.id
                                ) !== undefined &&
                                selectedPermByModule.find(
                                  (mod) => mod.id === selectedModule?.id
                                )?.permissions.length === selectedPerm.length
                              }
                              onChange={handleAllCheck}
                            />
                            <Typography
                              sx={{
                                textAlign: 'center',
                              }}
                            >
                              {sentenceCase(selectedModule?.name)}
                            </Typography>

                            <Typography></Typography>
                          </Box>
                        </AccordionSummary>

                        <AccordionDetails
                          sx={{
                            padding: '0px 33px 0px 42px',
                            margin: 0,
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '4px',
                            justifyItems: 'flex-start',
                            alignItems: 'flex-start',
                          }}
                        >
                          <FormGroup>
                            {selectedModule &&
                              selectedModule.permissions.map((perm) => {
                                return (
                                  <FormControlLabel
                                    key={perm.id}
                                    control={
                                      <CustomCheckBox
                                        value={perm.id}
                                        checked={
                                          perm.id ===
                                          selectedPerm.find(
                                            (p) => p.id === perm.id
                                          )?.id
                                        }
                                        onChange={handleSingleCheck}
                                      />
                                    }
                                    label={sentenceCase(perm.name)}
                                  />
                                )
                              })}
                          </FormGroup>
                        </AccordionDetails>

                        <AccordionActions
                          sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            alignItems: 'center',
                          }}
                        >
                          <Button
                            type="reset"
                            sx={{
                              height: '34px',
                              width: '85px',
                            }}
                            onClick={() => {}}
                          >
                            <Typography>Cancel</Typography>
                          </Button>
                          <Button
                            type="button"
                            sx={{
                              height: '34px',
                              width: '100px',
                            }}
                            onClick={() => handleDoneButton(selectedModule)}
                            // disabled={}
                          >
                            <Typography>Done</Typography>
                            <DoneRounded
                              sx={{
                                color: 'black',
                              }}
                            />
                          </Button>
                        </AccordionActions>
                      </Accordion>
                    )}
                  </Box>

                  <Stack
                    sx={{
                      flexDirection: 'row',
                      width: '100%',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      gap: '0%',
                      marginY: '20px',
                    }}
                  >
                    <Button
                      variant="outlined"
                      sx={{
                        width: '45%',
                        height: '34px',
                        borderRadius: '4px',
                        border: '1px solid  #AAADB0',
                        background: '#FFF',
                        boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                        fontSize: '13px',
                        fontStyle: 'normal',
                        fontWeight: 500,
                        lineHeight: '16px',
                      }}
                      onClick={() =>
                        setOpen
                          ? setOpen(false)
                          : dispatch(
                              setDrawer({
                                open: false,
                                drawerChildren: null,
                                header: '',
                              })
                            )
                      }
                    >
                      Cancel
                    </Button>
                    {isLoadingCreateRole ? (
                      <LoadingButton width="45%" height="34px" />
                    ) : (
                      <Button
                        variant="outlined"
                        type="submit"
                        sx={{
                          width: '45%',
                          height: '34px',
                          borderRadius: '4px',
                          border: '1px solid  #AAADB0',
                          background: '#FFF',
                          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                          fontSize: '13px',
                          fontStyle: 'normal',
                          fontWeight: 500,
                          lineHeight: '16px',
                        }}
                      >
                        Create a role
                      </Button>
                    )}
                  </Stack>
                </Stack>
              </Form>
            </FormikProvider>
          </Stack>
        </Stack>
      ) : isCreateRoleSuccess ? (
        <UserRoleCreateSuccess
          handleCloseDrawer={() => dispatch(setCreateRoleSuccess(false))}
        />
      ) : (
        <UserRoleCreateFailure />
      )}
    </>
  )
}

const SeeRightsComponent: React.FC<ISeeRights> = ({
  setOpen,
  role,
  setDialogSize,
}) => {
  const [accordionID, SetAccordionID] = React.useState<string>('')
  const [search, setSearch] = React.useState<string>('')
  const [filter, setFilter] = React.useState<IPermissionGroup[]>(
    role.permissionsGroup
  )

  const handleSearch = (search: string = '') => {
    let filteredPermissionGroups = role.permissionsGroup.filter(
      (permissionGroup: IPermissionGroup) =>
        permissionGroup.name.toLowerCase().includes(search.toLowerCase())
    )

    // If no permission group name is found, search through permissions
    if (filteredPermissionGroups.length === 0) {
      filteredPermissionGroups = role.permissionsGroup.filter(
        (permissionGroup: IPermissionGroup) =>
          permissionGroup.permissions.some((permission: IPermission) =>
            permission.name.toLowerCase().includes(search.toLowerCase())
          )
      )
    }

    filteredPermissionGroups = filteredPermissionGroups.map(
      (permissionGroup: IPermissionGroup) => {
        const filteredPermissions = permissionGroup.permissions.filter(
          (permission: IPermission) =>
            permission.name.toLowerCase().includes(search.toLowerCase())
        )
        return {
          ...permissionGroup,
          permissions: filteredPermissions,
        }
      }
    )

    // Update the accordion ID if the currently expanded group is not in the filtered list
    if (!filteredPermissionGroups.some((group) => group.id === accordionID)) {
      SetAccordionID('')
      setFilter(filteredPermissionGroups)
    } else {
      setFilter(filteredPermissionGroups)
    }
  }
  React.useEffect(() => {
    handleSearch(search)
  }, [search])
  return (
    <Stack
      sx={{
        width: '100%',
        padding: '16px 20px 8px 24px',
        display: 'flex',
        flexDirection: 'column',
        gap: '16px',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {' '}
        <Box
          sx={{
            display: 'flex',
            gap: '10px',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          <Typography variant="subtitle1">Role Rights</Typography>
          <CustomDrawerChip label={`${role.permissions.length} rights`} />
        </Box>
        <Button
          endIcon={<CloseRounded />}
          sx={{
            height: '36px',
            fontSize: '12px',
            fontWeight: 400,
            color: '#555C61',
          }}
          onClick={() => {
            setOpen(undefined)
            setDialogSize && setDialogSize('30%')
          }}
        >
          Cancel{' '}
        </Button>
      </Box>
      <Stack
        sx={{
          flexDirection: 'column',
          gap: '10px',
          overflow: 'hidden',
        }}
      >
        {' '}
        <Box
          sx={{
            position: 'static',
          }}
        >
          <CustomSearchInput
            value={search}
            onChange={(
              e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
            ) => {
              const text = e.target.value
              setSearch(text)
            }}
            sx={{
              width: '100%',
              '&.Mui-focused': {
                width: '100%',
                boxShadow: '0px 0px 0px 4px #D3CFDC3D',
              },
            }}
            placeholder="Search Role"
            startAdornment={<SearchRounded />}
          />
        </Box>
        <Box
          sx={{
            overflowY: 'auto',
          }}
        >
          {filter !== null &&
            filter.map((permissionGroup) => (
              <Accordion
                expanded={permissionGroup.id === accordionID}
                key={permissionGroup.id}
                elevation={0}
                sx={{
                  border:
                    permissionGroup.id === accordionID
                      ? '1px solid lightgray'
                      : 'hidden',
                  borderRadius: '8px',
                  '&::before': {
                    backgroundColor: 'transparent',
                  },
                  //height: permissionGroup.id === accordionID ? '500px' : 'auto',
                }}
                onChange={() => {
                  SetAccordionID(
                    permissionGroup.id === accordionID ? '' : permissionGroup.id
                  )
                }}
              >
                <AccordionSummary
                  expandIcon={<KeyboardArrowDownRounded />}
                  sx={{
                    position: 'static',
                  }}
                >
                  <Typography marginRight={1}>
                    {sentenceCase(permissionGroup.name)}
                  </Typography>
                  <CustomDrawerChip
                    label={`${permissionGroup.permissions.length} rights`}
                    sx={{
                      backgroundColor: '#F9FAFB',
                    }}
                  />
                </AccordionSummary>
                <AccordionDetails
                  sx={{
                    maxHeight: '500px',
                    overflow: 'auto',
                    marginLeft: '2px',
                    '&::-webkit-scrollbar': {
                      width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                      backgroundColor: 'lightgray transparent',
                      padding: '0px 4px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: 'darkgray',
                      borderRadius: '10px',
                    },
                  }}
                >
                  {permissionGroup.permissions.map((permission, index) => (
                    <Box
                      key={permission.id}
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        gap: '4px',
                        padding: '0px 32px 20px 32px',
                      }}
                    >
                      <Typography>{index + 1}. </Typography>
                      <Typography>{sentenceCase(permission.name)}</Typography>
                    </Box>
                  ))}
                </AccordionDetails>
              </Accordion>
            ))}
        </Box>
      </Stack>
    </Stack>
  )
}

export const ViewUserRole = ({ role }: { role: IRole }) => {
  const [open, setOpen] = useState<boolean>(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [filteredPermissions, setFilteredPermissions] = useState<IPermission[]>(
    role && role.permissions
  )
  const [anchorEl, setAnchorEl] = useState<HTMLSpanElement | null>(null)
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value)
    const filtered = role.permissions.filter((permission) => {
      return permission.name
        .toLowerCase()
        .includes(event.target.value.toLowerCase())
    })
    if (!event.target.value) {
      setFilteredPermissions(role.permissions)
    }
    setFilteredPermissions(filtered)
  }
  const handleClose = () => {
    setAnchorEl(null)
    setOpen(false)
  }
  return (
    <>
      <Tooltips
        id="popper"
        aria-describedby={open ? role && role.id : undefined}
        title={
          <>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '12px',
              }}
            >
              <Typography
                variant="label1"
                sx={{
                  fontWeight: 400,
                }}
              >
                See rights
              </Typography>
              <NorthEast
                sx={{
                  width: '14px',
                  height: '14px',
                }}
              />
            </Box>
          </>
        }
        placement="top"
      >
        <Typography
          variant="body2"
          sx={{
            textDecorationLine: 'underline',
          }}
          onClick={(event) => {
            setAnchorEl(event.currentTarget)
            //
          }}
        >
          {role && role.name}
        </Typography>
      </Tooltips>
      {role && (
        <Portal>
          <Popover
            id={role.id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
          >
            <Paper
              elevation={1}
              sx={{
                padding: '5%',
              }}
            >
              <Stack>
                <Typography
                  sx={{
                    fontWeight: '600',
                    fontSize: '16px',
                    color: '#101828',
                  }}
                >
                  {sentenceCase(role.name)} Rights ({role.permissions.length})
                </Typography>
                <Typography
                  sx={{
                    fontWeight: '400',
                    fontSize: '14px',
                  }}
                >
                  The following rights have been assigned to this role.
                </Typography>
              </Stack>
              <CustomSearchInput
                sx={{
                  height: '50px',
                  my: '10px',
                  width: 250,
                  '&.Mui-focused': {
                    width: 250,
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  },
                }}
                value={searchValue}
                onChange={handleSearch}
                placeholder="Search"
                endAdornment={
                  <InputAdornment position="start">
                    <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
                  </InputAdornment>
                }
              />
              <Stack
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '5px',
                  marginTop: '5%',
                  height: '30vh',
                  overflowY: 'scroll',
                }}
              >
                {filteredPermissions.map((permission) => (
                  <Chip
                    key={permission.id}
                    label={permission.name}
                    sx={{
                      color: '#555C61',
                      background: '#F8F9FC',
                      fontWeight: '500',
                      fontSize: '13px',
                      textAlign: 'left',
                      py: '5px',
                      width: '60%',
                      borderRadius: '16px',
                      justifyContent: 'flex-start',
                    }}
                  />
                ))}
              </Stack>
            </Paper>
          </Popover>
        </Portal>
      )}
    </>
  )
}

export const UserMoreMenu = ({ user }: { user: IUser }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  return (
    <>
      <IconButton
        sx={{
          width: '24px',
          height: '24px',
          borderRadius: '50%',
        }}
        onClick={handleClick}
      >
        <MoreVert />
      </IconButton>

      <Menu
        id="demo-customized-menu"
        slotProps={{
          list: {
            'aria-labelledby': 'demo-customized-button',
          },
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <EditUserDetails user={user} />
        <ViewMoreDetails user={user} />
        <DeactivateUser user={user} />
        <AssignProduct user={user} />
      </Menu>
    </>
  )
}

const EditUserDetails = ({ user }: { user: IUser }) => {
  const dispatch = useAppDispatch()
  const { isLoadingEditUser } = useAppSelector((state) => state.users)
  const { rolesList } = useAppSelector((state) => state.roles)
  const profile = globalAppSelector((state) => state.auth.decodedToken)
  const currentUser = user
  const [selectedRole, setSelectedRole] = useState<IRole>()
  const [selectedRoles, setSelectedRoles] = useState<IRole[]>(currentUser.roles)
  const [isCreateRoleOpen, setIsCreateRoleOpen] = useState<boolean>(false)
  const validationSchema = Yup.object({
    roleId: Yup.array(),
  })
  const [phone, setPhone] = useState<string>(currentUser.phoneNumber)
  const formik = useFormik({
    initialValues: {
      roleIds: currentUser.roles,
      comments: 'Updating a user',
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      let isActiveUserRoleUpdate = false
      const newValues = {
        ...values,
        roleIds: selectedRoles.map((role) => role.id),
      }
      if (
        profile.user_id === currentUser.id &&
        selectedRoles.find(
          (role) => role.id !== currentUser.roles.map((role) => role.id)[0]
        )
      ) {
        isActiveUserRoleUpdate = true
      }
      if (HasAccessToRights(['SUPER_UPDATE_USERS'])) {
        await updateUser(
          currentUser.id,
          newValues,
          dispatch,
          isActiveUserRoleUpdate
        )
      } else {
        await makeUpdateUser(currentUser.id, newValues, dispatch)
      }

      handleClose(null, 'close')
    },
  })
  const { errors, touched, handleSubmit, resetForm, values } = formik
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setSelectedRole(undefined)
    setCreateRoleOpen(false)
    setOpenDrawer(false)
    resetForm()
    setDialogSize('30%')
  }
  const handleSeeRights = (role: IRole) => {
    setSelectedRole(role)
    setCreateRoleOpen(false)
    setDialogSize('60%')
  }

  const handlePhoneChange = (value: string) => {
    setPhone(value)
    formik.setFieldTouched('phoneNumber', true, false)
    if (!matchIsValidTel(value)) {
      formik.setFieldError('phoneNumber', 'Invalid phone number')
    }
    formik.setFieldValue('phoneNumber', value)
  }
  const handleDelete = (roleToDelete: IRole) => {
    setSelectedRoles((items) => items.filter((item) => item !== roleToDelete))
  }

  const handleOpenCreateRole = () => {
    setIsCreateRoleOpen(!isCreateRoleOpen)
    setSelectedRole(undefined)
    dispatch(setCreateRoleSuccess(false))
    setDialogSize('60%')
    setCreateRoleOpen(!openCreateRole)
  }
  const [openCreateRole, setCreateRoleOpen] = useState(false)
  const [openDrawer, setOpenDrawer] = useState(false)
  const [dialogSize, setDialogSize] = useState<string>('30%')
  useEffect(() => {
    getRoles(dispatch)
  }, [])
  return (
    <>
      <AccessControlWrapper rights={ACCESS_CONTROLS.UPDATE_USERS}>
        <MenuItem
          onClick={() => {
            setOpenDrawer(true)
          }}
        >
          <Typography variant="body1">Edit Details</Typography>
        </MenuItem>
      </AccessControlWrapper>

      <Drawer
        open={openDrawer}
        anchor="right"
        variant="persistent"
        sx={{
          '.MuiDrawer-paper': {
            width: dialogSize,
          },
        }}
        onClose={handleClose}
        slotProps={{
          paper: {
            sx: {
              width: dialogSize,
            },
          },
        }}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            padding: '0px 16px 0px 16px',
          }}
        >
          {/* tittle */}
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: '700',
            }}
          >
            Edit staff user
          </Typography>

          {/* close button */}
          <IconButton
            onClick={() => {
              setOpenDrawer(false)
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack
          direction="row"
          sx={{
            width: openCreateRole || selectedRole ? '100%' : '30vw',
            height: '100%',
          }}
        >
          <Stack
            sx={{
              padding: '0px 16px 0px 16px',
              height: '100%',
              width: openCreateRole || selectedRole ? '50%' : '100%',
            }}
          >
            <FormikProvider value={formik}>
              <Form noValidate onSubmit={handleSubmit}>
                <TextField
                  fullWidth
                  label="Email"
                  margin="normal"
                  size="small"
                  value={currentUser.email}
                  disabled
                />

                <TextField
                  fullWidth
                  label="First Name"
                  margin="normal"
                  size="small"
                  value={currentUser.firstName}
                  disabled
                />
                <TextField
                  fullWidth
                  label="Last Name"
                  margin="normal"
                  size="small"
                  value={currentUser.lastName}
                  disabled
                />
                <FormControl
                  fullWidth
                  sx={{
                    marginTop: '20px',
                  }}
                >
                  <Typography>Phone Number</Typography>
                  <MuiTelInput
                    value={phone}
                    name="phoneNumber"
                    size="small"
                    defaultCountry="KE"
                    onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                    onChange={handlePhoneChange}
                    disabled
                  />
                </FormControl>

                <Typography variant="body1">Role</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    mb: '10px',
                  }}
                >
                  Pick an existing role or create a new one. You can pick more
                  than one role.
                </Typography>
                <Button
                  onClick={handleOpenCreateRole}
                  endIcon={<AddRounded />}
                  variant="outlined"
                  sx={{
                    width: '157px',
                    height: '34px',
                    borderRadius: '6px',
                    border: '1px solid #AAADB0',
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    gap: 0,
                  }}
                >
                  <Typography
                    variant="body3"
                    sx={{
                      textWrap: 'nowrap',
                      color: 'black',
                    }}
                  >
                    Create new role
                  </Typography>
                </Button>
                <FormControl
                  variant="outlined"
                  fullWidth
                  sx={{ marginTop: '24px' }}
                  error={Boolean(touched.roleIds && errors.roleIds)}
                >
                  <Autocomplete
                    size="small"
                    id="roleIds"
                    //name="roleIds"
                    options={rolesList}
                    multiple
                    limitTags={4}
                    disableCloseOnSelect
                    getOptionLabel={(option) => option.name}
                    isOptionEqualToValue={(option, value) =>
                      option.id === value.id
                    }
                    value={selectedRoles}
                    onChange={(event, newValue) => {
                      setSelectedRoles(newValue)
                      //TODO: fix this line of code, formik is not updating the value when values is deleted.Tentatively using React-state
                      formik.setFieldValue('roleIds', newValue)
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label={'Pick Existing Role'} />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => {
                        return (
                          <Chip
                            key={index}
                            label={option.name}
                            sx={{
                              minWidth: '30px',
                              height: '16px',
                              border: '1px solid #EAECF0 ',
                              background: '#FFFFFF',
                              borderRadius: '6px',
                            }}
                            deleteIcon={
                              <IconButton
                                {...getTagProps({ index })}
                                size="small"
                              >
                                <Close fontSize="small" />
                              </IconButton>
                            }
                            onDelete={() => {
                              handleDelete(option)
                            }}
                          />
                        )
                      })
                    }
                    renderOption={({ key, ...rest }, role, { selected }) => (
                      <ListItem key={key} {...rest} sx={{}}>
                        <Box
                          sx={{
                            border: '1px solid #AAADB0',
                            width: '100%',
                            height: '30px',
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            borderRadius: '6px',
                            padding: '4px 9px 4px 6px',
                          }}
                        >
                          {' '}
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'row',
                              gap: '5px',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              padding: '0px ',
                            }}
                          >
                            <CustomCheckBox checked={selected} />
                            <Typography>{role.name}</Typography>
                          </Box>
                          <Typography
                            sx={{
                              color: '#555C61',
                              fontSize: '12px',
                              fontStyle: 'normal',
                              fontWeight: 400,
                              lineHeight: '16px',
                              '&:hover': {
                                color: 'blue',
                              },
                            }}
                            onClick={() => {
                              handleSeeRights(role)
                            }}
                          >
                            See rights
                          </Typography>
                        </Box>
                      </ListItem>
                    )}
                  />
                </FormControl>
                <Stack
                  sx={{
                    flexDirection: 'row',
                    marginTop: '30px',
                    gap: '20px',
                  }}
                >
                  <Button
                    variant="outlined"
                    sx={{
                      width: '50%',
                      height: '40px',
                      borderRadius: '6px',
                    }}
                    onClick={(e) => handleClose(e, 'close')}
                  >
                    Cancel
                  </Button>
                  {isLoadingEditUser ? (
                    <LoadingButton />
                  ) : (
                    <Button
                      variant="contained"
                      type="submit"
                      sx={{
                        width: '50%',
                        height: '40px',
                        borderRadius: '6px',
                      }}
                    >
                      Save
                    </Button>
                  )}
                </Stack>
              </Form>
            </FormikProvider>
          </Stack>
          {(openCreateRole || selectedRole) && (
            <>
              <Divider orientation="vertical" variant="fullWidth" sx={{}} />

              <Stack
                sx={{
                  width: openCreateRole || selectedRole ? '50%' : '0%',

                  height: '100%',
                }}
              >
                {openCreateRole && (
                  <CreateRoleComponent
                    setOpen={setCreateRoleOpen}
                    setDialogSize={setDialogSize}
                  />
                )}

                {selectedRole && (
                  <SeeRightsComponent
                    role={selectedRole}
                    setOpen={setSelectedRole}
                    setDialogSize={setDialogSize}
                  />
                )}
              </Stack>
            </>
          )}
        </Stack>
      </Drawer>
    </>
  )
}
const DeactivateUser = ({ user }: { user: IUser }) => {
  const [open, setOpen] = useState<boolean>(false)
  const isLoading = useAppSelector((state) => state.users.isLoadingEditUser)

  const dispatch = useAppDispatch()
  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const handleStatusChange = async (reasons: string[]) => {
    const comments = reasons.join(', ')
    if (user.status === 'ACTIVE') {
      if (HasAccessToRights(['SUPER_DEACTIVATE_USERS'])) {
        await changeUserStatus(user.id, 'deactivate', dispatch, comments)
      } else {
        await deactivateUser(user.id, 'make', dispatch, comments)
      }
    } else {
      if (HasAccessToRights(['SUPER_ACTIVATE_USERS'])) {
        await changeUserStatus(user.id, 'activate', dispatch, comments)
      } else {
        await activateUser(user.id, 'make', dispatch, comments)
      }
    }

    handleClose(null, 'close')
  }
  return (
    <>
      <AccessControlWrapper rights={ACCESS_CONTROLS.DEACTIVATE_ACTIVATE_USERS}>
        <MenuItem onClick={() => setOpen(!open)}>
          <Typography variant="body1">
            {' '}
            {user.status !== 'ACTIVE' ? 'Activate' : 'Deactivate'}
          </Typography>
        </MenuItem>
      </AccessControlWrapper>
      <Dialog
        open={open}
        setOpen={setOpen}
        title={user.status === 'ACTIVE' ? 'Deactivate User' : 'Activate User'}
        buttonText={user.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
        buttonProps={{
          color: user.status === 'ACTIVE' ? '#EB0045' : '#12B76A',
        }}
        descriptionText={
          user.status === 'ACTIVE'
            ? `${user.firstName} will lose access to all assigned rights. Are you sure you want to proceed?`
            : ` ${user.firstName} will get access to all assigned rights. Are you sure you want to proceed?`
        }
        isLoading={isLoading}
        reasons={
          user.status === 'ACTIVE'
            ? ['Forgot Password', 'Leaving Company', 'Other']
            : ['Department relocation', 'Returning to Company', 'Other']
        }
        onClick={handleStatusChange}
      />
    </>
  )
}

const ViewMoreDetails = ({ user }: { user: IUser }) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()

  return (
    <MenuItem
      onClick={() => {
        dispatch(
          setSelectedApprovalRequest({
            id: '',
            maker: '',
            dateCreated: '',
            dateModified: '',
            makerCheckerType: {
              channel: '',
              checkerPermissions: [],
              description: undefined,
              makerPermissions: [],
              module: '',
              name: '',
              overridePermissions: [],
              type: '',
            },
            diff: [],
            status: '',
          })
        )
        dispatch(
          setSwitchToUserDetails({
            open: true,
            user: user,
          })
        )
        dispatch(setSingleUserData(user))

        router.push(`/users/details/`)
      }}
    >
      <Typography variant="body1">View more details</Typography>
    </MenuItem>
  )
}
const CustomFormControlLabel = styled((props: FormControlLabelProps) => (
  <FormControlLabel {...props} />
))(({ theme }) => ({
  '&.MuiFormControlLabel-root': {
    justifyContent: 'space-between',
    borderRadius: '8px',
    border: '1px solid #D0D5DD',
    background: '#FFFFFF',
    py: '1%',
    paddingLeft: '3%',
    marginBottom: '16px',
    marginLeft: '0px',
  },
  '.MuiFormControlLabel-label': {
    color: theme.palette.primary.main,
    fontWeight: 600,
    fontSize: '16px',
  },
}))

export const AssignProduct = ({ user }: { user: IUser }) => {
  const [open, setOpen] = useState<boolean>(false)
  // const { userProducts } = useAppSelector((state) => state.loans)

  const [assignedProducts, setAssignedProducts] = useState<string[]>([])
  const dispatch = useAppDispatch()
  const { isLoadingEditUser } = useAppSelector((state) => state.users)
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const { loanProducts } = useAppSelector((state) => state.loans)
  useEffect(() => {
    if (open) {
      getLoanProducts(dispatch, 'page=1&size=30')
    }
  }, [open])
  const handleAssignProduct = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setAssignedProducts([...assignedProducts, e.target.value])
    } else {
      setAssignedProducts(
        assignedProducts.filter((product) => product !== e.target.value)
      )
    }
  }
  const handleCancel = () => {
    setAssignedProducts([])
    setOpen(false)
  }
  const handleSubmit = async () => {
    await assignUserLoanProducts(assignedProducts, dispatch, user.id)
    setOpen(false)
  }
  return (
    <>
      <MenuItem onClick={() => setOpen(true)}>Assign Loan Products</MenuItem>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '30%',
          },
        }}
        open={open}
        variant={'persistent'}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '30%',
          },
        }}
      >
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: '2%',
            }}
          >
            <DialogTitle
              sx={{
                fontSize: '22px',
                fontWeight: '700',
              }}
            >
              Assign/Reassign Loan Products
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <DialogContent>
          <Typography variant="subtitle2">Select Products</Typography>
          <FormControl margin={'normal'} fullWidth>
            {loanProducts &&
              loanProducts.map((product) => (
                <CustomFormControlLabel
                  value={product.id}
                  key={product.id}
                  control={
                    <Checkbox
                      onChange={handleAssignProduct}
                      checked={assignedProducts.includes(product.id)}
                    />
                  }
                  label={product.name}
                  labelPlacement={'start'}
                />
              ))}
          </FormControl>
          <Stack direction="row" justifyContent={'space-between'} gap={'3%'}>
            <Button variant="outlined" fullWidth onClick={handleCancel}>
              Cancel
            </Button>
            {isLoadingEditUser ? (
              <LoadingButton />
            ) : (
              <Button variant="contained" fullWidth onClick={handleSubmit}>
                Save Changes
              </Button>
            )}
          </Stack>
        </DialogContent>
      </Drawer>
    </>
  )
}
