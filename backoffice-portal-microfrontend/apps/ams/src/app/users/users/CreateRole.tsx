'use client'

import { Form, FormikProvider, useFormik } from 'formik'
import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  createFilterOptions,
  FormControlLabel,
  FormGroup,
  IconButton,
  InputAdornment,
  InputBase,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import { sentenceCase } from 'tiny-case'
import React from 'react'
import * as Yup from 'yup'
import {
  CloseRounded,
  DoneRounded,
  EditRounded,
  SearchRounded,
} from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { IPermission, IPermissionGroup } from '@dtbx/store/interfaces'
import { HasAccessToRights } from '@dtbx/store/utils'
import { createRole, makeCreateRole } from '@/store/actions'
import { setDrawer } from '@dtbx/store/reducers'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { LoadingButton } from '@dtbx/ui/components/Loading'

const CustomCreateRoleInput = styled(InputBase)(({ theme }) => ({
  '& .MuiLabel': {
    marginTop: 3,
  },
  borderRadius: '4px',
  backgroundColor: '#fffff',
  border: `1px solid ${theme.palette.neutral.main}`,
  fontSize: 16,
  width: '100%',
  padding: '10px 12px',
  transition: theme.transitions.create(['border-color', 'box-shadow']),
  height: '40px',
  '&:focus': {
    boxShadow: theme.shadows[6],
    borderColor: theme.palette.neutral.main,
  },
}))

export const CreateUserRole = ({
  setOpen,
}: {
  setOpen: (val: boolean) => void
}) => {
  const dispatch = useAppDispatch()
  const modules = useAppSelector((state) => state.roles.permissionGroup)
  const modulesLoading = useAppSelector(
    (state) => state.roles.isPermissionGroupLoading
  )

  const { isLoadingCreateRole } = useAppSelector((state) => state.roles)
  const filterOptions = createFilterOptions({
    matchFrom: 'start',
    stringify: (option: IPermissionGroup) => option.name,
    ignoreCase: true,
  })

  const [selectedModules, setSelectedModules] = React.useState<
    IPermissionGroup[]
  >([])
  const [selectedModule, setSelectedModule] = React.useState<IPermissionGroup>(
    selectedModules[selectedModules.length - 1]
  )
  const [selectedPermByModule, setSelectedPermByModule] = React.useState<
    IPermissionGroup[]
  >([])
  const currentModule = selectedPermByModule.find(
    (module) => module.id === selectedModule?.id
  )
  const selectedPermissions = currentModule ? currentModule.permissions : []
  const [selectedPerm, setSelectedPerm] = React.useState<IPermission[]>([
    ...selectedPermissions,
  ])
  const [openDropDownAutoComplete, setOpenDropDownAutoComplete] =
    React.useState<boolean>(false)

  const [showAccordion, setShowAccordion] = React.useState<boolean>(false)

  const roleValidation = Yup.object().shape({
    name: Yup.string().required('Role Name is required'),
    description: Yup.string().required('Description is required'),
    permissions: Yup.array()
      .of(Yup.string())
      .min(1, 'Select at least one permission'),
  })
  const formik = useFormik({
    async onSubmit(values) {
      if (HasAccessToRights(['SUPER_CREATE_GROUPS'])) {
        await createRole(
          {
            name: values.name,
            description: values.description,
            permissions: [
              ...selectedPermByModule
                .map((pg) => pg.permissions.map((perms) => perms.id))
                .flat(),
            ],
            comments: 'Create a new role',
          },
          dispatch
        )
      } else {
        await makeCreateRole(
          {
            name: values.name,
            description: values.description,
            permissions: [
              ...selectedPermByModule
                .map((pg) => pg.permissions.map((perms) => perms.id))
                .flat(),
            ],
            comments: 'Create a new role',
          },
          dispatch
        )
      }
    },
    initialValues: {
      name: '',
      description: '',
      Permissions: [],
    },
    validationSchema: roleValidation,
  })

  /** Start of select Chip handlers */
  const handleEdit = (option: IPermissionGroup, index: number) => {
    const isEqual = option.id === selectedModules[index].id
    if (isEqual) {
      setSelectedModule(option)
    }
    setOpenDropDownAutoComplete(false)
  }

  const handleDelete = (option: IPermissionGroup) => {
    // remove the selected module from selectedModules
    setSelectedModules(
      selectedModules.filter((module) => module.id !== option.id)
    )

    // remove the selected module from selectedPermByModule
    setSelectedPermByModule(
      selectedPermByModule.filter((module) => module.id !== option.id)
    )
  }

  /** End of select chip button handlers */

  /**
   * start of accordion handlers
   */
  const handleSingleCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    const permId = e.target.value
    // first identify the with module with the permission,  which is selectedModule
    // then find the permission in the selectedPerm
    // if found, remove it
    const isPermInModule = selectedModule?.permissions.find(
      (perm) => perm.id === permId
    )
    const isPermInSelectedPerm = selectedPerm.find((perm) => perm.id === permId)

    if (e.target.checked) {
      if (!isPermInSelectedPerm && isPermInModule) {
        setSelectedPerm([...selectedPerm, isPermInModule])
      }
    }
    if (!e.target.checked) {
      // remove item with permId from selectedPerm
      if (isPermInSelectedPerm) {
        setSelectedPerm([...selectedPerm.filter((perm) => perm.id !== permId)])
      }
    }
  }
  const handleAllCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    const moduleID = e.target.value
    const currentModule = selectedModules.find(
      (module) => module.id === moduleID
    )

    if (e.target.checked) {
      if (currentModule) {
        //check if permission in
        if (selectedPerm.length !== currentModule.permissions.length) {
          setSelectedPerm([...currentModule.permissions])
          // check if module is in selectedPermByModule
          const moduleIndex = selectedPermByModule.findIndex(
            (mod) => mod.id === currentModule.id
          )
          if (moduleIndex > 0) {
            const updatedModule = selectedPermByModule[moduleIndex]
            updatedModule.permissions = currentModule.permissions
            setSelectedPermByModule([
              ...selectedPermByModule.slice(0, moduleIndex),
              updatedModule,
              ...selectedPermByModule.slice(moduleIndex + 1),
            ])
          } else {
            setSelectedPermByModule([
              ...selectedPermByModule,
              {
                ...currentModule,
                permissions: currentModule.permissions,
              },
            ])
          }
        }
      }
    }
    if (!e.target.checked) {
      // remove item with moduleID from selectedPermByModule
      if (selectedPerm.length === currentModule?.permissions.length) {
        setSelectedPerm([])
      }
      // find module in id in selectedByPermModule and remove it
      const moduleIndex = selectedPermByModule.findIndex(
        (mod) => mod.id === moduleID
      )
      // if deleted module is found, remove it
      if (moduleIndex >= 0) {
        setSelectedPermByModule(
          selectedPermByModule.filter((pg) => pg.id !== moduleID)
        )
      }
    }
  }

  const handleDoneButton = (module: IPermissionGroup) => {
    setOpenDropDownAutoComplete(true)

    setShowAccordion(false)
    // find module in id in selectedByPermModule
    // if found, update the permissions
    // if not found, add the module to selectedPermByModule with update selectedPerm as permissions
    const moduleIndex = selectedPermByModule.findIndex(
      (mod) => mod.id === module.id
    )
    if (moduleIndex > 0) {
      const updatedModule = selectedPermByModule[moduleIndex]
      updatedModule.permissions = selectedPerm
      setSelectedPermByModule([
        ...selectedPermByModule.slice(0, moduleIndex),
        updatedModule,
        ...selectedPermByModule.slice(moduleIndex + 1),
      ])
    } else {
      setSelectedPermByModule([
        ...selectedPermByModule,
        {
          ...module,
          permissions: selectedPerm,
        },
      ])
    }
  }
  /** End of Accordion Input handlers */

  const { errors, getFieldProps, touched, handleSubmit } = formik

  return (
    <>
      <Stack
        sx={{
          px: '2%',
        }}
      >
        {setOpen && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography
              variant="subtitle2"
              sx={{
                color: 'primary.main',
              }}
            >
              Create a new role
            </Typography>

            <Button
              endIcon={<CloseRounded />}
              sx={{
                height: '36px',
                fontSize: '12px',
                fontWeight: 400,
                color: '#555C61',
                padding: '0',
                px: '2px',
              }}
              onClick={() => setOpen && setOpen(false)}
            >
              Cancel{' '}
            </Button>
          </Box>
        )}
        <Stack
          sx={{
            height: '100%',
          }}
        >
          {/* textfield */}
          <FormikProvider value={formik}>
            <Form
              style={{
                height: '100%',
              }}
              onSubmit={handleSubmit}
            >
              <Stack
                sx={{
                  display: 'flex',
                  height: '80vh',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                }}
              >
                <Box sx={{}}>
                  <Box>
                    <Typography variant="subtitle3" component={'label'}>
                      Name
                    </Typography>
                    <CustomCreateRoleInput
                      size="small"
                      fullWidth
                      autoComplete="name"
                      type="text"
                      {...getFieldProps('name')}
                      error={Boolean(touched.name && errors.name)}
                    />
                    {touched.name && errors.name && (
                      <Typography variant="label3" color="error">
                        {errors.name}
                      </Typography>
                    )}
                  </Box>
                  <Box>
                    <Typography variant="subtitle3" component={'label'}>
                      Description
                    </Typography>
                    <CustomCreateRoleInput
                      size="small"
                      multiline
                      fullWidth
                      autoComplete="description"
                      type="text"
                      {...getFieldProps('description')}
                      error={Boolean(touched.name && errors.name)}
                    />
                    {touched.description && errors.description && (
                      <Typography variant="label3" color="error">
                        {errors.description}
                      </Typography>
                    )}
                  </Box>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '5px',
                    }}
                  >
                    <Typography variant="subtitle3" component={'label'}>
                      Rights
                    </Typography>
                    <Typography variant="label3">
                      Start typing to search for a module and select rights
                    </Typography>
                    <Autocomplete
                      open={openDropDownAutoComplete}
                      onOpen={() => setOpenDropDownAutoComplete(true)}
                      onClose={() => setOpenDropDownAutoComplete(false)}
                      filterOptions={filterOptions}
                      multiple
                      loading={modulesLoading}
                      sx={{
                        height: 'auto !important',
                      }}
                      size="small"
                      onChange={(event, newValue) => {
                        setSelectedModule(newValue[newValue.length - 1])
                        setSelectedModules(newValue)

                        setShowAccordion(true)
                      }}
                      onFocus={() => {
                        setOpenDropDownAutoComplete(false)
                        setShowAccordion(true)
                      }}
                      onBlur={() => {}}
                      isOptionEqualToValue={(option, value) =>
                        option.id === value.id
                      }
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Box
                            key={index}
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              height: '20px',
                              minWidth: '85px',
                              marginRight: '4px',
                              marginBottom: '4px',
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '2px',
                                borderRadius: '4px',
                                border: '1px solid #D0D5DD',
                                height: '20px',
                                padding: '2px 11px 2px 7px',
                              }}
                            >
                              <IconButton
                                size="small"
                                {...getTagProps({ index })}
                                onClick={() => {
                                  handleDelete(option)
                                }}
                              >
                                <CloseRounded />
                              </IconButton>
                              <Typography>
                                {sentenceCase(option.name)}
                              </Typography>
                              {(!showAccordion ||
                                index !== selectedModules.length - 1) && (
                                <IconButton
                                  size="small"
                                  onClick={() => handleEdit(option, index)}
                                >
                                  <EditRounded />
                                </IconButton>
                              )}
                            </Box>
                          </Box>
                        ))
                      }
                      renderOption={(props, option) => {
                        return (
                          <li {...props} key={option.id}>
                            {sentenceCase(option.name)}
                          </li>
                        )
                      }}
                      value={selectedModules}
                      getOptionKey={(option) => option.id}
                      getOptionLabel={(option) => option.name}
                      renderInput={(params) => {
                        return (
                          <TextField
                            {...params}
                            inputProps={{
                              ...params.inputProps,
                              startAdornment: (
                                <InputAdornment position="start">
                                  <SearchRounded />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )
                      }}
                      options={modules.data.filter(
                        (option) =>
                          !selectedModules.find(
                            (selected) => selected.id === option.id
                          )
                      )}
                    />
                  </Box>
                  {showAccordion && selectedModules.length > 0 && (
                    <Accordion
                      sx={{
                        border: '1px solid #D0D5DD',
                        marginTop: '10px',
                        borderRadius: '4px',
                        overflow: 'auto',
                        maxHeight: '500px',
                      }}
                      elevation={0}
                    >
                      <AccordionSummary>
                        {' '}
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'flex-start',
                            alignItems: 'center',
                            gap: '4px',
                          }}
                        >
                          <CustomCheckBox
                            value={selectedModule?.id}
                            checked={
                              selectedPermByModule.find(
                                (mod) => mod.id === selectedModule?.id
                              ) !== undefined &&
                              selectedPermByModule.find(
                                (mod) => mod.id === selectedModule?.id
                              )?.permissions.length === selectedPerm.length
                            }
                            onChange={handleAllCheck}
                          />
                          <Typography
                            sx={{
                              textAlign: 'center',
                            }}
                          >
                            {sentenceCase(selectedModule?.name)}
                          </Typography>

                          <Typography></Typography>
                        </Box>
                      </AccordionSummary>

                      <AccordionDetails
                        sx={{
                          padding: '0px 33px 0px 42px',
                          margin: 0,
                          display: 'flex',
                          flexDirection: 'column',
                          gap: '4px',
                          justifyItems: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                      >
                        <FormGroup>
                          {selectedModule &&
                            selectedModule.permissions.map((perm) => {
                              return (
                                <FormControlLabel
                                  key={perm.id}
                                  control={
                                    <CustomCheckBox
                                      value={perm.id}
                                      checked={
                                        perm.id ===
                                        selectedPerm.find(
                                          (p) => p.id === perm.id
                                        )?.id
                                      }
                                      onChange={handleSingleCheck}
                                    />
                                  }
                                  label={sentenceCase(perm.name)}
                                />
                              )
                            })}
                        </FormGroup>
                      </AccordionDetails>

                      <AccordionActions
                        sx={{
                          display: 'flex',
                          justifyContent: 'flex-end',
                          alignItems: 'center',
                        }}
                      >
                        <Button
                          type="reset"
                          sx={{
                            color: 'primary.primary3',
                          }}
                          onClick={() => {}}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          sx={{
                            color: 'primary.primary3',
                          }}
                          onClick={() => handleDoneButton(selectedModule)}
                          // disabled={}
                        >
                          Done
                          <DoneRounded
                            sx={{
                              color: 'primary.primary3',
                            }}
                          />
                        </Button>
                      </AccordionActions>
                    </Accordion>
                  )}
                </Box>

                <Stack
                  sx={{
                    flexDirection: 'row',
                    width: '100%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: '20px',
                  }}
                >
                  <Button
                    variant="outlined"
                    sx={{
                      border: '1px solid  #AAADB0',
                      fontWeight: 500,
                      width: '45%',
                    }}
                    onClick={() =>
                      setOpen
                        ? setOpen(false)
                        : dispatch(
                            setDrawer({
                              open: false,
                              drawerChildren: null,
                              header: '',
                            })
                          )
                    }
                  >
                    Cancel
                  </Button>
                  {isLoadingCreateRole ? (
                    <LoadingButton />
                  ) : (
                    <Button
                      variant="outlined"
                      type="submit"
                      disabled={selectedPermByModule.length < 1}
                      sx={{
                        border: '1px solid  #AAADB0',
                        fontWeight: 500,
                        width: '50%',
                        textWrap: 'nowrap',
                      }}
                    >
                      Create a role
                    </Button>
                  )}
                </Stack>
              </Stack>
            </Form>
          </FormikProvider>
        </Stack>
      </Stack>
    </>
  )
}
