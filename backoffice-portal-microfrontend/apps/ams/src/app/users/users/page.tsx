'use client'

import { Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { setUserSearchValue } from '@/store/reducers'
import { getUsers } from '@/store/actions'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

import { UsersList } from '@/app/users/users/UsersList'
import { UsersPageHeader } from '@/app/users/users/pageHeader'
import EmptySearchResult from '@/app/users/users/EmptySearchResult'

import EmptyUsers from './EmptyUsers'

const UserPage = () => {
  const dispatch = useAppDispatch()
  const [selectedUserCount, setSelectedUserCount] = useState<number>(0)
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [searchByField, setSearchByField] = useState<string>('email')
  const { usersResponse, searchUserValue, userFilterValue, isLoadingUsers } =
    useAppSelector((state) => state.users)

  useEffect(() => {
    const params = '?channel=IAM&module=users'
    dispatch(setUserSearchValue(''))
    getUsers(dispatch, {})
  }, [])

  return (
    <Stack
      sx={{
        flexDirection: 'column',
        px: { sm: '0.5%', md: '1%', lg: '1.5%' },
        py: '0.5%',
      }}
    >
      <UsersPageHeader
        selectedUserCount={selectedUserCount}
        selectedIds={selectedIds}
        searchByField={searchByField}
        setSearchByField={setSearchByField}
      />
      {
        <>
          {isLoadingUsers ? (
            <CustomSkeleton
              animation="pulse"
              variant="rectangular"
              width={'100%'}
              height={'60vh'}
            />
          ) : usersResponse?.data.length < 1 &&
            (searchUserValue !== '' ||
              Object.keys(userFilterValue).length > 0) ? (
            <EmptySearchResult />
          ) : usersResponse?.data.length < 1 &&
            (searchUserValue === '' ||
              Object.keys(userFilterValue).length === 0) ? (
            <EmptyUsers />
          ) : (
            <UsersList
              onSelectedCountChange={setSelectedUserCount}
              onExport={setSelectedIds}
              searchByField={searchByField}
            />
          )}
        </>
      }
    </Stack>
  )
}

export default UserPage
