import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  AutocompleteRenderInputParams,
  Avatar,
  Box,
  Button,
  Chip,
  debounce,
  DialogContent,
  DialogTitle,
  Divider,
  Drawer,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  InputLabel,
  List,
  ListItem,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import CloseIcon from '@mui/icons-material/Close'
import React, { useEffect, useMemo, useState } from 'react'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import { sentenceCase } from 'tiny-case'
import { AddRounded } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { IADUserProfile } from '@/store/interfaces'
import { IRole } from '@dtbx/store/interfaces'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  HasAccessToRights,
} from '@dtbx/store/utils'
import {
  createUser,
  getRoles,
  getUserADProfile,
  makeCreateUser,
} from '@/store/actions'
import { Expandmore } from '@dtbx/ui/icons'
import { ReadOnlyTypography } from '@dtbx/ui/components'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { CustomSearchInput } from '@dtbx/ui/components/Input'

import {
  UserRoleCreateFailure,
  UserRoleCreateSuccess,
} from '@/app/roles/CreateRoleResponse'
import { CreateUserRole } from '@/app/users/users/CreateRole'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import Close from '@mui/icons-material/Close'
import { setCreateRoleSuccess } from '@/store/reducers'

export const CreateUser = () => {
  const dispatch = useAppDispatch()
  const [open, setIsOpen] = React.useState(false)
  const [dialogSize, setDialogSize] = useState<string>('30%')
  const [selectedRole, setSelectedRole] = useState<IRole>()
  const {
    isLoadingCreateUser,
    isLoadingADUserDetails,
    isLoadedADUserDetailsFailure,
    isLoadedADUserDetailsSuccess,
    loadedADUserDetails,
  } = useAppSelector((state) => state.users)
  const [isCreateRoleOpen, setIsCreateRoleOpen] = useState(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [expandedGroup, setExpandedGroup] = useState<string>('')
  const handleSearchPermissions = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
  }
  const [phone, setPhone] = useState('')
  const [emailUserInput, setEmailUserInput] = React.useState('')
  const [userADProfile, setUserADProfile] = React.useState<IADUserProfile>({
    mail: '',
    mobilePhone: '',
    givenName: '',
    surname: '',
  })
  const [selectedRoles, setSelectedRoles] = React.useState<IRole[]>([])

  const { rolesList } = useAppSelector((state) => state.roles)

  const roles = rolesList
  const validationSchema = Yup.object({
    firstName: Yup.string().required('First name must not be empty'),
    middleName: Yup.string().optional(),
    lastName: Yup.string().required('Last name must not be empty'),
    email: Yup.string()
      .email()
      .required('Email must not be empty')
      .test('is-dtbafrica', 'Email must end with @dtbafrica.com', (value) => {
        return value?.endsWith('@dtbafrica.com')
      }),
    phoneNumber: Yup.string()
      .required('Phone must not be empty')
      .test('is-valid-phone', 'Invalid phone number', (value) => {
        return matchIsValidTel(value || '')
      }),
    roleIds: Yup.array().required('Role must not be empty'),
  })

  const formik = useFormik({
    initialValues: {
      firstName: userADProfile.givenName || '',
      middleName: '',
      lastName: userADProfile.surname || '',
      email: userADProfile.mail || '',
      phoneNumber: userADProfile.mobilePhone || '',
      roleIds: [] as IRole[],
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      const roles = selectedRoles.map((role) => role.id)
      const newValues = { ...values, roleIds: roles }
      if (HasAccessToRights(['SUPER_CREATE_USERS'])) {
        await createUser(newValues, dispatch)
      } else {
        await makeCreateUser(newValues, dispatch)
      }
      handleClose(null, 'close')
    },
  })
  const { errors, getFieldProps, touched, handleSubmit, resetForm } = formik
  const handleOpen = () => {
    setIsOpen(true)
  }
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    resetForm()
    setIsOpen(false)
  }
  const handleSeeRights = (role: IRole) => {
    setDialogSize('60%')
    setSelectedRole(role)
  }
  const handleCloseRights = () => {
    setDialogSize('30%')
    setSelectedRole(undefined)
  }
  const handlePhoneChange = (value: string) => {
    setPhone(value)
    formik.setFieldTouched('phoneNumber', true, false)
    if (!matchIsValidTel(value)) {
      formik.setFieldError('phoneNumber', 'Invalid phone number')
    }
    formik.setFieldValue('phoneNumber', value)
  }
  //handle create role

  const handleOpenCreateRole = () => {
    setIsCreateRoleOpen(!isCreateRoleOpen)
    dialogSize === '30%' ? setDialogSize('60%') : setDialogSize('30%')
  }
  const { isCreateRoleSuccess, isCreateRoleFailed } = useAppSelector(
    (state) => state.roles
  )
  const previousEmailInputRef = React.useRef('')
  const loadADUser = useMemo(
    () =>
      debounce(() => {
        if (
          emailUserInput !== 'undefined' &&
          emailUserInput !== previousEmailInputRef.current
        ) {
          previousEmailInputRef.current = emailUserInput
          getUserADProfile(emailUserInput, dispatch)
        }
      }, 500),
    [emailUserInput]
  )

  useEffect(() => {
    if (
      emailUserInput &&
      emailUserInput !== userADProfile.mail &&
      emailUserInput !== previousEmailInputRef.current
    ) {
      loadADUser()
    }

    return () => {
      loadADUser.clear()
    }
  }, [loadADUser, emailUserInput, userADProfile.mail])
  const handleDelete = (roleToDelete: IRole) => {
    setSelectedRoles((items) => items.filter((item) => item !== roleToDelete))
  }

  useEffect(() => {
    if (isCreateRoleSuccess || isCreateRoleFailed) {
      setDialogSize('60%')
      setIsCreateRoleOpen(false)
    } else {
      setDialogSize('30%')
    }
  }, [isCreateRoleSuccess, isCreateRoleFailed])

  useEffect(() => {
    if (open) {
      getRoles(dispatch)
    }
  }, [open])
  return (
    <>
      <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USERS}>
        <Button
          onClick={handleOpen}
          startIcon={<AddOutlinedIcon />}
          variant="contained"
          sx={{
            textWrap: 'noWrap',
          }}
        >
          Create new user
        </Button>
      </AccessControlWrapper>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: dialogSize,
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
        slotProps={{
          paper: {
            sx: {
              width: dialogSize,
            },
          },
        }}
      >
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: '2%',
            }}
          >
            <DialogTitle
              sx={{
                fontSize: '22px',
                fontWeight: '500',
              }}
            >
              Create a new staff user
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <DialogContent
          sx={{
            px: '5% !important',
            display: 'flex',
            flexDirection: 'row',
            gap: '5%',
          }}
        >
          <Stack
            sx={{
              width: dialogSize === '30%' ? '100%' : '50%',
            }}
          >
            {/* <Typography variant="body2">
              Please input user’s DTB email to populate their details.
            </Typography> */}

            <FormikProvider value={formik}>
              <Form noValidate onSubmit={handleSubmit}>
                <Autocomplete
                  // freeSolo
                  size="small"
                  options={loadedADUserDetails || []}
                  fullWidth
                  includeInputInList
                  autoComplete
                  loading={
                    isLoadingADUserDetails &&
                    !isLoadedADUserDetailsFailure &&
                    !isLoadedADUserDetailsSuccess
                  }
                  filterSelectedOptions
                  onInputChange={(_, newInput: string) => {
                    setEmailUserInput(newInput)
                  }}
                  getOptionLabel={(option: IADUserProfile) =>
                    option ? option.mail : ''
                  }
                  isOptionEqualToValue={(
                    opt: IADUserProfile,
                    val: IADUserProfile
                  ) => val.mail === opt.mail}
                  {...getFieldProps('email')}
                  renderInput={(params: AutocompleteRenderInputParams) => (
                    <TextField {...params} label="DTB Email" fullWidth />
                  )}
                  value={userADProfile}
                  slotProps={{
                    listbox: {
                      sx: {
                        marginTop: '10px',
                        padding: '10px 13px 18px 13px',
                        '&::-webkit-scrollbar': {
                          width: '5px',
                        },
                        '&::-webkit-scrollbar-track': {
                          backgroundColor: 'lightgray transparent',
                          padding: '0px 4px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: '#D0D5DD',
                          borderRadius: '10px',
                        },
                      },
                    },
                  }}
                  onChange={(
                    event: React.SyntheticEvent<Element, Event>,
                    value: IADUserProfile | IADUserProfile[] | null
                  ) => {
                    if (!Array.isArray(value) && value !== null) {
                      setUserADProfile(value && value)
                      formik.setFieldValue('email', value?.mail)
                      formik.setFieldValue('firstName', value?.givenName)
                      formik.setFieldValue('lastName', value?.surname)

                      handlePhoneChange(
                        '+254 ' + value?.mobilePhone?.slice(1) || ''
                      )
                    }
                  }}
                  renderOption={(props, opt: IADUserProfile) => {
                    return (
                      <ListItem
                        {...props}
                        sx={{
                          border: '1px solid #FAFAFA',
                          borderRadius: '6px',
                          padding: '9px 9px 9px 6px',
                          my: '5px',
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            gap: '10px',
                            justifyContent: 'flex-start',
                            alignItems: 'center',
                          }}
                        >
                          <Avatar>
                            {opt.givenName ? opt.givenName[0] : 'D'}
                            {opt.surname ? opt.surname[0] : 'D'}
                          </Avatar>
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                            }}
                          >
                            <Typography
                              variant="label1"
                              sx={{
                                fontSize: '14px',
                                fontStyle: 'normal',
                                fontWeight: 500,
                                lineHeight: '20px',
                                color: '',
                              }}
                            >
                              {opt.givenName} {opt.surname}
                            </Typography>
                            <Typography
                              variant="label2"
                              sx={{
                                fontSize: '14px',
                                fontStyle: 'normal',
                                fontWeight: 300,
                                lineHeight: '20px',
                                color: '',
                              }}
                            >
                              {opt.mail}
                            </Typography>
                          </Box>
                        </Box>
                      </ListItem>
                    )
                  }}
                />

                <ReadOnlyTypography
                  fullWidth
                  label="First Name"
                  margin="normal"
                  error={Boolean(touched.firstName && errors.firstName)}
                  helperText={touched.firstName && errors.firstName}
                  size="small"
                  {...getFieldProps('firstName')}
                />
                <ReadOnlyTypography
                  fullWidth
                  label="Last Name"
                  margin="normal"
                  error={Boolean(touched.lastName && errors.lastName)}
                  helperText={touched.lastName && errors.lastName}
                  size="small"
                  {...getFieldProps('lastName')}
                />
                <FormControl
                  fullWidth
                  sx={{
                    marginTop: '20px',
                  }}
                  error={Boolean(touched.phoneNumber && errors.phoneNumber)}
                >
                  <Typography>Phone Number</Typography>
                  <MuiTelInput
                    value={phone}
                    name="phoneNumber"
                    defaultCountry="KE"
                    onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                    onChange={handlePhoneChange}
                    size="small"
                  />
                  <FormHelperText>
                    {touched.phoneNumber && errors.phoneNumber}
                  </FormHelperText>
                </FormControl>

                <Typography variant="body1">Role</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    mb: '10px',
                  }}
                >
                  Pick an existing role or create a new one
                </Typography>
                <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ROLES}>
                  <Button
                    onClick={handleOpenCreateRole}
                    endIcon={<AddRounded />}
                    variant="outlined"
                    sx={{
                      width: '40%',
                      height: '34px',
                      borderRadius: '6px',
                      border: '1px solid #AAADB0',
                      boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                      gap: 0,
                    }}
                  >
                    <Typography
                      variant="body3"
                      sx={{
                        textWrap: 'nowrap',
                        color: 'black',
                      }}
                    >
                      Create new role
                    </Typography>
                  </Button>
                </AccessControlWrapper>

                <FormControl
                  variant="outlined"
                  fullWidth
                  sx={{ marginTop: '24px' }}
                  error={Boolean(touched.roleIds && errors.roleIds)}
                >
                  <Autocomplete
                    size="small"
                    disablePortal
                    id="combo-box-demo"
                    options={roles}
                    multiple
                    limitTags={4}
                    disableCloseOnSelect
                    getOptionLabel={(option) => option.name}
                    isOptionEqualToValue={(option, value) =>
                      option.id === value.id
                    }
                    value={selectedRoles}
                    onChange={(event, newValue) => {
                      setSelectedRoles([...newValue])
                      formik.setFieldValue('roleIds', newValue)
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label={'Pick Existing Role'} />
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => {
                        return (
                          <Chip
                            key={index}
                            label={option.name}
                            sx={{
                              minWidth: '30px',
                              height: '16px',
                              border: '1px solid #EAECF0 ',
                              background: '#FFFFFF',
                              borderRadius: '6px',
                            }}
                            deleteIcon={
                              <IconButton
                                {...getTagProps({ index })}
                                size="small"
                              >
                                <Close fontSize="small" />
                              </IconButton>
                            }
                            onDelete={() => {
                              handleDelete(option)
                            }}
                          />
                        )
                      })
                    }
                    renderOption={(props, role, { selected }) => (
                      <ListItem {...props} sx={{}}>
                        <Box
                          sx={{
                            border: '1px solid #AAADB0',
                            width: '100%',
                            height: '30px',
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            borderRadius: '6px',
                            padding: '4px 9px 4px 6px',
                          }}
                        >
                          {' '}
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'row',
                              gap: '5px',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              padding: '0px ',
                            }}
                          >
                            <CustomCheckBox checked={selected} />
                            <Typography>{role.name}</Typography>
                          </Box>
                          <Typography
                            sx={{
                              color: '#555C61',
                              fontSize: '12px',
                              fontStyle: 'normal',
                              fontWeight: 400,
                              lineHeight: '16px',
                              '&:hover': {
                                color: 'blue',
                              },
                            }}
                            onClick={() => {
                              handleSeeRights(role)
                            }}
                          >
                            See rights
                          </Typography>
                        </Box>
                      </ListItem>
                    )}
                  />
                </FormControl>
                <Stack
                  sx={{
                    flexDirection: 'row',
                    marginTop: '30px',
                    gap: '5%',
                  }}
                >
                  <Button
                    variant="outlined"
                    sx={{
                      width: '50%',
                    }}
                    onClick={(e) => handleClose(e, 'close')}
                  >
                    Cancel
                  </Button>
                  {isLoadingCreateUser ? (
                    <LoadingButton />
                  ) : (
                    <Button
                      variant="contained"
                      type="submit"
                      sx={{
                        width: '50%',
                        textWrap: 'nowrap',
                      }}
                      disabled={
                        !matchIsValidTel(phone) ||
                        (errors && Object.keys(errors).length > 0)
                      }
                    >
                      Create User
                    </Button>
                  )}
                </Stack>
              </Form>
            </FormikProvider>
          </Stack>
          {dialogSize === '60%' && (
            <Divider orientation="vertical" variant="fullWidth" />
          )}

          {selectedRole && (
            <Stack
              sx={{
                width: '50%',
                flexDirection: 'column',
              }}
            >
              <Stack
                flexDirection="row"
                sx={{
                  justifyContent: 'space-between',
                  marginLeft: '1%',
                  my: '15px',
                  alignItems: 'center',
                  alignContent: 'center',
                }}
              >
                <Stack
                  sx={{
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                    alignContent: 'center',
                    overflow: 'auto',
                    maxHeight: '80vh',
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      color: '#101828',
                    }}
                  >
                    {selectedRole.name}
                  </Typography>
                  <Chip
                    label={selectedRole.permissions.length + ' rights'}
                    sx={{
                      color: '#636363',
                      borderRadius: '6px',
                      border: '1px solid #EAECF0',
                      background: '#FFFFFF',
                    }}
                  />
                </Stack>
                <Stack
                  sx={{
                    flexDirection: 'row',
                    alignContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Typography variant={'body2'}>Close</Typography>
                  <IconButton aria-label="close" onClick={handleCloseRights}>
                    <CloseIcon fontSize={'small'} />
                  </IconButton>
                </Stack>
              </Stack>
              <CustomSearchInput
                value={searchValue}
                onChange={handleSearchPermissions}
                placeholder="Search"
                sx={{
                  width: '100% !important',
                }}
                startAdornment={
                  <InputAdornment position="start">
                    <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
                  </InputAdornment>
                }
              />
              {selectedRole.permissionsGroup.map((group) => (
                <Accordion
                  key={group.id}
                  expanded={expandedGroup === group.id}
                  onChange={() =>
                    setExpandedGroup(expandedGroup === group.id ? '' : group.id)
                  }
                  sx={{
                    border:
                      expandedGroup === group.id ? '1px solid #D0D5DD' : 'none',
                    borderRadius: '4px',
                    boxShadow: 'none',
                    '&.MuiAccordion-root::before': {
                      height: '0px',
                    },
                    '& .MuiPaper-root.MuiAccordion-root.Mui-expanded': {
                      border: '1px solid #E3E4E4 !important',
                    },
                  }}
                >
                  <AccordionSummary expandIcon={<Expandmore />}>
                    <Stack
                      sx={{
                        flexDirection: 'row',
                        gap: '10px',
                        alignItems: 'center',
                        alignContent: 'center',
                      }}
                    >
                      <Typography variant={'body1'}>
                        {sentenceCase(group.name)}
                      </Typography>
                      <Chip
                        label={`${group.permissions.length} rights`}
                        sx={{
                          background: '#F9FAFB',
                          padding: '2px',
                          borderRadius: '6px',
                          border: '1px solid #EAECF0',
                          fontWeight: 500,
                        }}
                      />
                    </Stack>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List>
                      {group.permissions.map((right, index) => (
                        <ListItem key={right.id}>
                          <Typography>
                            {index + 1}. {right.name}
                          </Typography>
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Stack>
          )}

          {isCreateRoleOpen ? (
            <Stack
              sx={{
                width: '50%',
                flexDirection: 'column',
              }}
            >
              <CreateUserRole setOpen={handleOpenCreateRole} />
            </Stack>
          ) : isCreateRoleSuccess ? (
            <Stack
              sx={{
                width: '50%',
                flexDirection: 'column',
              }}
            >
              <UserRoleCreateSuccess
                handleCloseDrawer={() => dispatch(setCreateRoleSuccess(false))}
              />
            </Stack>
          ) : isCreateRoleFailed ? (
            <Stack
              sx={{
                width: '50%',
                flexDirection: 'column',
              }}
            >
              <UserRoleCreateFailure />
            </Stack>
          ) : null}
        </DialogContent>
      </Drawer>
    </>
  )
}
