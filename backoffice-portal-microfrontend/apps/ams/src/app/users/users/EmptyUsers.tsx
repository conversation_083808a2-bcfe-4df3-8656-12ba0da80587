import { Box, Stack, Typography } from '@mui/material'
import Image from 'next/image'
import React from 'react'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'

import { CreateUser } from '@/app/users/users/CreateUser'

const EmptyUsers = () => {
  return (
    <Box
      sx={{
        width: '100%',
        height: 'auto',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {' '}
      <Box
        sx={{
          width: '884px',
          height: '479px',
          backgroundImage: 'url("/dashboard/background-pattern.svg")',
          backgroundRepeat: 'no-repeat',
          backgroundSize: '480px 480px',
          backgroundPosition: 'center',
          backgroundPositionX: '',
          flexShrink: 0,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            flex: '1 0 0',
            gap: '32px',
            paddingTop: '21.5%',
          }}
        >
          {/* text box */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '20px',
              alignSelf: 'stretch',
            }}
          >
            <Stack
              sx={{
                width: '56px',
                height: '56px',
                borderRadius: '12px',
                padding: '14px',
                justifyContent: 'center',
                alignItems: 'center',
                border: '1px solid #EAECF0',
              }}
            >
              <Image
                src={'/dashboard/icons/search-lg-2-x.svg'}
                alt="search"
                width={28}
                height={28}
              />
            </Stack>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '20px',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  textAlign: 'center',
                  width: '573px',
                }}
              >
                Seems like you haven’t created any users yet.
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  textWrap: 'wrap',
                  textAlign: 'center',
                  width: '360px',
                }}
              >
                You can do this by clicking on the button below to create and
                invite a new user.
              </Typography>
            </Box>
          </Box>
          <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ROLES}>
            <CreateUser />
          </AccessControlWrapper>
        </Box>
      </Box>
    </Box>
  )
}

export default EmptyUsers
