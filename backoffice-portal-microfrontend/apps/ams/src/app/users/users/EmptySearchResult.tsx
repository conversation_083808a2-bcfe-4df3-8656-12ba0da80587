import { Button, Stack, Typography } from '@mui/material'
import Image from 'next/image'
import React from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { setUserFilterValue, setUserSearchValue } from '@/store/reducers'
import { getUsers } from '@/store/actions'

const EmptySearchResult = () => {
  const dispatch = useAppDispatch()
  const { searchUserValue } = useAppSelector((state) => state.users)
  const handleClearFilter = async () => {
    dispatch(setUserSearchValue(''))
    dispatch(setUserFilterValue({}))
    await getUsers(dispatch, {})
  }
  return (
    <Stack
      sx={{
        minHeight: '70vh',
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
      }}
    >
      <Stack
        sx={{
          height: '40vh',
          backgroundImage: 'url(/dashboard/background-pattern.svg)',
          backgroundSize: 'contain',
          backgroundPosition: 'top',
          gap: '45px',
          alignContent: 'center',
          alignItems: 'center',
          pt: '10%',
        }}
      >
        <Stack
          sx={{
            width: '56px',
            height: '56px',
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: '12px',
            border: '1px solid #EAECF0',
          }}
        >
          <Image
            src={'/dashboard/icons/search-lg-2-x.svg'}
            alt="search"
            width={28}
            height={28}
          />
        </Stack>
        <Stack
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
            gap: '32px',
          }}
        >
          {/*header  */}
          <Stack
            sx={{
              justifyContent: 'center',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            <Typography variant="subtitle1">No user found</Typography>

            <Typography
              variant="subtitle3"
              sx={{
                textAlign: 'center',
              }}
            >
              Your{' '}
              {searchUserValue.length > 0
                ? `search "${searchUserValue}"`
                : 'filter criteria'}{' '}
              did not match any users. Please try again
            </Typography>
          </Stack>
          <Stack
            sx={{
              justifyContent: 'center',
              flexDirection: 'row',
            }}
          >
            <Button variant="outlined" onClick={handleClearFilter}>
              Clear search
            </Button>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default EmptySearchResult
