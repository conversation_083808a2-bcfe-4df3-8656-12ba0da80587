import React, { useState } from 'react'
import { Button, <PERSON>u, MenuItem } from '@mui/material'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { IApprovalRequest } from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'

import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'

export const RequestsMoreMenu = ({
  request,
}: {
  request: IApprovalRequest
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const dispatch = useAppDispatch()
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const router = useCustomRouter()
  const handleClose = async () => {
    await ApprovalRequestRouting(request, dispatch, router)
    setAnchorEl(null)
  }
  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: '500',
        }}
        endIcon={<KeyboardArrowDownIcon />}
      >
        Actions
      </Button>
      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <MenuItem onClick={handleClose}>Review Approval Request</MenuItem>
      </Menu>
    </>
  )
}
