import {
  Box,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { IHeadCell } from '@dtbx/store/interfaces'
import { getApprovalRequestTypes, getApprovals } from '@/store/actions'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import { EmptySearchAndFilter } from '@dtbx/ui/components/EmptyPage'

import { RequestsMoreMenu } from '@/app/users/approval-requests/RequestsMoreMenu'
import PageHeader from '@/app/users/approval-requests/pageHeader'

import { RequestChip } from '../../approval-requests/Pending'
import { CustomTableCell } from '@/app/roles/ListRoles'
import { formatTimestamp } from '@dtbx/store/utils'

const userRequestHeaders: IHeadCell[] = [
  {
    label: 'Request type',
    id: 'request_type',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Module',
    id: 'module',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Maker Timestamp',
    id: 'dateCreated',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Status',
    id: 'status',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Maker',
    id: 'maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Action',
    id: 'actions',
    alignCenter: false,
    alignRight: false,
  },
]

const Requests = () => {
  const dispatch = useAppDispatch()
  const {
    isLoadingRequests,
    approvalRequestResponse,
    userApprovalRequestFilters,
  } = useAppSelector((state) => state.approvalRequests)
  const [paginationOptions, setPaginationOptions] = useState({
    page: approvalRequestResponse.pageNumber,
    size: 10,
    totalPages: approvalRequestResponse.totalNumberOfPages,
  })

  /*************************Query params handlers***************************/
  const buildQueryParams = (
    filters: Record<string, any>,
    page: number,
    size: number = 10,
    channel: string = 'DBP'
  ): string => {
    const filterParams = Object.keys(filters)
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(filters[key])}`
      )
      .join('&')
    return `?channel=${channel}&${filterParams || 'module=users'}&page=${page}&size=${size}`
  }
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    const params = buildQueryParams(
      userApprovalRequestFilters,
      newOptions.page,
      newOptions.size
    )
    await getApprovals(dispatch, params)
  }
  /*************************end pagination handlers***************************/
  const approvalRequests = approvalRequestResponse?.data || []
  useEffect(() => {
    getApprovalRequestTypes(dispatch, 'DBP')
    getApprovals(dispatch, `?channel=DBP&module=users&page=1&size=10`)
  }, [])
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        px: '1.8%',
        py: '1.6%',
        gap: '17px',
      }}
    >
      <PageHeader />
      {isLoadingRequests ? (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '100%',
            height: '60vh',
          }}
        />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={userRequestHeaders}
                numSelected={0}
              />
              <TableBody>
                {approvalRequests.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <EmptySearchAndFilter
                        message="No requests match your filters"
                        additionalText="No records found. Please try again with different filters."
                        onClick={() => {
                          getApprovals(
                            dispatch,
                            `?channel=DBP&module=users&page=1&size=10`
                          )
                        }}
                      />
                    </TableCell>
                  </TableRow>
                ) : (
                  approvalRequests.map((row) => (
                    <TableRow key={row.id}>
                      <CustomTableCell
                        sx={{
                          padding: '10px 24px 10px 16px',
                        }}
                      >
                        <Box>
                          <RequestChip
                            label={row.makerCheckerType.name}
                            sx={{ width: 'auto' }}
                          />
                        </Box>
                      </CustomTableCell>
                      <CustomTableCell>
                        {row.makerCheckerType.module}
                      </CustomTableCell>
                      <CustomTableCell>
                        {formatTimestamp(row.dateCreated)}
                      </CustomTableCell>
                      <CustomTableCell>
                        <CustomerStatusChip label={row.status} />
                      </CustomTableCell>
                      <CustomTableCell>{row.maker}</CustomTableCell>
                      <CustomTableCell sx={{ padding: 0 }}>
                        <RequestsMoreMenu request={row} />
                      </CustomTableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {approvalRequestResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: approvalRequestResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </Stack>
  )
}

export default Requests
