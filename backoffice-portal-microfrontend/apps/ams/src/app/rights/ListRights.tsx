'use client'
import React, { useState } from 'react'
import {
  Box,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { IPermission, IRole } from '@dtbx/store/interfaces'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import {
  CustomActiveChip,
  CustomChip,
  CustomErrorChip,
} from '@dtbx/ui/components/Chip'

import { ViewRoles } from '@/app/rights/RightsDialogs'
import { getPermissionsFilter } from '@/store/actions'

type Order = 'asc' | 'desc'
export const CustomTableCell = styled(TableCell)(() => ({
  color: '#667085',
}))

const tableHeadList = [
  { id: 'name', label: 'Right', alignRight: false },
  { id: 'visible', label: 'Is Visible', alignRight: false },
  { id: 'module', label: 'Module', alignRight: false },
  { id: 'roles', label: 'Roles Assigned', alignRight: false },
]
export const ListRights = ({
  page,
  setPage,
}: {
  page: number
  setPage: (page: number) => void
}) => {
  const dispatch = useAppDispatch()
  const [selected, setSelected] = useState<readonly string[]>([])
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')
  const [paginationOptions, setPaginationOptions] = useState({
    page: page,
    size: 10,
    totalPages: 0,
  })

  // Retrieve the data from the store
  const { permissions, rolesList, permissionPageCount, permissionsCount } =
    useAppSelector((state) => state.roles)
  const [filteredPermissions] = useState<IPermission[]>(permissions)
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update local state
    getPermissionsFilter(dispatch, { ...newOptions })
    setPage(newOptions.page)
  }
  /*************************end pagination handlers**************************/

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = permissions.map((n: { id: string }) => n.id)
      setSelected(newSelected)
      return
    }
    setSelected([])
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }
  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property)
  }

  return (
    <Paper
      sx={{
        width: '100%',
        overflow: 'hidden',
        borderRadius: '4px',
        border: '1px solid var(--gray-200, #EAECF0)',
        background: '#FEFEFE',
        boxShadow:
          '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
      }}
    >
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="rights table" size="small">
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={tableHeadList}
            showCheckbox={true}
            rowCount={permissionsCount}
            numSelected={selected.length}
            onRequestSort={handleRequestSort}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {permissions &&
              permissions.map((row: IPermission) => {
                const { id, name, visible, groupName } = row
                const isItemSelected = selected.indexOf(id) !== -1
                return (
                  <TableRow
                    hover
                    key={id}
                    tabIndex={-1}
                    role="checkbox"
                    onClick={(event) => handleSelectOne(event, row.id)}
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        checked={isItemSelected}
                        slotProps={{
                          input: {
                            'aria-labelledby': id,
                          },
                        }}
                      />
                    </TableCell>
                    <TableCell component="th" scope="row" id={id}>
                      {sentenceCase(name)}
                    </TableCell>
                    <CustomTableCell>
                      {visible ? (
                        <CustomActiveChip label="Yes" />
                      ) : (
                        <CustomErrorChip label="No" />
                      )}
                    </CustomTableCell>

                    <CustomTableCell>{groupName}</CustomTableCell>

                    <CustomTableCell>
                      <Box
                        sx={{
                          display: 'flex',
                          gap: '3px',
                          flexWrap: 'wrap',
                        }}
                      >
                        {rolesList.slice(0, 2).map((role: IRole) => (
                          <CustomChip key={role.id} label={role.name} />
                        ))}
                        {rolesList.length > 2 && (
                          <ViewRoles roles={rolesList} permission={name} />
                        )}
                      </Box>
                    </CustomTableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>
      {permissionPageCount > 0 && (
        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: permissionPageCount,
          }}
          handlePagination={handlePagination}
        />
      )}
    </Paper>
  )
}
