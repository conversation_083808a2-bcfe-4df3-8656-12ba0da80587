import { Divider, Stack, Typography } from '@mui/material'
import React from 'react'
import { RightsIcon } from '@dtbx/ui/icons'

const layout = (props: { children: string }) => {
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <RightsIcon width="28" height="26" />
        <Typography variant="h6">Rights Management</Typography>
      </Stack>
      <Divider />
      {props.children}
    </Stack>
  )
}

export default layout
