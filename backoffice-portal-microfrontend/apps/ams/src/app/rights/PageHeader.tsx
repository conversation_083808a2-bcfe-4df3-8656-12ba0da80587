'use client'
import { Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { IFilter } from '@dtbx/store/interfaces'
import { getPermissionsGroup } from '@/store/actions'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'

export const RightsPageHeader = ({
  search,
  filter,
}: {
  search: (value: string) => void
  filter: (value: Record<string, string | string[]>) => void
}) => {
  const dispatch = useAppDispatch()
  const [searchValue, setSearchValue] = useState<string>('')
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    search(e.target.value)
  }
  const { permissionGroup } = useAppSelector((state) => state.roles)
  const handleFilterChange = (filters: Record<string, string | string[]>) => {
    filter(filters)
  }

  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const filters: IFilter[] = [
    {
      filterName: 'Is Visible',
      options: [
        { key: 'yes', value: 'yes', label: 'Yes' },
        { key: 'no', value: 'no', label: 'No' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'Module',
      options: permissionGroup.data
        ? [
            ...permissionGroup?.data.map((module) => {
              return {
                key: module.name,
                value: module.name,
                label: sentenceCase(module.name),
              }
            }),
          ]
        : [],
      type: 'dropdown/single',
    },
  ]
  useEffect(() => {
    handleFilterChange({})
  }, [])
  return (
    <Stack
      sx={{
        justifyContent: 'space-between',
        my: '17px',
      }}
    >
      <CustomFilterBox
        openFilter={openFilter}
        setOpenFilter={setOpenFilter}
        searchValue={searchValue}
        handleSearch={handleSearch}
        filters={filters}
        onFilterChange={handleFilterChange}
      />
    </Stack>
  )
}
