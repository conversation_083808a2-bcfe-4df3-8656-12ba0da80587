'use client'
import { useEffect, useState } from 'react'
import { Divider, Stack, Typography } from '@mui/material'
import { RequestsApprovalIcon } from '@dtbx/ui/icons'
import { useAppDispatch } from '@/store'
import { setSelectedApprovalRequest } from '@/store/reducers'
import { IApprovalRequest } from '@/store/interfaces'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'

import Pending from './Pending'
import Resolved from './All'

const HomePage = () => {
  const [value, setValue] = useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  const dispatch = useAppDispatch()
  useEffect(() => {
    dispatch(setSelectedApprovalRequest({} as IApprovalRequest))
  }, [])
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <RequestsApprovalIcon width="28" height="26" />
        <Typography variant="h6">Approval Requests</Typography>
      </Stack>
      <AntTabs
        sx={{
          marginLeft: '1%',
        }}
        onChange={handleChange}
        value={value}
      >
        <AntTab label={`Pending Requests`} />
        <AntTab label={`All Requests`} />
      </AntTabs>
      <Divider />
      <TabPanel value={value} index={0}>
        <Pending />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <Resolved />
      </TabPanel>
    </Stack>
  )
}

export default HomePage
