import { CloseRounded, FilterListRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Chip,
  ChipProps,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import { sentenceCase } from 'tiny-case'
import React, { useCallback, useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { IApprovalRequest } from '@/store/interfaces'
import { IFilter, IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { getApprovalRequestTypes, getApprovals } from '@/store/actions'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import { EmptySearchAndFilter } from '@dtbx/ui/components/EmptyPage'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import {
  DateRangePicker,
  DropDownMenu,
} from '@dtbx/ui/components/DropDownMenus'

import { PendingRequestMoreMenu } from '../../approval-requests/Pending/MoreMenu'
import RequestSearch from '../RequestSearch'
import { CustomDateRangePicker } from '@/app/approval-requests/CustomFilterBox'

export const RequestChip = styled(Chip)<ChipProps>(() => ({
  padding: '2px 8px',
  borderRadius: '16px',
  background: '#F3F5F5',
  height: '24px',
  width: 'auto',
  minWidth: '0',
}))

export const CustomTableCell = styled(TableCell)(() => ({
  padding: '16px 24px',
}))

const pendingRequest: IHeadCell[] = [
  {
    id: 'requestType',
    label: 'Request Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'module',
    label: 'Module',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker_timestamp',
    label: 'Maker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'action',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]
type ExtendedKeys =
  | keyof IApprovalRequest
  | keyof IApprovalRequest['makerCheckerType']
export const requestSearchByItems: {
  label: string
  value: Array<ExtendedKeys>
}[] = [{ label: 'Module', value: ['module'] }]

const Pending = () => {
  const dispatch = useAppDispatch()

  const [page, setPage] = useState(1)
  const [openFilterBar, setOpeFilterBar] = useState<boolean>(false)

  // filters

  const [dateRange, setDateRange] = useState<string[] | null>(null)
  const [status, setStatus] = useState<string | null>('')
  const [requestType, setRequestType] = useState<string | null>('')
  const [makerName, setMakerName] = useState<string>('')
  const {
    approvalRequestResponse,
    isLoadingRequests,
    approvalRequests,
    requestTypes,
    isRequestTypesLoading,
    isRequestTypesSuccess,
  } = useAppSelector((state) => state.approvalRequests)

  const { search } = useAppSelector((state) => state.customers)

  const makerNameFilter = () => {
    return search?.searchBy[0] === 'firstName'
      ? 'makerFirstName'
      : search?.searchBy[0] === 'lastName'
        ? 'makerLastName'
        : ''
  }

  const [paginationOptions, setPaginationOptions] = useState({
    page: approvalRequestResponse.pageNumber,
    size: 10,
    totalPages: approvalRequestResponse.totalNumberOfPages,
  })
  /*************************Query params handlers***************************/
  const buildQueryParams = (): string => {
    const params = new URLSearchParams()
    params.append('status', 'PENDING')
    params.append('channel', 'IAM')
    params.append('size', String(paginationOptions.size))
    params.append('page', String(page))
    if (makerName) params.append(makerNameFilter(), makerName)
    if (requestType) params.append('requestType', requestType)
    if (dateRange) {
      params.append('createDateFrom', dateRange[0])
      params.append('createDateTo', dateRange[1])
    }

    return `?${params.toString()}`
  }

  const fetchPendingApprovals = useCallback(async () => {
    const queryParams = buildQueryParams()
    await getApprovals(dispatch, queryParams)
  }, [
    paginationOptions,
    page,
    status,
    makerName,
    requestType,
    dateRange,
    search,
  ])

  /*************************start pagination handlers***************************/
  const handlePagination = (newOptions: PaginationOptions) => {
    const newPage = newOptions.page
    setPage(newPage)
    setPaginationOptions({ ...paginationOptions, page: newPage })
    fetchPendingApprovals()
  }

  useEffect(() => {
    fetchPendingApprovals()
  }, [requestType, dateRange, page, status, makerName])
  /*************************end pagination handlers**************************/

  /************************* Filter Handlers ***************************/

  const handleDateRangeFilterApply = (_filter: string, date: string[]) => {
    setDateRange(date)
  }

  const handleRequestTypeFilter = (requestTypeId: string) => {
    setRequestType(requestTypeId)
    fetchPendingApprovals()
  }
  const filter: IFilter = {
    filterName: 'Date created',
    options: [
      {
        label: 'Date created',
        value: 'dateCreated',
        key: '',
      },
    ],
    type: 'date',
  }
  return (
    <Box
      sx={{
        padding: '2% 2% 0 2.5%',
        display: 'flex',
        flexDirection: 'column',
        gap: '22px',
      }}
    >
      {/* search and filter */}

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          gap: '16px',
          flexDirection: 'column',
          width: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: '16px',
          }}
        >
          <RequestSearch
            searchByItems={[
              {
                label: 'Maker First Name',
                value: 'firstName',
              },
              {
                label: 'Maker Last Name',
                value: 'lastName',
              },
            ]}
            onSetSearch={(makerName: string) => {
              setMakerName(makerName)
              fetchPendingApprovals()
            }}
          />
          <Button
            sx={{
              display: 'flex',
              width: '131px',
              padding: '8px 42px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              height: '42px',
              borderRadius: '4px',
              border: '1.5px solid #D0D5DD',
              background: '#FFF',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            variant="outlined"
            startIcon={<FilterListRounded />}
            onClick={() => setOpeFilterBar(!openFilterBar)}
          >
            Filter
          </Button>
        </Box>
        {openFilterBar && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'flex-start',
              gap: '16px',
            }}
          >
            <Button
              onClick={async () => {
                setOpeFilterBar(false)
                setPage(1)
                setMakerName('')
                setDateRange(null)
                await getApprovals(
                  dispatch,
                  `?status=${'PENDING'}&channel=${'IAM'}&size=${10}&page=${page}`
                )
              }}
              sx={{
                minWidth: '131px',
                height: '40px',
                gap: '0px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
              endIcon={
                <Typography
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  <CloseRounded />
                </Typography>
              }
            >
              <Typography>Clear</Typography>
            </Button>
            <CustomDateRangePicker
              filter={filter}
              handleFilterChangeAction={handleDateRangeFilterApply}
              selectedDates={dateRange as string[]}
            />
            <DropDownMenu
              menuItems={
                isRequestTypesSuccess
                  ? [
                      ...requestTypes.map((item) => {
                        return { label: item.name, id: item.id }
                      }),
                    ]
                  : []
              }
              loading={isRequestTypesLoading}
              onSelect={handleRequestTypeFilter}
              buttonText={'Request type'}
              onButtonClick={(setOpen) => {
                setOpen((prev) => !prev)
                getApprovalRequestTypes(dispatch, 'IAM')
              }}
            />
          </Box>
        )}
      </Box>
      {/*<ReviewRequest />*/}
      {isLoadingRequests ? (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '100%',
            height: '60vh',
          }}
        />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={pendingRequest}
                numSelected={0}
              />
              <TableBody>
                {approvalRequests.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <EmptySearchAndFilter
                        message="No requests match your filters"
                        additionalText={`No records found. Please try again with different filters.`}
                        onClick={() => {
                          setPage(1)
                          setMakerName('')
                          setDateRange(null)
                          getApprovals(
                            dispatch,
                            `?status=${'PENDING'}&channel=${'DBP'}&size=${10}&page=${page}`
                          )
                        }}
                      />
                    </TableCell>
                  </TableRow>
                ) : (
                  approvalRequests.map((row, index) => (
                    <TableRow key={index || row.id}>
                      <CustomTableCell sx={{ padding: '10px 24px 10px 16px' }}>
                        <Box>
                          <RequestChip
                            label={sentenceCase(row.makerCheckerType.name)}
                            sx={{ width: 'auto' }}
                          />
                        </Box>
                      </CustomTableCell>
                      <CustomTableCell>
                        {sentenceCase(row.makerCheckerType.module)}
                      </CustomTableCell>
                      <CustomTableCell>{row.maker}</CustomTableCell>
                      <CustomTableCell>
                        {dayjs(row.dateCreated).format('MMMM D, YYYY hh:mm A')}
                      </CustomTableCell>
                      <CustomTableCell sx={{ padding: 0 }}>
                        <PendingRequestMoreMenu request={row} />
                      </CustomTableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {approvalRequestResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: approvalRequestResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </Box>
  )
}

export default Pending
