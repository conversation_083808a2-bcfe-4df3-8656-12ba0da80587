import { CloseRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  IconButton,
  MenuItem,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { formatTimestamp } from '@dtbx/store/utils'
import { RequestsApprovalIcon } from '@dtbx/ui/icons'
import { IApprovalRequest } from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'

import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'

const ResolvedRequestDetails = ({ request }: { request: IApprovalRequest }) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState<boolean>(false)

  const handleClose = (
    e: React.MouseEvent<HTMLButtonElement> | null,
    action: string
  ) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const router = useCustomRouter()

  const handleGoToModule = async () => {
    await ApprovalRequestRouting(request, dispatch, router)
    handleClose(null, 'close')
  }

  return (
    <>
      <MenuItem onClick={() => setOpen(!open)}>
        <Typography>See request summary</Typography>
      </MenuItem>
      <Dialog open={open}>
        <DialogContent
          sx={{
            padding: '12px 20px 7px 20px',
            width: '430px',
            display: 'flex',
            flexDirection: 'column',
            gap: '9px',
          }}
        >
          <Box>
            {/*header icons */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <RequestsApprovalIcon />
              <IconButton onClick={() => handleClose(null, 'close')}>
                <CloseRounded />
              </IconButton>
            </Box>
            <Typography
              variant="subtitle1"
              sx={{
                fontSize: '18px',
              }}
            >
              Approval request details
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            <TextField
              fullWidth
              label="Approval request type"
              sx={{}}
              value={
                request?.makerCheckerType.type
                  ? sentenceCase(request?.makerCheckerType.type)
                  : 'No type'
              }
            />
            <TextField
              fullWidth
              label="Module"
              sx={{
                width: 'auto',
              }}
              value={request?.makerCheckerType.module}
            />
            <TextField fullWidth label="Maker" sx={{}} value={request?.maker} />
            <TextField
              fullWidth
              label="Maker timestamp"
              sx={{}}
              value={formatTimestamp(request?.dateCreated)}
            />
            <TextField
              fullWidth
              label="Maker comment"
              sx={{}}
              value={request?.makerComments || 'No comment'}
            />
            <TextField
              fullWidth
              label="Checker"
              sx={{}}
              value={request?.checker}
            />
            <TextField
              fullWidth
              label="Checker timestamp"
              sx={{}}
              value={formatTimestamp(request?.dateModified)}
            />
            <TextField
              fullWidth
              label="Checker comment"
              sx={{}}
              value={request?.checkerComments || 'No comment'}
            />
          </Box>
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              gap: '13px',
              marginBottom: '12px',
            }}
          >
            <Button
              variant="outlined"
              sx={{
                height: '34px',
                width: '184px',
              }}
              onClick={(e) => handleClose(e, 'close')}
            >
              Back
            </Button>
            <Button
              variant="contained"
              sx={{
                height: '34px',
                width: '184px',
                textWrap: 'nowrap',
              }}
              onClick={handleGoToModule}
            >
              Go to module
            </Button>
          </Box>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ResolvedRequestDetails
