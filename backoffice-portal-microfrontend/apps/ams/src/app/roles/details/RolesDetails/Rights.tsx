import {
  Button,
  Chip,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { useAppSelector as globalAppSelector } from '@dtbx/store'
import {
  IHeadCell,
  IPermission,
  IRole,
} from '@dtbx/store/interfaces'
import { IApprovalPermission } from '@/store/interfaces'
import { Expandmore } from '@dtbx/ui/icons'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomErrorChip, CustomSuccessChip } from '@dtbx/ui/components/Chip'

const headCellItems: IHeadCell[] = [
  {
    label: 'Right',
    id: 'right',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'is Visible',
    id: 'isVisible',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Module',
    id: 'module',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Actions',
    id: 'actions',
    alignCenter: false,
    alignRight: false,
  },
]
export const RoleRightsView = ({ role }: { role: IRole }) => {
  const dispatch = useAppDispatch()
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value
    setSearchValue(search)
    setLoading(true)
    setPaginationOptions({...paginationOptions, page: 1 }) // Update local pagination state
    setTimeout(() => {
      setLoading(false)
    }, 10)
  }
  const request = useAppSelector(
    (state) => state?.approvalRequests?.selectedApprovalRequest
  )
  const navigation = globalAppSelector((state) => state?.navigation)
  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })
  const startIndex = (paginationOptions?.page - 1) * 10
  /*************************start pagination handlers***************************/
  const handlePagination = (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update local pagination state
  }
  /*************************end pagination handlers***************************/
  const filterResults = (permissions: IPermission[]) => {
    return permissions.filter((permission) => {
      if (permission.name.includes(searchValue)) {
        return permission
      }
    })
  }
  const approvalPermissions = () => {
    const newPermissions: IPermission[] = []
    const permissions = request?.diff
      ? request?.diff?.filter((perm) => perm.field === 'permissions')
      : []
    const permArray: IApprovalPermission[] = permissions.length
      ? (permissions[0]?.newValue as IApprovalPermission[])
      : []
    if (permArray && permArray.length > 0) {
      permArray.forEach((permission: IApprovalPermission) => {
        newPermissions.push({
          id: permission.id,
          name: permission.name,
          description: '',
          visible: permission.visible,
          groupName: permission.module.moduleName,
          module: permission.module.moduleName,
        })
      })
    }
    if (
      role &&
      role.permissions &&
      navigation.switchToRoleDetails.type === 'view'
    ) {
      return filterResults(role.permissions)
    }
    return filterResults(newPermissions)
  }

  return (
    <Stack
      sx={{
        display: 'flex',
        py: '1%',
        flexDirection: 'column',
        gap: '2vh',
      }}
    >
      {/* TODO: Add filter */}
      <CustomFilterBox
        openFilter={openFilter}
        setOpenFilter={setOpenFilter}
        searchValue={searchValue}
        handleSearch={handleSearch}
        filters={[]}
        onFilterChange={() => {}}
      />
      {/* rights table  */}
      <Paper
        elevation={0}
        sx={{
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          borderRadius: '4px',
          border: '1px solid #EAECF0',
          // background: '#FFFFFF',
        }}
      >
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
          }}
        >
          <Table sx={{ minWidth: 450 }}>
            <CustomTableHeader
              order={'desc'}
              showCheckbox={false}
              orderBy={''}
              rowCount={0}
              headLabel={headCellItems}
              numSelected={0}
            />
            <TableBody>
              {approvalPermissions()
                .slice(startIndex, startIndex + 10)
                .map((right: IPermission) => (
                  <TableRow
                    key={right.id}
                    sx={{
                      '&:last-child td, &:last-child th': { border: 0 },
                    }}
                  >
                    <TableCell
                      sx={{
                        width: '25%',
                      }}
                    >
                      <Chip label={sentenceCase(right.name)} />
                    </TableCell>
                    <TableCell
                      sx={{
                        width: '25%',
                      }}
                    >
                      {right.visible ? (
                        <CustomSuccessChip label={'Yes'} />
                      ) : (
                        <CustomErrorChip label={'No'} />
                      )}
                    </TableCell>

                    <TableCell
                      sx={{
                        width: '25%',
                      }}
                    >
                      {sentenceCase(right.groupName)}
                    </TableCell>
                    <TableCell
                      sx={{
                        width: '25%',
                      }}
                    >
                      <Button
                        variant={'outlined'}
                        sx={{
                          border: '1px solid #D0D5DD',
                          height: '34px',
                          gap: '0px',
                          width: '107px',
                          padding: '8px 28px',
                        }}
                        endIcon={<Expandmore />}
                      >
                        <Typography>Actions</Typography>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        {Math.ceil(approvalPermissions()?.length / 10) > 0 && !loading && (
          <CustomPagination
            options={{
              ...paginationOptions,
              totalPages: Math.ceil(approvalPermissions()?.length / 10),
            }}
            handlePagination={handlePagination}
          />
        )}
      </Paper>
    </Stack>
  )
}
