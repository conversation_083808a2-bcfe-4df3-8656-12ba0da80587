import {
  <PERSON>,
  But<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON><PERSON>le,
  <PERSON>er,
  IconButton,
  TextField,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  formatTimestamp,
  handleDiff,
} from '@dtbx/store/utils'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { CustomDialog } from '@dtbx/ui/components/Dialogs'

import { CheckerRequestsApiHandler } from '@/app/approval-requests/CheckerRequestsApiHandler'

export const ApprovalRequestDetails = () => {
  const [open, setOpen] = useState<boolean>(false)
  const [comments, setComments] = useState<string>('')
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { isLoadingUpdateRole, isLoadingDeleteRole } = useAppSelector(
    (state) => state.roles
  )
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const handleReject = async () => {
    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `REJECT_${selectedApprovalRequest.makerCheckerType.type}`,
      'Request rejected successfully'
    )
    setOpen(false)
  }
  const handleApprove = async () => {
    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `ACCEPT_${selectedApprovalRequest.makerCheckerType.type}`,
      'Request Approved successfully'
    )
    setOpen(false)
  }
  return (
    <>
      <Button
        variant="outlined"
        sx={{
          border: '1px solid #D0D5DD',
          height: '40px',
        }}
        onClick={() => setOpen(true)}
      >
        View Approval Request Details
      </Button>

      <Drawer
        open={open}
        onClose={handleClose}
        anchor={'right'}
        PaperProps={{
          sx: {
            width: '30%',
          },
        }}
        // ModalProps={{
        //   BackdropProps: {
        //     onClick: (event) => {
        //       event.stopPropagation()
        //     },
        //   },
        // }}
      >
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <DialogTitle
            sx={{
              fontSize: '18px',
              fontWeight: '700',
            }}
          >
            Approval Request Details
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <DialogContent
          sx={{
            px: '5% !important',
          }}
        >
          <TextField
            fullWidth
            label="Approval Request Type"
            margin={'normal'}
            value={selectedApprovalRequest?.makerCheckerType?.type}
          />
          {selectedApprovalRequest?.makerCheckerType?.type !==
            'ACTIVATE_GROUPS' && (
            <TextField
              fullWidth
              multiline
              value={handleDiff(selectedApprovalRequest.diff)}
              label="Changes Made"
              margin={'normal'}
            />
          )}
          <TextField
            fullWidth
            label={'Maker'}
            margin={'normal'}
            value={selectedApprovalRequest.maker}
          />
          <TextField
            fullWidth
            label={'Maker Timestamp'}
            margin={'normal'}
            value={formatTimestamp(selectedApprovalRequest.dateCreated)}
          />
          <TextField
            fullWidth
            label={'Maker Comment'}
            margin={'normal'}
            value={selectedApprovalRequest.makerComments}
          />
          <TextField
            multiline
            rows={3}
            fullWidth
            value={comments}
            placeholder={'Write your comments here'}
            label={'Checker Comments'}
            margin={'normal'}
            onChange={(e) => setComments(e.target.value)}
          />
        </DialogContent>
        {selectedApprovalRequest.status === 'PENDING' && (
          <DialogActions
            sx={{
              px: '5% !important',
            }}
          >
            {isLoadingUpdateRole || isLoadingDeleteRole ? (
              <LoadingButton />
            ) : (
              <AccessControlWrapper
                rights={ACCESS_CONTROLS.REJECT_APPROVALREQUEST_ROLES}
                makerId={selectedApprovalRequest?.maker}
              >
                <Button
                  fullWidth
                  type="button"
                  variant="outlined"
                  onClick={handleReject}
                  sx={{
                    background: '#E3E4E4',
                    border: '1px solid #AAADB0',
                  }}
                >
                  Reject
                </Button>
              </AccessControlWrapper>
            )}
            {isLoadingUpdateRole || isLoadingDeleteRole ? (
              <LoadingButton />
            ) : (
              <AccessControlWrapper
                rights={ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES}
                makerId={selectedApprovalRequest?.maker}
              >
                <Button
                  fullWidth
                  onClick={handleApprove}
                  type="submit"
                  variant="contained"
                >
                  Approve
                </Button>
              </AccessControlWrapper>
            )}
          </DialogActions>
        )}
      </Drawer>
    </>
  )
}
