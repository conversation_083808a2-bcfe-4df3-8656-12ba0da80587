import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Chip,
  createFilterO<PERSON>s,
  Drawer,
  FormControlLabel,
  FormGroup,
  IconButton,
  InputAdornment,
  ListItem,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import React, { useEffect, useState } from 'react'
import { CloseRounded, DoneRounded, SearchRounded } from '@mui/icons-material'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'
import { sentenceCase } from 'tiny-case'
import { IPermission, IPermissionGroup, IRole } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { HasAccessToRights } from '@dtbx/store/utils'
import { getPermissionsGroup, makeUpdateRole, updateRole } from '@/store/actions'
import { setDrawer } from '@dtbx/store/reducers'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { LoadingButton } from '@dtbx/ui/components'



const EditRole = ({ role }: { role: IRole }) => {
  const filterOptions = createFilterOptions({
    matchFrom: 'start',
    stringify: (option: IPermissionGroup) => option.name,
    ignoreCase: true,
  })
  const dispatch = useAppDispatch()
  const [isOpen, setIsOpen] = useState(false)
  const modules = useAppSelector((state) => state.roles.permissionGroup)
  const handleOpen = (event: React.MouseEvent) => {
    event.stopPropagation()
    setIsOpen(true)
  }
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setIsOpen(false)
  }
  const { isLoadingUpdateRole } = useAppSelector((state) => state.roles)

  const validationSchema = Yup.object({
    name: Yup.string().required('Role name is required'),
    description: Yup.string().required('Role description is required'),
    permissions: Yup.array(),
  })

  const formik = useFormik({
    initialValues: {
      name: role.name || '',
      description: role.description || '',
      permissions: [...role.permissions.map((perm) => perm.id)],
    },
    onSubmit: async (values) => {
      const data = {
        name: values.name,
        description: values.description,
        permissions: [
          ...selectedGroups
            .map((group) => group.permissions.map((right) => right.id))
            .flat(),
        ],
        comments: 'Role update',
      }
      if (HasAccessToRights(['SUPER_UPDATE_GROUPS'])) {
        await updateRole(role.id, data, dispatch)
      } else if (HasAccessToRights(['MAKE_UPDATE_GROUPS'])) {
        await makeUpdateRole(role.id, data, dispatch)
      }
      dispatch(setDrawer({ open: false, drawerChildren: null, header: '' }))
    },
    validationSchema: validationSchema,
  })
  //optimized for performance
  //accordion state
  const [showAccordion, setShowAccordion] = useState(false)
  const [openDropDownAutoComplete, setOpenDropDownAutoComplete] =
    useState<boolean>(false)
  //end of accordion state
  //selected groups
  const [selectedGroups, setSelectedGroups] = useState<IPermissionGroup[]>(
    role.permissionsGroup
  )
  const [selectedModule, setSelectedModule] = useState<IPermissionGroup>()

  //end of optimization
  const selectedPermissionsCountByModule = (moduleID: string) => {
    const group = selectedGroups.find((module) => module.id === moduleID)

    return group?.permissions.length || 0
  }
  /** Start of select Chip handlers */
  const handleEdit = (option: IPermissionGroup) => {
    setSelectedModule(modules.data.find((module) => module.id === option.id))
    setShowAccordion(true)
  }

  const handleDelete = (option: IPermissionGroup) => {
    if (selectedModule?.id === option.id) {
      setShowAccordion(false)
      setSelectedModule(undefined)
    }
    setSelectedGroups(selectedGroups.filter((group) => group.id !== option.id))
  }
  const handleSingleCheck = (
    e: React.ChangeEvent<HTMLInputElement>,
    permission: IPermission
  ) => {
    const permId = e.target.value
    if (selectedModule) {
      const isInSelectedGroups = selectedGroups.find(
        (group) => group.id === selectedModule.id
      )
      let newGroups = [...selectedGroups]
      if (isInSelectedGroups) {
        let newModule = { ...isInSelectedGroups }
        if (e.target.checked) {
          newModule = {
            ...isInSelectedGroups,
            permissions: [...isInSelectedGroups.permissions, permission],
          }
          newGroups = newGroups.map((group) => {
            if (group.id === isInSelectedGroups.id) {
              return { ...group, permissions: newModule.permissions }
            } else {
              return group
            }
          })
          setSelectedGroups(newGroups)
        } else {
          newModule.permissions = newModule.permissions.filter(
            (perm) => perm.id !== permId
          )
          newGroups = newGroups.map((group) => {
            if (group.id === isInSelectedGroups.id) {
              return { ...group, permissions: newModule.permissions }
            } else {
              return group
            }
          })
          setSelectedGroups(newGroups)
        }
      } else {
        if (e.target.checked) {
          selectedModule.permissions = [permission]
          newGroups.push(selectedModule)
          setSelectedGroups(newGroups)
        } else {
          console.log("EXCEPTION: Can't remove permission from module")
        }
      }
    }
  }
  const handleAllCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    const moduleID = e.target.value
    const selectedMod = modules.data.find((module) => module.id === moduleID)

    if (!selectedMod) return
    const newGroups = [...selectedGroups]
    if (!selectedGroups.find((group) => group.id === selectedMod.id)) {
      if (e.target.checked) {
        newGroups.push(selectedMod)
        setSelectedGroups([...newGroups])
      } else {
        setSelectedGroups(
          newGroups.filter((group) => group.id !== selectedMod.id)
        )
      }
    } else {
      newGroups.map((group) => {
        if (group.id === selectedMod.id) {
          if (e.target.checked) {
            group.permissions = selectedMod.permissions
          } else {
            group.permissions = []
          }
        }
      })
      setSelectedGroups(newGroups)
    }
  }
  const handleAutoCompleteSelect = (newValue: IPermissionGroup[]) => {
    newValue.map((group) => {
      if (!selectedGroups.find((selected) => selected.id === group.id)) {
        setSelectedModule(group)
        const newGroup = { ...group }
        newGroup.permissions = []
        setSelectedGroups([...selectedGroups, newGroup])
      }
    })
  }

  const handleCancelModuleEditSelection = () => {
    setShowAccordion(false)
    dispatch(
      setDrawer({
        open: false,
        header: '',
        drawerChildren: null,
      })
    )
  }
  const { errors, getFieldProps, touched, handleSubmit } = formik

  useEffect(() => {
    getPermissionsGroup(dispatch, {page: 1, size: 100})
  }, [])
  return (
    <>
      <MenuItem onClick={(e) => handleOpen(e)}>
        <Typography variant="label1">Edit Details</Typography>
      </MenuItem>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '35%',
          },
        }}
        variant={'persistent'}
        open={isOpen}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '30%',
          },
        }}
        ModalProps={{
          BackdropProps: {
            onClick: (event) => {
              event.stopPropagation()
            },
          },
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            px: '4%',
            py: '2%',
            borderBottom: '1px solid lightgray',
            backgroundColor: '#F9FAFB',
          }}
        >
          {/* DrawerHeader */}
          <Stack>
            <Typography variant="subtitle1">Edit Role</Typography>
          </Stack>
          <IconButton
            sx={{
              border: '1px solid #CBD5E1',
              backgroundColor: '#F1F5F9',
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
            onClick={() => handleClose(null, 'close')}
          >
            <CloseRounded
              sx={{
                fontSize: '20px',
              }}
            />
          </IconButton>
        </Stack>
        <Stack
          sx={{
            px: '4%',
          }}
        >
          <FormikProvider value={formik}>
            <Form onSubmit={handleSubmit}>
              <Stack
                sx={{
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  height: '100vh',
                }}
              >
                <Stack
                  sx={{
                    width: '100%',
                    height: '80vh',
                  }}
                >
                  <TextField
                    fullWidth
                    autoComplete="name"
                    type="text"
                    label="Name"
                    margin={'normal'}
                    {...getFieldProps('name')}
                    error={Boolean(touched.name && errors.name)}
                    helperText={touched.name && errors.name}
                  />
                  <TextField
                    multiline
                    fullWidth
                    size="small"
                    autoComplete="description"
                    type="text"
                    margin={'normal'}
                    label={'Description'}
                    {...getFieldProps('description')}
                    error={Boolean(touched.description && errors.description)}
                    helperText={touched.description && errors.description}
                  />
                  <Stack
                    sx={{
                      flexDirection: 'column',
                      gap: '8px',
                    }}
                  >
                    <Stack
                      sx={{
                        flexDirection: 'column',
                        gap: '5px',
                      }}
                    >
                      <Typography variant="label1">Rights</Typography>
                      <Typography variant="label2">
                        Click on a module tag to edit the rights
                      </Typography>
                    </Stack>
                    <Autocomplete
                      open={openDropDownAutoComplete}
                      fullWidth
                      onOpen={() => setOpenDropDownAutoComplete(true)}
                      onClose={() =>
                        setOpenDropDownAutoComplete(!openDropDownAutoComplete)
                      }
                      id="permissions"
                      filterOptions={filterOptions}
                      multiple
                      limitTags={3}
                      sx={{
                        height: 'auto !important',
                      }}
                      size="medium"
                      onChange={(event, newValue) => {
                        handleAutoCompleteSelect(newValue)
                      }}
                      onFocus={() => {
                        setOpenDropDownAutoComplete(false)
                        setShowAccordion(true)
                      }}
                      onBlur={() => {}}
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Stack
                            key={index}
                            sx={{
                              alignItems: 'center',
                              gap: 1,
                              minWidth: '85px',
                              mx: '4px',
                              my: '4px',
                            }}
                          >
                            <Stack
                              sx={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: '2px',
                                borderRadius: '6px',
                                border: '1px solid #D0D5DD',
                                px: '5px',
                              }}
                            >
                              <IconButton
                                size="small"
                                {...getTagProps({ index })}
                                onClick={() => {
                                  handleDelete(option)
                                }}
                              >
                                <CloseRounded />
                              </IconButton>
                              <Typography>
                                {sentenceCase(option.name)}
                              </Typography>
                              <Chip
                                label={`${selectedPermissionsCountByModule(option.id)} rights`}
                                sx={{
                                  border: 'hidden',
                                  borderRadius: '3px',
                                  background: '#F2F4F7',
                                }}
                              />
                              {(!selectedModule ||
                                (selectedModule &&
                                  selectedModule.id !== option.id)) && (
                                <IconButton
                                  size="small"
                                  onClick={() =>
                                    handleEdit(option as IPermissionGroup)
                                  }
                                >
                                  <EditOutlinedIcon />
                                </IconButton>
                              )}
                            </Stack>
                          </Stack>
                        ))
                      }
                      renderOption={(props, option) => {
                        return (
                          <ListItem {...props} key={option.id}>
                            {sentenceCase(option.name)}
                          </ListItem>
                        )
                      }}
                      value={selectedGroups}
                      getOptionKey={(option) => option.id}
                      getOptionLabel={(option) => option.name}
                      isOptionEqualToValue={(option, value) =>
                        option.id === value.id
                      }
                      renderInput={(params) => {
                        return (
                          <TextField
                            {...params}
                            inputProps={{
                              ...params.inputProps,
                              startAdornment: (
                                <InputAdornment position="start">
                                  <SearchRounded />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )
                      }}
                      options={[...modules.data].filter(
                        (option) =>
                          !selectedGroups.find(
                            (selected) => selected.id === option.id
                          )
                      )}
                    />
                  </Stack>
                  {showAccordion && selectedModule && (
                    <Accordion
                      sx={{
                        border: '1px solid #D0D5DD',
                        marginTop: '10px',
                        borderRadius: '4px',
                        overflow: 'auto',
                      }}
                      expanded={showAccordion}
                      elevation={0}
                    >
                      <AccordionSummary>
                        {' '}
                        <Stack
                          sx={{
                            flexDirection: 'row',
                            justifyContent: 'flex-start',
                            alignItems: 'center',
                            gap: '4px',
                          }}
                        >
                          {' '}
                          <FormGroup>
                            <FormControlLabel
                              control={
                                <CustomCheckBox
                                  value={selectedModule.id}
                                  checked={
                                    selectedGroups.find(
                                      (group) => group.id === selectedModule.id
                                    )?.permissions.length ===
                                    selectedModule.permissions.length
                                  }
                                  onChange={handleAllCheck}
                                />
                              }
                              label={sentenceCase(selectedModule.name)}
                            />
                          </FormGroup>
                          <Chip
                            label={`${selectedPermissionsCountByModule(selectedModule.id)} selected`}
                            sx={{
                              border: '1px solid #EAECF0',
                              borderRadius: '3px',
                              background: '#F2F4F7',
                              height: '18px',
                              minWidth: '39px',
                              padding: '0px 5px',
                            }}
                          />
                        </Stack>
                      </AccordionSummary>

                      <AccordionDetails
                        sx={{
                          padding: '0px 33px 0px 42px',
                          margin: 0,
                          display: 'flex',
                          flexDirection: 'column',
                          gap: '4px',
                          justifyItems: 'flex-start',
                          alignItems: 'flex-start',
                        }}
                      >
                        <FormGroup>
                          {selectedModule.permissions.map((permission) => (
                            <FormControlLabel
                              key={permission.id}
                              control={
                                <CustomCheckBox
                                  value={permission.id}
                                  checked={
                                    permission.id ===
                                    selectedGroups
                                      .find(
                                        (group) =>
                                          group.id === selectedModule.id
                                      )
                                      ?.permissions.find(
                                        (perm) => perm.id === permission.id
                                      )?.id
                                  }
                                  onChange={(e) =>
                                    handleSingleCheck(e, permission)
                                  }
                                />
                              }
                              label={sentenceCase(permission.name)}
                            />
                          ))}
                        </FormGroup>
                      </AccordionDetails>

                      <AccordionActions
                        sx={{
                          display: 'flex',
                          justifyContent: 'flex-end',
                          alignItems: 'center',
                        }}
                      >
                        <Button
                          type="reset"
                          variant="text"
                          onClick={handleCancelModuleEditSelection}
                        >
                          <Typography>Cancel</Typography>
                        </Button>
                        <Button
                          type="button"
                          variant="text"
                          onClick={() => {
                            setSelectedModule(undefined)
                            setShowAccordion(false)
                          }}
                        >
                          <Typography>Done</Typography>
                          <DoneRounded
                            sx={{
                              color: 'black',
                            }}
                          />
                        </Button>
                      </AccordionActions>
                    </Accordion>
                  )}
                </Stack>
                <Stack
                  sx={{
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    width: '100%',
                    height: '10vh',
                  }}
                >
                  <Button
                    sx={{
                      width: '45%',
                    }}
                    type="button"
                    variant="outlined"
                    onClick={() => handleClose(null, 'close')}
                  >
                    Cancel
                  </Button>
                  {isLoadingUpdateRole ? (
                    <LoadingButton width="45%" />
                  ) : (
                    <Button
                      sx={{
                        width: '45%',
                      }}
                      type="submit"
                      variant="contained"
                    >
                      Save
                    </Button>
                  )}
                </Stack>
              </Stack>
            </Form>
          </FormikProvider>
        </Stack>
      </Drawer>
    </>
  )
}

export default EditRole
