/* eslint-disable @typescript-eslint/no-unused-vars */
import { Dispatch } from 'redux'

import {
  setAccountLogs,
  setAccountLogsBackOffice,
  setAccountTariffs,
  setAccountTariffsLoading,
  setActivationFailure,
  setActivationLoading,
  setActivationSuccess,
  setBankBranches,
  setChangeTab,
  setCustomer,
  setCustomerAccountDetails,
  setCustomerAccountExists,
  setCustomerAccountSearch,
  setCustomerAccountsList,
  setCustomerApprovalBarOpen,
  setCustomerFailure,
  setCustomerLinkedAccountsList,
  setCustomerLoading,
  setCustomerPinDetails,
  setCustomerPinLogs,
  setCustomerPinLogsBackoffice,
  setCustomerProfileAccount,
  setCustomerProfileExists,
  setCustomers,
  setCustomersFailure,
  setCustomersLoading,
  setCustomersSuccess,
  setCustomerSuccess,
  setCustomerUnlinkedAccountsList,
  setDevice,
  setDeviceFailure,
  setDeviceLoading,
  setDeviceLogs,
  setDevices,
  setDevicesFailure,
  setDevicesLoading,
  setDevicesSuccess,
  setDeviceSuccess,
  setIsLoadingAccounts,
  setIsLoadingAccountsLogs,
  setIsLoadingAccountsLogsBackOffice,
  setIsLoadingDeviceLogs,
  setIsLoadingLinkAccounts,
  setIsLoadingUnlinkedAccounts,
  setIsRelinkingApprovalLoading,
  setIsRelinkingApprovalSuccessfull,
  setIsRelinkingLoading,
  setIsRelinkingRejectionLoading,
  setIsRelinkingRejectionSuccessfull,
  setIsRelinkingSuccessfull,
  setIsSuccessfulAccountsLogs,
  setIsSuccessfulAccountsLogsBackOffice,
  setIsSuccessfulDeviceLogs,
  setIsUnlinkingLoading,
  setIsUnlinkingLoadingSuper,
  setIsUnlinkingSuccessfull,
  setIsUnlinkingSuccessfullSuper,
  setIsViewAccountOpen,
  setNotificationEvents,
  setNotificationFrequencies,
  setRestrictAccountFailure,
  setRestrictAccountLoading,
  setRestrictAccountSuccess,
  setSecurityFailure,
  setSecurityLoading,
  setSecuritySuccess,
  setSelectedCustomer,
} from '../reducers'

import { setNotification } from '@dtbx/store/reducers'

import {
  setAccountNotificationLogs,
  setAccountNotificationPreferences,
  setAccountSubscriptionLogs,
} from '../reducers'

import { secureapi2, HasAccessToRights } from '@dtbx/store/utils'
import {
  IAcceptRejectRestrictAccountApprovals,
  IAccountActivation,
  IAccountLinkingCompletion,
  IActivateCustomerProfile,
  IApprovalRequest,
  IApproveCustomerPinReset,
  IApproveRejectCustomerProfileActivation,
  IApproveRejectCustomerUpdate,
  ICreateCustomerAccount,
  ICreateCustomerApprovals,
  ICreateCustomerDeactivate,
  ICustomer,
  ICustomerAccount,
  ICustomerAccountHistoryLogs,
  ICustomerPinReset,
  ICustomerProfileAccount,
  ICustomersFilter,
  IDeactivateCustomer,
  IDeactivateCustomerApprovals,
  IDeactivateCustomerDeviceParams,
  IFetchCustomerAccount,
  IGetCustomerDeviceDetail,
  IGetCustomerDevicesProps,
  INotificationEventsPayload,
  INotificationEventsPerAccount,
  IRejectCustomerDeviceParams,
  IRestrictAccountParams,
  IUpdateCustomerDetails,
} from '../interfaces'

/**
 * @function getCustomers
 * @description Fetches all customers based on provided filters.
 * @param filters An object containing filter parameters.
 * @param dispatch Dispatch function for Redux or similar state management library
 * @returns void
 */

const ENDPOINTS = '/dbp/customers'

export const getAllCustomers = async (
  filters: ICustomersFilter,
  dispatch: Dispatch
) => {
  const {
    dateCreatedFrom,
    dateCreatedTo,
    size,
    page,
    firstName,
    otherNames,
    lastName,
    phoneNumber,
    email,
    nationality,
    idNumber,
    accountNumber,
  } = filters
  let url = ENDPOINTS + '?'
  page !== undefined ? (url += `page=${page}`) : (url += `page=${0}`)
  size !== undefined ? (url += `&size=${size}`) : (url += `&size=${10}`)
  if (firstName) url += `&firstName=${firstName}`
  if (lastName) url += `&lastName=${lastName}`
  if (email) url += `&email=${email}`
  if (otherNames) url += `&otherNames=${otherNames}`
  if (phoneNumber) url += `&phoneNumber=${phoneNumber}`
  if (nationality) url += `&nationality=${nationality}`
  if (idNumber) url += `&idNumber=${idNumber}`
  if (accountNumber) url += `&accountNumber=${accountNumber}`
  if (dateCreatedFrom) url += `&dateCreatedFrom=${dateCreatedFrom}`
  if (dateCreatedTo) url += `&dateCreatedTo=${dateCreatedTo}`
  dispatch(setCustomersLoading(true))
  dispatch(setCustomersSuccess(false))
  dispatch(setCustomersFailure(false))
  dispatch(setChangeTab(0))
  try {
    const response = await secureapi2.get(url)
    dispatch(setCustomers(response.data))

    dispatch(setCustomersSuccess(true))

    dispatch(setCustomersLoading(false))
  } catch (e) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setCustomersSuccess(false))
    dispatch(setCustomersLoading(false))
    dispatch(setCustomersFailure(true))
  }
}
export const getCustomerProfileById = async (
  profileId: string,
  dispatch: Dispatch
) => {
  dispatch(setCustomerLoading(true))
  try {
    const res = await secureapi2.get(`${ENDPOINTS}/${profileId}`)
    dispatch(setCustomer(res.data))
    dispatch(setCustomerLoading(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(setCustomerLoading(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/**
 * @function getCustomerProfile -  gets a single customer profile based on the provided profileId
 * @param profileId - string - the unique identifier of the customer profile
 * @param dispatch - function - the dispatch function for Redux
 */

export const getCustomerProfile = async (
  profileId: string,
  dispatch: Dispatch
): Promise<ICustomer | null> => {
  const url = `${ENDPOINTS}/${profileId}`
  dispatch(setCustomerLoading(true))
  dispatch(setCustomer({} as ICustomer))
  try {
    const res = await secureapi2.get(url)
    dispatch(setCustomer(res.data))
    dispatch(setCustomerLoading(false))
    dispatch(setCustomerSuccess(true))
    dispatch(setCustomerFailure(false))
    return res.data
  } catch (err: unknown) {
    const message = (err as Error).message
    dispatch(setCustomerLoading(false))
    dispatch(setCustomerSuccess(false))
    dispatch(setCustomerFailure(true))
    dispatch(setCustomerAccountsList([]))
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
  return null
}

/**
 * @function getCustomerDevices - gets devices registered under a customer
 * @param profileId - string - the unique identifier of the customer profile
 * @param dispatch - function - the dispatch function for Redux
 */

export const getCustomerDevices = async ({
  params: {
    profileID,
    dateCreatedFrom,
    dateCreatedTo,
    status,
    deviceType,
    deviceId,
    page,
    size,
  },
  dispatch,
}: IGetCustomerDevicesProps) => {
  dispatch(setDevicesLoading(true))
  dispatch(setDevicesSuccess(false))
  dispatch(setDevicesFailure(false))
  let url = `dbp/customers/${profileID}/devices`

  url += `?page=${page}&size=${size}`
  if (dateCreatedFrom && dateCreatedTo)
    url += `&dateCreatedFrom=${dateCreatedFrom}&dateCreatedTo=${dateCreatedTo}`
  if (status) url += `&status=${status}`
  if (deviceType) url += `&deviceType=${deviceType}`
  if (deviceId) url += `&deviceId=${deviceId}`
  dispatch(setDevicesFailure(true))
  try {
    const response = await secureapi2.get(url)
    dispatch(setDevices(response.data))
    dispatch(setDevicesLoading(false))
    dispatch(setDevicesSuccess(true))
    dispatch(setDevicesFailure(false))
  } catch (e) {
    dispatch(setDevicesLoading(false))
    dispatch(setDevicesSuccess(false))
    dispatch(setDevicesFailure(true))

    dispatch(
      setNotification({
        message: (e as Error).message,
        type: 'error',
      })
    )
  }
}

export const getCustomerDeviceDetail = async ({
  profileID,
  deviceID,
  dispatch,
}: IGetCustomerDeviceDetail) => {
  const url = `dbp/customers/${profileID}/devices/${deviceID}`
  dispatch(setDeviceLoading(true))
  dispatch(setDeviceSuccess(false))
  dispatch(setDeviceFailure(false))
  try {
    const response = await secureapi2.get(url)
    dispatch(setDevice(response.data))
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    return response.data
  } catch (e) {
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(false))
    dispatch(setDeviceFailure(true))
    dispatch(
      setNotification({
        message: (e as Error).message,
        type: 'error',
      })
    )
  }
}

/**
 * @function getCustomerDeviceHistory
 * @param dispatch
 * @param profileID
 * @param deviceID
 */

export const getCustomerDeviceHistory = async (
  dispatch: Dispatch,
  profileID: string,
  deviceID: string
) => {
  const url = `dbp/customers/${profileID}/devices/${deviceID}/logs`
  dispatch(setIsLoadingDeviceLogs(true))
  try {
    const res = await secureapi2.get(url)
    dispatch(setIsLoadingDeviceLogs(false))
    dispatch(setIsSuccessfulDeviceLogs(true))
    dispatch(setDeviceLogs(res.data.data))
  } catch (error) {
    dispatch(setIsLoadingDeviceLogs(false))
    dispatch(setIsSuccessfulDeviceLogs(false))
  }
}

export const updateCustomerDetails = async ({
  email,
  profileID,
  dispatch,
}: IUpdateCustomerDetails) => {
  const reqData = {
    email: email,
    comments: '',
  }
  dispatch(setCustomersLoading(true))
  try {
    await secureapi2.patch(`${ENDPOINTS}/${profileID}`, reqData)
    dispatch(setCustomersLoading(false))
    dispatch(
      setNotification({
        message: 'Edit customer is successful',
        type: 'success',
      })
    )
  } catch (error) {
    const message = (error as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const makerUpdateCustomer = async ({
  email,
  profileID,
  dispatch,
}: IUpdateCustomerDetails) => {
  const reqData = {
    email: email,
    comments: '',
  }
  dispatch(setCustomersLoading(true))
  try {
    await secureapi2.patch(`${ENDPOINTS}/${profileID}/make`, reqData)
    dispatch(setCustomersLoading(false))
    dispatch(
      setNotification({
        message: 'Edit customer request is pending approval',
        type: 'success',
      })
    )
  } catch (error) {
    const message = (error as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const rejectUpdateCustomer = async ({
  approvalID,
  comments,
  dispatch,
}: IApproveRejectCustomerUpdate) => {
  dispatch(setCustomersLoading(true))
  try {
    await secureapi2.put(`${ENDPOINTS}/reject/${approvalID}`, {
      comments,
    })
    dispatch(setCustomersLoading(false))
    dispatch(
      setNotification({
        message: 'Edit customer request has been rejected',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const approveUpdateCustomer = async ({
  approvalID,
  comments,
  dispatch,
}: IApproveRejectCustomerUpdate) => {
  dispatch(setCustomersLoading(true))
  try {
    await secureapi2.put(`${ENDPOINTS}/update/approve/${approvalID}`, {
      comments,
    })
    dispatch(setCustomersLoading(false))
    dispatch(
      setNotification({
        message: 'Edit customer request has been approved',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const deactivateCustomer = async ({
  profileID,
  reason,
  dispatch,
}: IDeactivateCustomer) => {
  let url = ''
  const superdeactivateStatus = HasAccessToRights([
    'SUPER_DEACTIVATE_CUSTOMERS',
  ])
  if (superdeactivateStatus) {
    url = `${ENDPOINTS}/deactivate/${profileID}`
  } else {
    url = `${ENDPOINTS}/deactivate/${profileID}/make`
  }
  dispatch(setCustomersLoading(true))
  try {
    await secureapi2.patch(url, {
      blockReason: 'DataBreach',
      comments: reason,
    })
    dispatch(
      setNotification({
        message: superdeactivateStatus
          ? 'Customer deactivated successfully.'
          : 'Deactivate customer request is pending approval.',
        type: 'success',
      })
    )
    await getAllCustomers({ page: 1, size: 10 }, dispatch)
    dispatch(setCustomersLoading(false))
  } catch (error) {
    console.error('Error updating customer details', error)
    const message = (error as Error).message
    dispatch(setCustomersLoading(false))
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const rejectDeactivateCustomer = async ({
  approvalID,
  comments,
  dispatch,
}: IDeactivateCustomerApprovals) => {
  const url = `/dbp/customers/deactivate/reject/${approvalID}`
  dispatch(setCustomersLoading(true))
  try {
    await secureapi2.put(url, { comments })
    dispatch(
      setNotification({
        message: 'Customer deactivation has been rejected',
        type: 'success',
      })
    )
    dispatch(setCustomersLoading(false))
  } catch (error) {
    console.error('Error updating customer details', error)
    const message = (error as Error).message
    dispatch(setCustomersLoading(false))
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const acceptDeactivateCustomer = async ({
  approvalID,
  comments,
  dispatch,
}: IDeactivateCustomerApprovals) => {
  const url = `/dbp/customers/deactivate/approve/${approvalID}`
  dispatch(setCustomersLoading(true))
  try {
    await secureapi2.put(url, { comments })
    dispatch(
      setNotification({
        message: 'Customer deactivation has been approved',
        type: 'success',
      })
    )
    dispatch(setCustomersLoading(false))
  } catch (error) {
    console.error('Error updating customer details', error)
    const message = (error as Error).message
    dispatch(setCustomersLoading(false))
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const fetchCustomerAccount = async ({
  account,
  dispatch,
}: IFetchCustomerAccount) => {
  dispatch(setCustomerProfileExists(false))
  dispatch(setCustomerLoading(true))
  resetCustomerAccountDetails({ dispatch })
  const url = `/dbp/customers/customer-accounts?account=${account}`
  try {
    const res = await secureapi2.get(url)
    dispatch(setCustomerAccountDetails(res.data))
    dispatch(setCustomerLoading(false))
    dispatch(setCustomerProfileExists(!!res?.data?.profileId))
    if (!!res?.data?.profileId) {
      getCustomerProfileById(res?.data?.profileId, dispatch)
      getLinkedCustomerAccountsByProfileId(res?.data?.profileId, dispatch)
    }
  } catch (err) {
    resetCustomerAccountDetails({ dispatch })
    dispatch(setCustomerProfileExists(false))
    dispatch(setCustomerLoading(false))
    dispatch(
      setNotification({
        message: 'Customer profile not found',
        type: 'error',
      })
    )
  }
}

export const createCustomerAccount = async ({
  account,
  dispatch,
}: ICreateCustomerAccount) => {
  dispatch(setCustomerLoading(true))
  let url
  const superCreateStatus = HasAccessToRights(['SUPER_CREATE_CUSTOMERS'])
  if (superCreateStatus) {
    url = `/dbp/customers`
  } else {
    url = `/dbp/customers/make`
  }
  try {
    await secureapi2.post(url, account)
    dispatch(setCustomerAccountSearch({ searchValue: '' }))
    dispatch(setCustomerLoading(false))
    resetCustomerAccountDetails({ dispatch })
    dispatch(
      setNotification({
        message: superCreateStatus
          ? 'Customer profile created successfully'
          : 'Create customer request is pending approval.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(setCustomerLoading(false))
    const message = (err as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const acceptCreateCustomer = async ({
  approvalId,
  comments,
  dispatch,
}: ICreateCustomerApprovals) => {
  const url = `/dbp/customers/approve/${approvalId}`

  try {
    await secureapi2.put(url, { comments })
    dispatch(setCustomerSuccess(true))
    dispatch(setCustomerFailure(false))
    dispatch(
      setSelectedCustomer({
        customer: {} as ICustomer,
        isPendingAction: false,
        openDetails: false,
      })
    )
    dispatch(
      setNotification({
        message: 'Create customer request has been approved.',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const rejectCreateCustomer = async ({
  approvalId,
  comments,
  dispatch,
}: ICreateCustomerApprovals) => {
  const url = `/dbp/customers/reject/${approvalId}`

  try {
    await secureapi2.put(url, { comments })
    dispatch(setCustomerSuccess(true))
    dispatch(setCustomerFailure(false))
    dispatch(
      setSelectedCustomer({
        customer: {} as ICustomer,
        isPendingAction: false,
        openDetails: false,
      })
    )
    dispatch(
      setNotification({
        message: 'Create customer request has been rejected.',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const resetCustomerAccountDetails = ({
  dispatch,
}: {
  dispatch: Dispatch
}) => {
  dispatch(
    setCustomerAccountDetails({
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      postalAddress: '',
      gender: '',
      idType: '',
      idValue: '',
      cif: '',
      physicalAddress: '',
      country: '',
      customerPrefix: '',
      dateOfBirth: '',
      nationality: '',
      customerCategory: '',
      customerType: '',
      customerAccounts: [],
      comments: null,
    })
  )
}

export const accountType = (type: string) => {
  switch (type) {
    case 'I':
      return 'Individual'
    case 'C':
      return 'Company'
    case 'J':
      return 'Joint Mandate'
    default:
      return 'No account'
  }
}

// customer device de/activation

export const deactivateCustomerDevice = async ({
  profileID,
  deviceID,
  dispatch,
  comments,
}: IDeactivateCustomerDeviceParams) => {
  const url = `dbp/customers/${profileID}/devices/${deviceID}/deactivate`

  dispatch(setDeviceLoading(true))
  dispatch(setDeviceSuccess(false))
  dispatch(setDeviceFailure(false))
  try {
    await secureapi2.patch(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    dispatch(
      setNotification({
        message: 'Device has been deactivated successfully',
        type: 'success',
      })
    )
    await getCustomerDeviceDetail({
      profileID,
      deviceID,
      dispatch,
    })
  } catch (err) {
    const message = (err as Error).message
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(false))
    dispatch(setDeviceFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const makerDeactivateCustomerDevice = async ({
  profileID,
  deviceID,
  dispatch,
  comments,
}: IDeactivateCustomerDeviceParams) => {
  const url = `/dbp/customers/${profileID}/devices/${deviceID}/deactivate/make`
  dispatch(setDeviceLoading(true))
  dispatch(setDeviceSuccess(false))
  dispatch(setDeviceFailure(false))

  try {
    await secureapi2.patch(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    dispatch(
      setNotification({
        message: 'Deactivate device request is pending approval',
        type: 'success',
      })
    )
    await getCustomerDeviceDetail({
      profileID,
      deviceID,
      dispatch,
    })
  } catch (err) {
    const message = (err as Error).message
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(false))
    dispatch(setDeviceFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const rejectDeactivateCustomerDevice = async ({
  approvalId,
  comments,
  dispatch,
}: IRejectCustomerDeviceParams) => {
  const url = `dbp/customers/devices/deactivate/${approvalId}/reject`

  try {
    await secureapi2.put(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    dispatch(
      setNotification({
        message: 'Deactivate device request has been rejected',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const acceptDeactivateCustomerDevice = async ({
  approvalId,
  comments,
  dispatch,
}: IRejectCustomerDeviceParams) => {
  const url = `dbp/customers/devices/deactivate/${approvalId}/approve`

  try {
    await secureapi2.put(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    dispatch(
      setNotification({
        message: 'Deactivate device request has been approved',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const activateCustomerDevice = async ({
  profileID,
  deviceID,
  dispatch,
  comments,
}: IDeactivateCustomerDeviceParams) => {
  const url = `dbp/customers/${profileID}/devices/${deviceID}/activate`

  dispatch(setDeviceLoading(true))
  dispatch(setDeviceSuccess(false))
  dispatch(setDeviceFailure(false))
  try {
    await secureapi2.patch(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    dispatch(
      setNotification({
        message: 'Device has been activated successfully',
        type: 'success',
      })
    )
    await getCustomerDeviceDetail({
      profileID,
      deviceID,
      dispatch,
    })
  } catch (err) {
    const message = (err as Error).message
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(false))
    dispatch(setDeviceFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
export const makerActivateCustomerDevice = async ({
  profileID,
  deviceID,
  dispatch,
  comments,
}: IDeactivateCustomerDeviceParams) => {
  const url = `/dbp/customers/${profileID}/devices/${deviceID}/activate/make`
  dispatch(setDeviceLoading(true))
  dispatch(setDeviceSuccess(false))
  dispatch(setDeviceFailure(false))
  try {
    await secureapi2.patch(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    await getCustomerDeviceDetail({
      profileID,
      deviceID,
      dispatch,
    })
    dispatch(
      setNotification({
        message: 'Activate device request is pending approval',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(false))
    dispatch(setDeviceFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const acceptActivateCustomerDevice = async ({
  approvalId,
  comments,
  dispatch,
}: IRejectCustomerDeviceParams) => {
  const url = `dbp/customers/devices/activate/${approvalId}/approve`

  try {
    await secureapi2.put(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    dispatch(
      setNotification({
        message: 'Activate device request has been approved',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const rejectActivateCustomerDevice = async ({
  approvalId,
  comments,
  dispatch,
}: IRejectCustomerDeviceParams) => {
  const url = `dbp/customers/devices/activate/${approvalId}/reject`

  try {
    await secureapi2.put(url, { comments })
    dispatch(setDeviceLoading(false))
    dispatch(setDeviceSuccess(true))
    dispatch(setDeviceFailure(false))
    dispatch(
      setNotification({
        message: 'Activate device request has been rejected',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/** unlinked customer accounts unique to customer profile ******/
export const getUnlinkedCustomerAccountsByProfileId = async (
  profileId: string,
  dispatch: Dispatch
) => {
  dispatch(setCustomerUnlinkedAccountsList([]))
  dispatch(setIsLoadingUnlinkedAccounts(true))
  try {
    const response = await secureapi2.get(
      `/dbp/customers/${profileId}/customer-accounts`
    )
    dispatch(setIsLoadingUnlinkedAccounts(false))
    dispatch(setCustomerUnlinkedAccountsList(response.data.customerAccounts))
    return response.data
  } catch (e) {
    const message = (e as Error).message

    dispatch(setIsLoadingUnlinkedAccounts(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/**** get customer accounts linked to  a profile ****/
export const getLinkedCustomerAccountsByProfileId = async (
  profileId: string,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingAccounts(true))
  dispatch(setCustomerLinkedAccountsList([]))
  try {
    const response = await secureapi2.get(
      `/dbp/customers/${profileId}/accounts`
    )
    dispatch(setIsLoadingAccounts(false))
    dispatch(setCustomerLinkedAccountsList(response?.data?.data))
    return response?.data?.data
  } catch (e) {
    const message = (e as Error).message
    dispatch(setCustomerLinkedAccountsList([]))
    dispatch(setIsLoadingAccounts(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/**Filter linked accounts by profile id**/
export const filterLinkedCustomerAccounts = async (
  dispatch: Dispatch,
  profileId: string,
  params: string
) => {
  dispatch(setIsLoadingAccounts(true))
  try {
    const response = await secureapi2.get(
      `/dbp/customers/${profileId}/accounts${params ? '?' : ''}${params}`
    )
    dispatch(setIsLoadingAccounts(false))
    dispatch(setCustomerLinkedAccountsList(response?.data?.data))
    return response?.data?.data
  } catch (e) {
    const message = (e as Error).message
    dispatch(setCustomerLinkedAccountsList([]))
    dispatch(setIsLoadingAccounts(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/*** get linked customer account by profile id and account number ***/
export const getCustomerAccountByAccountNo = async (
  profileId: string,
  accountNo: string,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingAccounts(true))
  try {
    const response = await secureapi2.get(
      `/dbp/customers/${profileId}/accounts/${accountNo}`
    )
    dispatch(setIsLoadingAccounts(false))
    dispatch(setCustomerAccountExists(true))
    dispatch(setCustomerProfileAccount(response.data))
    return response.data
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsLoadingAccounts(false))
    dispatch(setIsViewAccountOpen(false))
    dispatch(setCustomerAccountExists(false))
    dispatch(setCustomerProfileAccount({} as ICustomerProfileAccount))
    dispatch(setNotification({ message, type: 'error' }))
  }
}
export const linkCustomerAccounts = async (
  profileId: string,
  data: { accounts: ICustomerAccount[]; comments: string },
  dispatch: Dispatch,
  type: string
) => {
  dispatch(setIsLoadingLinkAccounts(true))
  try {
    await secureapi2.put(
      `/dbp/customers/${profileId}/accounts${type === 'super' ? '' : '/make'}`,
      data
    )
    dispatch(setIsLoadingLinkAccounts(false))
    dispatch(
      setNotification({
        message: `Accounts linking request was successful ${type === 'super' ? '' : 'awaiting approval'}`,
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileId, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsLoadingLinkAccounts(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
export const approveRejectAccountLinking = async ({
  approvalId,
  comments,
  profileId,
  dispatch,
  action,
}: IAccountLinkingCompletion) => {
  const url = `/dbp/customers/accounts/${action}/${approvalId}`
  try {
    await secureapi2.put(url, {
      comments,
    })
    dispatch(setCustomerApprovalBarOpen(false))
    dispatch(
      setNotification({
        message: `Account request has been ${action === 'approve' ? 'approved' : 'rejected'}.`,
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileId, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/**
 * @function makeRelinkCustomerAccounts
 * @param accountNo
 * @param profileID
 * @param dispatch
 * @param comment
 */

export const makeRelinkCustomerAccounts = async (
  dispatch: Dispatch,
  profileID: string,
  accountNo: string,
  comment?: string
) => {
  const url = `/dbp/customers/${profileID}/accounts/relink-account/make`
  dispatch(setIsRelinkingLoading(true))
  try {
    await secureapi2.put(url, { accountNo, comments: comment })
    dispatch(setIsRelinkingLoading(false))
    dispatch(setIsRelinkingSuccessfull(true))
    dispatch(
      setNotification({
        message: 'Accounts relinking request was successful',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsLoadingLinkAccounts(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/** reject relink customer accounts
 * @function approveRelinkCustomerAccounts
 * @param approvalID
 * @param profileID
 * @param dispatch
 * @param comment
 */

export const approveRelinkCustomerAccounts = async (
  dispatch: Dispatch,
  profileID: string,
  approvalID: string,
  comment?: string
) => {
  const url = `/dbp/customers/${profileID}/relink-accounts/approve/${approvalID}`
  dispatch(setIsRelinkingApprovalLoading(true))
  try {
    await secureapi2.put(url, { comments: comment })
    dispatch(setIsRelinkingApprovalLoading(false))
    dispatch(setIsRelinkingApprovalSuccessfull(true))

    dispatch(
      setNotification({
        message: 'Accounts relinking request was successful',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileID, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsRelinkingApprovalLoading(false))
    dispatch(setIsRelinkingApprovalSuccessfull(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
/**
 * @function rejectRelinkCustomerAccounts
 * @param dispatch
 * @param profileID
 * @param accountNo
 * @param comemnt
 */

export const rejectRelinkCustomerAccounts = async (
  dispatch: Dispatch,
  profileID: string,
  approvalID: string,
  comemnt?: string
) => {
  const url = `/dbp/customers/${profileID}/relink-accounts/reject/${approvalID}`
  dispatch(setIsRelinkingRejectionLoading(true))
  try {
    await secureapi2.put(url, { comments: comemnt })
    dispatch(setIsRelinkingRejectionLoading(false))
    dispatch(setIsRelinkingRejectionSuccessfull(true))
    dispatch(
      setNotification({
        message: 'Accounts relinking request was successful',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsRelinkingRejectionLoading(false))
    dispatch(setIsRelinkingRejectionSuccessfull(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

/**
 * @function relinkCustomerAccounts
 * @param dispatch
 * @param profileID
 * @param accountNo
 * @param comemnt
 */
export const relinkCustomerAccounts = async (
  dispatch: Dispatch,
  profileID: string,
  accountNo: string,
  comemnt?: string
) => {
  const url = `/dbp/customers/${profileID}/accounts/relink-account`
  dispatch(setIsRelinkingLoading(false))
  try {
    await secureapi2.put(url, { accountNo, comments: comemnt })
    dispatch(setIsRelinkingLoading(false))
    dispatch(setIsRelinkingSuccessfull(true))
    dispatch(
      setNotification({
        message: 'Accounts relinking request was successful',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileID, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsRelinkingLoading(false))
    dispatch(setIsRelinkingSuccessfull(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const getAccountTariffs = async (dispatch: Dispatch) => {
  dispatch(setAccountTariffsLoading(true))
  try {
    const res = await secureapi2.get('/fee/tariffs')
    dispatch(setAccountTariffs(res.data.data))
    dispatch(setAccountTariffsLoading(false))
  } catch (error) {
    const message = (error as Error).message
    dispatch(setAccountTariffsLoading(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const resetCustomerPin = async ({
  profileID,
  dispatch,
  comments,
  role,
}: ICustomerPinReset & { role: 'super' | 'maker' }) => {
  const url = `dbp/customers/${profileID}/pins${role === 'maker' ? '/make' : ''}`
  const messageBase =
    role === 'maker'
      ? 'PIN reset successful pending approval'
      : 'PIN has been reset successfully'

  const updateStatus = (
    loading: boolean,
    success?: boolean,
    failure?: boolean
  ) => {
    dispatch(setSecurityLoading(loading))
    if (success !== undefined) dispatch(setSecuritySuccess(success))
    if (failure !== undefined) dispatch(setSecurityFailure(failure))
  }

  try {
    updateStatus(true)
    await secureapi2.put(url, { comments })
    updateStatus(false, true, false)
    dispatch(setNotification({ message: messageBase, type: 'success' }))
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (err as Error).message, type: 'error' })
    )
  }
}

/**
 * @function getCustomersAccountHistory
 * @description Fetches all customer account history based on provided filters.
 * @param filters An object containing filter parameters.
 * @param dispatch Dispatch function for Redux
 */
export const getCustomersAccountHistory = async ({
  dispatch,
  profileID,
  accountNo,
}: {
  dispatch: Dispatch
  profileID: string
  accountNo: string
}) => {
  dispatch(setIsLoadingAccountsLogs(true))
  const url = `/dbp/customers/${profileID}/accounts/${accountNo}/logs`
  try {
    const response = await secureapi2.get(url)
    dispatch(setAccountLogs(response.data?.data))
    dispatch(setIsLoadingAccountsLogs(false))
    dispatch(setIsSuccessfulAccountsLogs(true))
  } catch (e) {
    const message = (e as Error).message
    dispatch(setAccountLogs([] as ICustomerAccountHistoryLogs[]))
    dispatch(setIsLoadingAccountsLogs(false))
    dispatch(setIsSuccessfulAccountsLogs(true))
    return message
  }
}

export const deactivateCustomerAccount = async ({
  accountNo,
  profileId,
  comments,
  setOpen,
  setIsLoading,
  dispatch,
}: IAccountActivation) => {
  const superDeactivateStatus = HasAccessToRights(['SUPER_DEACTIVATE_ACCOUNTS'])
  const url = `/dbp/customers/${profileId}/accounts/${accountNo}/deactivate${superDeactivateStatus ? '' : '/make'}`
  setIsLoading(true)
  try {
    await secureapi2.patch(url, {
      comments,
    })
    setOpen(false)
    setIsLoading(false)
    await getCustomerAccountByAccountNo(profileId, accountNo, dispatch)
    dispatch(
      setNotification({
        message: superDeactivateStatus
          ? 'Account has been deactivated'
          : 'Deactivate account request is pending approval.',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    setIsLoading(false)
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
export const activateCustomerAccount = async ({
  accountNo,
  profileId,
  comments,
  setOpen,
  setIsLoading,
  dispatch,
}: IAccountActivation) => {
  const superDeactivateStatus = HasAccessToRights(['SUPER_DEACTIVATE_ACCOUNTS'])
  const url = `/dbp/customers/${profileId}/accounts/${accountNo}/activate${superDeactivateStatus ? '' : '/make'}`
  setIsLoading(true)
  try {
    await secureapi2.patch(url, {
      comments,
    })
    setOpen(false)
    setIsLoading(false)
    await getCustomerAccountByAccountNo(profileId, accountNo, dispatch)
    dispatch(
      setNotification({
        message: superDeactivateStatus
          ? 'Account has been activated'
          : 'Activate account request is pending approval.',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    setIsLoading(false)
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const acceptDeactivateCustomerAccount = async ({
  approvalId,
  comments,
  accountNo,
  profileId,
  dispatch,
}: ICreateCustomerDeactivate) => {
  const url = `/dbp/customers/accounts/deactivate/approve/${approvalId}`
  try {
    await secureapi2.put(url, {
      comments,
    })
    dispatch(setIsViewAccountOpen(false))
    dispatch(setCustomerApprovalBarOpen(false))
    dispatch(
      setNotification({
        message: 'Deactivate account request has been approved.',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileId, dispatch)
    await getCustomerAccountByAccountNo(profileId, accountNo, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const rejectDeactivateCustomerAccount = async ({
  approvalId,
  comments,
  accountNo,
  profileId,
  dispatch,
}: ICreateCustomerDeactivate) => {
  const url = `/dbp/customers/accounts/deactivate/reject/${approvalId}`
  try {
    await secureapi2.put(url, {
      comments,
    })
    dispatch(setCustomerApprovalBarOpen(false))
    dispatch(
      setNotification({
        message: 'Deactivate account request has been rejected.',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileId, dispatch)
    await getCustomerAccountByAccountNo(profileId, accountNo, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const acceptActivateCustomerAccount = async ({
  approvalId,
  comments,
  accountNo,
  profileId,
  dispatch,
}: ICreateCustomerDeactivate) => {
  const url = `/dbp/customers/accounts/activate/approve/${approvalId}`
  try {
    await secureapi2.put(url, {
      comments,
    })
    dispatch(setCustomerApprovalBarOpen(false))
    dispatch(
      setNotification({
        message: 'Activate account request has been approved.',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileId, dispatch)
    await getCustomerAccountByAccountNo(profileId, accountNo, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const rejectActivateCustomerAccount = async ({
  approvalId,
  comments,
  accountNo,
  profileId,
  dispatch,
}: ICreateCustomerDeactivate) => {
  const url = `/dbp/customers/accounts/activate/reject/${approvalId}`
  try {
    await secureapi2.put(url, {
      comments,
    })
    dispatch(setCustomerApprovalBarOpen(false))
    dispatch(
      setNotification({
        message: 'Activate account request has been rejected.',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileId, dispatch)
    await getCustomerAccountByAccountNo(profileId, accountNo, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const approveCustomerPinReset = async ({
  approvalID,
  comments,
  type,
  dispatch,
}: IApproveCustomerPinReset) => {
  const url = `/dbp/customers/pins/${type}/${approvalID}`
  const messageBase = `PIN reset ${type === 'approve' ? 'approved' : 'rejected'} successfully`
  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setSecurityLoading(loading))
    dispatch(setSecuritySuccess(success))
    dispatch(setSecurityFailure(failure))
  }

  try {
    updateStatus(true, false, false)
    await secureapi2.put(url, { comments })
    updateStatus(false, true, false)
    dispatch(setNotification({ message: messageBase, type: 'success' }))
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (err as Error).message, type: 'error' })
    )
  }
}
// Customer PIN

export const getCustomerPinDetails = async ({
  profileID,
  dispatch,
}: ICustomerPinReset) => {
  const url = `/dbp/customers/${profileID}/profile-pins`
  // alert("Access security questions")
  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setSecurityLoading(loading))
    dispatch(setSecuritySuccess(success))
    dispatch(setSecurityFailure(failure))
  }
  try {
    updateStatus(true, false, false)
    const response = await secureapi2.get(url)
    updateStatus(false, true, false)
    dispatch(setCustomerPinDetails(response.data))
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (err as Error).message, type: 'error' })
    )
  }
}

export const getCustomerPinHistory = async ({
  profileID,
  dispatch,
  size = 10,
  page,
  pinType,
}: ICustomerPinReset & { page?: number; size?: number }) => {
  const url = `/dbp/customers/${profileID}/pin-logs/${pinType}?size=${size}&page=${page}`

  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setSecurityLoading(loading))
    dispatch(setSecuritySuccess(success))
    dispatch(setSecurityFailure(failure))
  }

  try {
    updateStatus(true, false, false)
    const response = await secureapi2.get(url)
    updateStatus(false, true, false)
    dispatch(setCustomerPinLogs(response.data))
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (err as Error).message, type: 'error' })
    )
  }
}

export const getCustomerPinLogsBackoffice = async (
  profileID: string,
  dispatch: Dispatch,
  page: number,
  size: 10
) => {
  const url = `/dbp/customers/${profileID}/pin-changes?size=${size}&page=${page}`
  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setSecurityLoading(loading))
    dispatch(setSecuritySuccess(success))
    dispatch(setSecurityFailure(failure))
  }
  try {
    updateStatus(true, false, false)
    const response = await secureapi2.get(url)
    updateStatus(false, true, false)
    dispatch(setCustomerPinLogsBackoffice(response.data))
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (err as Error).message, type: 'error' })
    )
  }
}
export const getCustomerAccountsLogsBackoffice = async (
  dispatch: Dispatch,
  accountId: string
) => {
  const url = `/dbp/accounts/${accountId}/changes`
  dispatch(setIsLoadingAccountsLogsBackOffice(true))
  try {
    const res = await secureapi2.get(url)
    dispatch(setIsLoadingAccountsLogsBackOffice(false))
    dispatch(setIsSuccessfulAccountsLogsBackOffice(true))
    dispatch(setAccountLogsBackOffice(res.data.data))
  } catch (error) {
    dispatch(setIsLoadingAccountsLogsBackOffice(false))
    dispatch(setIsSuccessfulAccountsLogsBackOffice(true))
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  }
}

export const makerRestrictAccount = async ({
  profileId,
  accountNo,
  comments,
  dispatch,
}: IRestrictAccountParams) => {
  let url = ''
  const superdeactivateStatus = HasAccessToRights(['SUPER_DEACTIVATE_ACCOUNTS'])
  if (superdeactivateStatus) {
    url = `/dbp/customers/${profileId}/accounts/${accountNo}/restrict`
  } else {
    url = `/dbp/customers/${profileId}/accounts/${accountNo}/restrict/make`
  }
  dispatch(setRestrictAccountLoading(true))
  try {
    const res = await secureapi2.patch(
      url,

      { comments }
    )

    dispatch(setCustomerProfileAccount(res.data))
    dispatch(setRestrictAccountSuccess(true))

    dispatch(
      setNotification({
        message: superdeactivateStatus
          ? 'Restrict account is successful'
          : 'Restrict account is pending approval',
        type: 'success',
      })
    )
    dispatch(setRestrictAccountLoading(false))
    await getCustomerAccountByAccountNo(profileId, accountNo, dispatch)
  } catch (error) {
    const message = (error as Error).message

    dispatch(setRestrictAccountFailure(true))
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setRestrictAccountLoading(false))
  }
}

export const acceptRestrictAccount = async ({
  approvalId,
  comments,
  dispatch,
}: IAcceptRejectRestrictAccountApprovals) => {
  const url = `/dbp/customers/accounts/restrict/approve/${approvalId}`
  dispatch(setRestrictAccountLoading(true))
  try {
    await secureapi2.put(url, { comments })

    dispatch(setRestrictAccountSuccess(true))

    dispatch(
      setNotification({
        message: 'Restrict account request has been approved.',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message

    dispatch(setRestrictAccountFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setRestrictAccountLoading(false))
  }
}

export const rejectRestrictAccount = async ({
  approvalId,
  comments,
  dispatch,
}: IAcceptRejectRestrictAccountApprovals) => {
  const url = `/dbp/customers/accounts/restrict/reject/${approvalId}`
  dispatch(setRestrictAccountLoading(true))
  try {
    await secureapi2.put(url, { comments })

    dispatch(
      setNotification({
        message: 'Restrict account request has been rejected.',
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message

    dispatch(setRestrictAccountFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setRestrictAccountLoading(false))
  }
}

/**
 * @function makeUnlinkCustomerAccount - `Maker checker endpoint for unlinking customer accounts`
 * @function unlinkCustomerAccount - `Super admin endpoint for unlinking customer accounts`
 * @param {Dispatch} dispatch - `Dispatch function for Redux`
 * @param params
 * @function approveUnlinkCustomerAccount - `Approve unlinking customer accounts`
 * @function rejectUnlinkCustomerAccount - `Reject unlinking customer accounts`
 */

export const makeUnlinkCustomerAccounts = async (
  dispatch: Dispatch,
  params: {
    profileID: string
    accountNo: string
    comments?: string
  }
) => {
  const { profileID, accountNo, comments } = params
  const url = `/dbp/customers/${profileID}/accounts/${accountNo}/unlink/make`
  dispatch(setIsUnlinkingLoading(true))
  try {
    await secureapi2.patch(url, { comments: comments ? comments : '' })
    dispatch(setIsUnlinkingLoading(false))
    dispatch(setIsUnlinkingSuccessfull(true))
    dispatch(
      setNotification({
        message: 'Unlink account request was successful, awaiting approval',
        type: 'success',
      })
    )
  } catch (error) {
    dispatch(setIsUnlinkingLoading(false))
    dispatch(setIsUnlinkingSuccessfull(false))
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  }
}

export const unlinkCustomerAccount = async (
  dispatch: Dispatch,
  params: {
    profileID: string
    accountNo: string
    comments?: string
  }
) => {
  const { profileID, accountNo, comments } = params
  const url = `/dbp/customers/${profileID}/accounts/${accountNo}/unlink`
  dispatch(setIsUnlinkingLoading(true))
  try {
    await secureapi2.patch(url, { comments: comments ? comments : '' })
    dispatch(setIsUnlinkingLoadingSuper(false))
    dispatch(setIsUnlinkingSuccessfullSuper(true))
    dispatch(
      setNotification({
        message: 'Unlinking account was successful',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileID, dispatch)
  } catch (error) {
    dispatch(setIsUnlinkingLoadingSuper(false))
    dispatch(setIsUnlinkingSuccessfullSuper(false))
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  }
}

export const approveUnlinkCustomerAccount = async (
  dispatch: Dispatch,
  params: {
    approvalID: string
    comments?: string
    profileID: string
  }
) => {
  const { approvalID, comments, profileID } = params
  const url = `/dbp/customers/accounts/unlink/approve/${approvalID}`

  try {
    await secureapi2.put(url, { comments })
    dispatch(
      setNotification({
        message: 'Unlink account request has been approved',
        type: 'success',
      })
    )
    await getLinkedCustomerAccountsByProfileId(profileID, dispatch)
  } catch (error) {
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  }
}

export const rejectUnlinkCustomerAccount = async (
  dispatch: Dispatch,
  params: {
    approvalID: string
    comments?: string
    profileID: string
  }
) => {
  const { approvalID, comments, profileID } = params
  const url = `/dbp/customers/accounts/unlink/reject/${approvalID}`

  try {
    await secureapi2.put(url, { comments })
    dispatch(
      setNotification({
        message: 'Unlink account request has been rejected',
        type: 'success',
      })
    )

    await getLinkedCustomerAccountsByProfileId(profileID, dispatch)
  } catch (error) {
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  }
}
//Activate customer profile
export const activateCustomerProfile = async ({
  profileId,
  comments,
  dispatch,
}: IActivateCustomerProfile) => {
  let url = ''
  const superactivateStatus = HasAccessToRights(['SUPER_ACTIVATE_CUSTOMERS'])
  if (superactivateStatus) {
    url = `/dbp/customers/activate/${profileId}`
  } else {
    url = `/dbp/customers/activate/${profileId}/make`
  }
  dispatch(setActivationLoading(true))

  try {
    const res = await secureapi2.patch(url, {
      comments,
    })
    dispatch(setCustomerProfileAccount(res.data))

    dispatch(setActivationSuccess(true))
    dispatch(
      setNotification({
        message: superactivateStatus
          ? 'Account profile has been activated successfully'
          : 'Activate account profile request is pending approval.',
        type: 'success',
      })
    )
    await getAllCustomers({ page: 1, size: 10 }, dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(setActivationFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    console.error('Activation Error:', e)
    dispatch(setActivationLoading(false))
  }
}
//Activate customer profile Approvals
export const activateCustomerProfileApprove = async ({
  approvalId,
  comments,
  dispatch,
}: IApproveRejectCustomerProfileActivation) => {
  const url = `/dbp/customers/activate/approve/${approvalId}`
  dispatch(setActivationLoading(true))

  try {
    const res = await secureapi2.put(url, {
      comments,
    })
    dispatch(setCustomerProfileAccount(res.data))

    dispatch(setActivationSuccess(true))
    dispatch(
      setNotification({
        message: 'Account profile has been activated successfully',

        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(setActivationFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    console.error('Activation Error:', e)
    dispatch(setActivationLoading(false))
  }
}
//Activate customer profile Rejection
export const activateCustomerProfileReject = async ({
  approvalId,
  comments,
  dispatch,
}: IApproveRejectCustomerProfileActivation) => {
  const url = `/dbp/customers/activate/reject/${approvalId}`
  dispatch(setActivationLoading(true))

  try {
    const res = await secureapi2.put(url, {
      comments,
    })
    dispatch(setCustomerProfileAccount(res.data))

    dispatch(setActivationSuccess(true))
    dispatch(
      setNotification({
        message: 'Account profile activation request has been rejected',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(setActivationFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    console.error('Rejection Error:', e)
    dispatch(setActivationLoading(false))
  }
}

export const approveDeleteCustomer = async ({
  approvalID,
  type,
  comments,
  dispatch,
}: IApproveCustomerPinReset) => {
  const url = `/dbp/customers/delete/${type}/${approvalID}`
  const messageBase = `Customer deletion ${type === 'approve' ? 'approved' : 'rejected'} successfully`
  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setCustomerLoading(loading))
    dispatch(setCustomerLoading(success))
    dispatch(setCustomerLoading(failure))
  }
  try {
    updateStatus(true, false, false)
    await secureapi2.put(url, { comments })
    dispatch(setNotification({ message: messageBase, type: 'success' }))
  } catch (error) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}

export const deleteCustomerProfile = async ({
  profileID,
  comments,
  type,
  dispatch,
}: IApproveCustomerPinReset) => {
  const url = `dbp/customers/delete/${profileID}/${type === 'super' ? '' : 'make'}`

  const messageBase = `Customer profile deleted ${type === 'super' ? 'successfully' : 'pending approval'}`
  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setCustomerLoading(loading))
    dispatch(setCustomerLoading(success))
    dispatch(setCustomerLoading(failure))
  }
  try {
    updateStatus(true, true, false)
    await secureapi2.patch(url, { comments })
    dispatch(setNotification({ message: messageBase, type: 'success' }))
    await getAllCustomers({ page: 1, size: 10 }, dispatch)
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}
export const fetchNotificationEvents = async ({
  dispatch,
}: {
  dispatch: Dispatch
}) => {
  const url = `/notifications/events`
  try {
    const res = await secureapi2.get(url)
    dispatch(setNotificationEvents(res.data))
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const fetchNotificationEventFrequencies = async ({
  dispatch,
}: {
  dispatch: Dispatch
}) => {
  const url = `/notifications/events/frequencies`
  try {
    const res = await secureapi2.get(url)
    dispatch(setNotificationFrequencies(res?.data))
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const fetchNotificationEventsPerAccount = async ({
  accountId,
  dispatch,
}: {
  accountId: string
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/${accountId}/subscriptions?accountSource=CBS`
  try {
    const res = await secureapi2.get(url)
    dispatch(setAccountNotificationPreferences(res?.data))
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const subscribeToNotificationEvents = async ({
  events,
  dispatch,
}: {
  events: INotificationEventsPayload
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/subscribe${HasAccessToRights(['SUPER_UPDATE_ACCOUNTS_PREFERENCES']) ? '' : '/make'}`
  try {
    await secureapi2.post(url, events)
    dispatch(
      setNotification({
        message: HasAccessToRights(['SUPER_UPDATE_ACCOUNTS_PREFERENCES'])
          ? 'Notification preferences have been saved successfully'
          : 'Add account notification request is pending approval.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const unsubscribeToNotificationEvents = async ({
  accountId,
  eventId,
  comments,
  profileId,
  dispatch,
}: {
  accountId: string | undefined
  eventId: string | undefined
  comments: string | undefined
  profileId: string | undefined
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/${accountId}/unsubscribe${HasAccessToRights(['SUPER_UPDATE_ACCOUNTS_PREFERENCES']) ? '' : '/make'}`
  const payload = {
    eventId,
    profileId,
    accountSource: 'CBS',
    comments,
  }
  try {
    await secureapi2.post(url, payload)
    dispatch(
      setNotification({
        message: HasAccessToRights(['SUPER_UPDATE_ACCOUNTS_PREFERENCES'])
          ? 'Notification preferences have been removed'
          : 'Remove notification request is pending approval.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const editSubscriptionAlerts = async ({
  events,
  dispatch,
}: {
  events: INotificationEventsPayload
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/subscribe${HasAccessToRights(['SUPER_UPDATE_ACCOUNTS_PREFERENCES']) ? '' : '/make'}`
  try {
    await secureapi2.post(url, events)
    dispatch(
      setNotification({
        message: HasAccessToRights(['SUPER_UPDATE_ACCOUNTS_PREFERENCES'])
          ? 'Notification preferences have been edited successfully'
          : 'Edit account notification request is pending approval.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const fetchNotificationAlertsHistory = async ({
  accountNo,
  dispatch,
}: {
  accountNo: string
  dispatch: Dispatch
}) => {
  const url = `/dbp/accounts/${accountNo}/preferences/changes`
  try {
    const resp = await secureapi2.get(url)
    const notifications: IApprovalRequest[] = []
    const subscriptions: IApprovalRequest[] = []
    resp?.data?.data?.forEach((alert: IApprovalRequest) => {
      if (
        JSON.parse(alert.entity as string)?.eventId ===
          '0d48819f-4933-403c-a4c4-db0cf8d6a6d0' ||
        JSON.parse(alert.entity as string)?.event?.id ===
          '0d48819f-4933-403c-a4c4-db0cf8d6a6d0'
      ) {
        subscriptions.push(alert)
      } else {
        notifications.push(alert)
      }
    })
    dispatch(setAccountNotificationLogs(notifications))
    dispatch(setAccountSubscriptionLogs(subscriptions))
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const approveSubscribeNotification = async ({
  approvalId,
  type,
  comments,
  dispatch,
}: {
  approvalId: string
  type: string
  comments: string | undefined
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/subscribe/approve/${approvalId}`
  try {
    const resp = await secureapi2.put(url, {
      comments,
    })
    dispatch(
      setNotification({
        message:
          type === 'add'
            ? 'Add account notification request has been approved.'
            : 'Edit account notification request has been approved.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const rejectSubscribeNotification = async ({
  approvalId,
  type,
  comments,
  dispatch,
}: {
  approvalId: string
  type: string
  comments: string | undefined
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/subscribe/reject/${approvalId}`
  try {
    const resp = await secureapi2.put(url, {
      comments,
    })
    dispatch(
      setNotification({
        message:
          type === 'add'
            ? 'Add account notification request has been rejected.'
            : 'Edit account notification request has been rejected.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const approveUnsubscribeNotification = async ({
  approvalId,
  accountId,
  comments,
  dispatch,
}: {
  approvalId: string
  accountId: string
  comments: string | undefined
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/${accountId}/unsubscribe/approve/${approvalId}`
  try {
    const resp = await secureapi2.put(url, {
      comments,
    })
    dispatch(
      setNotification({
        message: 'Remove notification request has been approved.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const rejectUnsubscribeNotification = async ({
  approvalId,
  accountId,
  comments,
  dispatch,
}: {
  approvalId: string
  accountId: string
  comments: string | undefined
  dispatch: Dispatch
}) => {
  const url = `/notifications/accounts/${accountId}/unsubscribe/reject/${approvalId}`
  try {
    const resp = await secureapi2.put(url, {
      comments,
    })
    dispatch(
      setNotification({
        message: 'Remove notification request has been rejected.',
        type: 'success',
      })
    )
  } catch (err) {
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}
