import { IRole } from './roles'

export type FileFormat = 'excel' | 'csv' | 'json' | 'pdf'


export interface IUser {
  id: string
  firstName: string
  lastName: string
  middleName: string
  roles: IRole[]
  email: string
  phoneNumber: string
  dateCreated?: string
  status: string
  country?: string
  lastLoginDate?: string
  [key: string]: string | number | boolean | IRole[] | undefined
}
export interface IUsersResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IUser[]
}
export interface IDecodeToken {
  last_name: string
  first_name: string
  user_id: string
  authorities: string[]
  sub: string
  iat: number
  exp: number
  resources?: IResource[]
}
export interface IResource {
  resourceType: string
  resourceIds: string[]
}
export interface ICreateUser {
  firstName: string
  lastName: string
  middleName?: string
  roleIds: string[]
  email: string
  phoneNumber: string
  comments?: string
}
export interface IUpdateUser {
  roleIds: string[]
  comments: string
}
export interface ICheckUser {
  comments: string
}
export interface IADUserProfile {
  mail: string
  mobilePhone: string
  surname: string
  givenName: string
}
