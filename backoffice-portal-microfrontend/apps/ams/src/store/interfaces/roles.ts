export interface IRole {
  id: string
  name: string
  description: string
  creationDate: string
  custom: boolean
  status?: string
  createdBy?: string
  permissions: IPermission[]
  permissionsGroup: IPermissionGroup[]
  [key: string]:
    | string
    | boolean
    | IPermission[]
    | IPermissionGroup[]
    | undefined
}
export interface ICountry {
  id: string
  name: string
  callingCode: string
}

// export interface IGetRole {
//   id:               string;
//   name:             string;
//   description:      string;
//   custom:           boolean;
//   permissions:      IPermission[];
//   permissionsGroup: IPermissionGroup[];
//   creationDate:     Date;
//   createdBy:        string;
// }

export interface IRoleResponse {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
  data: IRole[]
}
export interface IPermission {
  id: string
  name: string
  description: string
  groupName: string
  visible: boolean
  module:
    | string
    | {
        id: string
        description: string
        moduleName: string
        approvalRequest: string | null
        dateCreated: string | null
        dateModified: string | null
        updatedBy: string | null
      }
}
export interface IPermissionResponse {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
  data: IPermission[]
}

export interface IPermissionGroupResponse {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
  data: IPermissionGroup[]
}
export interface IPermissionGroup {
  id: string
  name: string
  description: string
  permissions: IPermission[]
}

export interface ICreateRole {
  name: string
  description: string
  permissions: string[]
  comments: string
}
