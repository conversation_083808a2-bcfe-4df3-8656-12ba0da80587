import { combineReducers } from 'redux'

import rolesReducer from './rolesReducer'
import usersReducer from './usersReducer'
import customers from './customersReducer'
import approvalRequests from './ApprovalRequests'
import loansReducer from './loansReducer'
import {navigation, authReducer, overlays, notifications} from '@dtbx/store/reducers'
import chargeConfigurationReducer from './chargeConfigurationReducer'

const rootReducer = combineReducers({
  users: usersReducer,
  roles: rolesReducer,
  customers: customers,
  approvalRequests: approvalRequests,
  loans: loansReducer,
  navigation: navigation,
  notifications: notifications,
  auth: authReducer,
  overlay: overlays,
  chargeConfiguration: chargeConfigurationReducer,
})
export default rootReducer
