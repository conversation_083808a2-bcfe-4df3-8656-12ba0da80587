# RTK Query Setup with Multiple API Handlers

This document explains how to use RTK Query in the boma-yangu-client with support for multiple API handlers including `secureapi`, `secureapi2`, and custom handlers.

## Overview

Our RTK Query setup provides:
- **Compatibility with existing `secureapi` handlers** from the microfrontend
- **Flexible handler selection** (secureapi, secureapi2, custom)
- **Consistent error handling** across all handlers
- **Type safety** with TypeScript
- **Automatic token management** and refresh

## Architecture

### Files Structure
```
src/store/api/
├── baseQuery.ts      # Custom base query with handler support
├── apiSlice.ts       # Main API slice with examples
└── propertyApi.ts    # Feature-specific API slice example
```

### Handler Types

1. **`secureapi`** (default): Uses the main secure API instance
2. **`secureapi2`**: Uses the secondary secure API instance  
3. **`custom`**: Creates new axios instances for different base URLs

## Basic Usage

### 1. Simple Query with Default Handler (secureapi)

```typescript
import { useGetCurrentUserQuery } from '@/store/api/apiSlice'

const MyComponent = () => {
  const { data, error, isLoading } = useGetCurrentUserQuery()
  
  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {error.data?.message}</div>
  
  return <div>Welcome, {data?.name}</div>
}
```

### 2. Query with secureapi2 Handler

```typescript
const { data } = useGetPropertiesQuery(
  { page: 1, limit: 10 },
  // This endpoint uses secureapi2 as defined in the slice
)
```

### 3. Query with Custom Handler

```typescript
const { data } = useGetExternalDataQuery({
  endpoint: '/posts/1',
  baseUrl: 'https://jsonplaceholder.typicode.com'
})
```

## Creating Feature-Specific API Slices

### Step 1: Define Types

```typescript
interface Property {
  id: string
  title: string
  price: number
  // ... other fields
}
```

### Step 2: Extend the Main API Slice

```typescript
import { apiSlice } from './apiSlice'

export const propertyApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getProperties: builder.query<Property[], void>({
      query: () => ({
        url: '/properties',
        method: 'GET',
        handler: 'secureapi2', // Specify handler
      }),
      providesTags: ['Property'],
    }),
  }),
})

export const { useGetPropertiesQuery } = propertyApi
```

## Handler Configuration

### Query Arguments

All endpoints accept these parameters:

```typescript
interface CustomQueryArgs {
  url: string                    // Required: API endpoint
  method?: 'GET' | 'POST' | ... // Optional: HTTP method (default: GET)
  body?: any                     // Optional: Request body
  params?: Record<string, any>   // Optional: Query parameters
  headers?: Record<string, string> // Optional: Custom headers
  handler?: 'secureapi' | 'secureapi2' | 'custom' // Optional: Handler type
  baseUrl?: string              // Required for custom handler
}
```

### Examples by Handler Type

#### Using secureapi (default)
```typescript
query: () => ({
  url: '/users/me',
  method: 'GET',
  // handler defaults to 'secureapi'
})
```

#### Using secureapi2
```typescript
query: (id) => ({
  url: `/properties/${id}`,
  method: 'GET',
  handler: 'secureapi2',
})
```

#### Using custom handler
```typescript
query: ({ endpoint, baseUrl }) => ({
  url: endpoint,
  method: 'GET',
  handler: 'custom',
  baseUrl: baseUrl,
})
```

## Mutations

### Basic Mutation

```typescript
const [updateUser, { isLoading, error }] = useUpdateUserMutation()

const handleUpdate = async () => {
  try {
    await updateUser({
      id: 'user-id',
      data: { name: 'New Name' }
    }).unwrap()
    // Success handling
  } catch (error) {
    // Error handling
  }
}
```

### File Upload

```typescript
const [uploadImages] = useUploadPropertyImagesMutation()

const handleUpload = async (files: FileList) => {
  const formData = new FormData()
  Array.from(files).forEach(file => {
    formData.append('images', file)
  })
  
  await uploadImages({
    propertyId: 'property-id',
    images: formData
  })
}
```

## Error Handling

All handlers provide consistent error format:

```typescript
interface CustomError {
  status: number
  data: {
    message?: string
    error?: string
    errors?: string[]
    code?: string
    description?: string
  }
}
```

### Error Handling in Components

```typescript
const { data, error } = useGetPropertiesQuery()

if (error) {
  console.log('Status:', error.status)
  console.log('Message:', error.data?.message)
  console.log('Errors:', error.data?.errors)
}
```

## Advanced Patterns

### Conditional Queries

```typescript
const { data } = useGetPropertyByIdQuery(
  propertyId,
  { skip: !propertyId } // Skip if no propertyId
)
```

### Polling

```typescript
const { data } = useGetPropertiesQuery(
  { page: 1 },
  { pollingInterval: 30000 } // Poll every 30 seconds
)
```

### Manual Trigger

```typescript
const [trigger, result] = useLazyGetPropertiesQuery()

const handleSearch = () => {
  trigger({ search: 'keyword' })
}
```

## Best Practices

1. **Use feature-specific API slices** for better organization
2. **Leverage RTK Query's caching** with proper tag invalidation
3. **Handle loading and error states** in your components
4. **Use TypeScript** for better type safety
5. **Choose the appropriate handler** based on your endpoint requirements

## Migration from Direct secureapi Usage

### Before (Direct axios)
```typescript
const fetchUser = async () => {
  try {
    const response = await secureapi.get('/users/me')
    setUser(response.data)
  } catch (error) {
    setError(error.message)
  }
}
```

### After (RTK Query)
```typescript
const { data: user, error } = useGetCurrentUserQuery()
```

## Troubleshooting

### Common Issues

1. **"baseUrl is required when using custom handler"**
   - Ensure you provide `baseUrl` when using `handler: 'custom'`

2. **Token not being sent**
   - Check that `accessToken` exists in localStorage
   - Verify the handler is properly configured

3. **CORS issues with custom handlers**
   - Ensure the external API supports CORS
   - Check the base URL is correct

### Debug Tips

- Use Redux DevTools to inspect API state
- Check Network tab for actual HTTP requests
- Enable RTK Query dev mode for detailed logging

## Examples

See `src/features/examples/ApiExamples.tsx` for comprehensive usage examples.
