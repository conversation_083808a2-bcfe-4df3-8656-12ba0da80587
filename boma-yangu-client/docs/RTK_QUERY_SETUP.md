# RTK Query Setup with Multiple API Handlers

This document explains how to use RTK Query in the boma-yangu-client with support for multiple API handlers including `secureapi` & `secureapi2`

## 🎉 Setup!
 The implementation provides:

✅ **Seamless integration** with existing `secureapi` handlers
✅ **Flexible handler selection** (secureapi, secureapi2, custom)
✅ **Consistent error handling** across all handlers
✅ **Type safety** with TypeScript
✅ **Automatic token management** and refresh
✅ **Comprehensive testing** with unit tests
✅ **Real-world examples** and documentation

## 🚀 Quick Start

### 1. Import and Use in Components

```typescript
import { useGetCurrentUserQuery, useCreatePropertyMutation } from '@/store/api/apiSlice'

const MyComponent = () => {
  // Query with secureapi (default)
  const { data: user, isLoading, error } = useGetCurrentUserQuery()

  // Mutation with secureapi2
  const [createProperty] = useCreatePropertyMutation()

  return (
    <div>
      {isLoading ? 'Loading...' : `Welcome ${user?.name}`}
    </div>
  )
}
```

### 2. Use secureapi2 Handler

```typescript
import { useGetCustomersQuery } from '@/store/api/customersApi'

const CustomersComponent = () => {
  const { data: customers, isLoading, error } = useGetCustomersQuery({
    page: 0,
    size: 10
  })

  if (isLoading) return <div>Loading customers...</div>
  if (error) return <div>Error loading customers</div>

  return (
    <div>
      <h3>Customers ({customers?.totalElements})</h3>
      {customers?.data.map(customer => (
        <div key={customer.id}>
          {customer.firstName} {customer.lastName} - {customer.email}
        </div>
      ))}
    </div>
  )
}
```

### 3. Create Feature-Specific APIs

```typescript
import { apiSlice } from '@/store/api/apiSlice'

export const userApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getProfile: builder.query({
      query: () => ({ url: '/profile', handler: 'secureapi2' }),
    }),
  }),
})

export const { useGetProfileQuery } = userApi
```

## Overview

Our RTK Query setup provides:
- **Compatibility with existing `secureapi` handlers** from the microfrontend
- **Dual handler support** (secureapi, secureapi2)
- **Consistent error handling** across all handlers
- **Type safety** with TypeScript
- **Automatic token management** and refresh
- **Real-world customer API** based on x247 implementation

## Architecture

### Files Structure
```
src/store/api/
├── baseQuery.ts      # Custom base query with handler support
├── apiSlice.ts       # Main API slice with examples
└── customersApi.ts   # Customer API slice (based on x247)
```

### Handler Types

1. **`secureapi`** (default): Uses the main secure API instance
2. **`secureapi2`**: Uses the secondary secure API instance

## Basic Usage

### 1. Simple Query with Default Handler (secureapi)

```typescript
import { useGetCurrentUserQuery } from '@/store/api/apiSlice'

const MyComponent = () => {
  const { data, error, isLoading } = useGetCurrentUserQuery()
  
  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {error.data?.message}</div>
  
  return <div>Welcome, {data?.name}</div>
}
```

### 2. Query with secureapi2 Handler

```typescript
const { data: customers } = useGetCustomersQuery({
  page: 0,
  size: 10
  // This endpoint uses secureapi2 as defined in the slice
})
```

### 3. Customer Details Query

```typescript
const { data: customer } = useGetCustomerByIdQuery('customer-id')
```

## Creating Feature-Specific API Slices

### Step 1: Define Types

```typescript
interface ICustomer {
  id: string
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  // ... other fields
}

interface ICustomersFilter {
  page: number
  size: number
  firstName?: string
  email?: string
  // ... other filters
}
```

### Step 2: Extend the Main API Slice

```typescript
import { apiSlice } from './apiSlice'

export const customersApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCustomers: builder.query<ICustomersDataResponse, ICustomersFilter>({
      query: (filters) => {
        let url = '/dbp/customers?'
        url += `page=${filters.page}&size=${filters.size}`
        if (filters.firstName) url += `&firstName=${filters.firstName}`
        // ... build query parameters

        return {
          url,
          method: 'GET',
          handler: 'secureapi2', // Specify handler
        }
      },
      providesTags: ['Customer'],
    }),
  }),
})

export const { useGetCustomersQuery } = customersApi
```

## Handler Configuration

### Query Arguments

All endpoints accept these parameters:

```typescript
interface CustomQueryArgs {
  url: string                    // Required: API endpoint
  method?: 'GET' | 'POST' | ... // Optional: HTTP method (default: GET)
  body?: any                     // Optional: Request body
  params?: Record<string, any>   // Optional: Query parameters
  headers?: Record<string, string> // Optional: Custom headers
  handler?: 'secureapi' | 'secureapi2' // Optional: Handler type
}
```

### Examples by Handler Type

#### Using secureapi (default)
```typescript
query: () => ({
  url: '/users/me',
  method: 'GET',
  // handler defaults to 'secureapi'
})
```

#### Using secureapi2
```typescript
query: (id) => ({
  url: `/properties/${id}`,
  method: 'GET',
  handler: 'secureapi2',
})
```

#### Using customer API pattern
```typescript
query: (filters) => {
  let url = '/dbp/customers?'
  url += `page=${filters.page}&size=${filters.size}`
  // Build query parameters dynamically

  return {
    url,
    method: 'GET',
    handler: 'secureapi2',
  }
}
```

## Mutations

### Basic Mutation

```typescript
const [updateCustomer, { isLoading, error }] = useUpdateCustomerMutation()

const handleUpdate = async () => {
  try {
    await updateCustomer({
      profileId: 'customer-id',
      email: '<EMAIL>',
      comments: 'Updated via RTK Query'
    }).unwrap()
    // Success handling
  } catch (error) {
    // Error handling
  }
}
```

### Customer Deactivation

```typescript
const [deactivateCustomer] = useDeactivateCustomerMutation()

const handleDeactivate = async (customerId: string, reason: string) => {
  try {
    await deactivateCustomer({
      profileId: customerId,
      reason: reason
    }).unwrap()
    // Customer deactivated successfully
  } catch (error) {
    // Handle error
  }
}
```

## Error Handling

All handlers provide consistent error format:

```typescript
interface CustomError {
  status: number
  data: {
    message?: string
    error?: string
    errors?: string[]
    code?: string
    description?: string
  }
}
```

### Error Handling in Components

```typescript
const { data: customers, error } = useGetCustomersQuery({ page: 0, size: 10 })

if (error) {
  console.log('Status:', error.status)
  console.log('Message:', error.data?.message)
  console.log('Errors:', error.data?.errors)
}
```

## Advanced Patterns

### Conditional Queries

```typescript
const { data: customer } = useGetCustomerByIdQuery(
  customerId,
  { skip: !customerId } // Skip if no customerId
)
```

### Polling

```typescript
const { data: customers } = useGetCustomersQuery(
  { page: 0, size: 10 },
  { pollingInterval: 30000 } // Poll every 30 seconds
)
```

### Manual Trigger

```typescript
const [trigger, result] = useLazyGetCustomersQuery()

const handleSearch = () => {
  trigger({ page: 0, size: 10, firstName: 'John' })
}
```

## Best Practices

1. **Use feature-specific API slices** for better organization
2. **Leverage RTK Query's caching** with proper tag invalidation
3. **Handle loading and error states** in your components
4. **Use TypeScript** for better type safety
5. **Choose the appropriate handler** based on your endpoint requirements

## Migration from Direct secureapi Usage

### Before (Direct secureapi2)
```typescript
const fetchCustomers = async () => {
  try {
    const response = await secureapi2.get('/dbp/customers?page=0&size=10')
    setCustomers(response.data)
    setLoading(false)
  } catch (error) {
    setError(error.message)
    setLoading(false)
  }
}
```

### After (RTK Query)
```typescript
const { data: customers, isLoading, error } = useGetCustomersQuery({
  page: 0,
  size: 10
})
```

## Troubleshooting

### Common Issues

1. **Customer data not loading**
   - Verify the customer endpoint `/dbp/customers` is accessible
   - Check that `secureapi2` is properly configured

2. **Token not being sent**
   - Check that `accessToken` exists in localStorage
   - Verify the handler is properly configured

3. **Pagination not working**
   - Ensure page numbers start from 0 (not 1)
   - Check size parameter is within allowed limits

### Debug Tips

- Use Redux DevTools to inspect API state
- Check Network tab for actual HTTP requests
- Enable RTK Query dev mode for detailed logging

## Examples

See `src/features/examples/ApiExamples.tsx` for comprehensive usage examples including:
- Customer list display with pagination
- Individual customer details lookup
- Customer update mutations
- Error handling patterns
- Loading state management

## Customer API Features

The customer API implementation includes:

### Available Endpoints
- `getCustomers` - Paginated customer list with filtering
- `getCustomerById` - Individual customer details
- `updateCustomer` - Update customer information
- `deactivateCustomer` - Deactivate customer account
- `fetchCustomerAccount` - Get customer by account number
- `getLinkedCustomerAccounts` - Get linked customer accounts

### Filter Options
```typescript
interface ICustomersFilter {
  page: number
  size: number
  firstName?: string
  lastName?: string
  email?: string
  phoneNumber?: string
  nationality?: string
  idNumber?: string
  accountNumber?: string
  dateCreatedFrom?: string
  dateCreatedTo?: string
  isBlocked?: string
}
```

### Response Structure
```typescript
interface ICustomersDataResponse {
  data: ICustomer[]
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
}
```
