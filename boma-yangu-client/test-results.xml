<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="119" failures="0" errors="0" time="0.978898751">
    <testsuite name="src/__tests__/basic.test.ts" timestamp="2025-07-18T13:14:28.506Z" hostname="HODEVMAC27.local" tests="4" failures="0" errors="0" skipped="0" time="0.004606542">
        <testcase classname="src/__tests__/basic.test.ts" name="Basic Test Setup &gt; should run a basic test" time="0.001611709">
        </testcase>
        <testcase classname="src/__tests__/basic.test.ts" name="Basic Test Setup &gt; should handle string operations" time="0.000657459">
        </testcase>
        <testcase classname="src/__tests__/basic.test.ts" name="Basic Test Setup &gt; should handle array operations" time="0.000670833">
        </testcase>
        <testcase classname="src/__tests__/basic.test.ts" name="Basic Test Setup &gt; should handle object operations" time="0.000220625">
        </testcase>
    </testsuite>
    <testsuite name="src/app/__tests__/Error.test.tsx" timestamp="2025-07-18T13:14:28.507Z" hostname="HODEVMAC27.local" tests="5" failures="0" errors="0" skipped="0" time="0.047656708">
        <testcase classname="src/app/__tests__/Error.test.tsx" name="Error Component &gt; renders CustomError with error and reset props" time="0.018076416">
        </testcase>
        <testcase classname="src/app/__tests__/Error.test.tsx" name="Error Component &gt; calls reset function when reset button is clicked" time="0.012747917">
        </testcase>
        <testcase classname="src/app/__tests__/Error.test.tsx" name="Error Component &gt; handles string error" time="0.010169583">
        </testcase>
        <testcase classname="src/app/__tests__/Error.test.tsx" name="Error Component &gt; handles unknown error types" time="0.003149334">
        </testcase>
        <testcase classname="src/app/__tests__/Error.test.tsx" name="Error Component &gt; renders without crashing with null error" time="0.002436625">
        </testcase>
    </testsuite>
    <testsuite name="src/app/__tests__/layout.test.tsx" timestamp="2025-07-18T13:14:28.508Z" hostname="HODEVMAC27.local" tests="13" failures="0" errors="0" skipped="0" time="0.154276542">
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; RootLayout Structure &gt; renders the basic HTML structure with correct attributes" time="0.062537667">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; RootLayout Structure &gt; renders all provider components in correct order" time="0.020838584">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; RootLayout Structure &gt; passes correct props to ThemeConfig" time="0.013464958">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; RootLayout Structure &gt; renders children content" time="0.01932">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; DashboardLayout Components &gt; renders sidebar with correct props" time="0.005387042">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; DashboardLayout Components &gt; renders internal navigation bar" time="0.006872833">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; DashboardLayout Components &gt; renders local notification component" time="0.003407959">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; DashboardLayout Components &gt; displays user profile information in sidebar" time="0.002694083">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; Redux Integration &gt; uses Redux selectors correctly" time="0.004531166">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; Redux Integration &gt; handles collapsed sidebar state" time="0.003399125">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; Redux Integration &gt; displays notifications when present" time="0.003086">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; Authentication Integration &gt; passes authentication state to components" time="0.00399675">
        </testcase>
        <testcase classname="src/app/__tests__/layout.test.tsx" name="Layout Components - Component Isolation &gt; Authentication Integration &gt; handles authentication state properly" time="0.002366833">
        </testcase>
    </testsuite>
    <testsuite name="src/app/__tests__/sidebar.test.tsx" timestamp="2025-07-18T13:14:28.510Z" hostname="HODEVMAC27.local" tests="14" failures="0" errors="0" skipped="0" time="0.0185885">
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should export sidebarConfig as an array" time="0.001867">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have correct number of sidebar items" time="0.001707334">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have home sidebar item with correct properties" time="0.000937125">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have onboarding sidebar item with correct properties" time="0.001269791">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have unique IDs for all items" time="0.000535375">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have unique paths for all items" time="0.001213916">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have all items marked as production ready" time="0.000245625">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have all items belong to Boma module" time="0.000287792">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have valid path format for all items" time="0.000418333">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have non-empty titles for all items" time="0.000843208">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have React elements as icons" time="0.000462875">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should match ISidebarConfigItem interface structure" time="0.001106333">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should be ordered correctly (home first, then onboarding)" time="0.000475791">
        </testcase>
        <testcase classname="src/app/__tests__/sidebar.test.tsx" name="Sidebar Configuration &gt; should have consistent casing in titles" time="0.004035791">
        </testcase>
    </testsuite>
    <testsuite name="src/store/__tests__/AppProvider.test.tsx" timestamp="2025-07-18T13:14:28.511Z" hostname="HODEVMAC27.local" tests="6" failures="0" errors="0" skipped="0" time="0.047424167">
        <testcase classname="src/store/__tests__/AppProvider.test.tsx" name="AppProvider &gt; renders children correctly" time="0.02713925">
        </testcase>
        <testcase classname="src/store/__tests__/AppProvider.test.tsx" name="AppProvider &gt; provides Redux store to children" time="0.002989167">
        </testcase>
        <testcase classname="src/store/__tests__/AppProvider.test.tsx" name="AppProvider &gt; renders without crashing" time="0.00310175">
        </testcase>
        <testcase classname="src/store/__tests__/AppProvider.test.tsx" name="AppProvider &gt; wraps children with Provider component" time="0.001097416">
        </testcase>
        <testcase classname="src/store/__tests__/AppProvider.test.tsx" name="AppProvider &gt; handles multiple children" time="0.004495458">
        </testcase>
        <testcase classname="src/store/__tests__/AppProvider.test.tsx" name="AppProvider &gt; accepts React.ReactNode as children prop" time="0.007628584">
        </testcase>
    </testsuite>
    <testsuite name="src/store/__tests__/index.test.tsx" timestamp="2025-07-18T13:14:28.511Z" hostname="HODEVMAC27.local" tests="7" failures="0" errors="0" skipped="0" time="0.006343667">
        <testcase classname="src/store/__tests__/index.test.tsx" name="Store Configuration &gt; should create store with correct configuration" time="0.001724875">
        </testcase>
        <testcase classname="src/store/__tests__/index.test.tsx" name="Store Configuration &gt; should have correct initial state structure" time="0.000794125">
        </testcase>
        <testcase classname="src/store/__tests__/index.test.tsx" name="Store Configuration &gt; should export correct TypeScript types" time="0.000215459">
        </testcase>
        <testcase classname="src/store/__tests__/index.test.tsx" name="Store Configuration &gt; should configure middleware correctly" time="0.000970667">
        </testcase>
        <testcase classname="src/store/__tests__/index.test.tsx" name="Store Configuration &gt; should handle serializable check configuration" time="0.000292542">
        </testcase>
        <testcase classname="src/store/__tests__/index.test.tsx" name="Store Configuration &gt; should create a new store instance with configureStore" time="0.000190459">
        </testcase>
        <testcase classname="src/store/__tests__/index.test.tsx" name="Store Configuration &gt; should maintain state consistency" time="0.000709041">
        </testcase>
    </testsuite>
    <testsuite name="src/test-utils/__tests__/mocks.test.ts" timestamp="2025-07-18T13:14:28.512Z" hostname="HODEVMAC27.local" tests="18" failures="0" errors="0" skipped="0" time="0.006877667">
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockDtbxComponents &gt; should provide mock functions for all DTBX components" time="0.0019015">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockDtbxComponents &gt; should return children for wrapper components" time="0.000322167">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockDtbxComponents &gt; should return null for non-wrapper components" time="0.000119791">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockThemeComponents &gt; should provide mock functions for theme components" time="0.000162958">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockThemeComponents &gt; should return children for theme wrapper components" time="0.000093584">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockIcons &gt; should provide mock functions for icon components" time="0.000107541">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockIcons &gt; should return null for icon components" time="0.000157083">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockStoreHooks &gt; should provide mock functions for store hooks" time="0.000095167">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockStoreHooks &gt; should return a function from useAppDispatch" time="0.000164125">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockStoreActions &gt; should provide mock functions for store actions" time="0.000130709">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockStoreUtils &gt; should provide mock functions for store utilities" time="0.00006925">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; mockStoreUtils &gt; should return true by default for isLoggedIn" time="0.000053083">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; setupMocks &gt; should be a function" time="0.000069083">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; setupMocks &gt; should execute without throwing" time="0.000389">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; resetMocks &gt; should be a function" time="0.000094291">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; resetMocks &gt; should execute without throwing" time="0.00034225">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; resetMocks &gt; should reset all mock functions" time="0.000425708">
        </testcase>
        <testcase classname="src/test-utils/__tests__/mocks.test.ts" name="Mock Utilities &gt; Mock Integration &gt; should work together as a complete mocking system" time="0.000384125">
        </testcase>
    </testsuite>
    <testsuite name="src/test-utils/__tests__/setup.test.ts" timestamp="2025-07-18T13:14:28.513Z" hostname="HODEVMAC27.local" tests="15" failures="0" errors="0" skipped="0" time="0.013529458">
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Global Mocks &gt; should mock window.matchMedia" time="0.001294375">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Global Mocks &gt; should mock IntersectionObserver" time="0.0011865">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Global Mocks &gt; should mock ResizeObserver" time="0.000287125">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Browser API Mocks &gt; should provide functional matchMedia mock" time="0.000232">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Browser API Mocks &gt; should allow matchMedia event listeners" time="0.000905084">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Browser API Mocks &gt; should allow IntersectionObserver operations" time="0.000712083">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Browser API Mocks &gt; should allow ResizeObserver operations" time="0.000728416">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Console Mocks &gt; should mock console.warn and console.error" time="0.00028825">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Console Mocks &gt; should suppress console output in tests" time="0.001126792">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Test Environment &gt; should have jsdom environment available" time="0.000363708">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Test Environment &gt; should support DOM operations" time="0.002880792">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Test Environment &gt; should support window properties" time="0.0003595">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Mock Functionality &gt; should create working mock functions" time="0.000359542">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Mock Functionality &gt; should support mock implementations" time="0.000179042">
        </testcase>
        <testcase classname="src/test-utils/__tests__/setup.test.ts" name="Test Setup Configuration &gt; Mock Functionality &gt; should support mock return values" time="0.000087167">
        </testcase>
    </testsuite>
    <testsuite name="src/test-utils/__tests__/test-utils.test.tsx" timestamp="2025-07-18T13:14:28.514Z" hostname="HODEVMAC27.local" tests="9" failures="0" errors="0" skipped="0" time="0.004605958">
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; createTestStore &gt; should create a store with default state" time="0.001434333">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; createTestStore &gt; should create a store with custom initial state" time="0.0002975">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; createTestStore &gt; should allow dispatching actions" time="0.000742625">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; Mock Data &gt; should provide mockUser with correct structure" time="0.000203583">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; Mock Data &gt; should provide mockAuthState with correct structure" time="0.000374">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; Mock Data &gt; should have consistent mock data" time="0.000078708">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; Mock Data &gt; should provide mockUser with correct structure" time="0.000150583">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; Mock Data &gt; should provide mockAuthState with correct structure" time="0.000255083">
        </testcase>
        <testcase classname="src/test-utils/__tests__/test-utils.test.tsx" name="Test Utils &gt; Mock Data &gt; should have consistent mock data" time="0.000193792">
        </testcase>
    </testsuite>
    <testsuite name="src/app/onboarding/__tests__/page.test.tsx" timestamp="2025-07-18T13:14:28.514Z" hostname="HODEVMAC27.local" tests="4" failures="0" errors="0" skipped="0" time="0.169769208">
        <testcase classname="src/app/onboarding/__tests__/page.test.tsx" name="Onboarding Page Route &gt; renders the OnboardingPage component" time="0.090955958">
        </testcase>
        <testcase classname="src/app/onboarding/__tests__/page.test.tsx" name="Onboarding Page Route &gt; renders without crashing" time="0.027453625">
        </testcase>
        <testcase classname="src/app/onboarding/__tests__/page.test.tsx" name="Onboarding Page Route &gt; displays all OnboardingPage content" time="0.024203584">
        </testcase>
        <testcase classname="src/app/onboarding/__tests__/page.test.tsx" name="Onboarding Page Route &gt; has proper component structure" time="0.025781417">
        </testcase>
    </testsuite>
    <testsuite name="src/app/home/<USER>/page.test.tsx" timestamp="2025-07-18T13:14:28.515Z" hostname="HODEVMAC27.local" tests="3" failures="0" errors="0" skipped="0" time="0.097101333">
        <testcase classname="src/app/home/<USER>/page.test.tsx" name="Home Page &gt; renders the HomePage component" time="0.074270042">
        </testcase>
        <testcase classname="src/app/home/<USER>/page.test.tsx" name="Home Page &gt; renders without crashing" time="0.011005875">
        </testcase>
        <testcase classname="src/app/home/<USER>/page.test.tsx" name="Home Page &gt; displays all HomePage content" time="0.010819333">
        </testcase>
    </testsuite>
    <testsuite name="src/features/home/<USER>/HomePage.test.tsx" timestamp="2025-07-18T13:14:28.515Z" hostname="HODEVMAC27.local" tests="6" failures="0" errors="0" skipped="0" time="0.180112583">
        <testcase classname="src/features/home/<USER>/HomePage.test.tsx" name="HomePage &gt; renders the main heading" time="0.139825084">
        </testcase>
        <testcase classname="src/features/home/<USER>/HomePage.test.tsx" name="HomePage &gt; renders the font test description" time="0.010378542">
        </testcase>
        <testcase classname="src/features/home/<USER>/HomePage.test.tsx" name="HomePage &gt; renders all typography variants" time="0.010990417">
        </testcase>
        <testcase classname="src/features/home/<USER>/HomePage.test.tsx" name="HomePage &gt; has proper structure with Stack container" time="0.00360725">
        </testcase>
        <testcase classname="src/features/home/<USER>/HomePage.test.tsx" name="HomePage &gt; renders all text content correctly" time="0.003967458">
        </testcase>
        <testcase classname="src/features/home/<USER>/HomePage.test.tsx" name="HomePage &gt; has accessible heading structure" time="0.010075625">
        </testcase>
    </testsuite>
    <testsuite name="src/features/onboarding/__tests__/OnboardingPage.test.tsx" timestamp="2025-07-18T13:14:28.515Z" hostname="HODEVMAC27.local" tests="8" failures="0" errors="0" skipped="0" time="0.224674834">
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; renders the main heading" time="0.151673125">
        </testcase>
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; renders the font test description" time="0.0141845">
        </testcase>
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; renders all typography variants" time="0.022677666">
        </testcase>
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; has proper structure with Stack container and padding" time="0.007317209">
        </testcase>
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; renders all text content correctly" time="0.009164917">
        </testcase>
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; has accessible heading structure" time="0.011354375">
        </testcase>
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; has proper container styling with padding" time="0.002662791">
        </testcase>
        <testcase classname="src/features/onboarding/__tests__/OnboardingPage.test.tsx" name="OnboardingPage &gt; displays font testing content in correct order" time="0.002206541">
        </testcase>
    </testsuite>
    <testsuite name="src/store/reducers/__tests__/store.test.ts" timestamp="2025-07-18T13:14:28.516Z" hostname="HODEVMAC27.local" tests="7" failures="0" errors="0" skipped="0" time="0.003331584">
        <testcase classname="src/store/reducers/__tests__/store.test.ts" name="Root Reducer &gt; should combine all reducers correctly" time="0.00091275">
        </testcase>
        <testcase classname="src/store/reducers/__tests__/store.test.ts" name="Root Reducer &gt; should have correct initial state structure" time="0.000539875">
        </testcase>
        <testcase classname="src/store/reducers/__tests__/store.test.ts" name="Root Reducer &gt; should handle auth actions" time="0.00043625">
        </testcase>
        <testcase classname="src/store/reducers/__tests__/store.test.ts" name="Root Reducer &gt; should handle navigation actions" time="0.000174">
        </testcase>
        <testcase classname="src/store/reducers/__tests__/store.test.ts" name="Root Reducer &gt; should handle notification actions" time="0.000149541">
        </testcase>
        <testcase classname="src/store/reducers/__tests__/store.test.ts" name="Root Reducer &gt; should handle overlay actions" time="0.000124125">
        </testcase>
        <testcase classname="src/store/reducers/__tests__/store.test.ts" name="Root Reducer &gt; should not mutate state" time="0.00024975">
        </testcase>
    </testsuite>
</testsuites>
