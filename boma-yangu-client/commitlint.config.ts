import type { UserConfig } from "@commitlint/types";
import { RuleConfigSeverity } from "@commitlint/types";

const Configuration: UserConfig = {
  // using Conventional Commits 1.0.0 specifications by Angular
  extends: ["@commitlint/config-conventional"],
  rules: {
    "scope-enum": [RuleConfigSeverity.Disabled],
    "scope-empty": [RuleConfigSeverity.Error, "never"],
  },
};

export default Configuration;
