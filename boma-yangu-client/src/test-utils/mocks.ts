import { vi } from "vitest";

// Mock @dtbx/ui components
export const mockDtbxComponents = {
  AuthWrapper: vi.fn(({ children }: { children: React.ReactNode }) => children),
  CustomScrollbar: vi.fn(
    ({ children }: { children: React.ReactNode }) => children,
  ),
  InActivity: vi.fn(({ children }: { children: React.ReactNode }) => children),
  LocalNotification: vi.fn(() => null),
  Sidebar: vi.fn(() => null),
  InternalNavBar: vi.fn(() => null),
};

// Mock @dtbx/ui/theme
export const mockThemeComponents = {
  NextAppDirEmotionCacheProvider: vi.fn(
    ({ children }: { children: React.ReactNode }) => children,
  ),
  ThemeConfig: vi.fn(({ children }: { children: React.ReactNode }) => children),
};

// Mock @dtbx/ui/icons
export const mockIcons = {
  HomeIcon: vi.fn(() => null),
  UserProfileIcon: vi.fn(() => null),
};

// Mock @dtbx/store hooks
export const mockStoreHooks = {
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(() => vi.fn()),
};

// Mock @dtbx/store actions
export const mockStoreActions = {
  refreshToken: vi.fn(),
  clearNotification: vi.fn(),
  setSidebarCollapsed: vi.fn(),
};

// Mock @dtbx/store utils
export const mockStoreUtils = {
  isLoggedIn: vi.fn(() => true),
};

// Setup all mocks
export const setupMocks = () => {
  // Mock @dtbx/ui/components
  vi.mock("@dtbx/ui/components", () => mockDtbxComponents);

  // Mock @dtbx/ui/theme
  vi.mock("@dtbx/ui/theme", () => mockThemeComponents);

  // Mock @dtbx/ui/icons
  vi.mock("@dtbx/ui/icons", () => mockIcons);

  // Mock @dtbx/store
  vi.mock("@dtbx/store", () => mockStoreHooks);

  // Mock @dtbx/store/actions
  vi.mock("@dtbx/store/actions", () => mockStoreActions);

  // Mock @dtbx/store/reducers
  vi.mock("@dtbx/store/reducers", () => ({
    clearNotification: mockStoreActions.clearNotification,
    setSidebarCollapsed: mockStoreActions.setSidebarCollapsed,
  }));

  // Mock @dtbx/store/utils
  vi.mock("@dtbx/store/utils", () => mockStoreUtils);
};

// Reset all mocks
export const resetMocks = () => {
  vi.clearAllMocks();
  Object.values(mockStoreHooks).forEach((mock) => mock.mockReset());
  Object.values(mockStoreActions).forEach((mock) => mock.mockReset());
  Object.values(mockStoreUtils).forEach((mock) => mock.mockReset());
};
