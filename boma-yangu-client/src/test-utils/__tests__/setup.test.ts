import { describe, it, expect, vi, beforeEach } from "vitest";

describe("Test Setup Configuration", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Global Mocks", () => {
    it("should mock window.matchMedia", () => {
      expect(window.matchMedia).toBeDefined();

      const mediaQuery = window.matchMedia("(max-width: 768px)");
      expect(mediaQuery).toHaveProperty("matches");
      expect(mediaQuery).toHaveProperty("media");
      expect(mediaQuery).toHaveProperty("addListener");
      expect(mediaQuery).toHaveProperty("removeListener");
      expect(mediaQuery).toHaveProperty("addEventListener");
      expect(mediaQuery).toHaveProperty("removeEventListener");
      expect(mediaQuery).toHaveProperty("dispatchEvent");
    });

    it("should mock IntersectionObserver", () => {
      expect(global.IntersectionObserver).toBeDefined();

      const observer = new IntersectionObserver(() => {});
      expect(observer).toHaveProperty("observe");
      expect(observer).toHaveProperty("unobserve");
      expect(observer).toHaveProperty("disconnect");

      expect(typeof observer.observe).toBe("function");
      expect(typeof observer.unobserve).toBe("function");
      expect(typeof observer.disconnect).toBe("function");
    });

    it("should mock ResizeObserver", () => {
      expect(global.ResizeObserver).toBeDefined();

      const observer = new ResizeObserver(() => {});
      expect(observer).toHaveProperty("observe");
      expect(observer).toHaveProperty("unobserve");
      expect(observer).toHaveProperty("disconnect");

      expect(typeof observer.observe).toBe("function");
      expect(typeof observer.unobserve).toBe("function");
      expect(typeof observer.disconnect).toBe("function");
    });
  });

  describe("Browser API Mocks", () => {
    it("should provide functional matchMedia mock", () => {
      const mediaQuery = "(max-width: 768px)";
      const result = window.matchMedia(mediaQuery);

      expect(result.matches).toBe(false);
      expect(result.media).toBe(mediaQuery);
      expect(result.onchange).toBe(null);
    });

    it("should allow matchMedia event listeners", () => {
      const mediaQuery = window.matchMedia("(max-width: 768px)");
      const listener = vi.fn();

      expect(() => {
        mediaQuery.addEventListener("change", listener);
        mediaQuery.removeEventListener("change", listener);
        mediaQuery.addListener(listener);
        mediaQuery.removeListener(listener);
        mediaQuery.dispatchEvent(new Event("change"));
      }).not.toThrow();
    });

    it("should allow IntersectionObserver operations", () => {
      const callback = vi.fn();
      const observer = new IntersectionObserver(callback);
      const element = document.createElement("div");

      expect(() => {
        observer.observe(element);
        observer.unobserve(element);
        observer.disconnect();
      }).not.toThrow();
    });

    it("should allow ResizeObserver operations", () => {
      const callback = vi.fn();
      const observer = new ResizeObserver(callback);
      const element = document.createElement("div");

      expect(() => {
        observer.observe(element);
        observer.unobserve(element);
        observer.disconnect();
      }).not.toThrow();
    });
  });

  describe("Console Mocks", () => {
    it("should mock console.warn and console.error", () => {
      // These are mocked in beforeEach, so they should be mock functions
      expect(vi.isMockFunction(console.warn)).toBe(true);
      expect(vi.isMockFunction(console.error)).toBe(true);
    });

    it("should suppress console output in tests", () => {
      console.warn("This warning should be suppressed");
      console.error("This error should be suppressed");

      expect(console.warn).toHaveBeenCalledWith(
        "This warning should be suppressed",
      );
      expect(console.error).toHaveBeenCalledWith(
        "This error should be suppressed",
      );
    });
  });

  describe("Test Environment", () => {
    it("should have jsdom environment available", () => {
      expect(document).toBeDefined();
      expect(window).toBeDefined();
      expect(global).toBeDefined();
    });

    it("should support DOM operations", () => {
      const element = document.createElement("div");
      element.textContent = "Test content";
      document.body.appendChild(element);

      expect(element.textContent).toBe("Test content");
      expect(document.body.contains(element)).toBe(true);

      // Cleanup
      document.body.removeChild(element);
    });

    it("should support window properties", () => {
      expect(window.location).toBeDefined();
      expect(window.document).toBeDefined();
      expect(window.navigator).toBeDefined();
    });
  });

  describe("Mock Functionality", () => {
    it("should create working mock functions", () => {
      const mockFn = vi.fn();
      mockFn("test");

      expect(mockFn).toHaveBeenCalledWith("test");
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it("should support mock implementations", () => {
      const mockFn = vi.fn().mockImplementation((x: number) => x * 2);
      const result = mockFn(5);

      expect(result).toBe(10);
      expect(mockFn).toHaveBeenCalledWith(5);
    });

    it("should support mock return values", () => {
      const mockFn = vi.fn().mockReturnValue("mocked value");
      const result = mockFn();

      expect(result).toBe("mocked value");
    });
  });
});
