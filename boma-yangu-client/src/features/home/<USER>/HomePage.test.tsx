import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import HomePage from "../HomePage";

// Create a simple test theme
const testTheme = createTheme();

// Simple wrapper for Material-UI components
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={testTheme}>{children}</ThemeProvider>
);

describe("HomePage", () => {
  it("renders the main heading", () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>,
    );

    const heading = screen.getByRole("heading", { level: 1 });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent("Boma Yangu Home Page");
  });

  it("renders the font test description", () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>,
    );

    const description = screen.getByText(/This is a test of the BlissPro font/);
    expect(description).toBeInTheDocument();
  });

  it("renders all typography variants", () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>,
    );

    // Check for h1 heading
    expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent(
      "Boma Yangu Home Page",
    );

    // Check for h6 heading by text content since there are multiple h6 elements
    expect(screen.getByText("Font Test - Heading 6")).toBeInTheDocument();

    // Check for body text
    expect(screen.getByText("Font Test - Subtitle 1")).toBeInTheDocument();
    expect(screen.getByText("Font Test - Body 2 text")).toBeInTheDocument();
  });

  it("has proper structure with Stack container", () => {
    const { container } = render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>,
    );

    // Check that the component renders without crashing
    expect(container.firstChild).toBeInTheDocument();
  });

  it("renders all text content correctly", () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>,
    );

    // Verify all expected text is present
    expect(screen.getByText("Boma Yangu Home Page")).toBeInTheDocument();
    expect(
      screen.getByText(/This is a test of the BlissPro font/),
    ).toBeInTheDocument();
    expect(screen.getByText("Font Test - Heading 6")).toBeInTheDocument();
    expect(screen.getByText("Font Test - Subtitle 1")).toBeInTheDocument();
    expect(screen.getByText("Font Test - Body 2 text")).toBeInTheDocument();
  });

  it("has accessible heading structure", () => {
    render(
      <TestWrapper>
        <HomePage />
      </TestWrapper>,
    );

    const headings = screen.getAllByRole("heading");
    expect(headings.length).toBeGreaterThanOrEqual(2); // h1 and at least one h6

    // Check that we have an h1 heading
    const h1Heading = screen.getByRole("heading", { level: 1 });
    expect(h1Heading).toHaveProperty("tagName", "H1");
    expect(h1Heading).toHaveTextContent("Boma Yangu Home Page");
  });
});
