'use client'

import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Alert,
  CircularProgress,
  Text<PERSON>ield,
  Stack,
} from '@mui/material'

// Import the API hooks
import {
  useGetCurrentUserQuery,
  useGetPropertiesQuery,
  useGetExternalDataQuery,
  useUpdateUserMutation,
  useCreatePropertyMutation,
} from '@/store/api/apiSlice'

import {
  useGetPropertyByIdQuery,
  useCreatePropertyMutation as useCreatePropertyFromPropertyApi,
} from '@/store/api/propertyApi'

// Helper function to check if error has data property
const hasErrorData = (error: any): error is { data: { message?: string } } => {
  return error && typeof error === 'object' && 'data' in error
}

const ApiExamples: React.FC = () => {
  const [externalApiUrl, setExternalApiUrl] = useState('https://jsonplaceholder.typicode.com')
  const [externalEndpoint, setExternalEndpoint] = useState('/posts/1')
  const [propertyId, setPropertyId] = useState('123')

  // Example 1: Using secureapi (default handler)
  const {
    data: currentUser,
    error: userError,
    isLoading: userLoading,
  } = useGetCurrentUserQuery()

  // Example 2: Using secureapi2 handler
  const {
    data: properties,
    error: propertiesError,
    isLoading: propertiesLoading,
  } = useGetPropertiesQuery({ page: 1, limit: 10 })

  // Example 3: Using custom handler with external API
  const {
    data: externalData,
    error: externalError,
    isLoading: externalLoading,
    refetch: refetchExternal,
  } = useGetExternalDataQuery(
    { endpoint: externalEndpoint, baseUrl: externalApiUrl },
    { skip: !externalApiUrl || !externalEndpoint }
  )

  // Example 4: Using property-specific API with secureapi2
  const {
    data: propertyDetails,
    error: propertyError,
    isLoading: propertyLoading,
  } = useGetPropertyByIdQuery(propertyId, { skip: !propertyId })

  // Mutations
  const [updateUser] = useUpdateUserMutation()
  const [createProperty] = useCreatePropertyMutation()

  const handleUpdateUser = async () => {
    try {
      await updateUser({
        id: 'current-user-id',
        data: { name: 'Updated Name' },
      }).unwrap()
      alert('User updated successfully!')
    } catch (error) {
      console.error('Failed to update user:', error)
      alert('Failed to update user')
    }
  }

  const handleCreateProperty = async () => {
    try {
      await createProperty({
        title: 'New Property',
        description: 'A beautiful property',
        price: 500000,
        location: 'Nairobi',
      }).unwrap()
      alert('Property created successfully!')
    } catch (error) {
      console.error('Failed to create property:', error)
      alert('Failed to create property')
    }
  }

  const handleTestExternalApi = () => {
    refetchExternal()
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        RTK Query API Examples
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This page demonstrates how to use RTK Query with different API handlers:
        secureapi, secureapi2, and custom handlers.
      </Typography>

      <Stack spacing={3}>
        {/* Example 1: secureapi (default) */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              1. Current User (secureapi)
            </Typography>
            {userLoading && <CircularProgress size={20} />}
            {userError && (
              <Alert severity="error">
                Error: {hasErrorData(userError) ? userError.data?.message : 'Failed to fetch user'}
              </Alert>
            )}
            {currentUser && (
              <Box>
                <Typography variant="body2">
                  User data loaded successfully!
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleUpdateUser}
                  sx={{ mt: 1 }}
                >
                  Update User
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Example 2: secureapi2 */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              2. Properties (secureapi2)
            </Typography>
            {propertiesLoading && <CircularProgress size={20} />}
            {propertiesError && (
              <Alert severity="error">
                Error: {hasErrorData(propertiesError) ? propertiesError.data?.message : 'Failed to fetch properties'}
              </Alert>
            )}
            {properties && (
              <Box>
                <Typography variant="body2">
                  Found {properties.data?.length || 0} properties
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleCreateProperty}
                  sx={{ mt: 1 }}
                >
                  Create Property
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Example 3: Custom handler */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              3. External API (Custom Handler)
            </Typography>
            <Box sx={{ mb: 2 }}>
              <TextField
                label="Base URL"
                value={externalApiUrl}
                onChange={(e) => setExternalApiUrl(e.target.value)}
                fullWidth
                size="small"
                sx={{ mb: 1 }}
              />
              <TextField
                label="Endpoint"
                value={externalEndpoint}
                onChange={(e) => setExternalEndpoint(e.target.value)}
                fullWidth
                size="small"
              />
            </Box>
            <Button
              variant="contained"
              onClick={handleTestExternalApi}
              disabled={!externalApiUrl || !externalEndpoint}
              sx={{ mb: 2 }}
            >
              Test External API
            </Button>
            {externalLoading && <CircularProgress size={20} />}
            {externalError && (
              <Alert severity="error">
                Error: {hasErrorData(externalError) ? externalError.data?.message : 'Failed to fetch external data'}
              </Alert>
            )}
            {externalData && (
              <Alert severity="success">
                External API call successful! Check console for data.
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Example 4: Property details with secureapi2 */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              4. Property Details (secureapi2 via propertyApi)
            </Typography>
            <TextField
              label="Property ID"
              value={propertyId}
              onChange={(e) => setPropertyId(e.target.value)}
              size="small"
              sx={{ mb: 2 }}
            />
            {propertyLoading && <CircularProgress size={20} />}
            {propertyError && (
              <Alert severity="error">
                Error: {hasErrorData(propertyError) ? propertyError.data?.message : 'Failed to fetch property'}
              </Alert>
            )}
            {propertyDetails && (
              <Alert severity="success">
                Property details loaded: {propertyDetails.title}
              </Alert>
            )}
          </CardContent>
        </Card>
      </Stack>

      {/* Debug information */}
      <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>
          Debug Information
        </Typography>
        <Typography variant="body2" component="pre">
          {JSON.stringify(
            {
              currentUser: !!currentUser,
              properties: !!properties,
              externalData: !!externalData,
              propertyDetails: !!propertyDetails,
            },
            null,
            2
          )}
        </Typography>
      </Box>
    </Box>
  )
}

export default ApiExamples
