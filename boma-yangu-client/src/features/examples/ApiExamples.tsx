'use client'

import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>po<PERSON>,
  Alert,
  CircularProgress,
  TextField,
  Stack,
} from '@mui/material'

// Import the API hooks
import {
  useGetCurrentUserQuery,
  useGetPropertiesQuery,
  useUpdateUserMutation,
  useCreatePropertyMutation,
} from '@/store/api/apiSlice'

import {
  useGetCustomersQuery,
  useGetCustomerByIdQuery,
  useUpdateCustomerMutation,
} from '@/store/api/customersApi'

// Helper function to check if error has data property
const hasErrorData = (error: any): error is { data: { message?: string } } => {
  return error && typeof error === 'object' && 'data' in error
}

const ApiExamples: React.FC = () => {
  const [customerId, setCustomerId] = useState('123')

  // Example 1: Using secureapi (default handler)
  const {
    data: currentUser,
    error: userError,
    isLoading: userLoading,
  } = useGetCurrentUserQuery()

  // Example 2: Using secureapi2 handler - Get customers list
  const {
    data: customers,
    error: customersError,
    isLoading: customersLoading,
    refetch: refetchCustomers,
  } = useGetCustomersQuery({ page: 0, size: 10 })

  // Example 3: Using secureapi2 - Get customer by ID
  const {
    data: customerDetails,
    error: customerError,
    isLoading: customerLoading,
  } = useGetCustomerByIdQuery(customerId, { skip: !customerId })

  // Mutations
  const [updateUser] = useUpdateUserMutation()
  const [updateCustomer] = useUpdateCustomerMutation()

  const handleUpdateUser = async () => {
    try {
      await updateUser({
        id: 'current-user-id',
        data: { name: 'Updated Name' },
      }).unwrap()
      alert('User updated successfully!')
    } catch (error) {
      console.error('Failed to update user:', error)
      alert('Failed to update user')
    }
  }

  const handleUpdateCustomer = async () => {
    try {
      await updateCustomer({
        profileId: customerId,
        email: '<EMAIL>',
        comments: 'Updated via RTK Query',
      }).unwrap()
      alert('Customer update request submitted!')
    } catch (error) {
      console.error('Failed to update customer:', error)
      alert('Failed to update customer')
    }
  }

  const handleRefreshCustomers = () => {
    refetchCustomers()
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        RTK Query API Examples
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This page demonstrates how to use RTK Query with different API handlers:
        secureapi, secureapi2, and custom handlers.
      </Typography>

      <Stack spacing={3}>
        {/* secureapi (default) */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              1. Current User (secureapi)
            </Typography>
            {userLoading && <CircularProgress size={20} />}
            {userError && (
              <Alert severity="error">
                Error: {hasErrorData(userError) ? userError.data?.message : 'Failed to fetch user'}
              </Alert>
            )}
            {currentUser && (
              <Box>
                <Typography variant="body2">
                  User data loaded successfully!
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleUpdateUser}
                  sx={{ mt: 1 }}
                >
                  Update User
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* secureapi2 - Customers List */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              2. Customers List (secureapi2)
            </Typography>
            {customersLoading && <CircularProgress size={20} />}
            {customersError && (
              <Alert severity="error">
                Error: {hasErrorData(customersError) ? customersError.data?.message : 'Failed to fetch customers'}
              </Alert>
            )}
            {customers && (
              <Box>
                <Typography variant="body2" gutterBottom>
                  Found {customers.data?.length || 0} customers (Total: {customers.totalElements})
                </Typography>
                <Box sx={{ maxHeight: 200, overflow: 'auto', mb: 2 }}>
                  {customers.data?.slice(0, 5).map((customer) => (
                    <Box key={customer.id} sx={{ p: 1, border: '1px solid #eee', mb: 1, borderRadius: 1 }}>
                      <Typography variant="body2" fontWeight="bold">
                        {customer.firstName} {customer.lastName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {customer.email} | {customer.phoneNumber}
                      </Typography>
                    </Box>
                  ))}
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleRefreshCustomers}
                  sx={{ mt: 1 }}
                >
                  Refresh Customers
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Customer Details by ID */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              3. Customer Details (secureapi2)
            </Typography>
            <TextField
              label="Customer ID"
              value={customerId}
              onChange={(e) => setCustomerId(e.target.value)}
              size="small"
              sx={{ mb: 2 }}
              fullWidth
            />
            {customerLoading && <CircularProgress size={20} />}
            {customerError && (
              <Alert severity="error">
                Error: {hasErrorData(customerError) ? customerError.data?.message : 'Failed to fetch customer'}
              </Alert>
            )}
            {customerDetails && (
              <Box>
                <Alert severity="success" sx={{ mb: 2 }}>
                  Customer details loaded successfully!
                </Alert>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="body2"><strong>Name:</strong> {customerDetails.firstName} {customerDetails.lastName}</Typography>
                  <Typography variant="body2"><strong>Email:</strong> {customerDetails.email}</Typography>
                  <Typography variant="body2"><strong>Phone:</strong> {customerDetails.phoneNumber}</Typography>
                  <Typography variant="body2"><strong>ID Number:</strong> {customerDetails.idNumber}</Typography>
                  <Typography variant="body2"><strong>Status:</strong> {customerDetails.isBlocked ? 'Blocked' : 'Active'}</Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleUpdateCustomer}
                  sx={{ mt: 2 }}
                >
                  Update Customer
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      </Stack>

      {/* Debug information */}
      <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>
          Debug Information
        </Typography>
        <Typography variant="body2" component="pre">
          {JSON.stringify(
            {
              currentUser: !!currentUser,
              customers: !!customers,
              customerDetails: !!customerDetails,
              customersCount: customers?.totalElements || 0,
            },
            null,
            2
          )}
        </Typography>
      </Box>
    </Box>
  )
}

export default ApiExamples
