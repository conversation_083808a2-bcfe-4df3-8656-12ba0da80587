import { useState, useCallback } from 'react'
import {
  useGetCurrentUserQuery,
  useGetPropertiesQuery,
  useGetExternalDataQuery,
  useUpdateUserMutation,
  useCreatePropertyMutation,
} from '@/store/api/apiSlice'

import {
  useGetPropertyByIdQuery,
  useCreatePropertyMutation as useCreatePropertyFromPropertyApi,
} from '@/store/api/propertyApi'

/**
 * Custom hook that demonstrates various RTK Query patterns
 * and provides a clean interface for components
 */
export const useApiDemo = () => {
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>('')
  const [externalApiConfig, setExternalApiConfig] = useState({
    baseUrl: 'https://jsonplaceholder.typicode.com',
    endpoint: '/posts/1',
  })

  // Queries using different handlers
  const userQuery = useGetCurrentUserQuery()
  const propertiesQuery = useGetPropertiesQuery({ page: 1, limit: 10 })
  const externalQuery = useGetExternalDataQuery(
    externalApiConfig,
    { skip: !externalApiConfig.baseUrl || !externalApiConfig.endpoint }
  )
  const propertyQuery = useGetPropertyByIdQuery(
    selectedPropertyId,
    { skip: !selectedPropertyId }
  )

  // Mutations
  const [updateUser, updateUserResult] = useUpdateUserMutation()
  const [createProperty, createPropertyResult] = useCreatePropertyMutation()
  const [createPropertyFromApi, createPropertyFromApiResult] = useCreatePropertyFromPropertyApi()

  // Action handlers
  const handleUpdateUser = useCallback(async (userData: any) => {
    try {
      const result = await updateUser({
        id: 'current-user',
        data: userData,
      }).unwrap()
      return { success: true, data: result }
    } catch (error) {
      return { success: false, error }
    }
  }, [updateUser])

  const handleCreateProperty = useCallback(async (propertyData: any) => {
    try {
      const result = await createProperty(propertyData).unwrap()
      return { success: true, data: result }
    } catch (error) {
      return { success: false, error }
    }
  }, [createProperty])

  const handleCreatePropertyFromApi = useCallback(async (propertyData: any) => {
    try {
      const result = await createPropertyFromApi(propertyData).unwrap()
      return { success: true, data: result }
    } catch (error) {
      return { success: false, error }
    }
  }, [createPropertyFromApi])

  const handleTestExternalApi = useCallback(() => {
    externalQuery.refetch()
  }, [externalQuery])

  const updateExternalApiConfig = useCallback((config: Partial<typeof externalApiConfig>) => {
    setExternalApiConfig(prev => ({ ...prev, ...config }))
  }, [])

  // Computed states
  const isLoading = userQuery.isLoading || 
                   propertiesQuery.isLoading || 
                   externalQuery.isLoading || 
                   propertyQuery.isLoading

  const hasErrors = userQuery.error || 
                   propertiesQuery.error || 
                   externalQuery.error || 
                   propertyQuery.error

  const isMutating = updateUserResult.isLoading || 
                    createPropertyResult.isLoading || 
                    createPropertyFromApiResult.isLoading

  return {
    // Query states
    queries: {
      user: userQuery,
      properties: propertiesQuery,
      external: externalQuery,
      property: propertyQuery,
    },

    // Mutation states
    mutations: {
      updateUser: updateUserResult,
      createProperty: createPropertyResult,
      createPropertyFromApi: createPropertyFromApiResult,
    },

    // Actions
    actions: {
      updateUser: handleUpdateUser,
      createProperty: handleCreateProperty,
      createPropertyFromApi: handleCreatePropertyFromApi,
      testExternalApi: handleTestExternalApi,
      setSelectedPropertyId,
      updateExternalApiConfig,
    },

    // Computed states
    state: {
      isLoading,
      hasErrors,
      isMutating,
      selectedPropertyId,
      externalApiConfig,
    },

    // Raw data for easy access
    data: {
      user: userQuery.data,
      properties: propertiesQuery.data,
      external: externalQuery.data,
      property: propertyQuery.data,
    },

    // Error helpers
    errors: {
      user: userQuery.error,
      properties: propertiesQuery.error,
      external: externalQuery.error,
      property: propertyQuery.error,
    },
  }
}

// Type exports for consumers
export type ApiDemoHook = ReturnType<typeof useApiDemo>
export type ApiDemoQueries = ApiDemoHook['queries']
export type ApiDemoMutations = ApiDemoHook['mutations']
export type ApiDemoActions = ApiDemoHook['actions']
export type ApiDemoState = ApiDemoHook['state']
export type ApiDemoData = ApiDemoHook['data']
export type ApiDemoErrors = ApiDemoHook['errors']
