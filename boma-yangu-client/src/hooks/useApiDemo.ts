import { useState, useCallback } from 'react'
import {
  useGetCurrentUserQuery,
  useGetPropertiesQuery,
  useUpdateUserMutation,
  useCreatePropertyMutation,
} from '@/store/api/apiSlice'

import {
  useGetCustomersQuery,
  useGetCustomerByIdQuery,
  useUpdateCustomerMutation,
} from '@/store/api/customersApi'

/**
 * Custom hook that demonstrates various RTK Query patterns
 * and provides a clean interface for components
 */
export const useApiDemo = () => {
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('')

  // Queries using different handlers
  const userQuery = useGetCurrentUserQuery()
  const propertiesQuery = useGetPropertiesQuery({ page: 1, limit: 10 })
  const customersQuery = useGetCustomersQuery({ page: 0, size: 10 })
  const customerQuery = useGetCustomerByIdQuery(
    selectedCustomerId,
    { skip: !selectedCustomerId }
  )

  // Mutations
  const [updateUser, updateUserResult] = useUpdateUserMutation()
  const [createProperty, createPropertyResult] = useCreatePropertyMutation()
  const [updateCustomer, updateCustomerResult] = useUpdateCustomerMutation()

  // Action handlers
  const handleUpdateUser = useCallback(async (userData: any) => {
    try {
      const result = await updateUser({
        id: 'current-user',
        data: userData,
      }).unwrap()
      return { success: true, data: result }
    } catch (error) {
      return { success: false, error }
    }
  }, [updateUser])

  const handleCreateProperty = useCallback(async (propertyData: any) => {
    try {
      const result = await createProperty(propertyData).unwrap()
      return { success: true, data: result }
    } catch (error) {
      return { success: false, error }
    }
  }, [createProperty])

  const handleUpdateCustomer = useCallback(async (customerData: any) => {
    try {
      const result = await updateCustomer(customerData).unwrap()
      return { success: true, data: result }
    } catch (error) {
      return { success: false, error }
    }
  }, [updateCustomer])

  const handleRefreshCustomers = useCallback(() => {
    customersQuery.refetch()
  }, [customersQuery])

  // Computed states
  const isLoading = userQuery.isLoading ||
                   propertiesQuery.isLoading ||
                   customersQuery.isLoading ||
                   customerQuery.isLoading

  const hasErrors = userQuery.error ||
                   propertiesQuery.error ||
                   customersQuery.error ||
                   customerQuery.error

  const isMutating = updateUserResult.isLoading ||
                    createPropertyResult.isLoading ||
                    updateCustomerResult.isLoading

  return {
    // Query states
    queries: {
      user: userQuery,
      properties: propertiesQuery,
      customers: customersQuery,
      customer: customerQuery,
    },

    // Mutation states
    mutations: {
      updateUser: updateUserResult,
      createProperty: createPropertyResult,
      updateCustomer: updateCustomerResult,
    },

    // Actions
    actions: {
      updateUser: handleUpdateUser,
      createProperty: handleCreateProperty,
      updateCustomer: handleUpdateCustomer,
      refreshCustomers: handleRefreshCustomers,
      setSelectedCustomerId,
    },

    // Computed states
    state: {
      isLoading,
      hasErrors,
      isMutating,
      selectedCustomerId,
    },

    // Raw data for easy access
    data: {
      user: userQuery.data,
      properties: propertiesQuery.data,
      customers: customersQuery.data,
      customer: customerQuery.data,
    },

    // Error helpers
    errors: {
      user: userQuery.error,
      properties: propertiesQuery.error,
      customers: customersQuery.error,
      customer: customerQuery.error,
    },
  }
}

// Type exports for consumers
export type ApiDemoHook = ReturnType<typeof useApiDemo>
export type ApiDemoQueries = ApiDemoHook['queries']
export type ApiDemoMutations = ApiDemoHook['mutations']
export type ApiDemoActions = ApiDemoHook['actions']
export type ApiDemoState = ApiDemoHook['state']
export type ApiDemoData = ApiDemoHook['data']
export type ApiDemoErrors = ApiDemoHook['errors']
