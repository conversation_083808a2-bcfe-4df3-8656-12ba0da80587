"use client";
import "@dtbx/ui/theme/index.css";
import "./globals.css";
import React, { ReactNode } from "react";
import { Box } from "@mui/material";
import { NextAppDirEmotionCacheProvider, ThemeConfig } from "@dtbx/ui/theme";
import "./fonts.css";
import { useAppSelector, useAppDispatch } from "@dtbx/store";
import { refreshToken } from "@dtbx/store/actions";
import { clearNotification, setSidebarCollapsed } from "@dtbx/store/reducers";
import AppProvider from "@/store/AppProvider";
import {
  AuthWrapper,
  InActivity,
  LocalNotification,
} from "@dtbx/ui/components";

import { Sidebar } from "@dtbx/ui/components/Sidebar";
import { InternalNavBar } from "@dtbx/ui/components/Appbar";

import { isLoggedIn } from "@dtbx/store/utils";
import { sidebarConfig } from "./sidebar";

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <html lang="en" style={{ height: '100%', overflow: 'hidden' }}>
      <body style={{
        margin: 0,
        padding: 0,
        height: '100vh',
        overflow: 'hidden',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: "mui" }}>
            <ThemeConfig themeType="main">
              <InActivity isLoggedIn={isLoggedIn}>
                <DashboardLayout>{children}</DashboardLayout>
              </InActivity>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  );
}

function DashboardLayout({ children }: { children: ReactNode }) {
  const dispatch = useAppDispatch();
  const { isSidebarCollapsed } = useAppSelector(
    (state) => state.navigation
  );
  const profile = useAppSelector((state) => state.auth.decodedToken);
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  );
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    "info";
  return (
    <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
      <Box sx={{
        height: "100vh",
        display: "flex",
        flexDirection: "row",
        overflow: "hidden"
      }}>
        {/* Fixed Sidebar */}
        <Box sx={{
          position: "fixed",
          left: 0,
          top: 0,
          height: "100vh",
          zIndex: 1200,
          width: isSidebarCollapsed ? "5vw" : "12vw",
          transition: "width 0.3s ease"
        }}>
          <Sidebar
            bgColor="#FFFFFF"
            sidebarConfig={sidebarConfig}
            sidebarCollapsed={(val) => dispatch(setSidebarCollapsed(val))}
          />
        </Box>

        {/* Main Content Area */}
        <Box
          sx={{
            marginLeft: isSidebarCollapsed ? "5vw" : "12vw",
            width: isSidebarCollapsed ? "95vw" : "88vw",
            height: "100vh",
            display: "flex",
            flexDirection: "column",
            transition: "margin-left 0.3s ease, width 0.3s ease"
          }}
        >
          {/* Fixed Navbar */}
          <Box sx={{
            position: "sticky",
            top: 0,
            zIndex: 1100,
            backgroundColor: "#FFFFFF",
            borderBottom: "1px solid #E5E7EB"
          }}>
            <InternalNavBar profile={profile} refreshToken={refreshToken} />
          </Box>

          {/* Scrollable Content Area */}
          <Box
            className="custom-scrollbar content-scroll"
            sx={{
              flex: 1,
              overflow: "auto",
              backgroundColor: "#F2F4F7",
              position: "relative"
            }}
          >
            <LocalNotification
              clearNotification={() => dispatch(clearNotification())}
              notification={notification}
              notificationType={notificationType}
            />
            <Box sx={{ padding: "16px" }}>
              {children}
            </Box>
          </Box>
        </Box>
      </Box>
    </AuthWrapper>
  );
}
