"use client";
import "@dtbx/ui/theme/index.css";
import "./globals.css";
import React, { ReactNode } from "react";
import { Box } from "@mui/material";
import { NextAppDirEmotionCacheProvider, ThemeConfig } from "@dtbx/ui/theme";
import "./fonts.css";
import { useAppSelector, useAppDispatch } from "@dtbx/store";
import { refreshToken } from "@dtbx/store/actions";
import { clearNotification, setSidebarCollapsed } from "@dtbx/store/reducers";
import AppProvider from "@/store/AppProvider";
import {
  AuthWrapper,
  InActivity,
  LocalNotification,
} from "@dtbx/ui/components";

import { Sidebar } from "@dtbx/ui/components/Sidebar";
import { InternalNavBar } from "@dtbx/ui/components/Appbar";

import { isLoggedIn } from "@dtbx/store/utils";
import { sidebarConfig } from "./sidebar";

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <html lang="en" style={{ height: '100%', overflow: 'hidden' }}>
      <body style={{
        margin: 0,
        padding: 0,
        height: '100vh',
        overflow: 'hidden',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: "mui" }}>
            <ThemeConfig themeType="main">
              <InActivity isLoggedIn={isLoggedIn}>
                <DashboardLayout>{children}</DashboardLayout>
              </InActivity>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  );
}

function DashboardLayout({ children }: { children: ReactNode }) {
  const dispatch = useAppDispatch();
  const { isSidebarCollapsed } = useAppSelector(
    (state) => state.navigation
  );
  const profile = useAppSelector((state) => state.auth.decodedToken);
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  );
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    "info";
  return (
    <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
      <Box sx={{
        height: "100vh",
        display: "flex",
        flexDirection: "row",
        overflow: "hidden"
      }}>
        {/* Flexible Sidebar */}
        <Box
          className={`sidebar-transition ${isSidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'}`}
          sx={{
            width: isSidebarCollapsed ? "60px" : "240px",
            minWidth: isSidebarCollapsed ? "60px" : "240px",
            height: "100vh",
            transition: "width 0.3s cubic-bezier(0.4, 0, 0.2, 1), min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
            flexShrink: 0,
            borderRight: "1px solid #E5E7EB",
            backgroundColor: "#FFFFFF",
            zIndex: isSidebarCollapsed ? 1000 : 1200
          }}
        >
          <Sidebar
            bgColor="#FFFFFF"
            sidebarConfig={sidebarConfig}
            sidebarCollapsed={(val) => dispatch(setSidebarCollapsed(val))}
          />
        </Box>

        {/* Flexible Main Content Area */}
        <Box
          className="content-transition"
          sx={{
            flex: 1,
            height: "100vh",
            display: "flex",
            flexDirection: "column",
            minWidth: 0, // Prevents flex item from overflowing
            overflow: "hidden"
          }}
        >
          {/* Flexible Navbar */}
          <Box sx={{
            backgroundColor: "#FFFFFF",
            borderBottom: "1px solid #E5E7EB",
            flexShrink: 0
          }}>
            <InternalNavBar profile={profile} refreshToken={refreshToken} />
          </Box>

          {/* Flexible Scrollable Content Area */}
          <Box
            className="custom-scrollbar content-scroll"
            sx={{
              flex: 1,
              overflow: "auto",
              backgroundColor: "#F2F4F7",
              position: "relative",
              minHeight: 0 // Allows flex item to shrink below content size
            }}
          >
            <LocalNotification
              clearNotification={() => dispatch(clearNotification())}
              notification={notification}
              notificationType={notificationType}
            />
            <Box sx={{
              padding: "16px",
              minHeight: "calc(100vh - 64px)" // Ensures content takes full height minus navbar
            }}>
              {children}
            </Box>
          </Box>
        </Box>
      </Box>
    </AuthWrapper>
  );
}
