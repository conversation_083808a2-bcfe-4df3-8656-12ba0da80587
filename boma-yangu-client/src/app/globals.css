/* Global styles for flexible layout */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Ensure the root div takes full height */
#__next {
  height: 100%;
}

/* Flexible layout utilities */
.flex-layout {
  display: flex;
  height: 100vh;
}

.sidebar-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar styles for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth scrolling for content areas */
.content-scroll {
  scroll-behavior: smooth;
}

/* Prevent text selection on sidebar and navbar */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Ensure proper box sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Remove default margins and paddings from common elements */
h1, h2, h3, h4, h5, h6, p, ul, ol, li {
  margin: 0;
  padding: 0;
}

/* Ensure images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #2196F3;
  outline-offset: 2px;
}

/* Hide scrollbar for webkit browsers while keeping functionality */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

/* Responsive layout adjustments */
@media (max-width: 768px) {
  .sidebar-collapsed {
    width: 0 !important;
    min-width: 0 !important;
    overflow: hidden;
  }

  .sidebar-expanded {
    width: 280px !important;
    min-width: 280px !important;
  }
}

@media (max-width: 480px) {
  .sidebar-expanded {
    width: 100vw !important;
    min-width: 100vw !important;
    position: absolute;
    z-index: 1300;
    background: white;
  }
}
