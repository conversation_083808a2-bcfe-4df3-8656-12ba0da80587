//@ts-nocheck
import { describe, it, expect, vi } from "vitest";
import { render } from "@testing-library/react";

// Mock the CustomError component
vi.mock("@dtbx/ui/components", () => ({
  CustomError: ({ error, reset }: { error: unknown; reset: () => void }) => (
    <div data-testid="custom-error">
      <span data-testid="error-message">{String(error)}</span>
      <button data-testid="reset-button" onClick={reset}>
        Reset
      </button>
    </div>
  ),
}));

import Error from "../Error";

describe("Error Component", () => {
  it("renders CustomError with error and reset props", () => {
    const mockError = new Error("Test error message");
    const mockReset = vi.fn();

    const { getByTestId } = render(
      <Error error={mockError} reset={mockReset} />,
    );

    expect(getByTestId("custom-error")).toBeInTheDocument();
    expect(getByTestId("error-message")).toHaveTextContent("[object Object]");
    expect(getByTestId("reset-button")).toBeInTheDocument();
  });

  it("calls reset function when reset button is clicked", () => {
    const mockError = new Error("Test error");
    const mockReset = vi.fn();

    const { getByTestId } = render(
      <Error error={mockError} reset={mockReset} />,
    );

    const resetButton = getByTestId("reset-button");
    resetButton.click();

    expect(mockReset).toHaveBeenCalledTimes(1);
  });

  it("handles string error", () => {
    const mockError = "String error message";
    const mockReset = vi.fn();

    const { getByTestId } = render(
      <Error error={mockError} reset={mockReset} />,
    );

    expect(getByTestId("error-message")).toHaveTextContent(
      "String error message",
    );
  });

  it("handles unknown error types", () => {
    const mockError = { message: "Object error" };
    const mockReset = vi.fn();

    const { getByTestId } = render(
      <Error error={mockError} reset={mockReset} />,
    );

    expect(getByTestId("error-message")).toHaveTextContent("[object Object]");
  });

  it("renders without crashing with null error", () => {
    const mockReset = vi.fn();

    const { getByTestId } = render(<Error error={null} reset={mockReset} />);

    expect(getByTestId("custom-error")).toBeInTheDocument();
  });
});
