'use client'

import React from 'react'
import { Box, Typography, Container } from '@mui/material'
import ApiExamples from '@/features/examples/ApiExamples'

export default function ApiDemoPage() {
  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          RTK Query API Demo
        </Typography>
        <Typography variant="body1" sx={{ mb: 4 }}>
          This page demonstrates the RTK Query setup with multiple API handlers.
          You can test different endpoints using secureapi, secureapi2, and custom handlers.
        </Typography>
        
        <ApiExamples />
      </Box>
    </Container>
  )
}
