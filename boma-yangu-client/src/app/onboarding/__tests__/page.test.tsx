import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import OnboardingPageRoute from "../page";

// Create a simple test theme
const testTheme = createTheme();

// Simple wrapper for Material-UI components
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider theme={testTheme}>{children}</ThemeProvider>
);

describe("Onboarding Page Route", () => {
  it("renders the OnboardingPage component", () => {
    render(
      <TestWrapper>
        <OnboardingPageRoute />
      </TestWrapper>,
    );

    // Check that the OnboardingPage content is rendered
    const heading = screen.getByRole("heading", { level: 1 });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent("Boma Yangu Onboarding Page");
  });

  it("renders without crashing", () => {
    const { container } = render(
      <TestWrapper>
        <OnboardingPageRoute />
      </TestWrapper>,
    );
    expect(container.firstChild).toBeInTheDocument();
  });

  it("displays all OnboardingPage content", () => {
    render(
      <TestWrapper>
        <OnboardingPageRoute />
      </TestWrapper>,
    );

    // Verify that all the OnboardingPage content is present
    expect(screen.getByText("Boma Yangu Onboarding Page")).toBeInTheDocument();
    expect(
      screen.getByText(/This is a test of the BlissPro font/),
    ).toBeInTheDocument();
    expect(screen.getByText("Font Test - Heading 6")).toBeInTheDocument();
    expect(screen.getByText("Font Test - Subtitle 1")).toBeInTheDocument();
    expect(screen.getByText("Font Test - Body 2 text")).toBeInTheDocument();
  });

  it("has proper component structure", () => {
    render(
      <TestWrapper>
        <OnboardingPageRoute />
      </TestWrapper>,
    );

    // Check that the component has the expected structure
    const headings = screen.getAllByRole("heading");
    expect(headings.length).toBeGreaterThanOrEqual(2); // h1 and at least one h6
  });
});
