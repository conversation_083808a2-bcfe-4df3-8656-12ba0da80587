import { UserProfileIcon, HomeIcon } from "@dtbx/ui/icons";
import { ISidebarConfigItem } from "@dtbx/ui/components/Sidebar";
import DeveloperModeIcon from '@mui/icons-material/DeveloperMode';

export const sidebarConfig: ISidebarConfigItem[] = [
  {
    id: "1",
    title: "home",
    path: "/home",
    module: "Boma",
    icon: <HomeIcon />,
    isProductionReady: true,
  },
  {
    id: "2",
    title: "Onboarding",
    path: "/onboarding",
    module: "Bo<PERSON>",
    icon: <UserProfileIcon />,
    isProductionReady: true,
  },
  {
    id: "3",
    title: "Examples",
    path: "/api-demo",
    module: "Bo<PERSON>",
    icon: <DeveloperModeIcon />,
    isProductionReady: true,
  },
];
