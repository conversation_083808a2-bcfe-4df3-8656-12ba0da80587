'use client'

import React, { useState, useEffect } from 'react'
import { Box, Button, <PERSON>pography, <PERSON><PERSON>, Card, CardContent } from '@mui/material'
import { refreshToken } from '@dtbx/store/actions'
import { jwtDecode } from 'jwt-decode'

interface TokenInfo {
  exists: boolean
  value?: string
  decoded?: any
  isExpired?: boolean
  expiresAt?: string
}

const TokenDebugger: React.FC = () => {
  const [accessTokenInfo, setAccessTokenInfo] = useState<TokenInfo>({ exists: false })
  const [refreshTokenInfo, setRefreshTokenInfo] = useState<TokenInfo>({ exists: false })
  const [refreshing, setRefreshing] = useState(false)
  const [refreshResult, setRefreshResult] = useState<string>('')

  const analyzeToken = (tokenKey: string): TokenInfo => {
    const token = localStorage.getItem(tokenKey)
    if (!token) {
      return { exists: false }
    }

    try {
      const decoded = jwtDecode(token)
      const expiresAt = new Date((decoded as any).exp * 1000).toISOString()
      const isExpired = (decoded as any).exp * 1000 < Date.now()

      return {
        exists: true,
        value: token.substring(0, 50) + '...',
        decoded,
        isExpired,
        expiresAt,
      }
    } catch (error) {
      return {
        exists: true,
        value: token.substring(0, 50) + '...',
        decoded: null,
      }
    }
  }

  const updateTokenInfo = () => {
    setAccessTokenInfo(analyzeToken('accessToken'))
    setRefreshTokenInfo(analyzeToken('refreshToken'))
  }

  useEffect(() => {
    updateTokenInfo()
  }, [])

  const handleRefreshToken = async () => {
    setRefreshing(true)
    setRefreshResult('')
    
    try {
      console.log('🔄 Starting token refresh...')
      console.log('📋 Current refresh token:', localStorage.getItem('refreshToken')?.substring(0, 50) + '...')
      
      await refreshToken()
      
      console.log('✅ Token refresh completed')
      setRefreshResult('✅ Token refresh successful!')
      updateTokenInfo()
    } catch (error) {
      console.error('❌ Token refresh failed:', error)
      setRefreshResult(`❌ Token refresh failed: ${(error as Error).message}`)
    } finally {
      setRefreshing(false)
    }
  }

  const handleClearTokens = () => {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    updateTokenInfo()
    setRefreshResult('🗑️ Tokens cleared')
  }

  const TokenCard: React.FC<{ title: string; tokenInfo: TokenInfo }> = ({ title, tokenInfo }) => (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        
        {!tokenInfo.exists ? (
          <Alert severity="warning">No {title.toLowerCase()} found in localStorage</Alert>
        ) : (
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>Value:</strong> {tokenInfo.value}
            </Typography>
            
            {tokenInfo.decoded ? (
              <>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Expires:</strong> {tokenInfo.expiresAt}
                </Typography>
                <Alert severity={tokenInfo.isExpired ? 'error' : 'success'}>
                  {tokenInfo.isExpired ? 'Token is EXPIRED' : 'Token is VALID'}
                </Alert>
                <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
                  <Typography variant="caption" component="pre">
                    {JSON.stringify(tokenInfo.decoded, null, 2)}
                  </Typography>
                </Box>
              </>
            ) : (
              <Alert severity="error">Invalid token format (cannot decode)</Alert>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  )

  return (
    <Box sx={{ p: 3, maxWidth: 800 }}>
      <Typography variant="h4" gutterBottom>
        🔍 Token Debugger
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        This component helps debug token refresh issues by showing the current state of your tokens.
      </Typography>

      <TokenCard title="Access Token" tokenInfo={accessTokenInfo} />
      <TokenCard title="Refresh Token" tokenInfo={refreshTokenInfo} />

      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          onClick={handleRefreshToken}
          disabled={refreshing || !refreshTokenInfo.exists}
          sx={{ mr: 2 }}
        >
          {refreshing ? 'Refreshing...' : '🔄 Test Refresh Token'}
        </Button>
        
        <Button
          variant="outlined"
          onClick={updateTokenInfo}
          sx={{ mr: 2 }}
        >
          🔄 Refresh Info
        </Button>
        
        <Button
          variant="outlined"
          color="error"
          onClick={handleClearTokens}
        >
          🗑️ Clear Tokens
        </Button>
      </Box>

      {refreshResult && (
        <Alert severity={refreshResult.includes('✅') ? 'success' : 'error'} sx={{ mb: 2 }}>
          {refreshResult}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            🔧 Debug Steps
          </Typography>
          <Typography variant="body2" component="div">
            <ol>
              <li><strong>Check tokens exist:</strong> Both access and refresh tokens should be present</li>
              <li><strong>Check token format:</strong> Tokens should be valid JWTs that can be decoded</li>
              <li><strong>Check expiration:</strong> Access token can be expired, but refresh token should be valid</li>
              <li><strong>Test refresh:</strong> Click "Test Refresh Token" to see if the refresh endpoint works</li>
              <li><strong>Check network:</strong> Open DevTools → Network tab and watch the refresh request</li>
            </ol>
          </Typography>
        </CardContent>
      </Card>
    </Box>
  )
}

export default TokenDebugger
