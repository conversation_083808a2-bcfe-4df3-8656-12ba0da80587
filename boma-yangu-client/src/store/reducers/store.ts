import { combineReducers } from '@reduxjs/toolkit'
import {
  authReducer,
  navigation,
  notifications,
  overlays,
} from '@dtbx/store/reducers'
import { apiSlice } from '../api/apiSlice'

 const rootReducer = combineReducers({
  auth: authReducer,
  navigation,
  notifications,
  overlays,
  [apiSlice.reducerPath]: apiSlice.reducer,
})

export type RootReducer = ReturnType<typeof rootReducer>
export { rootReducer }
export default rootReducer
