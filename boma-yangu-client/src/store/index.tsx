import { configureStore } from "@reduxjs/toolkit";
import { persistReducer } from "redux-persist";
import {persistConfig} from "@dtbx/store"
import storage from 'redux-persist/lib/storage'

import rootReducer, {RootReducer} from "./reducers/store";
import { apiSlice } from "./api/apiSlice";


export const persistedReducer = persistReducer<RootReducer>(
  {
    key: 'backoffice',
    storage,
    blacklist: [],
  },
  rootReducer
)

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }).concat(apiSlice.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
