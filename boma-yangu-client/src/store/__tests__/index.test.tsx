import { describe, it, expect, vi } from "vitest";
import { configureStore } from "@reduxjs/toolkit";
import store, { RootState, AppDispatch } from "../index";
import { rootReducer } from "../reducers/store";

// Mock redux-persist
vi.mock("redux-persist", () => ({
  persistReducer: vi.fn((config, reducer) => reducer),
  persistStore: vi.fn(),
}));

// Mock @dtbx/store
vi.mock("@dtbx/store", () => ({
  persistConfig: {
    key: "root",
    storage: {},
  },
}));

describe("Store Configuration", () => {
  it("should create store with correct configuration", () => {
    expect(store).toBeDefined();
    expect(store.getState).toBeDefined();
    expect(store.dispatch).toBeDefined();
  });

  it("should have correct initial state structure", () => {
    const state = store.getState();

    // Check that all expected reducers are present
    expect(state).toHaveProperty("auth");
    expect(state).toHaveProperty("navigation");
    expect(state).toHaveProperty("notifications");
    expect(state).toHaveProperty("overlays");
  });

  it("should export correct TypeScript types", () => {
    const state = store.getState();
    const dispatch = store.dispatch;

    // These should compile without TypeScript errors
    const typedState: RootState = state;
    const typedDispatch: AppDispatch = dispatch;

    expect(typedState).toBeDefined();
    expect(typedDispatch).toBeDefined();
  });

  it("should configure middleware correctly", () => {
    // Test that the store has the expected middleware
    // This is more of a smoke test to ensure the store is configured
    const testAction = { type: "TEST_ACTION" };

    expect(() => {
      store.dispatch(testAction);
    }).not.toThrow();
  });

  it("should handle serializable check configuration", () => {
    // Test that persist actions are ignored in serializable check
    const persistAction = { type: "persist/PERSIST", payload: {} };
    const rehydrateAction = { type: "persist/REHYDRATE", payload: {} };

    expect(() => {
      store.dispatch(persistAction);
      store.dispatch(rehydrateAction);
    }).not.toThrow();
  });

  it("should create a new store instance with configureStore", () => {
    const newStore = configureStore({
      reducer: rootReducer,
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          serializableCheck: {
            ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
          },
        }),
    });

    expect(newStore).toBeDefined();
    expect(newStore.getState()).toHaveProperty("auth");
    expect(newStore.getState()).toHaveProperty("navigation");
    expect(newStore.getState()).toHaveProperty("notifications");
    expect(newStore.getState()).toHaveProperty("overlays");
  });

  it("should maintain state consistency", () => {
    const initialState = store.getState();

    // Dispatch a test action
    store.dispatch({ type: "TEST_ACTION" });

    const newState = store.getState();

    // State structure should remain consistent
    expect(Object.keys(newState)).toEqual(Object.keys(initialState));
  });
});
