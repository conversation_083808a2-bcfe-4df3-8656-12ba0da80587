import { CustomQueryArgs } from './baseQuery'

/**
 * Utility functions for creating common API query patterns
 */

// Helper to create a query with secureapi handler
export const createSecureApiQuery = (
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  additionalConfig?: Partial<CustomQueryArgs>
): CustomQueryArgs => ({
  url,
  method,
  handler: 'secureapi',
  ...additionalConfig,
})

// Helper to create a query with secureapi2 handler
export const createSecureApi2Query = (
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  additionalConfig?: Partial<CustomQueryArgs>
): CustomQueryArgs => ({
  url,
  method,
  handler: 'secureapi2',
  ...additionalConfig,
})

// Helper to create a query with custom handler
export const createCustomApiQuery = (
  url: string,
  baseUrl: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
  additionalConfig?: Partial<CustomQueryArgs>
): CustomQueryArgs => ({
  url,
  method,
  handler: 'custom',
  baseUrl,
  ...additionalConfig,
})

// Common query patterns
export const queryPatterns = {
  // Get single item by ID
  getById: (endpoint: string, handler: 'secureapi' | 'secureapi2' = 'secureapi') => 
    (id: string) => ({
      url: `${endpoint}/${id}`,
      method: 'GET' as const,
      handler,
    }),

  // Get list with pagination
  getList: (endpoint: string, handler: 'secureapi' | 'secureapi2' = 'secureapi') => 
    (params?: Record<string, any>) => ({
      url: endpoint,
      method: 'GET' as const,
      params,
      handler,
    }),

  // Create new item
  create: (endpoint: string, handler: 'secureapi' | 'secureapi2' = 'secureapi') => 
    (data: any) => ({
      url: endpoint,
      method: 'POST' as const,
      body: data,
      handler,
    }),

  // Update existing item
  update: (endpoint: string, handler: 'secureapi' | 'secureapi2' = 'secureapi') => 
    ({ id, data }: { id: string; data: any }) => ({
      url: `${endpoint}/${id}`,
      method: 'PUT' as const,
      body: data,
      handler,
    }),

  // Partial update
  patch: (endpoint: string, handler: 'secureapi' | 'secureapi2' = 'secureapi') => 
    ({ id, data }: { id: string; data: any }) => ({
      url: `${endpoint}/${id}`,
      method: 'PATCH' as const,
      body: data,
      handler,
    }),

  // Delete item
  delete: (endpoint: string, handler: 'secureapi' | 'secureapi2' = 'secureapi') => 
    (id: string) => ({
      url: `${endpoint}/${id}`,
      method: 'DELETE' as const,
      handler,
    }),
}

// Tag generation helpers for RTK Query caching
export const tagHelpers = {
  // Generate tags for a list endpoint
  listTags: <T extends { id: string | number }>(
    tagType: string,
    result?: { data: T[] }
  ) =>
    result
      ? [
          ...result.data.map(({ id }) => ({ type: tagType, id })),
          { type: tagType, id: 'LIST' },
        ]
      : [{ type: tagType, id: 'LIST' }],

  // Generate tags for a single item
  itemTags: (tagType: string, id: string | number) => [
    { type: tagType, id },
  ],

  // Generate invalidation tags for mutations
  invalidateList: (tagType: string) => [{ type: tagType, id: 'LIST' }],
  invalidateItem: (tagType: string, id: string | number) => [
    { type: tagType, id },
  ],
  invalidateBoth: (tagType: string, id: string | number) => [
    { type: tagType, id },
    { type: tagType, id: 'LIST' },
  ],
}

// Error handling utilities
export const errorUtils = {
  // Extract error message from RTK Query error
  getErrorMessage: (error: any): string => {
    if (error?.data?.message) return error.data.message
    if (error?.data?.error) return error.data.error
    if (error?.data?.errors?.[0]) return error.data.errors[0]
    if (error?.message) return error.message
    return 'An unexpected error occurred'
  },

  // Check if error is a specific status code
  isErrorStatus: (error: any, status: number): boolean => {
    return error?.status === status
  },

  // Check for common error types
  isUnauthorized: (error: any): boolean => errorUtils.isErrorStatus(error, 401),
  isForbidden: (error: any): boolean => errorUtils.isErrorStatus(error, 403),
  isNotFound: (error: any): boolean => errorUtils.isErrorStatus(error, 404),
  isServerError: (error: any): boolean => {
    const status = error?.status
    return status >= 500 && status < 600
  },
}

// Common transformResponse functions
export const transformers = {
  // Extract data from paginated response
  extractPaginatedData: (response: any) => ({
    data: response.data || [],
    total: response.total || 0,
    page: response.page || 1,
    totalPages: response.totalPages || 1,
  }),

  // Transform single item response
  extractSingleItem: (response: any) => response.data || response,

  // Transform for file upload responses
  extractFileUrls: (response: any) => ({
    urls: response.fileUrls || response.urls || [],
    message: response.message || 'Files uploaded successfully',
  }),
}

// Query configuration presets
export const queryConfigs = {
  // For data that changes frequently
  realtime: {
    pollingInterval: 5000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  },

  // For relatively static data
  cached: {
    pollingInterval: 0,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  },

  // For user-specific data
  userSpecific: {
    refetchOnFocus: true,
    refetchOnReconnect: true,
  },

  // For background updates
  background: {
    pollingInterval: 30000,
    refetchOnFocus: false,
  },
}

// Example usage patterns
export const examples = {
  // Standard CRUD operations
  createCrudEndpoints: (entityName: string, endpoint: string, handler: 'secureapi' | 'secureapi2' = 'secureapi') => ({
    [`get${entityName}List`]: {
      query: queryPatterns.getList(endpoint, handler),
      providesTags: (result: any) => tagHelpers.listTags(entityName, result),
    },
    [`get${entityName}ById`]: {
      query: queryPatterns.getById(endpoint, handler),
      providesTags: (result: any, error: any, id: string) => tagHelpers.itemTags(entityName, id),
    },
    [`create${entityName}`]: {
      query: queryPatterns.create(endpoint, handler),
      invalidatesTags: tagHelpers.invalidateList(entityName),
    },
    [`update${entityName}`]: {
      query: queryPatterns.update(endpoint, handler),
      invalidatesTags: (result: any, error: any, { id }: { id: string }) => 
        tagHelpers.invalidateBoth(entityName, id),
    },
    [`delete${entityName}`]: {
      query: queryPatterns.delete(endpoint, handler),
      invalidatesTags: (result: any, error: any, id: string) => 
        tagHelpers.invalidateBoth(entityName, id),
    },
  }),
}
