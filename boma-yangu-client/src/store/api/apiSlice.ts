import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery, CustomQueryArgs } from './baseQuery'

// Enhanced query args that include handler selection
interface EnhancedQueryArgs extends Omit<CustomQueryArgs, 'url'> {
  url: string
}

// Create the main API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: axiosBaseQuery(),
  tagTypes: [
    'User',
    'Property',
    'Customer',
    'Transaction',
    'Document',
    'Notification',
  ],
  endpoints: (builder) => ({
    getCurrentUser: builder.query<any, void>({
      query: () => ({
        url: '/users/me',
        method: 'GET',
      }),
      providesTags: ['User'],
    }),

    // Example endpoint using secureapi2
    getProperties: builder.query<any, { page?: number; limit?: number }>({
      query: (params) => ({
        url: '/properties',
        method: 'GET',
        params,
        handler: 'secureapi2',
      }),
      providesTags: ['Property'],
    }),

    // Example mutation using secureapi
    updateUser: builder.mutation<any, { id: string; data: any }>({
      query: ({ id, data }) => ({
        url: `/users/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    // Example mutation using secureapi2
    createProperty: builder.mutation<any, any>({
      query: (propertyData) => ({
        url: '/properties',
        method: 'POST',
        body: propertyData,
        handler: 'secureapi2',
      }),
      invalidatesTags: ['Property'],
    }),
  }),
})

// Export hooks for usage in components
export const {
  useGetCurrentUserQuery,
  useGetPropertiesQuery,
  useUpdateUserMutation,
  useCreatePropertyMutation,
} = apiSlice

// Export the reducer
export default apiSlice.reducer

// Helper function to create endpoints with specific handlers
export const createEndpointWithHandler = (
  handler: 'secureapi' | 'secureapi2'
) => {
  return (url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET') => ({
    url,
    method,
    handler,
  })
}

// Utility functions for common patterns
export const secureApiEndpoint = createEndpointWithHandler('secureapi')
export const secureApi2Endpoint = createEndpointWithHandler('secureapi2')

// Type exports for use in other files
export type { CustomQueryArgs, CustomError } from './baseQuery'
export type { EnhancedQueryArgs }
