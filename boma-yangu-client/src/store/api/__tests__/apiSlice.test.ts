import { describe, it, expect, vi, beforeEach } from 'vitest'
import { configureStore } from '@reduxjs/toolkit'
import { apiSlice } from '../apiSlice'

// Mock the secureapi imports
vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    request: vi.fn(),
    interceptors: {
      request: { use: vi.fn() },
      response: { use: vi.fn() },
    },
  },
  secureapi2: {
    request: vi.fn(),
    interceptors: {
      request: { use: vi.fn() },
      response: { use: vi.fn() },
    },
  },
}))

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      request: vi.fn(),
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
    })),
  },
  AxiosError: class AxiosError extends Error {
    constructor(message: string, public response?: any) {
      super(message)
    }
  },
}))

describe('API Slice', () => {
  let store: ReturnType<typeof configureStore>

  beforeEach(() => {
    // Create a test store with our API slice
    store = configureStore({
      reducer: {
        [apiSlice.reducerPath]: apiSlice.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(apiSlice.middleware),
    })
  })

  it('should create the API slice with correct reducer path', () => {
    expect(apiSlice.reducerPath).toBe('api')
  })

  it('should have the correct tag types', () => {
    expect(apiSlice.tagTypes).toContain('User')
    expect(apiSlice.tagTypes).toContain('Property')
    expect(apiSlice.tagTypes).toContain('Transaction')
    expect(apiSlice.tagTypes).toContain('Document')
    expect(apiSlice.tagTypes).toContain('Notification')
  })

  it('should have the expected endpoints', () => {
    const endpoints = apiSlice.endpoints
    
    expect(endpoints.getCurrentUser).toBeDefined()
    expect(endpoints.getProperties).toBeDefined()
    expect(endpoints.getExternalData).toBeDefined()
    expect(endpoints.updateUser).toBeDefined()
    expect(endpoints.createProperty).toBeDefined()
  })

  it('should configure getCurrentUser endpoint correctly', () => {
    const endpoint = apiSlice.endpoints.getCurrentUser
    
    // Test the query function
    const queryResult = endpoint.query()
    expect(queryResult).toEqual({
      url: '/users/me',
      method: 'GET',
    })
  })

  it('should configure getProperties endpoint with secureapi2 handler', () => {
    const endpoint = apiSlice.endpoints.getProperties
    
    // Test the query function
    const queryResult = endpoint.query({ page: 1, limit: 10 })
    expect(queryResult).toEqual({
      url: '/properties',
      method: 'GET',
      params: { page: 1, limit: 10 },
      handler: 'secureapi2',
    })
  })

  it('should configure getExternalData endpoint with custom handler', () => {
    const endpoint = apiSlice.endpoints.getExternalData
    
    // Test the query function
    const queryResult = endpoint.query({
      endpoint: '/test',
      baseUrl: 'https://api.example.com',
    })
    expect(queryResult).toEqual({
      url: '/test',
      method: 'GET',
      handler: 'custom',
      baseUrl: 'https://api.example.com',
    })
  })

  it('should configure updateUser mutation correctly', () => {
    const endpoint = apiSlice.endpoints.updateUser
    
    // Test the query function
    const queryResult = endpoint.query({
      id: 'user-123',
      data: { name: 'Updated Name' },
    })
    expect(queryResult).toEqual({
      url: '/users/user-123',
      method: 'PUT',
      body: { name: 'Updated Name' },
    })
  })

  it('should configure createProperty mutation with secureapi2 handler', () => {
    const endpoint = apiSlice.endpoints.createProperty
    
    // Test the query function
    const queryResult = endpoint.query({
      title: 'Test Property',
      price: 100000,
    })
    expect(queryResult).toEqual({
      url: '/properties',
      method: 'POST',
      body: {
        title: 'Test Property',
        price: 100000,
      },
      handler: 'secureapi2',
    })
  })

  it('should integrate with Redux store correctly', () => {
    const state = store.getState()
    
    // Check that the API slice is properly integrated
    expect(state).toHaveProperty(apiSlice.reducerPath)
    expect(state[apiSlice.reducerPath]).toBeDefined()
  })

  it('should export the correct hooks', () => {
    // Import the hooks to verify they exist
    const {
      useGetCurrentUserQuery,
      useGetPropertiesQuery,
      useGetExternalDataQuery,
      useUpdateUserMutation,
      useCreatePropertyMutation,
    } = require('../apiSlice')

    expect(useGetCurrentUserQuery).toBeDefined()
    expect(useGetPropertiesQuery).toBeDefined()
    expect(useGetExternalDataQuery).toBeDefined()
    expect(useUpdateUserMutation).toBeDefined()
    expect(useCreatePropertyMutation).toBeDefined()
  })
})
