import { describe, it, expect, vi, beforeEach } from 'vitest'
import { axiosBaseQuery } from '../baseQuery'

// Mock the secureapi imports
const mockSecureApi = {
  request: vi.fn(),
  interceptors: {
    request: { use: vi.fn() },
    response: { use: vi.fn() },
  },
}

const mockSecureApi2 = {
  request: vi.fn(),
  interceptors: {
    request: { use: vi.fn() },
    response: { use: vi.fn() },
  },
}

vi.mock('@dtbx/store/utils', () => ({
  secureapi: mockSecureApi,
  secureapi2: mockSecureApi2,
}))

// Mock axios
const mockAxiosCreate = vi.fn(() => ({
  request: vi.fn(),
  interceptors: {
    request: { use: vi.fn() },
    response: { use: vi.fn() },
  },
}))

vi.mock('axios', () => ({
  default: {
    create: mockAxiosCreate,
  },
  AxiosError: class AxiosError extends Error {
    constructor(message: string, public response?: any) {
      super(message)
    }
  },
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('axiosBaseQuery', () => {
  let baseQuery: ReturnType<typeof axiosBaseQuery>

  beforeEach(() => {
    vi.clearAllMocks()
    baseQuery = axiosBaseQuery()
    mockLocalStorage.getItem.mockReturnValue('mock-token')
  })

  it('should create a base query function', () => {
    expect(typeof baseQuery).toBe('function')
  })

  it('should use secureapi by default', async () => {
    const mockResponse = { data: { id: 1, name: 'Test User' } }
    mockSecureApi.request.mockResolvedValue(mockResponse)

    const result = await baseQuery({
      url: '/users/me',
      method: 'GET',
    })

    expect(mockSecureApi.request).toHaveBeenCalledWith({
      url: '/users/me',
      method: 'GET',
      data: undefined,
      params: undefined,
      headers: undefined,
    })

    expect(result).toEqual({
      data: mockResponse.data,
      meta: {
        request: mockResponse.request,
        response: mockResponse,
      },
    })
  })

  it('should use secureapi2 when specified', async () => {
    const mockResponse = { data: { properties: [] } }
    mockSecureApi2.request.mockResolvedValue(mockResponse)

    const result = await baseQuery({
      url: '/properties',
      method: 'GET',
      handler: 'secureapi2',
    })

    expect(mockSecureApi2.request).toHaveBeenCalledWith({
      url: '/properties',
      method: 'GET',
      data: undefined,
      params: undefined,
      headers: undefined,
    })

    expect(result).toEqual({
      data: mockResponse.data,
      meta: {
        request: mockResponse.request,
        response: mockResponse,
      },
    })
  })

  it('should create custom axios instance for custom handler', async () => {
    const mockCustomInstance = {
      request: vi.fn().mockResolvedValue({ data: { external: true } }),
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
    }
    mockAxiosCreate.mockReturnValue(mockCustomInstance)

    const result = await baseQuery({
      url: '/external-endpoint',
      method: 'GET',
      handler: 'custom',
      baseUrl: 'https://api.external.com',
    })

    expect(mockAxiosCreate).toHaveBeenCalledWith({
      baseURL: 'https://api.external.com',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    })

    expect(mockCustomInstance.request).toHaveBeenCalledWith({
      url: '/external-endpoint',
      method: 'GET',
      data: undefined,
      params: undefined,
      headers: undefined,
    })
  })

  it('should throw error when custom handler is used without baseUrl', async () => {
    await expect(
      baseQuery({
        url: '/test',
        handler: 'custom',
        // baseUrl is missing
      })
    ).rejects.toThrow('baseUrl is required when using custom handler')
  })

  it('should handle axios errors correctly', async () => {
    const axiosError = {
      response: {
        status: 404,
        data: {
          message: 'Not found',
          error: 'Resource not found',
        },
      },
    }
    mockSecureApi.request.mockRejectedValue(axiosError)

    const result = await baseQuery({
      url: '/nonexistent',
      method: 'GET',
    })

    expect(result).toEqual({
      error: {
        status: 404,
        data: {
          message: 'Not found',
          error: 'Resource not found',
        },
      },
    })
  })

  it('should handle network errors correctly', async () => {
    const networkError = new Error('Network Error')
    mockSecureApi.request.mockRejectedValue(networkError)

    const result = await baseQuery({
      url: '/test',
      method: 'GET',
    })

    expect(result).toEqual({
      error: {
        status: 503,
        data: { message: 'Network error or server unavailable' },
      },
    })
  })

  it('should pass through request parameters correctly', async () => {
    const mockResponse = { data: { success: true } }
    mockSecureApi.request.mockResolvedValue(mockResponse)

    await baseQuery({
      url: '/test',
      method: 'POST',
      body: { name: 'Test' },
      params: { page: 1 },
      headers: { 'Custom-Header': 'value' },
    })

    expect(mockSecureApi.request).toHaveBeenCalledWith({
      url: '/test',
      method: 'POST',
      data: { name: 'Test' },
      params: { page: 1 },
      headers: { 'Custom-Header': 'value' },
    })
  })

  it('should cache custom axios instances', async () => {
    const baseUrl = 'https://api.example.com'
    
    // First call
    await baseQuery({
      url: '/test1',
      handler: 'custom',
      baseUrl,
    })

    // Second call with same baseUrl
    await baseQuery({
      url: '/test2',
      handler: 'custom',
      baseUrl,
    })

    // Should only create one instance for the same baseUrl
    expect(mockAxiosCreate).toHaveBeenCalledTimes(1)
  })
})
