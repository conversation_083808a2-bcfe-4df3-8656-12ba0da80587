import { apiSlice } from './apiSlice'
export interface ICustomer {
  id: string
  blockReason: string
  country: string
  firstName: string
  otherNames: string
  lastName: string
  phoneNumber: string
  accountNumber?: string
  email: string
  nationality: string
  idNumber: string
  idType: string
  onboardingType: string
  pinStatus: string
  isBlocked: boolean
  dateCreated: string
  dateModified: string
  sex: string
  postalAddress?: string
  storeOfValues: StoreOfValue[]
  profileAccountStoreIds: IprofileAccountStoreIds[]
}

export interface StoreOfValue {
  storeCode: string
  customerId: string
}

export interface IprofileAccountStoreIds {
  profileId: string
  storeCode: string
  description: string
  customerId: string
}

export interface ICustomersFilter {
  dateCreatedFrom?: string
  dateCreatedTo?: string
  size: number
  page: number
  firstName?: string
  otherNames?: string
  lastName?: string
  phoneNumber?: string
  email?: string
  nationality?: string
  idNumber?: string
  accountNumber?: string
  isBlocked?: string
}

export interface ICustomersDataResponse {
  data: ICustomer[]
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
}

// Extend the main API slice with customer-specific endpoints
export const customersApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get customers list using secureapi2
    getCustomers: builder.query<ICustomersDataResponse, ICustomersFilter>({
      query: (filters) => {
        const {
          dateCreatedFrom,
          dateCreatedTo,
          size,
          page,
          firstName,
          otherNames,
          lastName,
          phoneNumber,
          email,
          nationality,
          idNumber,
          accountNumber,
          isBlocked,
        } = filters

        // Build URL with query parameters
        let url = '/dbp/customers?'
        url += `page=${page ?? 0}`
        url += `&size=${size ?? 10}`
        
        if (firstName) url += `&firstName=${firstName}`
        if (lastName) url += `&lastName=${lastName}`
        if (email) url += `&email=${email}`
        if (otherNames) url += `&otherNames=${otherNames}`
        if (phoneNumber) url += `&phoneNumber=${phoneNumber}`
        if (nationality) url += `&nationality=${nationality}`
        if (idNumber) url += `&idNumber=${idNumber}`
        if (accountNumber) url += `&accountNumber=${accountNumber}`
        if (dateCreatedFrom) url += `&dateCreatedFrom=${dateCreatedFrom}`
        if (dateCreatedTo) url += `&dateCreatedTo=${dateCreatedTo}`
        if (isBlocked) url += `&isBlocked=${isBlocked}`

        return {
          url,
          method: 'GET',
          handler: 'secureapi2',
        }
      },
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Customer' as const, id })),
              { type: 'Customer', id: 'LIST' },
            ]
          : [{ type: 'Customer', id: 'LIST' }],
    }),

    // Get customer by ID
    getCustomerById: builder.query<ICustomer, string>({
      query: (id) => ({
        url: `/dbp/customers/${id}`,
        method: 'GET',
        handler: 'secureapi2',
      }),
      providesTags: (result, error, id) => [{ type: 'Customer', id }],
    }),

    // Update customer details
    updateCustomer: builder.mutation<
      any,
      { profileId: string; email: string; comments?: string }
    >({
      query: ({ profileId, email, comments = '' }) => ({
        url: `/dbp/customers/${profileId}/make`,
        method: 'PATCH',
        body: {
          email,
          comments,
        },
        handler: 'secureapi2',
      }),
      invalidatesTags: (result, error, { profileId }) => [
        { type: 'Customer', id: profileId },
        { type: 'Customer', id: 'LIST' },
      ],
    }),

    // Deactivate customer
    deactivateCustomer: builder.mutation<
      any,
      { profileId: string; reason: string }
    >({
      query: ({ profileId, reason }) => ({
        url: `/dbp/customers/${profileId}/deactivate`,
        method: 'PATCH',
        body: {
          blockReason: 'DataBreach',
          comments: reason,
        },
        handler: 'secureapi2',
      }),
      invalidatesTags: (result, error, { profileId }) => [
        { type: 'Customer', id: profileId },
        { type: 'Customer', id: 'LIST' },
      ],
    }),

    // Fetch customer account by account number
    fetchCustomerAccount: builder.query<any, string>({
      query: (account) => ({
        url: `/dbp/customers/customer-accounts?account=${account}`,
        method: 'GET',
        handler: 'secureapi2',
      }),
      providesTags: ['Customer'],
    }),

    // Get linked customer accounts by profile ID
    getLinkedCustomerAccounts: builder.query<any, string>({
      query: (profileId) => ({
        url: `/dbp/customers/${profileId}/accounts`,
        method: 'GET',
        handler: 'secureapi2',
      }),
      providesTags: (result, error, profileId) => [
        { type: 'Customer', id: profileId },
      ],
    }),
  }),
})

// Export hooks for use in components
export const {
  useGetCustomersQuery,
  useGetCustomerByIdQuery,
  useUpdateCustomerMutation,
  useDeactivateCustomerMutation,
  useFetchCustomerAccountQuery,
  useGetLinkedCustomerAccountsQuery,
} = customersApi

// Export the enhanced endpoints for manual usage if needed
export const {
  endpoints: {
    getCustomers,
    getCustomerById,
    updateCustomer,
    deactivateCustomer,
    fetchCustomerAccount,
    getLinkedCustomerAccounts,
  },
} = customersApi
