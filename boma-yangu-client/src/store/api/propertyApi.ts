import { apiSlice } from './apiSlice'

// Define types for your property-related data
export interface Property {
  id: string
  title: string
  description: string
  price: number
  location: string
  images: string[]
  status: 'available' | 'sold' | 'pending'
  createdAt: string
  updatedAt: string
}

export interface PropertyFilters {
  page?: number
  limit?: number
  status?: string
  minPrice?: number
  maxPrice?: number
  location?: string
}

export interface CreatePropertyRequest {
  title: string
  description: string
  price: number
  location: string
  images?: string[]
}

// Extend the main API slice with property-specific endpoints
export const propertyApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get properties using secureapi (default)
    getProperties: builder.query<
      { data: Property[]; total: number; page: number },
      PropertyFilters
    >({
      query: (filters) => ({
        url: '/properties',
        method: 'GET',
        params: filters,
        // Uses secureapi by default
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'Property' as const, id })),
              { type: 'Property', id: 'LIST' },
            ]
          : [{ type: 'Property', id: 'LIST' }],
    }),

    // Get property details using secureapi2
    getPropertyById: builder.query<Property, string>({
      query: (id) => ({
        url: `/properties/${id}`,
        method: 'GET',
        handler: 'secureapi2', // Using the second API handler
      }),
      providesTags: (result, error, id) => [{ type: 'Property', id }],
    }),

    // Create property using secureapi
    createProperty: builder.mutation<Property, CreatePropertyRequest>({
      query: (propertyData) => ({
        url: '/properties',
        method: 'POST',
        body: propertyData,
      }),
      invalidatesTags: [{ type: 'Property', id: 'LIST' }],
    }),

    // Update property using secureapi2
    updateProperty: builder.mutation<
      Property,
      { id: string; data: Partial<CreatePropertyRequest> }
    >({
      query: ({ id, data }) => ({
        url: `/properties/${id}`,
        method: 'PUT',
        body: data,
        handler: 'secureapi2',
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Property', id },
        { type: 'Property', id: 'LIST' },
      ],
    }),

    // Delete property using secureapi
    deleteProperty: builder.mutation<void, string>({
      query: (id) => ({
        url: `/properties/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Property', id },
        { type: 'Property', id: 'LIST' },
      ],
    }),

    // Example of using a custom handler for external API
    getPropertyValuation: builder.query<
      { estimatedValue: number; confidence: number },
      { propertyId: string; externalApiUrl: string }
    >({
      query: ({ propertyId, externalApiUrl }) => ({
        url: `/valuation/${propertyId}`,
        method: 'GET',
        handler: 'custom',
        baseUrl: externalApiUrl,
      }),
    }),

    // Upload property images using secureapi with file handling
    uploadPropertyImages: builder.mutation<
      { imageUrls: string[] },
      { propertyId: string; images: FormData }
    >({
      query: ({ propertyId, images }) => ({
        url: `/properties/${propertyId}/images`,
        method: 'POST',
        body: images,
        headers: {
          // Don't set Content-Type for FormData, let the browser set it
        },
      }),
      invalidatesTags: (result, error, { propertyId }) => [
        { type: 'Property', id: propertyId },
      ],
    }),
  }),
})

// Export hooks for use in components
export const {
  useGetPropertiesQuery,
  useGetPropertyByIdQuery,
  useCreatePropertyMutation,
  useUpdatePropertyMutation,
  useDeletePropertyMutation,
  useGetPropertyValuationQuery,
  useUploadPropertyImagesMutation,
} = propertyApi

// Export the enhanced endpoints for manual usage if needed
export const {
  endpoints: {
    getProperties,
    getPropertyById,
    createProperty,
    updateProperty,
    deleteProperty,
    getPropertyValuation,
    uploadPropertyImages,
  },
} = propertyApi
