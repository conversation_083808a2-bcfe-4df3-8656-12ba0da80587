// Main API exports
export { apiSlice } from './apiSlice'
export type { EnhancedQueryArgs, CustomQueryArgs, CustomError } from './apiSlice'

// Base query exports
export { axiosBaseQuery } from './baseQuery'

// Feature-specific API exports
export { propertyApi } from './propertyApi'
export type { Property, PropertyFilters, CreatePropertyRequest } from './propertyApi'

// Utility exports
export {
  createSecureApiQuery,
  createSecureApi2Query,
  createCustomApiQuery,
  queryPatterns,
  tagHelpers,
  errorUtils,
  transformers,
  queryConfigs,
  examples,
} from './utils'

// Re-export commonly used hooks from apiSlice
export {
  useGetCurrentUserQuery,
  useGetPropertiesQuery,
  useGetExternalDataQuery,
  useUpdateUserMutation,
  useCreatePropertyMutation,
} from './apiSlice'

// Re-export commonly used hooks from propertyApi
export {
  useGetPropertiesQuery as useGetPropertiesFromPropertyApi,
  useGetPropertyByIdQuery,
  useCreatePropertyMutation as useCreatePropertyFromPropertyApi,
  useUpdatePropertyMutation,
  useDeletePropertyMutation,
  useGetPropertyValuationQuery,
  useUploadPropertyImagesMutation,
} from './propertyApi'
