// Main API exports
export { apiSlice } from './apiSlice'
export type { EnhancedQueryArgs, CustomQueryArgs, CustomError } from './apiSlice'

// Base query exports
export { axiosBaseQuery } from './baseQuery'

// Feature-specific API exports
export { customersApi } from './customersApi'
export type {
  ICustomer,
  ICustomersFilter,
  ICustomersDataResponse,
  StoreOfValue,
  IprofileAccountStoreIds
} from './customersApi'

// Utility exports
export {
  createSecureApiQuery,
  createSecureApi2Query,
  queryPatterns,
  tagHelpers,
  errorUtils,
  transformers,
  queryConfigs,
  examples,
} from './utils'

// Re-export commonly used hooks from apiSlice
export {
  useGetCurrentUserQuery,
  useGetPropertiesQuery,
  useUpdateUserMutation,
  useCreatePropertyMutation,
} from './apiSlice'



// Re-export commonly used hooks from customersApi
export {
  useGetCustomersQuery,
  useGetCustomerByIdQuery,
  useUpdateCustomerMutation,
  useDeactivateCustomerMutation,
  useFetchCustomerAccountQuery,
  useGetLinkedCustomerAccountsQuery,
} from './customersApi'
