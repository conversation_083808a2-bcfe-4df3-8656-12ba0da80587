import { BaseQueryFn } from '@reduxjs/toolkit/query'
import { AxiosError, AxiosRequestConfig, AxiosInstance } from 'axios'
import { secureapi, secureapi2 } from '@dtbx/store/utils'
import axios from 'axios'

// Types for our custom base query
export interface CustomQueryArgs {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  body?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  handler?: 'secureapi' | 'secureapi2' | 'custom'
  baseUrl?: string
}

export interface CustomError {
  status: number
  data: {
    message?: string
    error?: string
    errors?: string[]
    code?: string
    description?: string
  }
}

// Create a custom axios instance for non-secureapi calls
const createCustomAxiosInstance = (baseUrl: string): AxiosInstance => {
  const instance = axios.create({
    baseURL: baseUrl,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
  })

  // Add token interceptor similar to secureapi
  instance.interceptors.request.use((config: any) => {
    const token = localStorage.getItem('accessToken')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  })

  // Add basic error handling
  instance.interceptors.response.use(
    (response: any) => response,
    (error: AxiosError) => {
      // Transform error to match secureapi error format
      const responseData = error.response?.data as any
      const customError: CustomError = {
        status: error.response?.status || 500,
        data: {
          message: responseData?.message || error.message || 'Something went wrong',
          error: responseData?.error,
          errors: responseData?.errors,
          code: responseData?.code,
          description: responseData?.description,
        },
      }
      return Promise.reject(customError)
    }
  )

  return instance
}

// Cache for custom axios instances to avoid recreating them
const customInstanceCache = new Map<string, AxiosInstance>()

const getAxiosInstance = (handler: string, baseUrl?: string): AxiosInstance => {
  switch (handler) {
    case 'secureapi':
      return secureapi
    case 'secureapi2':
      return secureapi2
    case 'custom':
      if (!baseUrl) {
        throw new Error('baseUrl is required when using custom handler')
      }
      
      // Use cached instance if available
      if (customInstanceCache.has(baseUrl)) {
        return customInstanceCache.get(baseUrl)!
      }
      
      // Create and cache new instance
      const instance = createCustomAxiosInstance(baseUrl)
      customInstanceCache.set(baseUrl, instance)
      return instance
    default:
      return secureapi // Default fallback
  }
}

// Custom base query function
export const axiosBaseQuery = (): BaseQueryFn<
  CustomQueryArgs,
  unknown,
  CustomError
> => async (args) => {
  try {
    const { 
      url, 
      method = 'GET', 
      body, 
      params, 
      headers,
      handler = 'secureapi',
      baseUrl 
    } = args

    const axiosInstance = getAxiosInstance(handler, baseUrl)

    const config: AxiosRequestConfig = {
      url,
      method,
      data: body,
      params,
      headers,
    }

    const result = await axiosInstance.request(config)
    
    return {
      data: result.data,
      meta: {
        request: result.request,
        response: result,
      },
    }
  } catch (axiosError) {
    // Handle both secureapi errors and custom errors
    const error = axiosError as any
    
    // If it's already in our custom error format, use it
    if (error.status && error.data) {
      return {
        error: error as CustomError,
      }
    }

    // If it's an axios error, transform it
    if (error.response) {
      return {
        error: {
          status: error.response.status,
          data: error.response.data || { message: error.message },
        } as CustomError,
      }
    }

    // Fallback for network errors
    return {
      error: {
        status: 503,
        data: { message: 'Network error or server unavailable' },
      } as CustomError,
    }
  }
}
