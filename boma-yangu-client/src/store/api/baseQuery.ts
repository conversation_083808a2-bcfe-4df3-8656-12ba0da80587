import { BaseQueryFn } from '@reduxjs/toolkit/query'
import { AxiosRequestConfig, AxiosInstance } from 'axios'
import { secureapi, secureapi2 } from '@dtbx/store/utils'

// Types for our simplified base query
export interface CustomQueryArgs {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  body?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  handler?: 'secureapi' | 'secureapi2'
}

export interface CustomError {
  status: number
  data: {
    message?: string
    error?: string
    errors?: string[]
    code?: string
    description?: string
  }
}

const getAxiosInstance = (handler: string): AxiosInstance => {
  switch (handler) {
    case 'secureapi':
      return secureapi
    case 'secureapi2':
      return secureapi2
    default:
      return secureapi // Default fallback
  }
}

export const axiosBaseQuery = (): BaseQueryFn<
  CustomQueryArgs,
  unknown,
  CustomError
> => async (args) => {
  try {
    const {
      url,
      method = 'GET',
      body,
      params,
      headers,
      handler = 'secureapi'
    } = args

    const axiosInstance = getAxiosInstance(handler)

    const config: AxiosRequestConfig = {
      url,
      method,
      data: body,
      params,
      headers,
    }

    const result = await axiosInstance.request(config)
    
    return {
      data: result.data,
      meta: {
        request: result.request,
        response: result,
      },
    }
  } catch (axiosError) {
    // Handle both secureapi errors and custom errors
    const error = axiosError as any
    
    // If it's already in our custom error format, use it
    if (error.status && error.data) {
      return {
        error: error as CustomError,
      }
    }

    // If it's an axios error, transform it
    if (error.response) {
      return {
        error: {
          status: error.response.status,
          data: error.response.data || { message: error.message },
        } as CustomError,
      }
    }

    // Fallback for network errors
    return {
      error: {
        status: 503,
        data: { message: 'Network error or server unavailable' },
      } as CustomError,
    }
  }
}
