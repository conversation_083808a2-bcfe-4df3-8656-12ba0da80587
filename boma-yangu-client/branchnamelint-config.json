{"branchNameLinter": {"prefixes": ["feature", "bugfix", "hotfix", "release", "poc", "test"], "suggestions": {"features": "feature", "feat": "feature", "releases": "release"}, "banned": [], "skip": [], "disallowed": ["main", "dev", "uat", "pre-prod", "prod"], "separator": "/", "msgBranchBanned": "❌ Invalid branch name: \"%s\". \nBranches with the name \"%s\" are not allowed. \nRename your branch with: git branch -m <new-branch-name>", "msgBranchDisallowed": "Pushing directly to \"%s\" branch is not allowed.", "msgPrefixNotAllowed": "❌ Invalid prefix: \"%s\". \nBranch prefix must be one of [feature, bugfix, hotfix, release, poc, test]. \nRename your branch with: git branch -m <new-branch-name>", "msgPrefixSuggestion": "❌ Invalid branch name. Instead of \"%s\" try \"%s\".", "msgSeparatorRequired": "❌ Invalid branch name: \"%s\". \nBranch name must contain a separator \"%s\". \nRename your branch with: git branch -m <new-branch-name>"}}