{"/Users/<USER>/projects/work/boma-yangu-client/src/app/Error.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/Error.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}}, "fnMap": {"0": {"name": "Error", "decl": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 30}}, "loc": {"start": {"line": 11, "column": 3}, "end": {"line": 13, "column": null}}}}, "branchMap": {}, "s": {"0": 7}, "f": {"0": 7}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/app/layout.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/layout.tsx", "statementMap": {"0": {"start": {"line": 29, "column": 2}, "end": {"line": 44, "column": null}}, "1": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": null}}, "2": {"start": {"line": 50, "column": 49}, "end": {"line": 52, "column": null}}, "3": {"start": {"line": 51, "column": 15}, "end": {"line": 51, "column": null}}, "4": {"start": {"line": 53, "column": 18}, "end": {"line": 53, "column": null}}, "5": {"start": {"line": 53, "column": 44}, "end": {"line": 53, "column": 67}}, "6": {"start": {"line": 54, "column": 23}, "end": {"line": 56, "column": null}}, "7": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": null}}, "8": {"start": {"line": 58, "column": 4}, "end": {"line": 59, "column": null}}, "9": {"start": {"line": 58, "column": 30}, "end": {"line": 58, "column": 71}}, "10": {"start": {"line": 60, "column": 2}, "end": {"line": 94, "column": null}}, "11": {"start": {"line": 66, "column": 37}, "end": {"line": 66, "column": null}}, "12": {"start": {"line": 86, "column": 39}, "end": {"line": 86, "column": null}}}, "fnMap": {"0": {"name": "RootLayout", "decl": {"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": 35}}, "loc": {"start": {"line": 28, "column": 4}, "end": {"line": 46, "column": null}}}, "1": {"name": "DashboardLayout", "decl": {"start": {"line": 48, "column": 9}, "end": {"line": 48, "column": 25}}, "loc": {"start": {"line": 48, "column": 64}, "end": {"line": 96, "column": null}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 5}}, "loc": {"start": {"line": 51, "column": 15}, "end": {"line": 51, "column": null}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 53, "column": 33}, "end": {"line": 53, "column": 34}}, "loc": {"start": {"line": 53, "column": 44}, "end": {"line": 53, "column": 67}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 5}}, "loc": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": null}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 58, "column": 19}, "end": {"line": 58, "column": 20}}, "loc": {"start": {"line": 58, "column": 30}, "end": {"line": 58, "column": 71}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 66, "column": 28}, "end": {"line": 66, "column": 29}}, "loc": {"start": {"line": 66, "column": 37}, "end": {"line": 66, "column": null}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 86, "column": 33}, "end": {"line": 86, "column": 39}}, "loc": {"start": {"line": 86, "column": 39}, "end": {"line": 86, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 58, "column": 4}, "end": {"line": 59, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": null}}, {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": null}}]}, "1": {"loc": {"start": {"line": 73, "column": 19}, "end": {"line": 73, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 73, "column": 40}, "end": {"line": 73, "column": 49}}, {"start": {"line": 73, "column": 49}, "end": {"line": 73, "column": null}}]}}, "s": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 13, "7": 13, "8": 13, "9": 13, "10": 13, "11": 0, "12": 0}, "f": {"0": 13, "1": 13, "2": 13, "3": 13, "4": 13, "5": 13, "6": 0, "7": 0}, "b": {"0": [13, 0], "1": [1, 12]}}, "/Users/<USER>/projects/work/boma-yangu-client/src/app/sidebar.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/sidebar.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 51}, "end": {"line": 21, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 2}, "f": {}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/app/home/<USER>": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/home/<USER>", "statementMap": {"0": {"start": {"line": 3, "column": 13}, "end": {"line": 6, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 19}}, "loc": {"start": {"line": 3, "column": 19}, "end": {"line": 6, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 3}, "f": {"0": 3}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/app/onboarding/page.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/app/onboarding/page.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 19}, "end": {"line": 5, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": 25}}, "loc": {"start": {"line": 3, "column": 25}, "end": {"line": 5, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 4}, "f": {"0": 4}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/features/home/<USER>": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/features/home/<USER>", "statementMap": {"0": {"start": {"line": 3, "column": 17}, "end": {"line": 22, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 20, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 17}, "end": {"line": 3, "column": 23}}, "loc": {"start": {"line": 3, "column": 23}, "end": {"line": 22, "column": null}}}}, "branchMap": {}, "s": {"0": 2, "1": 9}, "f": {"0": 9}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/features/onboarding/OnboardingPage.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/features/onboarding/OnboardingPage.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 23}, "end": {"line": 22, "column": null}}, "1": {"start": {"line": 4, "column": 2}, "end": {"line": 20, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 29}}, "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 22, "column": null}}}}, "branchMap": {}, "s": {"0": 2, "1": 12}, "f": {"0": 12}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/store/AppProvider.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/store/AppProvider.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": null}}, "1": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}}, "fnMap": {"0": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 36}}, "loc": {"start": {"line": 15, "column": 3}, "end": {"line": 17, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 6}, "f": {"0": 6}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/store/index.tsx": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/store/index.tsx", "statementMap": {"0": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": null}}, "1": {"start": {"line": 10, "column": 14}, "end": {"line": 18, "column": null}}, "2": {"start": {"line": 13, "column": 4}, "end": {"line": 17, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 15}}, "loc": {"start": {"line": 13, "column": 4}, "end": {"line": 17, "column": null}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1}, "f": {"0": 1}, "b": {}}, "/Users/<USER>/projects/work/boma-yangu-client/src/store/reducers/store.ts": {"path": "/Users/<USER>/projects/work/boma-yangu-client/src/store/reducers/store.ts", "statementMap": {"0": {"start": {"line": 9, "column": 27}, "end": {"line": 14, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 3}, "f": {}, "b": {}}}