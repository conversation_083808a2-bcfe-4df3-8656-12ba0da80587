#!/usr/bin/env node

/**
 * Script to check coverage for changed files and enforce 70% minimum
 * Usage: node scripts/check-changed-files-coverage.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Get list of changed files from git
 * @returns {Array} - List of changed file paths
 */
function getChangedFiles() {
  try {
    // Get staged files (for pre-commit) or changed files in current branch
    let gitCommand = 'git diff --cached --name-only';
    let output = execSync(gitCommand, { encoding: 'utf8' }).trim();
    
    // If no staged files, check for uncommitted changes
    if (!output) {
      gitCommand = 'git diff --name-only';
      output = execSync(gitCommand, { encoding: 'utf8' }).trim();
    }
    
    // If still no changes, check against main branch
    if (!output) {
      gitCommand = 'git diff main --name-only';
      output = execSync(gitCommand, { encoding: 'utf8' }).trim();
    }
    
    return output ? output.split('\n').filter(file => file.trim()) : [];
  } catch (error) {
    console.warn('Warning: Could not get changed files from git');
    return [];
  }
}

/**
 * Filter for source files that need coverage checking
 * @param {Array} files - List of file paths
 * @returns {Array} - Filtered source files
 */
function filterSourceFiles(files) {
  return files.filter(file => {
    // Include TypeScript/JavaScript source files in src/
    if (!file.startsWith('src/')) return false;
    if (!file.match(/\.(ts|tsx|js|jsx)$/)) return false;
    
    // Exclude test files, config files, and utilities
    if (file.match(/\.(test|spec)\.(ts|tsx|js|jsx)$/)) return false;
    if (file.includes('test-utils/')) return false;
    if (file.includes('__tests__/')) return false;
    if (file.match(/\.(config|setup)\.(ts|tsx|js|jsx)$/)) return false;
    
    return true;
  });
}

/**
 * Run coverage for specific files
 * @param {Array} files - List of source files
 * @returns {Object} - Coverage results per file
 */
function getCoverageForFiles(files) {
  if (files.length === 0) {
    return {};
  }

  try {
    console.log(`🧪 Checking coverage for ${files.length} changed file(s)...`);

    // Run full test suite with coverage to get accurate results
    const coverageOutput = execSync(
      'pnpm test:coverage',
      { encoding: 'utf8', stdio: ['pipe', 'pipe', 'pipe'] }
    );

    // Extract coverage information from output
    const coverageResults = {};

    // Parse the coverage table output
    files.forEach(file => {
      const fileName = path.basename(file);
      const relativePath = file.replace('src/', '');

      // Look for the file in coverage output with various patterns
      const patterns = [
        new RegExp(`${fileName}\\s+\\|\\s+(\\d+(?:\\.\\d+)?)`, 'g'),
        new RegExp(`${relativePath}\\s+\\|\\s+(\\d+(?:\\.\\d+)?)`, 'g'),
        new RegExp(`${file}\\s+\\|\\s+(\\d+(?:\\.\\d+)?)`, 'g')
      ];

      let coverage = 0;

      for (const pattern of patterns) {
        const matches = [...coverageOutput.matchAll(pattern)];
        if (matches.length > 0) {
          // Get the first percentage (usually statements coverage)
          coverage = parseFloat(matches[0][1]);
          break;
        }
      }

      // If still no coverage found, try a simpler approach
      if (coverage === 0) {
        // Look for any line containing the filename and a percentage
        const lines = coverageOutput.split('\n');
        for (const line of lines) {
          if (line.includes(fileName) && line.includes('%')) {
            const percentMatch = line.match(/(\\d+(?:\\.\\d+)?)%/);
            if (percentMatch) {
              coverage = parseFloat(percentMatch[1]);
              break;
            }
          }
        }
      }

      coverageResults[file] = coverage;
    });

    return coverageResults;

  } catch (error) {
    console.error('Error running coverage:', error.message);

    // Return 0% coverage for all files if coverage check fails
    const results = {};
    files.forEach(file => {
      results[file] = 0;
    });
    return results;
  }
}

/**
 * Check if all files meet the coverage threshold
 * @param {Object} coverageResults - Coverage results per file
 * @param {number} threshold - Minimum coverage percentage (default: 70)
 * @returns {boolean} - True if all files meet threshold
 */
function checkCoverageThreshold(coverageResults, threshold = 70) {
  const files = Object.keys(coverageResults);
  
  if (files.length === 0) {
    console.log('✅ No source files changed - coverage check passed');
    return true;
  }
  
  console.log(`\n📊 Coverage Results (minimum: ${threshold}%):`);
  
  let allPassed = true;
  
  files.forEach(file => {
    const coverage = coverageResults[file];
    const status = coverage >= threshold ? '✅' : '❌';
    const fileName = path.relative(process.cwd(), file);
    
    console.log(`  ${status} ${fileName}: ${coverage}%`);
    
    if (coverage < threshold) {
      allPassed = false;
    }
  });
  
  return allPassed;
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🔍 Checking coverage for changed files...\n');
    
    // Get changed files
    const changedFiles = getChangedFiles();
    console.log(`📁 Found ${changedFiles.length} changed file(s)`);
    
    // Filter for source files
    const sourceFiles = filterSourceFiles(changedFiles);
    console.log(`📝 Found ${sourceFiles.length} source file(s) to check`);
    
    if (sourceFiles.length === 0) {
      console.log('✅ No source files to check - coverage validation passed');
      return;
    }
    
    // Get coverage for the files
    const coverageResults = getCoverageForFiles(sourceFiles);
    
    // Check if all files meet the threshold
    const passed = checkCoverageThreshold(coverageResults, 70);
    
    if (passed) {
      console.log('\n✅ All changed files meet the 70% coverage requirement!');
    } else {
      console.log('\n❌ Some files do not meet the 70% coverage requirement.');
      console.log('Please add tests to increase coverage before committing.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error checking coverage:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { 
  getChangedFiles, 
  filterSourceFiles, 
  getCoverageForFiles, 
  checkCoverageThreshold 
};
