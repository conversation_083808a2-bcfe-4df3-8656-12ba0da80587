#!/usr/bin/env node

/**
 * Commit message validation script
 * Enforces Conventional Commits with mandatory scope
 * Usage: node scripts/validate-commit-msg.js <commit-msg-file>
 */

const fs = require('fs');
const path = require('path');

// Valid commit types
const VALID_TYPES = [
  'feat',     // New feature
  'fix',      // Bug fix
  'docs',     // Documentation changes
  'style',    // Code style changes (formatting, etc.)
  'refactor', // Code refactoring
  'test',     // Adding or updating tests
  'chore',    // Maintenance tasks
  'perf',     // Performance improvements
  'ci',       // CI/CD changes
  'build',    // Build system changes
  'revert'    // Revert previous commit
];

// Valid scopes for this project
const VALID_SCOPES = [
  // Features
  'auth',
  'home',
  'onboarding',
  'dashboard',
  'profile',
  'settings',
  'notifications',
  
  // Components
  'components',
  'ui',
  'layout',
  'sidebar',
  'navbar',
  'forms',
  'buttons',
  'modals',
  
  // Technical
  'api',
  'store',
  'hooks',
  'utils',
  'types',
  'config',
  'deps',
  'security',
  
  // Testing & Quality
  'tests',
  'coverage',
  'lint',
  'ci',
  'build',
  
  // Documentation
  'docs',
  'readme',
  'contributing',
  
  // Infrastructure
  'deployment',
  'env',
  'docker',
  'scripts'
];

/**
 * Validate commit message format
 * @param {string} message - The commit message
 * @returns {Object} - Validation result
 */
function validateCommitMessage(message) {
  const lines = message.trim().split('\n');
  const firstLine = lines[0];
  
  // Skip merge commits and revert commits with special formats
  if (firstLine.startsWith('Merge ') || firstLine.startsWith('Revert ')) {
    return { valid: true, message: 'Merge/Revert commit - validation skipped' };
  }
  
  // Conventional Commits pattern with mandatory scope
  // Format: type(scope): description
  const conventionalCommitPattern = /^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)\(([a-z-]+)\): (.{1,72})$/;
  
  const match = firstLine.match(conventionalCommitPattern);
  
  if (!match) {
    return {
      valid: false,
      message: `Invalid commit message format!

Expected format: type(scope): description

Examples:
  feat(auth): add OAuth2 login integration
  fix(sidebar): resolve navigation menu collapse
  docs(readme): update installation instructions
  test(components): add HomePage unit tests

Rules:
  ✅ Type must be one of: ${VALID_TYPES.join(', ')}
  ✅ Scope is MANDATORY and must be in parentheses
  ✅ Scope must be one of the predefined scopes
  ✅ Description must be 1-72 characters
  ✅ Use present tense ("add" not "added")
  ✅ Use lowercase for type and scope

Your message: "${firstLine}"`
    };
  }
  
  const [, type, scope, description] = match;
  
  // Validate type
  if (!VALID_TYPES.includes(type)) {
    return {
      valid: false,
      message: `Invalid commit type: "${type}"

Valid types: ${VALID_TYPES.join(', ')}

Your message: "${firstLine}"`
    };
  }
  
  // Validate scope
  if (!VALID_SCOPES.includes(scope)) {
    return {
      valid: false,
      message: `Invalid commit scope: "${scope}"

Valid scopes:
  Features: auth, home, onboarding, dashboard, profile, settings, notifications
  Components: components, ui, layout, sidebar, navbar, forms, buttons, modals
  Technical: api, store, hooks, utils, types, config, deps, security
  Testing: tests, coverage, lint, ci, build
  Docs: docs, readme, contributing
  Infrastructure: deployment, env, docker, scripts

Your message: "${firstLine}"`
    };
  }
  
  // Validate description
  if (description.length < 1) {
    return {
      valid: false,
      message: `Commit description is too short!

Description must be at least 1 character.

Your message: "${firstLine}"`
    };
  }
  
  if (description.length > 72) {
    return {
      valid: false,
      message: `Commit description is too long!

Description must be 72 characters or less.
Current length: ${description.length}

Your message: "${firstLine}"`
    };
  }
  
  // Check for proper capitalization (should start with lowercase)
  if (description[0] !== description[0].toLowerCase()) {
    return {
      valid: false,
      message: `Commit description should start with lowercase!

Use: "${type}(${scope}): ${description[0].toLowerCase()}${description.slice(1)}"
Not: "${firstLine}"

Your message: "${firstLine}"`
    };
  }
  
  return {
    valid: true,
    message: `✅ Valid commit message: ${type}(${scope}): ${description}`
  };
}

/**
 * Main function
 */
function main() {
  const commitMsgFile = process.argv[2];
  
  if (!commitMsgFile) {
    console.error('❌ Error: Commit message file not provided');
    process.exit(1);
  }
  
  if (!fs.existsSync(commitMsgFile)) {
    console.error(`❌ Error: Commit message file not found: ${commitMsgFile}`);
    process.exit(1);
  }
  
  try {
    const commitMessage = fs.readFileSync(commitMsgFile, 'utf8');
    const validation = validateCommitMessage(commitMessage);
    
    if (validation.valid) {
      console.log(`✅ ${validation.message}`);
      process.exit(0);
    } else {
      console.error(`❌ ${validation.message}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Error reading commit message: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { validateCommitMessage, VALID_TYPES, VALID_SCOPES };
